package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * dataset_datas DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库数据表DTO
 */
@Data
public class DatasetDataDTO {

    @Schema(description = "主键ID")
    private String _id;

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "团队ID")
    private String teamId;

    @Schema(description = "团队成员ID")
    private String tmbId;

    @Schema(description = "知识库ID")
    private String datasetId;

    @Schema(description = "集合ID")
    private String collectionId;

    @Schema(description = "问题内容")
    private String q;

    @Schema(description = "答案内容")
    private String a;

    @Schema(description = "索引列表")
    private List<IndexDTO> indexes;

    @Schema(description = "分块索引")
    private Integer chunkIndex;

    @Schema(description = "历史记录")
    private List<Object> history;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "来源名称")
    private String sourceName;

    @Schema(description = "是否拥有者")
    private Boolean isOwner;

    @Schema(description = "绑定文件的id")
    private String fileId;

    @Schema(description = "类型（folder/file/images）")
    private String type;

    @Schema(description = "类型为图片时: 绑定的图片Id")
    private String imageId;

    @Schema(description = "type=images时: 绑定的图片大小")
    private long imageSize;

    @Schema(description = "类型为图片时: 绑定图片预览URL")
    private String imagePreviewUrl;

    @Data
    public static class IndexDTO {
        @Schema(description = "索引类型")
        private String type;

        @Schema(description = "数据ID")
        private String dataId;

        @Schema(description = "文本内容")
        private String text;

        @Schema(description = "索引主键ID")
        private String _id;
    }
} 