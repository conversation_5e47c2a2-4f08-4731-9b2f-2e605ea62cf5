package com.sinitek.mind.dataset.core.vector.impl;

import com.sinitek.mind.dataset.core.vector.IDatasetVectorStore;
import com.sinitek.mind.dataset.core.vector.IDatasetVectorStoreFactory;
import com.sinitek.mind.dataset.dto.DatasetDataDTO;
import com.sinitek.mind.dataset.dto.DatasetTrainingDTO;
import com.sinitek.sirm.common.spring.SpringFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 向量存储通用抽象类
 *
 * <AUTHOR>
 * date 2025-07-26
 */
public abstract class AbstractDatasetVectorStore implements IDatasetVectorStore {

    /**
     * 通过datasetId获取VectorStore，默认实现
     */
    public VectorStore getVectorStoreByDatabaseId(String datasetId) {
        IDatasetVectorStoreFactory datasetVectorStoreFactory = SpringFactory.getBean(IDatasetVectorStoreFactory.class);
        return datasetVectorStoreFactory.getVectorStoreByDatabaseId(datasetId);
    }

    /**
     * 进行相似性搜索
     * @param query
     * @param datasetIdList
     * @return
     */
    public List<Document> similaritySearch(String query, List<String> datasetIdList) {
        // TODO: 检查datasetIdList的向量模型是同一个
        if (CollectionUtils.isEmpty(datasetIdList)) {
            return new ArrayList<>();
        }
        VectorStore vectorStore = this.getVectorStoreByDatabaseId(datasetIdList.get(0));
        FilterExpressionBuilder filter = new FilterExpressionBuilder();

        Filter.Expression datasetIdFilter;
        if (datasetIdList.size() == 1) {
            datasetIdFilter = filter.eq("datasetId", datasetIdList.get(0)).build();
        } else {
            datasetIdFilter = filter.in("datasetId", datasetIdList).build();
        }

        SearchRequest searchRequest = SearchRequest.builder()
                .query(query)
                .filterExpression(datasetIdFilter)
                .topK(1000)
                .build();
        return vectorStore.similaritySearch(searchRequest);
    }

    /**
     * datasetTraining转换为向量数据
     * @param datasetTraining
     */
    public void addDatasetTraining(DatasetTrainingDTO datasetTraining) {
        String datasetId = datasetTraining.getDatasetId();

        String text = datasetTraining.getText();
        Document doc = new Document(text);
        Map<String, Object> metadata = doc.getMetadata();
        metadata.put("datasetId", datasetId);
        metadata.put("teamId", datasetTraining.getTeamId());
        metadata.put("collectionId", datasetTraining.getCollectionId());
        metadata.put("dataId", datasetTraining.getDataId());

        VectorStore vectorStore = this.getVectorStoreByDatabaseId(datasetId);
        vectorStore.add(List.of(doc));
    }

    @Override
    public void addDatasetDataDTO(DatasetDataDTO datasetDataDTO) {
        String datasetId = datasetDataDTO.getDatasetId();

        String text = datasetDataDTO.getQ();
        Document doc = new Document(text);
        Map<String, Object> metadata = doc.getMetadata();
        metadata.put("datasetId", datasetId);
        metadata.put("teamId", datasetDataDTO.getTeamId());
        metadata.put("collectionId", datasetDataDTO.getCollectionId());
        metadata.put("dataId", datasetDataDTO.getId());

        VectorStore vectorStore = this.getVectorStoreByDatabaseId(datasetId);
        vectorStore.add(List.of(doc));
    }

    @Override
    public void deleteDatasetVectorData(String datasetId) {
        VectorStore vectorStore = this.getVectorStoreByDatabaseId(datasetId);
        // 构建条件表达式，查询指定datasetId的向量数据
        FilterExpressionBuilder filter = new FilterExpressionBuilder();
        Filter.Expression datasetIdFilter = filter.eq("datasetId", datasetId).build();
        
        // 执行删除
        vectorStore.delete(datasetIdFilter);
    }
}
