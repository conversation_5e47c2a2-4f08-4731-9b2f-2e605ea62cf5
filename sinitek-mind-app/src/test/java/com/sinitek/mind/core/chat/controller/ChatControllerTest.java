package com.sinitek.mind.core.chat.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.chat.dto.*;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import com.sinitek.mind.core.workflow.model.FlowNodeOutputItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * ChatController - 单元测试
 */
@Slf4j
public class ChatControllerTest extends MindBaseTestApplication {

    /**
     * 测试聊天接口完整流程
     */
    @Test
    @SneakyThrows
    @DisplayName("测试聊天接口完整流程")
    public void testChatCompleteFlow() {
        // 创建应用
        String appId = createApp();
        
        try {
            // 测试初始化聊天
            String chatId = testInitChat(appId);
            
            // 测试分页获取聊天记录
            testGetPaginationRecords(appId, chatId);
            
            // 测试获取聊天历史记录
            testGetHistories(appId);
            
            // 测试更新历史标题
            testUpdateHistory(appId, chatId);
            
            // 测试删除历史
            testDelHistory(appId, chatId);
            
            // 测试清空历史
            testClearHistories(appId);
            
        } finally {
            // 清理：删除应用
            deleteApp(appId);
        }
    }

    /**
     * 测试初始化聊天接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试初始化聊天接口")
    public void testInitChat() {
        // 创建应用
        String appId = createApp();
        
        try {
            // 测试初始化聊天
            testInitChat(appId);
        } finally {
            // 清理：删除应用
            deleteApp(appId);
        }
    }

    /**
     * 测试分页获取聊天记录接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试分页获取聊天记录接口")
    public void testGetPaginationRecords() {
        // 创建应用
        String appId = createApp();
        
        try {
            // 先初始化聊天
            String chatId = testInitChat(appId);
            
            // 测试分页获取聊天记录
            testGetPaginationRecords(appId, chatId);
        } finally {
            // 清理：删除应用
            deleteApp(appId);
        }
    }

    /**
     * 测试获取聊天历史记录接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取聊天历史记录接口")
    public void testGetHistories() {
        // 创建应用
        String appId = createApp();
        
        try {
            // 测试获取聊天历史记录
            testGetHistories(appId);
        } finally {
            // 清理：删除应用
            deleteApp(appId);
        }
    }

    /**
     * 测试更新历史标题接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试更新历史标题接口")
    public void testUpdateHistory() {
        // 创建应用
        String appId = createApp();
        
        try {
            // 先初始化聊天
            String chatId = testInitChat(appId);
            
            // 测试更新历史标题
            testUpdateHistory(appId, chatId);
        } finally {
            // 清理：删除应用
            deleteApp(appId);
        }
    }

    /**
     * 测试删除历史接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试删除历史接口")
    public void testDelHistory() {
        // 创建应用
        String appId = createApp();
        
        try {
            // 先初始化聊天
            String chatId = testInitChat(appId);
            
            // 测试删除历史
            testDelHistory(appId, chatId);
        } finally {
            // 清理：删除应用
            deleteApp(appId);
        }
    }

    /**
     * 测试清空历史接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试清空历史接口")
    public void testClearHistories() {
        // 创建应用
        String appId = createApp();
        
        try {
            // 测试清空历史
            testClearHistories(appId);
        } finally {
            // 清理：删除应用
            deleteApp(appId);
        }
    }

    /**
     * 测试免登录初始化聊天接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试免登录初始化聊天接口")
    public void testOutLinkInitChat() {
        // 构造请求参数
        String shareId = "test_share_id_" + System.currentTimeMillis();
        String outLinkUid = "test_outlink_uid_" + System.currentTimeMillis();
        String authToken = "test_auth_token_" + System.currentTimeMillis();

        // 执行请求
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/chat/outLink/init")
                        .param("shareId", shareId)
                        .param("outLinkUid", outLinkUid)
                        .param("authToken", authToken)
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/chat/outLink/init接口的返回结果: {}", contentAsString);
    }

    /**
     * 初始化聊天
     */
    @SneakyThrows
    private String testInitChat(String appId) {
        // 执行初始化聊天请求
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/chat/init")
                        .param("appId", appId)
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/chat/init接口的返回结果: {}", contentAsString);

        // 从响应中提取chatId
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readTree(contentAsString).path("data").path("chatId").asText();
    }

    /**
     * 分页获取聊天记录
     */
    @SneakyThrows
    private void testGetPaginationRecords(String appId, String chatId) {
        // 构造请求参数
        GetPaginationRecordsRequest request = new GetPaginationRecordsRequest();
        request.setAppId(appId);
        request.setChatId(chatId);
        request.setPageSize(10);
        request.setOffset(0);

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/chat/getPaginationRecords")
                        .content(JsonUtil.toJsonString(request))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/chat/getPaginationRecords接口的返回结果: {}", contentAsString);
    }

    /**
     * 获取聊天历史记录
     */
    @SneakyThrows
    private void testGetHistories(String appId) {
        // 构造请求参数
        GetHistoriesRequest request = new GetHistoriesRequest();
        request.setAppId(appId);

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/chat/getHistories")
                        .content(JsonUtil.toJsonString(request))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/chat/getHistories接口的返回结果: {}", contentAsString);
    }

    /**
     * 更新历史标题
     */
    @SneakyThrows
    private void testUpdateHistory(String appId, String chatId) {
        // 构造请求参数
        UpdateHistoryRequest request = new UpdateHistoryRequest();
        request.setAppId(appId);
        request.setChatId(chatId);
        request.setTitle("测试更新标题");
        request.setCustomTitle("测试自定义标题");
        request.setTop(false);

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/chat/updateHistory")
                        .content(JsonUtil.toJsonString(request))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/chat/updateHistory接口的返回结果: {}", contentAsString);
    }

    /**
     * 删除历史
     */
    @SneakyThrows
    private void testDelHistory(String appId, String chatId) {
        // 构造请求参数
        DelHistoryRequest request = new DelHistoryRequest();
        request.setAppId(appId);
        request.setChatId(chatId);

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/chat/delHistory")
                        .content(JsonUtil.toJsonString(request))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/chat/delHistory接口的返回结果: {}", contentAsString);
    }

    /**
     * 清空历史
     */
    @SneakyThrows
    private void testClearHistories(String appId) {
        // 构造请求参数
        ClearHistortiesRequest request = new ClearHistortiesRequest();
        request.setAppId(appId);

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/chat/clearHistories")
                        .content(JsonUtil.toJsonString(request))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/chat/clearHistories接口的返回结果: {}", contentAsString);
    }

    /**
     * 创建应用（复用AppControllerTest中的创建逻辑）
     */
    @SneakyThrows
    private String createApp() {
        // 构造创建应用请求
        CreateAppDTO createAppDTO = new CreateAppDTO();
        createAppDTO.setParentId(null);
        createAppDTO.setAvatar("core/app/type/simpleFill");
        createAppDTO.setName("聊天测试应用" + System.currentTimeMillis());
        createAppDTO.setType("simple");
        
        // 构造简化的modules数据
        List<StoreNodeItemType> modules = new ArrayList<>();
        
        // 添加userGuide节点
        StoreNodeItemType userGuideNode = new StoreNodeItemType();
        userGuideNode.setNodeId("userGuide");
        userGuideNode.setName("common:core.module.template.system_config");
        userGuideNode.setIntro("common:core.module.template.config_params");
        userGuideNode.setAvatar("core/workflow/template/systemConfig");
        userGuideNode.setFlowNodeType("userGuide");
        
        com.sinitek.mind.core.app.model.PositionInfo userGuidePosition = new com.sinitek.mind.core.app.model.PositionInfo();
        userGuidePosition.setX(531.2422736065552);
        userGuidePosition.setY(-486.7611729549753);
        userGuideNode.setPosition(userGuidePosition);
        userGuideNode.setVersion("481");
        
        // 添加userGuide的inputs
        List<FlowNodeInputItemType> userGuideInputs = new ArrayList<>();
        
        // welcomeText输入项
        FlowNodeInputItemType welcomeTextInput = new FlowNodeInputItemType();
        welcomeTextInput.setKey("welcomeText");
        welcomeTextInput.setRenderTypeList(List.of("hidden"));
        welcomeTextInput.setValueType("string");
        welcomeTextInput.setLabel("core.app.Welcome Text");
        welcomeTextInput.setValue("");
        userGuideInputs.add(welcomeTextInput);
        
        // variables输入项
        FlowNodeInputItemType variablesInput = new FlowNodeInputItemType();
        variablesInput.setKey("variables");
        variablesInput.setRenderTypeList(List.of("hidden"));
        variablesInput.setValueType("any");
        variablesInput.setLabel("core.app.Chat Variable");
        variablesInput.setValue(new ArrayList<>());
        userGuideInputs.add(variablesInput);
        
        // questionGuide输入项
        FlowNodeInputItemType questionGuideInput = new FlowNodeInputItemType();
        questionGuideInput.setKey("questionGuide");
        questionGuideInput.setValueType("object");
        questionGuideInput.setRenderTypeList(List.of("hidden"));
        questionGuideInput.setLabel("core.app.Question Guide");
        Map<String, Object> questionGuideValue = new HashMap<>();
        questionGuideValue.put("open", false);
        questionGuideInput.setValue(questionGuideValue);
        userGuideInputs.add(questionGuideInput);
        
        userGuideNode.setInputs(userGuideInputs);
        userGuideNode.setOutputs(new ArrayList<>());
        modules.add(userGuideNode);
        
        // 添加workflowStart节点
        StoreNodeItemType workflowStartNode = new StoreNodeItemType();
        workflowStartNode.setNodeId("448745");
        workflowStartNode.setName("common:core.module.template.work_start");
        workflowStartNode.setIntro("");
        workflowStartNode.setAvatar("core/workflow/template/workflowStart");
        workflowStartNode.setFlowNodeType("workflowStart");
        
        com.sinitek.mind.core.app.model.PositionInfo workflowStartPosition = new com.sinitek.mind.core.app.model.PositionInfo();
        workflowStartPosition.setX(558.4082376415505);
        workflowStartPosition.setY(123.72387429194112);
        workflowStartNode.setPosition(workflowStartPosition);
        workflowStartNode.setVersion("481");
        
        // 添加workflowStart的inputs
        List<FlowNodeInputItemType> workflowStartInputs = new ArrayList<>();
        
        FlowNodeInputItemType userChatInput = new FlowNodeInputItemType();
        userChatInput.setKey("userChatInput");
        userChatInput.setRenderTypeList(List.of("reference", "textarea"));
        userChatInput.setValueType("string");
        userChatInput.setLabel("common:core.module.input.label.user question");
        userChatInput.setRequired(true);
        userChatInput.setToolDescription("common:core.module.input.label.user question");
        workflowStartInputs.add(userChatInput);
        
        workflowStartNode.setInputs(workflowStartInputs);
        
        // 添加workflowStart的outputs
        List<FlowNodeOutputItemType> workflowStartOutputs = new ArrayList<>();
        
        FlowNodeOutputItemType userChatOutput = new FlowNodeOutputItemType();
        userChatOutput.setId("userChatInput");
        userChatOutput.setKey("userChatInput");
        userChatOutput.setLabel("core.module.input.label.user question");
        userChatOutput.setValueType("string");
        userChatOutput.setType("static");
        workflowStartOutputs.add(userChatOutput);
        
        workflowStartNode.setOutputs(workflowStartOutputs);
        modules.add(workflowStartNode);
        
        createAppDTO.setModules(modules);
        createAppDTO.setEdges(new ArrayList<>());
        
        // 设置chatConfig
        createAppDTO.setChatConfig(new com.sinitek.mind.core.app.model.AppChatConfigType());

        // 执行创建请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/create")
                        .content(JsonUtil.toJsonString(createAppDTO))
        );

        // 验证结果并获取应用ID
        MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/app/create接口的返回结果: {}", contentAsString);

        // 从响应中提取应用ID
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readTree(contentAsString).path("data").asText();
    }

    /**
     * 删除应用
     */
    @SneakyThrows
    private void deleteApp(String appId) {
        // 执行删除请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/del")
                        .param("appId", appId)
        );

        // 验证结果
        perform.andExpect(status().isOk());

        log.info("/app/del接口调用成功，删除应用ID: {}", appId);
    }
}