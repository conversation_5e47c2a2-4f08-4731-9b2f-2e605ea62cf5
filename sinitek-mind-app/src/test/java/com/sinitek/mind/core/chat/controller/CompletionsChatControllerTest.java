package com.sinitek.mind.core.chat.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.chat.dto.V2ChatCompletionRequest;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import com.sinitek.mind.core.workflow.model.FlowNodeOutputItemType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.mind.support.openapi.dto.CreateApiKeyRequestDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.io.BufferedReader;
import java.io.StringReader;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.hamcrest.Matchers.notNullValue;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * CompletionsChatController - 单元测试
 */
@Slf4j
public class CompletionsChatControllerTest extends MindBaseTestApplication {

    public static final String APP_DEFAULT_MODULE = "my-gpt-4.1-mini";
    @LocalServerPort
    private int port;

    private WebClient webClient;

    @BeforeEach
    public void setUp() throws Exception {
        super.before();
        // 初始化WebClient
        this.webClient = WebClient.builder()
                .baseUrl("http://localhost:" + port)
                .defaultHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader("accesstoken", adminToken)
                .build();
    }

    /**
     * 测试API对话接口完整流程（需要登录）
     */
    @Test
    @SneakyThrows
    @DisplayName("测试API对话接口完整流程（需要登录）")
    public void testApiV2ChatCompletions() {
        // 创建应用
        String appId = createApp();
        
        try {
            // 测试对话接口
            testApiChatCompletion(appId);
        } finally {
            // 清理：删除应用
            deleteApp(appId);
        }
    }

    /**
     * 测试Open-API对话接口完整流程（需要API密钥）
     */
    @Test
    @SneakyThrows
    @DisplayName("测试Open-API对话接口完整流程（需要API密钥）")
    public void testOpenApiV2ChatCompletions() {
        // 创建应用
        String appId = createApp();
        
        try {
            // 创建API密钥
            String apiKey = createApiKey(appId);
            
            // 测试Open-API对话接口
            testOpenApiChatCompletion(appId, apiKey);
            
            // 清理API密钥
            deleteApiKey(apiKey);
        } finally {
            // 清理：删除应用
            deleteApp(appId);
        }
    }

    /**
     * 测试API对话接口（需要登录）
     */
    @SneakyThrows
    private void testApiChatCompletion(String appId) {
        // 构造对话请求
        V2ChatCompletionRequest request = buildChatCompletionRequest(appId);
        
        // 使用WebClient处理流式响应
        processEventStreamWithWebClient("/mind/api/v2/chat/completions", request, "API");
    }

    /**
     * 测试Open-API对话接口（需要API密钥）
     */
    @SneakyThrows
    private void testOpenApiChatCompletion(String appId, String apiKey) {
        // 构造对话请求
        V2ChatCompletionRequest request = buildChatCompletionRequest(appId);
        
        // 为Open-API创建单独的WebClient（使用Bearer Token）
        WebClient openApiWebClient = WebClient.builder()
                .baseUrl("http://localhost:" + port)
                .defaultHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader("Authorization", "Bearer " + apiKey)
                .build();
        
        // 使用WebClient处理流式响应
        processEventStreamWithWebClient(openApiWebClient, "/mind/open-api/v2/chat/completions", request, "Open-API");
    }

    /**
     * 构造对话请求数据
     */
    private V2ChatCompletionRequest buildChatCompletionRequest(String appId) {
        V2ChatCompletionRequest request = new V2ChatCompletionRequest();
        request.setAppId(appId);
        request.setChatId(null); // 新建对话
        request.setStream(true);
        request.setDetail(true);
        request.setRetainDatasetCite(false);
        
        // 构造消息内容
        List<ChatCompletionMessageParam> messages = new ArrayList<>();
        ChatCompletionMessageParam message = new ChatCompletionMessageParam();
        message.setContent("你是谁");
        message.setRole("user");
        messages.add(message);
        request.setMessages(messages);
        
        // 设置变量
        Map<String, Object> variables = new HashMap<>();
        request.setVariables(variables);
        
        return request;
    }

    /**
     * 创建应用
     */
    @SneakyThrows
    private String createApp() {
        // 构造创建应用请求
        CreateAppDTO createAppDTO = new CreateAppDTO();
        createAppDTO.setParentId(null);
        createAppDTO.setAvatar("core/app/type/simpleFill");
        createAppDTO.setName("聊天测试应用" + System.currentTimeMillis());
        createAppDTO.setType("simple");
        
        // 构造完整的modules数据（基于提供的JSON数据）
        List<StoreNodeItemType> modules = new ArrayList<>();
        
        // 添加userGuide节点
        StoreNodeItemType userGuideNode = new StoreNodeItemType();
        userGuideNode.setNodeId("userGuide");
        userGuideNode.setName("common:core.module.template.system_config");
        userGuideNode.setIntro("common:core.module.template.config_params");
        userGuideNode.setAvatar("core/workflow/template/systemConfig");
        userGuideNode.setFlowNodeType("userGuide");
        
        com.sinitek.mind.core.app.model.PositionInfo userGuidePosition = new com.sinitek.mind.core.app.model.PositionInfo();
        userGuidePosition.setX(531.2422736065552);
        userGuidePosition.setY(-486.7611729549753);
        userGuideNode.setPosition(userGuidePosition);
        userGuideNode.setVersion("481");
        
        // 添加userGuide的inputs
        List<FlowNodeInputItemType> userGuideInputs = new ArrayList<>();
        
        // welcomeText输入项
        FlowNodeInputItemType welcomeTextInput = new FlowNodeInputItemType();
        welcomeTextInput.setKey("welcomeText");
        welcomeTextInput.setRenderTypeList(List.of("hidden"));
        welcomeTextInput.setValueType("string");
        welcomeTextInput.setLabel("core.app.Welcome Text");
        welcomeTextInput.setValue("");
        userGuideInputs.add(welcomeTextInput);
        
        // variables输入项
        FlowNodeInputItemType variablesInput = new FlowNodeInputItemType();
        variablesInput.setKey("variables");
        variablesInput.setRenderTypeList(List.of("hidden"));
        variablesInput.setValueType("any");
        variablesInput.setLabel("core.app.Chat Variable");
        variablesInput.setValue(new ArrayList<>());
        userGuideInputs.add(variablesInput);
        
        // questionGuide输入项
        FlowNodeInputItemType questionGuideInput = new FlowNodeInputItemType();
        questionGuideInput.setKey("questionGuide");
        questionGuideInput.setValueType("object");
        questionGuideInput.setRenderTypeList(List.of("hidden"));
        questionGuideInput.setLabel("core.app.Question Guide");
        Map<String, Object> questionGuideValue = new HashMap<>();
        questionGuideValue.put("open", false);
        questionGuideInput.setValue(questionGuideValue);
        userGuideInputs.add(questionGuideInput);
        
        // tts输入项
        FlowNodeInputItemType ttsInput = new FlowNodeInputItemType();
        ttsInput.setKey("tts");
        ttsInput.setRenderTypeList(List.of("hidden"));
        ttsInput.setValueType("any");
        ttsInput.setLabel("");
        Map<String, Object> ttsValue = new HashMap<>();
        ttsValue.put("type", "web");
        ttsInput.setValue(ttsValue);
        userGuideInputs.add(ttsInput);
        
        // whisper输入项
        FlowNodeInputItemType whisperInput = new FlowNodeInputItemType();
        whisperInput.setKey("whisper");
        whisperInput.setRenderTypeList(List.of("hidden"));
        whisperInput.setValueType("any");
        whisperInput.setLabel("");
        Map<String, Object> whisperValue = new HashMap<>();
        whisperValue.put("open", false);
        whisperValue.put("autoSend", false);
        whisperValue.put("autoTTSResponse", false);
        whisperInput.setValue(whisperValue);
        userGuideInputs.add(whisperInput);
        
        // scheduleTrigger输入项
        FlowNodeInputItemType scheduleTriggerInput = new FlowNodeInputItemType();
        scheduleTriggerInput.setKey("scheduleTrigger");
        scheduleTriggerInput.setRenderTypeList(List.of("hidden"));
        scheduleTriggerInput.setValueType("any");
        scheduleTriggerInput.setLabel("");
        scheduleTriggerInput.setValue(null);
        userGuideInputs.add(scheduleTriggerInput);
        
        userGuideNode.setInputs(userGuideInputs);
        userGuideNode.setOutputs(new ArrayList<>());
        modules.add(userGuideNode);
        
        // 添加workflowStart节点
        StoreNodeItemType workflowStartNode = new StoreNodeItemType();
        workflowStartNode.setNodeId("448745");
        workflowStartNode.setName("common:core.module.template.work_start");
        workflowStartNode.setIntro("");
        workflowStartNode.setAvatar("core/workflow/template/workflowStart");
        workflowStartNode.setFlowNodeType("workflowStart");
        
        com.sinitek.mind.core.app.model.PositionInfo workflowStartPosition = new com.sinitek.mind.core.app.model.PositionInfo();
        workflowStartPosition.setX(558.4082376415505);
        workflowStartPosition.setY(123.72387429194112);
        workflowStartNode.setPosition(workflowStartPosition);
        workflowStartNode.setVersion("481");
        
        // 添加workflowStart的inputs
        List<FlowNodeInputItemType> workflowStartInputs = new ArrayList<>();
        
        FlowNodeInputItemType userChatInput = new FlowNodeInputItemType();
        userChatInput.setKey("userChatInput");
        userChatInput.setRenderTypeList(List.of("reference", "textarea"));
        userChatInput.setValueType("string");
        userChatInput.setLabel("common:core.module.input.label.user question");
        userChatInput.setRequired(true);
        userChatInput.setToolDescription("common:core.module.input.label.user question");
        workflowStartInputs.add(userChatInput);
        
        workflowStartNode.setInputs(workflowStartInputs);
        
        // 添加workflowStart的outputs
        List<FlowNodeOutputItemType> workflowStartOutputs = new ArrayList<>();
        
        FlowNodeOutputItemType userChatOutput = new FlowNodeOutputItemType();
        userChatOutput.setId("userChatInput");
        userChatOutput.setKey("userChatInput");
        userChatOutput.setLabel("core.module.input.label.user question");
        userChatOutput.setValueType("string");
        userChatOutput.setType("static");
        workflowStartOutputs.add(userChatOutput);
        
        workflowStartNode.setOutputs(workflowStartOutputs);
        modules.add(workflowStartNode);
        
        // 添加AI聊天节点
        StoreNodeItemType chatNode = new StoreNodeItemType();
        chatNode.setNodeId("loOvhld2ZTKa");
        chatNode.setName("common:core.module.template.ai_chat");
        chatNode.setIntro("common:core.module.template.ai_chat_intro");
        chatNode.setAvatar("core/workflow/template/aiChat");
        chatNode.setFlowNodeType("chatNode");
        chatNode.setShowStatus(true);
        
        com.sinitek.mind.core.app.model.PositionInfo chatNodePosition = new com.sinitek.mind.core.app.model.PositionInfo();
        chatNodePosition.setX(1097.7317280958762);
        chatNodePosition.setY(-244.16014496351386);
        chatNode.setPosition(chatNodePosition);
        chatNode.setVersion("481");
        
        // 添加AI聊天节点的inputs
        List<FlowNodeInputItemType> chatNodeInputs = new ArrayList<>();
        
        // model输入项
        FlowNodeInputItemType modelInput = new FlowNodeInputItemType();
        modelInput.setKey("model");
        modelInput.setRenderTypeList(List.of("settingLLMModel", "reference"));
        modelInput.setLabel("core.module.input.label.aiModel");
        modelInput.setValueType("string");
        modelInput.setValue(APP_DEFAULT_MODULE);
        chatNodeInputs.add(modelInput);
        
        // temperature输入项
        FlowNodeInputItemType temperatureInput = new FlowNodeInputItemType();
        temperatureInput.setKey("temperature");
        temperatureInput.setRenderTypeList(List.of("hidden"));
        temperatureInput.setLabel("");
        temperatureInput.setValueType("number");
        temperatureInput.setMin(0);
        temperatureInput.setMax(10);
        temperatureInput.setStep(1);
        chatNodeInputs.add(temperatureInput);
        
        // maxToken输入项
        FlowNodeInputItemType maxTokenInput = new FlowNodeInputItemType();
        maxTokenInput.setKey("maxToken");
        maxTokenInput.setRenderTypeList(List.of("hidden"));
        maxTokenInput.setLabel("");
        maxTokenInput.setValueType("number");
        maxTokenInput.setMin(100);
        maxTokenInput.setMax(4000);
        maxTokenInput.setStep(50);
        chatNodeInputs.add(maxTokenInput);
        
        // isResponseAnswerText输入项
        FlowNodeInputItemType isResponseAnswerTextInput = new FlowNodeInputItemType();
        isResponseAnswerTextInput.setKey("isResponseAnswerText");
        isResponseAnswerTextInput.setRenderTypeList(List.of("hidden"));
        isResponseAnswerTextInput.setLabel("");
        isResponseAnswerTextInput.setValue(true);
        isResponseAnswerTextInput.setValueType("boolean");
        chatNodeInputs.add(isResponseAnswerTextInput);
        
        // quoteTemplate输入项
        FlowNodeInputItemType quoteTemplateInput = new FlowNodeInputItemType();
        quoteTemplateInput.setKey("quoteTemplate");
        quoteTemplateInput.setRenderTypeList(List.of("hidden"));
        quoteTemplateInput.setLabel("");
        quoteTemplateInput.setValueType("string");
        chatNodeInputs.add(quoteTemplateInput);
        
        // quotePrompt输入项
        FlowNodeInputItemType quotePromptInput = new FlowNodeInputItemType();
        quotePromptInput.setKey("quotePrompt");
        quotePromptInput.setRenderTypeList(List.of("hidden"));
        quotePromptInput.setLabel("");
        quotePromptInput.setValueType("string");
        chatNodeInputs.add(quotePromptInput);
        
        // systemPrompt输入项
        FlowNodeInputItemType systemPromptInput = new FlowNodeInputItemType();
        systemPromptInput.setKey("systemPrompt");
        systemPromptInput.setRenderTypeList(List.of("textarea", "reference"));
        systemPromptInput.setMax(3000);
        systemPromptInput.setValueType("string");
        systemPromptInput.setLabel("core.ai.Prompt");
        systemPromptInput.setDescription("core.app.tip.systemPromptTip");
        systemPromptInput.setPlaceholder("core.app.tip.chatNodeSystemPromptTip");
        systemPromptInput.setValue("");
        chatNodeInputs.add(systemPromptInput);
        
        // history输入项
        FlowNodeInputItemType historyInput = new FlowNodeInputItemType();
        historyInput.setKey("history");
        historyInput.setRenderTypeList(List.of("numberInput", "reference"));
        historyInput.setValueType("chatHistory");
        historyInput.setLabel("core.module.input.label.chat history");
        historyInput.setRequired(true);
        historyInput.setMin(0);
        historyInput.setMax(30);
        historyInput.setValue(6);
        chatNodeInputs.add(historyInput);
        
        // userChatInput输入项（引用workflowStart的输出）
        FlowNodeInputItemType chatUserChatInput = new FlowNodeInputItemType();
        chatUserChatInput.setKey("userChatInput");
        chatUserChatInput.setRenderTypeList(List.of("reference", "textarea"));
        chatUserChatInput.setValueType("string");
        chatUserChatInput.setLabel("common:core.module.input.label.user question");
        chatUserChatInput.setRequired(true);
        chatUserChatInput.setToolDescription("common:core.module.input.label.user question");
        chatUserChatInput.setValue(Arrays.asList("448745", "userChatInput"));
        chatNodeInputs.add(chatUserChatInput);
        
        // quoteQA输入项
        FlowNodeInputItemType quoteQAInput = new FlowNodeInputItemType();
        quoteQAInput.setKey("quoteQA");
        quoteQAInput.setRenderTypeList(List.of("settingDatasetQuotePrompt"));
        quoteQAInput.setLabel("");
        quoteQAInput.setDebugLabel("common:core.module.Dataset quote.label");
        quoteQAInput.setDescription("");
        quoteQAInput.setValueType("datasetQuote");
        chatNodeInputs.add(quoteQAInput);
        
        // aiChatReasoning输入项
        FlowNodeInputItemType aiChatReasoningInput = new FlowNodeInputItemType();
        aiChatReasoningInput.setKey("aiChatReasoning");
        aiChatReasoningInput.setRenderTypeList(List.of("hidden"));
        aiChatReasoningInput.setLabel("");
        aiChatReasoningInput.setValueType("boolean");
        aiChatReasoningInput.setValue(true);
        chatNodeInputs.add(aiChatReasoningInput);
        
        chatNode.setInputs(chatNodeInputs);
        
        // 添加AI聊天节点的outputs
        List<FlowNodeOutputItemType> chatNodeOutputs = new ArrayList<>();
        
        FlowNodeOutputItemType historyOutput = new FlowNodeOutputItemType();
        historyOutput.setId("history");
        historyOutput.setKey("history");
        historyOutput.setLabel("core.module.output.label.New context");
        historyOutput.setDescription("core.module.output.description.New context");
        historyOutput.setValueType("chatHistory");
        historyOutput.setType("static");
        chatNodeOutputs.add(historyOutput);
        
        FlowNodeOutputItemType answerTextOutput = new FlowNodeOutputItemType();
        answerTextOutput.setId("answerText");
        answerTextOutput.setKey("answerText");
        answerTextOutput.setLabel("core.module.output.label.Ai response content");
        answerTextOutput.setDescription("core.module.output.description.Ai response content");
        answerTextOutput.setValueType("string");
        answerTextOutput.setType("static");
        chatNodeOutputs.add(answerTextOutput);
        
        chatNode.setOutputs(chatNodeOutputs);
        modules.add(chatNode);
        
        createAppDTO.setModules(modules);
        
        // 构造edges数据
        List<StoreEdgeItemType> edges = new ArrayList<>();

        StoreEdgeItemType edge = new StoreEdgeItemType();
        edge.setSource("448745");
        edge.setTarget("loOvhld2ZTKa");
        edge.setSourceHandle("448745-source-right");
        edge.setTargetHandle("loOvhld2ZTKa-target-left");
        edges.add(edge);
        
        createAppDTO.setEdges(edges);
        
        // 设置chatConfig
        createAppDTO.setChatConfig(new com.sinitek.mind.core.app.model.AppChatConfigType());

        // 执行创建请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/create")
                        .content(JsonUtil.toJsonString(createAppDTO))
        );

        // 验证结果并获取应用ID
        MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/app/create接口的返回结果: {}", contentAsString);

        // 从响应中提取应用ID
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readTree(contentAsString).path("data").asText();
    }

    /**
     * 删除应用
     */
    @SneakyThrows
    private void deleteApp(String appId) {
        // 执行删除请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/del")
                        .param("appId", appId)
        );

        // 验证结果
        perform.andExpect(status().isOk());

        log.info("/app/del接口调用成功，删除应用ID: {}", appId);
    }

    /**
     * 创建API密钥
     */
    @SneakyThrows
    private String createApiKey(String appId) {
        // 创建API密钥请求
        CreateApiKeyRequestDTO requestDTO = new CreateApiKeyRequestDTO();
        requestDTO.setAppId(appId);
        requestDTO.setName("测试API密钥_" + System.currentTimeMillis());
        
        CreateApiKeyRequestDTO.LimitDTO limitDTO = new CreateApiKeyRequestDTO.LimitDTO();
        limitDTO.setExpiredTime(new Date(System.currentTimeMillis() + 86400000L)); // 24小时后过期
        limitDTO.setMaxUsagePoints(1000L);
        
        requestDTO.setLimit(limitDTO);
        
        // 执行创建请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/openapi/create")
                        .content(JsonUtil.toJsonString(requestDTO))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/openapi/create接口的返回结果: {}", contentAsString);
        
        // 解析返回的API密钥
        java.util.Map<String, Object> responseMap = JsonUtil.toJavaObject(contentAsString, java.util.Map.class);
        return (String) responseMap.get("data");
    }

    /**
     * 删除API密钥
     */
    @SneakyThrows
    private void deleteApiKey(String apiKey) {
        // 通过API密钥查找对应的ID
        String apiKeyId = getApiKeyId(apiKey);
        
        if (apiKeyId != null) {
            // 创建删除请求
            com.sinitek.mind.common.dto.IdDTO deleteRequest = new com.sinitek.mind.common.dto.IdDTO();
            deleteRequest.setId(apiKeyId);
            
            // 执行删除请求
            ResultActions perform = mockMvc.perform(
                    postMockRequestForAdmin("/mind/api/support/openapi/delete")
                            .content(JsonUtil.toJsonString(deleteRequest))
            );

            // 验证结果
            perform.andExpect(status().isOk());

            log.info("/openapi/delete接口调用成功，删除API密钥ID: {}", apiKeyId);
        }
    }

    /**
     * 通过API密钥获取对应的ID
     */
    @SneakyThrows
    private String getApiKeyId(String apiKey) {
        try {
            // 查询API密钥列表
            ResultActions listPerform = mockMvc.perform(
                    getMockRequestForAdmin("/mind/api/support/openapi/list")
            );

            String listContent = listPerform.andExpect(status().isOk())
                    .andExpect(jsonPathCodeIs200())
                    .andReturn().getResponse().getContentAsString();

            // 解析列表结果，找到对应的API密钥ID
            java.util.Map<String, Object> listResponseMap = JsonUtil.toJavaObject(listContent, java.util.Map.class);
            java.util.List<java.util.Map<String, Object>> apiKeys = (java.util.List<java.util.Map<String, Object>>) listResponseMap.get("data");
            
            // 查找包含目标API密钥的记录
            if (apiKeys != null && !apiKeys.isEmpty()) {
                for (java.util.Map<String, Object> apiKeyInfo : apiKeys) {
                    String key = (String) apiKeyInfo.get("key");
                    if (apiKey.equals(key)) {
                        String id = (String) apiKeyInfo.get("id");
                        log.info("通过API密钥匹配获取到ID: {}", id);
                        return id;
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取API密钥ID失败: {}", e.getMessage());
        }
        
        return null;
    }

    /**
     * 使用WebClient处理Server-Sent Events流
     * 
     * @param endpoint 接口路径
     * @param request 请求对象
     * @param apiType API类型（API或Open-API）
     */
    @SneakyThrows
    private void processEventStreamWithWebClient(String endpoint, V2ChatCompletionRequest request, String apiType) {
        processEventStreamWithWebClient(this.webClient, endpoint, request, apiType);
    }

    /**
     * 使用WebClient处理Server-Sent Events流
     * 
     * @param webClient WebClient实例
     * @param endpoint 接口路径
     * @param request 请求对象
     * @param apiType API类型（API或Open-API）
     */
    @SneakyThrows
    private void processEventStreamWithWebClient(WebClient webClient, String endpoint, V2ChatCompletionRequest request, String apiType) {
        log.info("开始使用WebClient处理{}事件流", apiType);
        
        AtomicBoolean hasAnswerEvent = new AtomicBoolean(false);
        AtomicBoolean hasDoneEvent = new AtomicBoolean(false);
        AtomicBoolean hasErrorEvent = new AtomicBoolean(false);
        AtomicBoolean hasFlowNodeResponseEvent = new AtomicBoolean(false);
        AtomicBoolean flowNodeResponseHasError = new AtomicBoolean(false);
        List<String> events = Collections.synchronizedList(new ArrayList<>());
        
        try {
            // 使用CountDownLatch等待异步处理完成
            CountDownLatch latch = new CountDownLatch(1);
            
            // 创建Flux来接收SSE事件流
            Flux<ServerSentEvent<String>> eventStream = webClient.post()
                    .uri(endpoint)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(JsonUtil.toJsonString(request))
                    .retrieve()
                    .onStatus(
                        status -> status.is4xxClientError() || status.is5xxServerError(),
                        response -> response.bodyToMono(String.class)
                            .map(body -> new RuntimeException("HTTP error: " + body))
                    )
                    .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<String>>() {})
                    .timeout(Duration.ofSeconds(30)) // 设置超时时间
                    .doOnComplete(() -> {
                        log.info("{}事件流接收完成", apiType);
                        latch.countDown();
                    })
                    .doOnError(error -> {
                        log.error("{}事件流接收发生错误: {}", apiType, error.getMessage());
                        latch.countDown();
                    })
                    .doOnCancel(() -> {
                        log.warn("{}事件流被取消", apiType);
                        latch.countDown();
                    });
            
            // 处理每个事件
            eventStream.subscribe(
                sseEvent -> {
                    String eventType = sseEvent.event();
                    String data = sseEvent.data();
                    
                    // 将事件信息存储为字符串格式用于调试
                    String eventString = "event: " + eventType + "\ndata: " + data;
                    events.add(eventString);
                    
                    log.info("{}接收到SSE事件 - event: {}, data: {}", apiType, eventType, data);
                    
                    // 处理不同类型的事件
                    if ("answer".equals(eventType) && "[DONE]".equals(data)) {
                        hasDoneEvent.set(true);
                        log.info("{}检测到完成事件: answer -> [DONE]", apiType);
                    } else if ("answer".equals(eventType)) {
                        hasAnswerEvent.set(true);
                        log.info("{}检测到回答事件: {}", apiType, data);
                    } else if ("error".equals(eventType)) {
                        hasErrorEvent.set(true);
                        log.error("{}检测到错误事件: {}", apiType, data);
                    } else if ("flowNodeResponse".equals(eventType)) {
                        hasFlowNodeResponseEvent.set(true);
                        log.info("{}检测到流程节点响应事件", apiType);
                        
                        // 验证flowNodeResponse事件中的JSON对象不包含error字段
                        if (data != null && !data.trim().isEmpty()) {
                            try {
                                Map<String, Object> flowNodeData = JsonUtil.toJavaObject(data, Map.class);
                                if (flowNodeData.containsKey("error")) {
                                    flowNodeResponseHasError.set(true);
                                    log.error("{}flowNodeResponse事件包含error字段: {}", apiType, flowNodeData.get("error"));
                                } else {
                                    log.debug("{}flowNodeResponse事件验证通过，不包含error字段", apiType);
                                }
                            } catch (Exception e) {
                                log.error("{}解析flowNodeResponse事件数据失败: {}", apiType, e.getMessage());
                            }
                        }
                    } else if ("flowNodeStatus".equals(eventType)) {
                        log.info("{}检测到流程节点状态事件: {}", apiType, data);
                    } else if ("workflowDuration".equals(eventType)) {
                        log.info("{}检测到工作流持续时间事件: {}", apiType, data);
                    } else if (eventType == null && data != null) {
                        // 如果没有event字段，尝试从data推断事件类型
                        String inferredEventType = inferEventTypeFromData(data);
                        log.info("{}事件类型为空，从数据推断事件类型: {}, 数据: {}", apiType, inferredEventType, data);
                        
                        if ("answer".equals(inferredEventType) && "[DONE]".equals(data)) {
                            hasDoneEvent.set(true);
                            log.info("{}检测到完成事件: answer -> [DONE]", apiType);
                        } else if ("answer".equals(inferredEventType)) {
                            hasAnswerEvent.set(true);
                            log.info("{}检测到回答事件: {}", apiType, data);
                        }
                    }
                },
                error -> {
                    log.error("{}事件流订阅发生错误: {}", apiType, error.getMessage());
                    latch.countDown();
                },
                () -> {
                    log.info("{}事件流订阅完成", apiType);
                    latch.countDown();
                }
            );
            
            // 等待事件流处理完成（最多等待30秒）
            boolean completed = latch.await(30, TimeUnit.SECONDS);
            if (!completed) {
                log.warn("{}事件流处理超时", apiType);
            }
            
        } catch (Exception e) {
            log.error("处理{}事件流时发生异常: {}", apiType, e.getMessage(), e);
            throw e;
        }
        
        // 打印完整SSE事件格式用于调试
        printSseEvents(events, apiType);
        
        // 断言验证
        assertTrue(events.size() > 0, apiType + "应该返回至少一个事件");
        assertFalse(hasErrorEvent.get(), apiType + "不应该有错误事件");
        assertTrue(hasDoneEvent.get(), apiType + "应该有完成事件(answer -> [DONE])");
        assertFalse(flowNodeResponseHasError.get(), apiType + "flowNodeResponse事件中的JSON对象不应该包含error字段");
        
        log.info("{}事件流验证通过", apiType);
    }

    /**
     * 解析Server-Sent Event
     * 
     * @param event 事件字符串
     * @return 解析后的事件数据
     */
    private Map<String, String> parseSseEvent(String event) {
        Map<String, String> eventData = new HashMap<>();
        
        try (BufferedReader reader = new BufferedReader(new StringReader(event))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.startsWith("event:")) {
                    String eventValue = line.substring(6).trim();
                    eventData.put("event", eventValue);
                } else if (line.startsWith("data:")) {
                    String dataValue = line.substring(5).trim();
                    eventData.put("data", dataValue);
                }
            }
        } catch (Exception e) {
            log.error("解析SSE事件时发生异常: {}", e.getMessage(), e);
        }
        
        return eventData;
    }

    /**
     * 根据数据内容推断事件类型
     * 
     * @param data 接收到的数据
     * @return 推断的事件类型
     */
    private String inferEventTypeFromData(String data) {
        if (data == null || data.trim().isEmpty()) {
            return "unknown";
        }
        
        String trimmedData = data.trim();
        
        // 根据您提供的数据格式进行推断
        if ("[DONE]".equals(trimmedData)) {
            return "answer";
        }
        
        // 检查是否为JSON格式
        if (trimmedData.startsWith("{") && trimmedData.endsWith("}")) {
            try {
                Map<String, Object> jsonData = JsonUtil.toJavaObject(trimmedData, Map.class);
                
                // 根据JSON字段推断事件类型
                if (jsonData.containsKey("runningTime") && jsonData.containsKey("nodeId")) {
                    return "flowNodeResponse";
                }
                if (jsonData.containsKey("status") && jsonData.containsKey("name")) {
                    return "flowNodeStatus";
                }
                if (jsonData.containsKey("durationSeconds")) {
                    return "workflowDuration";
                }
                if (jsonData.containsKey("choices") && jsonData.containsKey("created")) {
                    return "answer";
                }
                if (jsonData.containsKey("error")) {
                    return "error";
                }
            } catch (Exception e) {
                // 如果不是JSON格式，继续其他判断
            }
        }
        
        // 默认情况下，根据内容特征推断
        if (trimmedData.contains("nodeId") && trimmedData.contains("runningTime")) {
            return "flowNodeResponse";
        }
        if (trimmedData.contains("status") && trimmedData.contains("running")) {
            return "flowNodeStatus";
        }
        if (trimmedData.contains("durationSeconds")) {
            return "workflowDuration";
        }
        if (trimmedData.contains("choices") || trimmedData.contains("created")) {
            return "answer";
        }
        
        return "unknown";
    }

    /**
     * 打印完整SSE事件格式（用于调试）
     * 
     * @param events 事件列表
     */
    private void printSseEvents(List<String> events, String apiType) {
        log.info("========== {} SSE事件完整格式开始 ==========", apiType);
        for (int i = 0; i < events.size(); i++) {
            String event = events.get(i);
            log.info("事件 {}: {}", i + 1, event);
            
            // 解析事件格式
            Map<String, String> parsedEvent = parseSseEvent(event);
            String eventType = parsedEvent.get("event");
            String data = parsedEvent.get("data");
            
            log.info("  事件类型: {}", eventType != null ? eventType : "未指定");
            log.info("  数据内容: {}", data);
            log.info("  ---");
        }
        log.info("========== {} SSE事件完整格式结束 ==========", apiType);
    }
}