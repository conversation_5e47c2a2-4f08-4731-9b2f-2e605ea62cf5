package com.sinitek.mind.core.app.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.dto.GetPathDTO;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;

import java.util.ArrayList;

import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * AppFolderController - 单元测试
 *
 * <AUTHOR>
 */
@Slf4j
public class AppFolderControllerTest extends MindBaseTestApplication {

    /**
     * 测试App文件夹完整流程：创建 → 获取路径 → 删除
     * 使用指定的测试数据：{"name":"测试","intro":"123","parentId":null}
     */
    @Test
    @SneakyThrows
    @DisplayName("测试App文件夹完整流程")
    public void testAppFolderCompleteFlow() {
        // 1. 创建文件夹
        Long folderId = createFolder();
        log.info("创建文件夹成功，ID: {}", folderId);
        
        // 2. 获取文件夹路径
        getFolderPath(folderId);
        
        // 3. 删除文件夹
        deleteFolder(folderId);
    }

    /**
     * 测试不同类型的路径获取
     */
    @Test
    @SneakyThrows
    @DisplayName("测试不同类型的路径获取")
    public void testGetPathWithDifferentTypes() {
        // 1. 创建文件夹
        Long folderId = createFolder();
        log.info("创建文件夹成功，ID: {}", folderId);
        
        // 2. 测试获取当前路径
        getFolderPathWithType(folderId, "current");
        
        // 3. 测试获取父路径
        getFolderPathWithType(folderId, "parent");
        
        // 4. 删除文件夹
        deleteFolder(folderId);
    }

    /**
     * 创建文件夹
     * 使用指定的测试数据：{"name":"测试","intro":"123","parentId":null}
     */
    @SneakyThrows
    private Long createFolder() {
        CreateAppDTO createFolderDTO = new CreateAppDTO();
        createFolderDTO.setParentId(null);
        createFolderDTO.setName("测试");
        createFolderDTO.setType(AppTypeEnum.FOLDER.getValue());
        createFolderDTO.setAvatar("core/app/type/folder");
        createFolderDTO.setIntro("123");
        createFolderDTO.setModules(new ArrayList<>());
        createFolderDTO.setEdges(new ArrayList<>());

        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/folder/create")
                        .content(JsonUtil.toJsonString(createFolderDTO))
        );

        // 验证结果并获取文件夹ID
        MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/folder/create接口的返回结果: {}", contentAsString);

        // 从响应中提取文件夹ID
        ObjectMapper mapper = new ObjectMapper();
        String folderId = mapper.readTree(contentAsString).path("data").asText();
        return Long.valueOf(folderId);
    }

    /**
     * 获取文件夹路径
     */
    @SneakyThrows
    private void getFolderPath(Long folderId) {
        GetPathDTO getPathDTO = new GetPathDTO();
        getPathDTO.setSourceId(folderId);
        getPathDTO.setType("current");

        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/app/folder/path")
                        .content(JsonUtil.toJsonString(getPathDTO))
        );

        // 验证结果
        MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/folder/path接口的返回结果: {}", contentAsString);
    }

    /**
     * 获取文件夹路径（指定类型）
     */
    @SneakyThrows
    private void getFolderPathWithType(Long folderId, String type) {
        GetPathDTO getPathDTO = new GetPathDTO();
        getPathDTO.setSourceId(folderId);
        getPathDTO.setType(type);

        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/app/folder/path")
                        .content(JsonUtil.toJsonString(getPathDTO))
        );

        // 验证结果
        MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/folder/path接口的返回结果 (type={}): {}", type, contentAsString);
    }

    /**
     * 删除文件夹
     * 使用app的删除接口
     */
    @SneakyThrows
    private void deleteFolder(Long folderId) {
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/del")
                        .param("appId", String.valueOf(folderId))
        );

        // 验证结果
        perform.andExpect(status().isOk());

        String contentAsString = perform.andReturn().getResponse().getContentAsString();
        log.info("/app/del接口的返回结果: {}", contentAsString);
    }

}