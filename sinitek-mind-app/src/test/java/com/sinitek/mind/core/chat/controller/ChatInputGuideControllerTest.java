package com.sinitek.mind.core.chat.controller;

import com.sinitek.mind.MindBaseTestApplication;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.ResultActions;

import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * ChatInputGuideController - 单元测试
 */
@Slf4j
public class ChatInputGuideControllerTest extends MindBaseTestApplication {

    /**
     * 测试统计聊天输入引导总数 - 成功场景
     */
    @Test
    @SneakyThrows
    @DisplayName("测试统计聊天输入引导总数 - 成功场景")
    public void testCountTotal_Success() {
        // 创建应用
        String appId = createApp();
        
        try {
            // 执行请求
            ResultActions perform = mockMvc.perform(
                    getMockRequestForAdmin("/mind/api/core/chat/inputGuide/countTotal")
                            .param("appId", appId)
            );

            // 验证结果
            perform.andExpect(status().isOk())
                    .andExpect(jsonPathSuccessIsTrue())
                    .andExpect(jsonPath("$.data", notNullValue()))
                    .andExpect(jsonPath("$.data", is(0)));

            String contentAsString = perform.andReturn().getResponse().getContentAsString();
            log.info("/inputGuide/countTotal接口的返回结果: {}", contentAsString);
        } finally {
            // 清理：删除应用
            deleteApp(appId);
        }
    }

    /**
     * 测试统计聊天输入引导总数 - 缺少参数
     */
    @Test
    @SneakyThrows
    @DisplayName("测试统计聊天输入引导总数 - 缺少参数")
    public void testCountTotal_MissingParameter() {
        // 执行请求 - 不传appId参数
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/chat/inputGuide/countTotal")
        );

        // 验证结果 - 应该返回400错误
        perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false));

        String contentAsString = perform.andReturn().getResponse().getContentAsString();
        log.info("/inputGuide/countTotal接口缺少参数的返回结果: {}", contentAsString);
    }

    /**
     * 创建应用（复用AppControllerTest中的创建逻辑）
     */
    @SneakyThrows
    private String createApp() {
        // 构造创建应用请求
        com.sinitek.mind.core.app.dto.CreateAppDTO createAppDTO = new com.sinitek.mind.core.app.dto.CreateAppDTO();
        createAppDTO.setParentId(null);
        createAppDTO.setAvatar("core/app/type/simpleFill");
        createAppDTO.setName("聊天输入引导测试应用" + System.currentTimeMillis());
        createAppDTO.setType("simple");
        
        // 构造简化的modules数据
        java.util.List<com.sinitek.mind.core.workflow.model.StoreNodeItemType> modules = new java.util.ArrayList<>();
        
        // 添加userGuide节点
        com.sinitek.mind.core.workflow.model.StoreNodeItemType userGuideNode = new com.sinitek.mind.core.workflow.model.StoreNodeItemType();
        userGuideNode.setNodeId("userGuide");
        userGuideNode.setName("common:core.module.template.system_config");
        userGuideNode.setIntro("common:core.module.template.config_params");
        userGuideNode.setAvatar("core/workflow/template/systemConfig");
        userGuideNode.setFlowNodeType("userGuide");
        
        com.sinitek.mind.core.app.model.PositionInfo userGuidePosition = new com.sinitek.mind.core.app.model.PositionInfo();
        userGuidePosition.setX(531.2422736065552);
        userGuidePosition.setY(-486.7611729549753);
        userGuideNode.setPosition(userGuidePosition);
        userGuideNode.setAppVersion(481L);
        
        // 添加userGuide的inputs
        java.util.List<com.sinitek.mind.core.workflow.model.FlowNodeInputItemType> userGuideInputs = new java.util.ArrayList<>();
        
        // welcomeText输入项
        com.sinitek.mind.core.workflow.model.FlowNodeInputItemType welcomeTextInput = new com.sinitek.mind.core.workflow.model.FlowNodeInputItemType();
        welcomeTextInput.setKey("welcomeText");
        welcomeTextInput.setRenderTypeList(java.util.List.of("hidden"));
        welcomeTextInput.setValueType("string");
        welcomeTextInput.setLabel("core.app.Welcome Text");
        welcomeTextInput.setValue("");
        userGuideInputs.add(welcomeTextInput);
        
        // variables输入项
        com.sinitek.mind.core.workflow.model.FlowNodeInputItemType variablesInput = new com.sinitek.mind.core.workflow.model.FlowNodeInputItemType();
        variablesInput.setKey("variables");
        variablesInput.setRenderTypeList(java.util.List.of("hidden"));
        variablesInput.setValueType("any");
        variablesInput.setLabel("core.app.Chat Variable");
        variablesInput.setValue(new java.util.ArrayList<>());
        userGuideInputs.add(variablesInput);
        
        // questionGuide输入项
        com.sinitek.mind.core.workflow.model.FlowNodeInputItemType questionGuideInput = new com.sinitek.mind.core.workflow.model.FlowNodeInputItemType();
        questionGuideInput.setKey("questionGuide");
        questionGuideInput.setValueType("object");
        questionGuideInput.setRenderTypeList(java.util.List.of("hidden"));
        questionGuideInput.setLabel("core.app.Question Guide");
        java.util.Map<String, Object> questionGuideValue = new java.util.HashMap<>();
        questionGuideValue.put("open", false);
        questionGuideInput.setValue(questionGuideValue);
        userGuideInputs.add(questionGuideInput);
        
        userGuideNode.setInputs(userGuideInputs);
        userGuideNode.setOutputs(new java.util.ArrayList<>());
        modules.add(userGuideNode);
        
        // 添加workflowStart节点
        com.sinitek.mind.core.workflow.model.StoreNodeItemType workflowStartNode = new com.sinitek.mind.core.workflow.model.StoreNodeItemType();
        workflowStartNode.setNodeId("448745");
        workflowStartNode.setName("common:core.module.template.work_start");
        workflowStartNode.setIntro("");
        workflowStartNode.setAvatar("core/workflow/template/workflowStart");
        workflowStartNode.setFlowNodeType("workflowStart");
        
        com.sinitek.mind.core.app.model.PositionInfo workflowStartPosition = new com.sinitek.mind.core.app.model.PositionInfo();
        workflowStartPosition.setX(558.4082376415505);
        workflowStartPosition.setY(123.72387429194112);
        workflowStartNode.setPosition(workflowStartPosition);
        workflowStartNode.setAppVersion(481L);
        
        // 添加workflowStart的inputs
        java.util.List<com.sinitek.mind.core.workflow.model.FlowNodeInputItemType> workflowStartInputs = new java.util.ArrayList<>();
        
        com.sinitek.mind.core.workflow.model.FlowNodeInputItemType userChatInput = new com.sinitek.mind.core.workflow.model.FlowNodeInputItemType();
        userChatInput.setKey("userChatInput");
        userChatInput.setRenderTypeList(java.util.List.of("reference", "textarea"));
        userChatInput.setValueType("string");
        userChatInput.setLabel("common:core.module.input.label.user question");
        userChatInput.setRequired(true);
        userChatInput.setToolDescription("common:core.module.input.label.user question");
        workflowStartInputs.add(userChatInput);
        
        workflowStartNode.setInputs(workflowStartInputs);
        
        // 添加workflowStart的outputs
        java.util.List<com.sinitek.mind.core.workflow.model.FlowNodeOutputItemType> workflowStartOutputs = new java.util.ArrayList<>();
        
        com.sinitek.mind.core.workflow.model.FlowNodeOutputItemType userChatOutput = new com.sinitek.mind.core.workflow.model.FlowNodeOutputItemType();
        userChatOutput.setId("userChatInput");
        userChatOutput.setKey("userChatInput");
        userChatOutput.setLabel("core.module.input.label.user question");
        userChatOutput.setValueType("string");
        userChatOutput.setType("static");
        workflowStartOutputs.add(userChatOutput);
        
        workflowStartNode.setOutputs(workflowStartOutputs);
        modules.add(workflowStartNode);
        
        createAppDTO.setModules(modules);
        createAppDTO.setEdges(new java.util.ArrayList<>());
        
        // 设置chatConfig
        createAppDTO.setChatConfig(new com.sinitek.mind.core.app.model.AppChatConfigType());

        // 执行创建请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/create")
                        .content(com.sinitek.sirm.common.utils.JsonUtil.toJsonString(createAppDTO))
        );

        // 验证结果并获取应用ID
        org.springframework.test.web.servlet.MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/app/create接口的返回结果: {}", contentAsString);

        // 从响应中提取应用ID
        com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
        return mapper.readTree(contentAsString).path("data").asText();
    }

    /**
     * 删除应用
     */
    @SneakyThrows
    private void deleteApp(String appId) {
        // 执行删除请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/del")
                        .param("appId", appId)
        );

        // 验证结果
        perform.andExpect(status().isOk());

        log.info("/app/del接口调用成功，删除应用ID: {}", appId);
    }
}