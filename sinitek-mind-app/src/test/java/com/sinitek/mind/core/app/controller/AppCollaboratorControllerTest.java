package com.sinitek.mind.core.app.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.core.app.dto.AppChangeOwnerDTO;
import com.sinitek.mind.core.app.dto.AppClbDeleteDTO;
import com.sinitek.mind.core.app.dto.AppClbUpdateDTO;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.PositionInfo;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * AppCollaboratorController - 单元测试
 *
 * <AUTHOR>
 */
@Slf4j
public class AppCollaboratorControllerTest extends MindBaseTestApplication {

    private static String TEST_APP_ID;
    private static final String TEST_OWNER_ID_1 = "999000001";
    private static final String TEST_OWNER_ID_2 = "22";
    private static final String TEST_OWNER_ID_3 = "21";
    
    @Autowired
    private AppRepository appRepository;

    /**
     * 每个单元测试执行前自动初始化测试数据
     */
    @BeforeEach
    public void initTestData() {
        clearInitData();
        createInitData();
    }

    /**
     * 每个单元测试执行后清理测试数据
     */
    @AfterEach
    public void cleanupTestData() {
        deleteApp();
    }

    /**
     * 清理初始化数据
     */
    @SneakyThrows
    private void clearInitData() {
        // 删除可能存在的测试数据
        AppClbDeleteDTO deleteDTO = new AppClbDeleteDTO();
        deleteDTO.setAppId(TEST_APP_ID);
        deleteDTO.setTmbId(TEST_OWNER_ID_1);

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/app/collaborator/delete")
                .content(JsonUtil.toJsonString(deleteDTO));

        try {
            mockMvc.perform(request);
        } catch (Exception e) {
            // 忽略错误，因为可能不存在要删除的数据
            log.info("清理初始化数据时出现异常，可能是数据不存在：{}", e.getMessage());
        }
    }

    /**
     * 创建初始化数据
     */
    @SneakyThrows
    private void createInitData() {
        // 创建应用
        TEST_APP_ID = createApp();
        
        // 创建协作者数据
        AppClbUpdateDTO updateDTO = new AppClbUpdateDTO();
        updateDTO.setAppId(TEST_APP_ID);
        updateDTO.setPermission(PermissionConstant.READ_PER);
        List<String> members = new ArrayList<>();
        members.add(TEST_OWNER_ID_1);
        updateDTO.setMembers(members);

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/app/collaborator/update")
                .content(JsonUtil.toJsonString(updateDTO));

        mockMvc.perform(request)
                .andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200());
    }

    /**
     * 测试App协作者CRUD接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试App协作者CRUD接口")
    public void testAppCollaboratorCrud() {
        // 获取协作者列表
        getCollaboratorList();

        // 更新协作者
        updateCollaborator();

        // 删除协作者
        deleteCollaborator();
    }

    /**
     * 测试App更新拥有者
     */
    @Test
    @SneakyThrows
    @DisplayName("测试App更新拥有者")
    public void testAppChangeOwnerId() {
        // 直接调用接口更新拥有者
        AppChangeOwnerDTO appChangeOwnerDTO = new AppChangeOwnerDTO();

        appChangeOwnerDTO.setAppId(TEST_APP_ID);
        appChangeOwnerDTO.setOwnerId(TEST_OWNER_ID_1);

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/app/collaborator/changeOwner")
                .content(JsonUtil.toJsonString(appChangeOwnerDTO));

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/app/changeOwner接口的返回结果: {}", contentAsString);
    }

    /**
     * 获取App协作者列表
     */
    @SneakyThrows
    private void getCollaboratorList() {
        MockHttpServletRequestBuilder request = getMockRequestForAdmin("/mind/api/core/app/collaborator/list")
                .param("appId", TEST_APP_ID);

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/app/collaborator/list接口的返回结果: {}", contentAsString);
    }

    /**
     * 更新App协作者
     */
    @SneakyThrows
    private void updateCollaborator() {
        // 构造更新协作者请求
        AppClbUpdateDTO updateDTO = new AppClbUpdateDTO();
        updateDTO.setAppId(TEST_APP_ID);
        updateDTO.setPermission(PermissionConstant.WRITE_PER);
        List<String> members = new ArrayList<>();
        members.add(TEST_OWNER_ID_2);
        updateDTO.setMembers(members);

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/app/collaborator/update")
                .content(JsonUtil.toJsonString(updateDTO));

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/app/collaborator/update接口的返回结果: {}", contentAsString);

        // 验证更新后的列表
        getCollaboratorList();
    }

    /**
     * 删除App协作者
     */
    @SneakyThrows
    private void deleteCollaborator() {
        // 构造删除协作者请求
        AppClbDeleteDTO deleteDTO = new AppClbDeleteDTO();
        deleteDTO.setAppId(TEST_APP_ID);
        deleteDTO.setTmbId(TEST_OWNER_ID_2);

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/app/collaborator/delete")
                .content(JsonUtil.toJsonString(deleteDTO));

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/app/collaborator/delete接口的返回结果: {}", contentAsString);

        // 验证删除后的列表
        getCollaboratorList();
    }

    /**
     * 创建应用
     */
    @SneakyThrows
    private String createApp() {
        // 构造创建应用请求
        CreateAppDTO createAppDTO = new CreateAppDTO();
        createAppDTO.setParentId(null);
        createAppDTO.setAvatar("core/app/type/simpleFill");
        createAppDTO.setName("测试应用" + System.currentTimeMillis());
        createAppDTO.setType(AppTypeEnum.SIMPLE.getValue());
        
        // 构造modules数据
        List<StoreNodeItemType> modules = new ArrayList<>();
        
        // 添加userGuide节点
        StoreNodeItemType userGuideNode = new StoreNodeItemType();
        userGuideNode.setNodeId("userGuide");
        userGuideNode.setName("common:core.module.template.system_config");
        userGuideNode.setIntro("common:core.module.template.config_params");
        userGuideNode.setAvatar("core/workflow/template/systemConfig");
        userGuideNode.setFlowNodeType("userGuide");
        
        PositionInfo userGuidePosition = new PositionInfo();
        userGuidePosition.setX(531.2422736065552);
        userGuidePosition.setY(-486.7611729549753);
        userGuideNode.setPosition(userGuidePosition);
        userGuideNode.setVersion("481");
        
        // 添加userGuide的inputs
        List<FlowNodeInputItemType> userGuideInputs = new ArrayList<>();
        
        // welcomeText输入项
        FlowNodeInputItemType welcomeTextInput = new FlowNodeInputItemType();
        welcomeTextInput.setKey("welcomeText");
        welcomeTextInput.setRenderTypeList(List.of("hidden"));
        welcomeTextInput.setValueType("string");
        welcomeTextInput.setLabel("core.app.Welcome Text");
        welcomeTextInput.setValue("");
        userGuideInputs.add(welcomeTextInput);
        
        // variables输入项
        FlowNodeInputItemType variablesInput = new FlowNodeInputItemType();
        variablesInput.setKey("variables");
        variablesInput.setRenderTypeList(List.of("hidden"));
        variablesInput.setValueType("any");
        variablesInput.setLabel("core.app.Chat Variable");
        variablesInput.setValue(new ArrayList<>());
        userGuideInputs.add(variablesInput);
        
        userGuideNode.setInputs(userGuideInputs);
        userGuideNode.setOutputs(new ArrayList<>());
        modules.add(userGuideNode);
        
        // 添加workflowStart节点
        StoreNodeItemType workflowStartNode = new StoreNodeItemType();
        workflowStartNode.setNodeId("448745");
        workflowStartNode.setName("common:core.module.template.work_start");
        workflowStartNode.setIntro("");
        workflowStartNode.setAvatar("core/workflow/template/workflowStart");
        workflowStartNode.setFlowNodeType("workflowStart");
        
        PositionInfo workflowStartPosition = new PositionInfo();
        workflowStartPosition.setX(558.4082376415505);
        workflowStartPosition.setY(123.72387429194112);
        workflowStartNode.setPosition(workflowStartPosition);
        workflowStartNode.setVersion("481");
        
        // 添加workflowStart的inputs
        List<FlowNodeInputItemType> workflowStartInputs = new ArrayList<>();
        
        FlowNodeInputItemType userChatInput = new FlowNodeInputItemType();
        userChatInput.setKey("userChatInput");
        userChatInput.setRenderTypeList(List.of("reference", "textarea"));
        userChatInput.setValueType("string");
        userChatInput.setLabel("common:core.module.input.label.user question");
        userChatInput.setRequired(true);
        userChatInput.setToolDescription("common:core.module.input.label.user question");
        workflowStartInputs.add(userChatInput);
        
        workflowStartNode.setInputs(workflowStartInputs);
        workflowStartNode.setOutputs(new ArrayList<>());
        modules.add(workflowStartNode);
        
        createAppDTO.setModules(modules);
        
        // 构造edges数据
        List<StoreEdgeItemType> edges = new ArrayList<>();
        createAppDTO.setEdges(edges);
        
        // 设置chatConfig
        createAppDTO.setChatConfig(new AppChatConfigType());

        // 执行创建请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/create")
                        .content(JsonUtil.toJsonString(createAppDTO))
        );

        // 验证结果并获取应用ID
        MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/app/create接口的返回结果: {}", contentAsString);

        // 从响应中提取应用ID
        ObjectMapper mapper = new ObjectMapper();
        String appId = mapper.readTree(contentAsString).path("data").asText();
        return appId;
    }

    /**
     * 删除应用
     */
    @SneakyThrows
    private void deleteApp() {
        if (TEST_APP_ID != null) {
            try {
                // 执行删除请求
                ResultActions perform = mockMvc.perform(
                        postMockRequestForAdmin("/mind/api/core/app/del")
                                .param("appId", TEST_APP_ID)
                );

                // 验证结果
                perform.andExpect(status().isOk());

                log.info("/app/del接口调用成功，删除应用ID: {}", TEST_APP_ID);
            } catch (Exception e) {
                log.warn("删除应用时出现异常，可能是应用不存在：{}", e.getMessage());
            }
        }
    }

}