package com.sinitek.mind.core.app.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.dto.UpdateHttpPluginDTO;
import com.sinitek.mind.core.app.model.PluginData;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;

import java.util.HashMap;
import java.util.Map;

import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * HttpPluginController - 单元测试
 */
@Slf4j
public class HttpPluginControllerTest extends MindBaseTestApplication {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 测试HttpPluginController完整CRUD流程
     */
    @Test
    @SneakyThrows
    @DisplayName("测试HttpPluginController完整CRUD流程")
    public void testHttpPluginCrud() {
        // 创建HTTP插件
        String appId = createHttpPlugin();

        // 更新HTTP插件
        updateHttpPlugin(appId);

        // 删除HTTP插件（通过AppController）
        deleteHttpPlugin(appId);
    }

    /**
     * 测试获取OpenAPI Schema接口 - 成功场景
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取OpenAPI Schema接口 - 成功场景")
    public void testGetApiSchemaByUrlSuccess() {
        // 构造请求参数
        Map<String, String> request = new HashMap<>();
        request.put("url", "https://petstore3.swagger.io/api/v3/openapi.json");

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/httpPlugin/getApiSchemaByUrl")
                        .content(JsonUtil.toJsonString(request))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/httpPlugin/getApiSchemaByUrl接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试获取OpenAPI Schema接口 - 缺少URL参数
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取OpenAPI Schema接口 - 缺少URL参数")
    public void testGetApiSchemaByUrlMissingUrl() {
        // 构造请求参数 - 不包含url字段
        Map<String, String> request = new HashMap<>();
        request.put("invalidField", "test");

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/httpPlugin/getApiSchemaByUrl")
                        .content(JsonUtil.toJsonString(request))
        );

        // 验证结果 - 应该返回错误
        perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("缺少参数"));
    }

    /**
     * 测试获取OpenAPI Schema接口 - 空URL参数
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取OpenAPI Schema接口 - 空URL参数")
    public void testGetApiSchemaByUrlEmptyUrl() {
        // 构造请求参数 - url为空字符串
        Map<String, String> request = new HashMap<>();
        request.put("url", "");

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/httpPlugin/getApiSchemaByUrl")
                        .content(JsonUtil.toJsonString(request))
        );

        // 验证结果 - 应该返回错误
        perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("缺少参数"));
    }


    /**
     * 创建HTTP插件
     */
    @SneakyThrows
    private String createHttpPlugin() {
        // 构造创建HTTP插件请求
        CreateAppDTO createAppDTO = buildHttpPluginCreateDTO();

        // 执行创建请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/httpPlugin/create")
                        .content(JsonUtil.toJsonString(createAppDTO))
        );

        // 验证结果并获取应用ID
        MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/httpPlugin/create接口的返回结果: {}", contentAsString);

        // 从响应中提取应用ID
        String appId = objectMapper.readTree(contentAsString).path("data").asText();
        return appId;
    }

    /**
     * 更新HTTP插件
     */
    @SneakyThrows
    private void updateHttpPlugin(String appId) {
        // 构造更新HTTP插件请求
        UpdateHttpPluginDTO updateDTO = new UpdateHttpPluginDTO();
        updateDTO.setAppId(appId);
        updateDTO.setName("更新后的HTTP插件名称");
        updateDTO.setAvatar("core/app/type/httpPlugin");
        updateDTO.setIntro("更新后的HTTP插件介绍");

        // 构造pluginData
        String pluginDataStr = "{\"apiSchemaStr\":\"openapi: 3.0.3\\ninfo:\\n  title: 知识库管理API-被更新了\\n  version: 2.0.0\\n  description: 用于Dify自定义工具的完整知识库管理，检索的原子能力\\nservers:\\n  - url: http://**************:9997\\n    description: 知识库服务基础路径\\n\\npaths:\\n  /wxmanage/milvus/adduserknowledge.json:\\n    post:\\n      operationId: 新增用户知识库\\n      summary: 创建新知识库\\n      requestBody:\\n        required: true\\n        content:\\n          application/x-www-form-urlencoded:\\n            schema:\\n              type: object\\n              properties:\\n                name:\\n                  type: string\\n                  description: 知识库名称\\n                  example: \\\"新库1\\\"\\n                description:\\n                  type: string\\n                  description: 知识库描述\\n                  example: \\\"描述\\\"\\n                fileobjids:\\n                  type: string\\n                  description: 关联文件ID列表(逗号分隔)\\n                  example: \\\"83837,83838\\\"\\n                apikey:\\n                  type: string\\n                  default: \\\"3d92ed62-6dac-4d98-8489-339d3ed0d368\\\"\\n              required:\\n                - name\\n                - fileobjids\\n      responses:\\n        '200':\\n          description: 创建成功\\n          content:\\n            application/json:\\n              schema:\\n                type: object\\n                properties:\\n                  success:\\n                    type: boolean\\n                  knowledgeid:\\n                    type: integer\\n                    example: 12700\\n\",\"customHeaders\":\"{\\\"Authorization\\\":\\\"Bearer\\\"}\"}";

        PluginData pluginData = objectMapper.readValue(pluginDataStr, PluginData.class);
        updateDTO.setPluginData(pluginData);
        // 执行更新请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/httpPlugin/update")
                        .content(JsonUtil.toJsonString(updateDTO))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/httpPlugin/update接口的返回结果: {}", contentAsString);
    }

    /**
     * 删除HTTP插件（通过AppController）
     */
    @SneakyThrows
    private void deleteHttpPlugin(String appId) {
        // 执行删除请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/del")
                        .param("appId", appId)
        );

        // 验证结果
        perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200());

        log.info("/app/del接口调用成功，删除应用ID: {}", appId);
    }

    /**
     * 构造HTTP插件创建请求DTO
     */
    @SneakyThrows
    private CreateAppDTO buildHttpPluginCreateDTO() {
        String json = """
                {"parentId":null,"name":"测试","intro":"测试","avatar":"/imgs/app/httpPluginFill.svg","pluginData":{"apiSchemaStr":"openapi: 3.0.3\\ninfo:\\n  title: 知识库管理API\\n  version: 2.0.0\\n  description: 用于Dify自定义工具的完整知识库管理，检索的原子能力\\nservers:\\n  - url: http://**************:9997\\n    description: 知识库服务基础路径\\n\\npaths:\\n  /wxmanage/milvus/adduserknowledge.json:\\n    post:\\n      operationId: 新增用户知识库\\n      summary: 创建新知识库\\n      requestBody:\\n        required: true\\n        content:\\n          application/x-www-form-urlencoded:\\n            schema:\\n              type: object\\n              properties:\\n                name:\\n                  type: string\\n                  description: 知识库名称\\n                  example: \\"新库1\\"\\n                description:\\n                  type: string\\n                  description: 知识库描述\\n                  example: \\"描述\\"\\n                fileobjids:\\n                  type: string\\n                  description: 关联文件ID列表(逗号分隔)\\n                  example: \\"83837,83838\\"\\n                apikey:\\n                  type: string\\n                  default: \\"3d92ed62-6dac-4d98-8489-339d3ed0d368\\"\\n              required:\\n                - name\\n                - fileobjids\\n      responses:\\n        '200':\\n          description: 创建成功\\n          content:\\n            application/json:\\n              schema:\\n                type: object\\n                properties:\\n                  success:\\n                    type: boolean\\n                  knowledgeid:\\n                    type: integer\\n                    example: 12700\\n","customHeaders":"{\\"Authorization\\":\\"Bearer\\"}"}}
                """;
        return objectMapper.readValue(json, CreateAppDTO.class);
    }
}