package com.sinitek.mind.core.app.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.PositionInfo;
import com.sinitek.mind.core.chat.dto.GetChatLogsRequest;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import com.sinitek.mind.core.workflow.model.FlowNodeOutputItemType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.*;

import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * AppController - 单元测试
 */
@Slf4j
public class AppControllerTest extends MindBaseTestApplication {

    private static final String TEST_APP_ID = "689995b8941c5f474070459a";

    /**
     * 测试应用CRUD接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试应用CRUD接口")
    public void testAppCrud() {
        // 创建应用
        Long appId = createApp();

        // 更新应用
        updateApp(appId);

        // 获取应用详情
        getAppDetail(appId);

        // 删除应用
        deleteApp(appId);
    }

    /**
     * 测试获取应用列表接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取应用列表接口")
    public void testGetAppList() {
        // 构造请求参数
        ListAppDTO listAppDTO = new ListAppDTO();
        listAppDTO.setSearchKey("test");

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/list")
                        .content(JsonUtil.toJsonString(listAppDTO))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/app/list接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试复制应用接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试复制应用接口")
    public void testCopyApp() {
        // 先创建一个应用
        Long appId = createApp();

        // 执行复制操作
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/app/copy")
                        .param("appId", String.valueOf(appId))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/app/copy接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试转换工作流接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试转换工作流接口")
    public void testTransitionWorkflow() {
        // 先创建一个应用
        Long appId = createApp();

        // 构造请求参数
        TransitionWorkflowDTO transitionWorkflowDTO = new TransitionWorkflowDTO();
        transitionWorkflowDTO.setAppId(appId);
        transitionWorkflowDTO.setCreateNew(true);

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/transitionWorkflow")
                        .content(JsonUtil.toJsonString(transitionWorkflowDTO))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/app/transitionWorkflow接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试恢复应用继承权限接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试恢复应用继承权限接口")
    public void testResumeInheritPermission() {
        // 先创建一个应用
        Long appId = createApp();

        // 执行请求
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/app/resumeInheritPermission")
                        .param("appId", String.valueOf(appId))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/app/resumeInheritPermission接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试获取应用基本信息接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取应用基本信息接口")
    public void testGetBasicInfo() {
        // 先创建一个应用
        Long appId = createApp();

        // 构造请求参数
        List<Long> appIds = new ArrayList<>();
        appIds.add(appId);

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/getBasicInfo")
                        .content(JsonUtil.toJsonString(appIds))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/app/getBasicInfo接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试获取应用聊天日志接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取应用聊天日志接口")
    public void testGetChatLogs() {
        // 先创建一个应用
        Long appId = createApp();

        // 构造请求参数
        GetChatLogsRequest request = new GetChatLogsRequest();
        request.setAppId(appId);
        request.setDateStart(new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000)); // 7天前
        request.setDateEnd(new Date());

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/getChatLogs")
                        .content(JsonUtil.toJsonString(request))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/app/getChatLogs接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试导出聊天日志接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试导出聊天日志接口")
    public void testExportChatLogs() {
        // 先创建一个应用
        Long appId = createApp();

        // 构造请求参数
        ExportChatLogsBody request = new ExportChatLogsBody();
        request.setAppId(appId);
        request.setDateStart(new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000)); // 7天前
        request.setDateEnd(new Date());
        request.setTitle("test_export");
        request.setSourcesMap(new HashMap<>());

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/exportChatLogs")
                        .content(JsonUtil.toJsonString(request))
        );

        // 验证结果 - 导出接口可能直接写入response，不返回JSON
        perform.andExpect(status().isOk());

        log.info("/app/exportChatLogs接口调用成功");
    }

    /**
     * 创建应用
     */
    @SneakyThrows
    private Long createApp() {
        // 构造创建应用请求
        CreateAppDTO createAppDTO = new CreateAppDTO();
        createAppDTO.setParentId(null);
        createAppDTO.setAvatar("core/app/type/simpleFill");
        createAppDTO.setName("测试" + System.currentTimeMillis());
        createAppDTO.setType("simple");
        
        // 构造modules数据
        List<StoreNodeItemType> modules = new ArrayList<>();
        
        // 添加userGuide节点
        StoreNodeItemType userGuideNode = new StoreNodeItemType();
        userGuideNode.setNodeId("userGuide");
        userGuideNode.setName("common:core.module.template.system_config");
        userGuideNode.setIntro("common:core.module.template.config_params");
        userGuideNode.setAvatar("core/workflow/template/systemConfig");
        userGuideNode.setFlowNodeType("userGuide");
        
        PositionInfo userGuidePosition = new PositionInfo();
        userGuidePosition.setX(531.2422736065552);
        userGuidePosition.setY(-486.7611729549753);
        userGuideNode.setPosition(userGuidePosition);
        userGuideNode.setAppVersion(481L);
        
        // 添加userGuide的inputs
        List<FlowNodeInputItemType> userGuideInputs = new ArrayList<>();
        
        // welcomeText输入项
        FlowNodeInputItemType welcomeTextInput = new FlowNodeInputItemType();
        welcomeTextInput.setKey("welcomeText");
        welcomeTextInput.setRenderTypeList(List.of("hidden"));
        welcomeTextInput.setValueType("string");
        welcomeTextInput.setLabel("core.app.Welcome Text");
        welcomeTextInput.setValue("");
        userGuideInputs.add(welcomeTextInput);
        
        // variables输入项
        FlowNodeInputItemType variablesInput = new FlowNodeInputItemType();
        variablesInput.setKey("variables");
        variablesInput.setRenderTypeList(List.of("hidden"));
        variablesInput.setValueType("any");
        variablesInput.setLabel("core.app.Chat Variable");
        variablesInput.setValue(new ArrayList<>());
        userGuideInputs.add(variablesInput);
        
        // questionGuide输入项
        FlowNodeInputItemType questionGuideInput = new FlowNodeInputItemType();
        questionGuideInput.setKey("questionGuide");
        questionGuideInput.setValueType("object");
        questionGuideInput.setRenderTypeList(List.of("hidden"));
        questionGuideInput.setLabel("core.app.Question Guide");
        Map<String, Object> questionGuideValue = new HashMap<>();
        questionGuideValue.put("open", false);
        questionGuideInput.setValue(questionGuideValue);
        userGuideInputs.add(questionGuideInput);
        
        // tts输入项
        FlowNodeInputItemType ttsInput = new FlowNodeInputItemType();
        ttsInput.setKey("tts");
        ttsInput.setRenderTypeList(List.of("hidden"));
        ttsInput.setValueType("any");
        ttsInput.setLabel("");
        Map<String, Object> ttsValue = new HashMap<>();
        ttsValue.put("type", "web");
        ttsInput.setValue(ttsValue);
        userGuideInputs.add(ttsInput);
        
        // whisper输入项
        FlowNodeInputItemType whisperInput = new FlowNodeInputItemType();
        whisperInput.setKey("whisper");
        whisperInput.setRenderTypeList(List.of("hidden"));
        whisperInput.setValueType("any");
        whisperInput.setLabel("");
        Map<String, Object> whisperValue = new HashMap<>();
        whisperValue.put("open", false);
        whisperValue.put("autoSend", false);
        whisperValue.put("autoTTSResponse", false);
        whisperInput.setValue(whisperValue);
        userGuideInputs.add(whisperInput);
        
        // scheduleTrigger输入项
        FlowNodeInputItemType scheduleTriggerInput = new FlowNodeInputItemType();
        scheduleTriggerInput.setKey("scheduleTrigger");
        scheduleTriggerInput.setRenderTypeList(List.of("hidden"));
        scheduleTriggerInput.setValueType("any");
        scheduleTriggerInput.setLabel("");
        scheduleTriggerInput.setValue(null);
        userGuideInputs.add(scheduleTriggerInput);
        
        userGuideNode.setInputs(userGuideInputs);
        userGuideNode.setOutputs(new ArrayList<>());
        modules.add(userGuideNode);
        
        // 添加workflowStart节点
        StoreNodeItemType workflowStartNode = new StoreNodeItemType();
        workflowStartNode.setNodeId("448745");
        workflowStartNode.setName("common:core.module.template.work_start");
        workflowStartNode.setIntro("");
        workflowStartNode.setAvatar("core/workflow/template/workflowStart");
        workflowStartNode.setFlowNodeType("workflowStart");
        
        PositionInfo workflowStartPosition = new PositionInfo();
        workflowStartPosition.setX(558.4082376415505);
        workflowStartPosition.setY(123.72387429194112);
        workflowStartNode.setPosition(workflowStartPosition);
        workflowStartNode.setAppVersion(481L);
        
        // 添加workflowStart的inputs
        List<FlowNodeInputItemType> workflowStartInputs = new ArrayList<>();
        
        FlowNodeInputItemType userChatInput = new FlowNodeInputItemType();
        userChatInput.setKey("userChatInput");
        userChatInput.setRenderTypeList(List.of("reference", "textarea"));
        userChatInput.setValueType("string");
        userChatInput.setLabel("common:core.module.input.label.user question");
        userChatInput.setRequired(true);
        userChatInput.setToolDescription("common:core.module.input.label.user question");
        workflowStartInputs.add(userChatInput);
        
        workflowStartNode.setInputs(workflowStartInputs);
        
        // 添加workflowStart的outputs
        List<FlowNodeOutputItemType> workflowStartOutputs = new ArrayList<>();
        
        FlowNodeOutputItemType userChatOutput = new FlowNodeOutputItemType();
        userChatOutput.setId("userChatInput");
        userChatOutput.setKey("userChatInput");
        userChatOutput.setLabel("core.module.input.label.user question");
        userChatOutput.setValueType("string");
        userChatOutput.setType("static");
        workflowStartOutputs.add(userChatOutput);
        
        workflowStartNode.setOutputs(workflowStartOutputs);
        modules.add(workflowStartNode);
        
        // 添加chatNode节点
        StoreNodeItemType chatNode = new StoreNodeItemType();
        chatNode.setNodeId("loOvhld2ZTKa");
        chatNode.setName("common:core.module.template.ai_chat");
        chatNode.setIntro("common:core.module.template.ai_chat_intro");
        chatNode.setAvatar("core/workflow/template/aiChat");
        chatNode.setFlowNodeType("chatNode");
        chatNode.setShowStatus(true);
        
        PositionInfo chatNodePosition = new PositionInfo();
        chatNodePosition.setX(1097.7317280958762);
        chatNodePosition.setY(-244.16014496351386);
        chatNode.setPosition(chatNodePosition);
        chatNode.setAppVersion(481L);
        
        // 添加chatNode的inputs
        List<FlowNodeInputItemType> chatNodeInputs = new ArrayList<>();
        
        // model输入项
        FlowNodeInputItemType modelInput = new FlowNodeInputItemType();
        modelInput.setKey("model");
        modelInput.setRenderTypeList(List.of("settingLLMModel", "reference"));
        modelInput.setLabel("core.module.input.label.aiModel");
        modelInput.setValueType("string");
        modelInput.setValue("gpt-4o-mini");
        chatNodeInputs.add(modelInput);
        
        // temperature输入项
        FlowNodeInputItemType temperatureInput = new FlowNodeInputItemType();
        temperatureInput.setKey("temperature");
        temperatureInput.setRenderTypeList(List.of("hidden"));
        temperatureInput.setLabel("");
        temperatureInput.setValueType("number");
        temperatureInput.setMin(0);
        temperatureInput.setMax(10);
        temperatureInput.setStep(1);
        chatNodeInputs.add(temperatureInput);
        
        // maxToken输入项
        FlowNodeInputItemType maxTokenInput = new FlowNodeInputItemType();
        maxTokenInput.setKey("maxToken");
        maxTokenInput.setRenderTypeList(List.of("hidden"));
        maxTokenInput.setLabel("");
        maxTokenInput.setValueType("number");
        maxTokenInput.setMin(100);
        maxTokenInput.setMax(4000);
        maxTokenInput.setStep(50);
        chatNodeInputs.add(maxTokenInput);
        
        // isResponseAnswerText输入项
        FlowNodeInputItemType isResponseAnswerTextInput = new FlowNodeInputItemType();
        isResponseAnswerTextInput.setKey("isResponseAnswerText");
        isResponseAnswerTextInput.setRenderTypeList(List.of("hidden"));
        isResponseAnswerTextInput.setLabel("");
        isResponseAnswerTextInput.setValue(true);
        isResponseAnswerTextInput.setValueType("boolean");
        chatNodeInputs.add(isResponseAnswerTextInput);
        
        // quoteTemplate输入项
        FlowNodeInputItemType quoteTemplateInput = new FlowNodeInputItemType();
        quoteTemplateInput.setKey("quoteTemplate");
        quoteTemplateInput.setRenderTypeList(List.of("hidden"));
        quoteTemplateInput.setLabel("");
        quoteTemplateInput.setValueType("string");
        chatNodeInputs.add(quoteTemplateInput);
        
        // quotePrompt输入项
        FlowNodeInputItemType quotePromptInput = new FlowNodeInputItemType();
        quotePromptInput.setKey("quotePrompt");
        quotePromptInput.setRenderTypeList(List.of("hidden"));
        quotePromptInput.setLabel("");
        quotePromptInput.setValueType("string");
        chatNodeInputs.add(quotePromptInput);
        
        // systemPrompt输入项
        FlowNodeInputItemType systemPromptInput = new FlowNodeInputItemType();
        systemPromptInput.setKey("systemPrompt");
        systemPromptInput.setRenderTypeList(List.of("textarea", "reference"));
        systemPromptInput.setMax(3000);
        systemPromptInput.setValueType("string");
        systemPromptInput.setLabel("core.ai.Prompt");
        systemPromptInput.setDescription("core.app.tip.systemPromptTip");
        systemPromptInput.setPlaceholder("core.app.tip.chatNodeSystemPromptTip");
        systemPromptInput.setValue("");
        chatNodeInputs.add(systemPromptInput);
        
        // history输入项
        FlowNodeInputItemType historyInput = new FlowNodeInputItemType();
        historyInput.setKey("history");
        historyInput.setRenderTypeList(List.of("numberInput", "reference"));
        historyInput.setValueType("chatHistory");
        historyInput.setLabel("core.module.input.label.chat history");
        historyInput.setRequired(true);
        historyInput.setMin(0);
        historyInput.setMax(30);
        historyInput.setValue(6);
        chatNodeInputs.add(historyInput);
        
        // userChatInput输入项
        FlowNodeInputItemType userChatInputForChat = new FlowNodeInputItemType();
        userChatInputForChat.setKey("userChatInput");
        userChatInputForChat.setRenderTypeList(List.of("reference", "textarea"));
        userChatInputForChat.setValueType("string");
        userChatInputForChat.setLabel("common:core.module.input.label.user question");
        userChatInputForChat.setRequired(true);
        userChatInputForChat.setToolDescription("common:core.module.input.label.user question");
        userChatInputForChat.setValue(List.of("448745", "userChatInput"));
        chatNodeInputs.add(userChatInputForChat);
        
        // quoteQA输入项
        FlowNodeInputItemType quoteQAInput = new FlowNodeInputItemType();
        quoteQAInput.setKey("quoteQA");
        quoteQAInput.setRenderTypeList(List.of("settingDatasetQuotePrompt"));
        quoteQAInput.setLabel("");
        quoteQAInput.setDebugLabel("common:core.module.Dataset quote.label");
        quoteQAInput.setDescription("");
        quoteQAInput.setValueType("datasetQuote");
        chatNodeInputs.add(quoteQAInput);
        
        // aiChatReasoning输入项
        FlowNodeInputItemType aiChatReasoningInput = new FlowNodeInputItemType();
        aiChatReasoningInput.setKey("aiChatReasoning");
        aiChatReasoningInput.setRenderTypeList(List.of("hidden"));
        aiChatReasoningInput.setLabel("");
        aiChatReasoningInput.setValueType("boolean");
        aiChatReasoningInput.setValue(true);
        chatNodeInputs.add(aiChatReasoningInput);
        
        chatNode.setInputs(chatNodeInputs);
        
        // 添加chatNode的outputs
        List<FlowNodeOutputItemType> chatNodeOutputs = new ArrayList<>();
        
        // history输出项
        FlowNodeOutputItemType historyOutput = new FlowNodeOutputItemType();
        historyOutput.setId("history");
        historyOutput.setKey("history");
        historyOutput.setLabel("core.module.output.label.New context");
        historyOutput.setDescription("core.module.output.description.New context");
        historyOutput.setValueType("chatHistory");
        historyOutput.setType("static");
        chatNodeOutputs.add(historyOutput);
        
        // answerText输出项
        FlowNodeOutputItemType answerTextOutput = new FlowNodeOutputItemType();
        answerTextOutput.setId("answerText");
        answerTextOutput.setKey("answerText");
        answerTextOutput.setLabel("core.module.output.label.Ai response content");
        answerTextOutput.setDescription("core.module.output.description.Ai response content");
        answerTextOutput.setValueType("string");
        answerTextOutput.setType("static");
        chatNodeOutputs.add(answerTextOutput);
        
        chatNode.setOutputs(chatNodeOutputs);
        modules.add(chatNode);
        
        createAppDTO.setModules(modules);
        
        // 构造edges数据
        List<StoreEdgeItemType> edges = new ArrayList<>();
        StoreEdgeItemType edge = new StoreEdgeItemType();
        edge.setSource("448745");
        edge.setTarget("loOvhld2ZTKa");
        edge.setSourceHandle("448745-source-right");
        edge.setTargetHandle("loOvhld2ZTKa-target-left");
        edges.add(edge);
        createAppDTO.setEdges(edges);
        
        // 设置chatConfig
        createAppDTO.setChatConfig(new AppChatConfigType());

        // 执行创建请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/create")
                        .content(JsonUtil.toJsonString(createAppDTO))
        );

        // 验证结果并获取应用ID
        MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/app/create接口的返回结果: {}", contentAsString);

        // 从响应中提取应用ID
        ObjectMapper mapper = new ObjectMapper();
        String appId = mapper.readTree(contentAsString).path("data").asText();
        return Long.valueOf(appId);
    }

    /**
     * 更新应用
     */
    @SneakyThrows
    private void updateApp(Long appId) {
        // 构造更新应用请求
        AppUpdateDTO updateDTO = new AppUpdateDTO();
        updateDTO.setName("更新后的应用名称");
        updateDTO.setType("simple");
        updateDTO.setNodes(new ArrayList<>());
        updateDTO.setEdges(new ArrayList<>());
        updateDTO.setChatConfig(new AppChatConfigType());

        // 执行更新请求
        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/app/update")
                .param("appId", String.valueOf(appId))
                .content(JsonUtil.toJsonString(updateDTO));

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/app/update接口的返回结果: {}", contentAsString);
    }

    /**
     * 获取应用详情
     */
    @SneakyThrows
    private void getAppDetail(Long appId) {
        // 执行获取详情请求
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/app/detail")
                        .param("appId", String.valueOf(appId))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data._id", is(appId)))
                .andReturn().getResponse().getContentAsString();

        log.info("/app/detail接口的返回结果: {}", contentAsString);
    }

    /**
     * 删除应用
     */
    @SneakyThrows
    private void deleteApp(Long appId) {
        // 执行删除请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/del")
                        .param("appId", String.valueOf(appId))
        );

        // 验证结果
        perform.andExpect(status().isOk());

        log.info("/app/del接口调用成功");
    }
}