package com.sinitek.mind.core.app.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.PositionInfo;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * AppVersionController - 单元测试
 *
 * <AUTHOR>
 */
@Slf4j
public class AppVersionControllerTest extends MindBaseTestApplication {

    private static Long TEST_APP_ID;
    private static Long TEST_VERSION_ID;

    /**
     * 每个单元测试执行前自动初始化测试数据
     */
    @BeforeEach
    public void initTestData() {
        createApp();
    }

    /**
     * 每个单元测试执行后清理测试数据
     */
    @AfterEach
    public void cleanupTestData() {
        deleteApp();
    }

    /**
     * 测试获取应用版本列表
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取应用版本列表")
    public void testGetVersionList() {
        // 构造请求参数
        VersionListRequest request = new VersionListRequest();
        request.setAppId(TEST_APP_ID);
        request.setPageSize(10);
        request.setOffset(0);

        // 执行请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/version/list")
                        .content(JsonUtil.toJsonString(request))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/version/list接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试获取应用最新版本
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取应用最新版本")
    public void testGetLatestVersion() {
        // 执行请求
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/app/version/latest")
                        .param("appId", String.valueOf(TEST_APP_ID))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/version/latest接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试发布应用版本
     */
    @Test
    @SneakyThrows
    @DisplayName("测试发布应用版本")
    public void testPublishApp() {
        // 构造发布请求
        PublishAppDTO publishAppDTO = createPublishAppDTO();

        // 执行发布请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/version/publish")
                        .param("appId", String.valueOf(TEST_APP_ID))
                        .content(JsonUtil.toJsonString(publishAppDTO))
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/version/publish接口的返回结果: {}", contentAsString);

        PublishAppDTO notPublishAppDTO = createPublishAppDTO();
        notPublishAppDTO.setIsPublish(false);

        // 执行发布请求
        ResultActions perform2 = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/version/publish")
                        .param("appId", String.valueOf(TEST_APP_ID))
                        .content(JsonUtil.toJsonString(notPublishAppDTO))
        );

        // 验证结果
        String contentAsString2 = perform2.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/version/publish接口的返回结果(不发布): {}", contentAsString2);

        // 从响应中提取版本ID
        ObjectMapper mapper = new ObjectMapper();
        TEST_VERSION_ID = Long.valueOf(mapper.readTree(contentAsString).path("data").path("versionId").asText());
    }

    /**
     * 测试更新应用版本
     */
    @Test
    @SneakyThrows
    @DisplayName("测试更新应用版本")
    public void testUpdateAppVersion() {
        // 先发布一个版本
        testPublishApp();

        if (TEST_VERSION_ID != null) {
            // 构造更新请求
            UpdateAppVersionDTO updateRequest = new UpdateAppVersionDTO();
            updateRequest.setAppId(TEST_APP_ID);
            updateRequest.setVersionId(TEST_VERSION_ID);
            updateRequest.setVersionName("更新后的版本名称");

            // 执行更新请求
            ResultActions perform = mockMvc.perform(
                    postMockRequestForAdmin("/mind/api/core/app/version/update")
                            .content(JsonUtil.toJsonString(updateRequest))
            );

            // 验证结果
            String contentAsString = perform.andExpect(status().isOk())
                    .andExpect(jsonPathSuccessIsTrue())
                    .andReturn().getResponse().getContentAsString();

            log.info("/version/update接口的返回结果: {}", contentAsString);
        }
    }

    /**
     * 测试获取应用版本详情
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取应用版本详情")
    public void testGetVersionDetail() {
        // 先发布一个版本
        testPublishApp();

        if (TEST_VERSION_ID != null) {
            // 构造详情请求
            VersionDetailRequest detailRequest = new VersionDetailRequest();
            detailRequest.setAppId(TEST_APP_ID);
            detailRequest.setVersionId(TEST_VERSION_ID);

            // 执行详情请求
            ResultActions perform = mockMvc.perform(
                    postMockRequestForAdmin("/mind/api/core/app/version/detail")
                            .content(JsonUtil.toJsonString(detailRequest))
            );

            // 验证结果
            String contentAsString = perform.andExpect(status().isOk())
                    .andExpect(jsonPathSuccessIsTrue())
                    .andExpect(jsonPath("$.data", notNullValue()))
                    .andReturn().getResponse().getContentAsString();

            log.info("/version/detail接口的返回结果: {}", contentAsString);
        }
    }

    /**
     * 创建应用
     */
    @SneakyThrows
    private void createApp() {
        // 构造创建应用请求
        CreateAppDTO createAppDTO = new CreateAppDTO();
        createAppDTO.setParentId(null);
        createAppDTO.setAvatar("core/app/type/simpleFill");
        createAppDTO.setName("测试应用版本" + System.currentTimeMillis());
        createAppDTO.setType(AppTypeEnum.SIMPLE.getValue());

        // 构造modules数据
        List<StoreNodeItemType> modules = new ArrayList<>();

        // 添加userGuide节点
        StoreNodeItemType userGuideNode = new StoreNodeItemType();
        userGuideNode.setNodeId("userGuide");
        userGuideNode.setName("common:core.module.template.system_config");
        userGuideNode.setIntro("common:core.module.template.config_params");
        userGuideNode.setAvatar("core/workflow/template/systemConfig");
        userGuideNode.setFlowNodeType("userGuide");

        PositionInfo userGuidePosition = new PositionInfo();
        userGuidePosition.setX(531.2422736065552);
        userGuidePosition.setY(-486.7611729549753);
        userGuideNode.setPosition(userGuidePosition);
        userGuideNode.setVersion(481L);

        // 添加userGuide的inputs
        List<FlowNodeInputItemType> userGuideInputs = new ArrayList<>();

        // welcomeText输入项
        FlowNodeInputItemType welcomeTextInput = new FlowNodeInputItemType();
        welcomeTextInput.setKey("welcomeText");
        welcomeTextInput.setRenderTypeList(List.of("hidden"));
        welcomeTextInput.setValueType("string");
        welcomeTextInput.setLabel("core.app.Welcome Text");
        welcomeTextInput.setValue("");
        userGuideInputs.add(welcomeTextInput);

        // variables输入项
        FlowNodeInputItemType variablesInput = new FlowNodeInputItemType();
        variablesInput.setKey("variables");
        variablesInput.setRenderTypeList(List.of("hidden"));
        variablesInput.setValueType("any");
        variablesInput.setLabel("core.app.Chat Variable");
        variablesInput.setValue(new ArrayList<>());
        userGuideInputs.add(variablesInput);

        userGuideNode.setInputs(userGuideInputs);
        userGuideNode.setOutputs(new ArrayList<>());
        modules.add(userGuideNode);

        // 添加workflowStart节点
        StoreNodeItemType workflowStartNode = new StoreNodeItemType();
        workflowStartNode.setNodeId("448745");
        workflowStartNode.setName("common:core.module.template.work_start");
        workflowStartNode.setIntro("");
        workflowStartNode.setAvatar("core/workflow/template/workflowStart");
        workflowStartNode.setFlowNodeType("workflowStart");

        PositionInfo workflowStartPosition = new PositionInfo();
        workflowStartPosition.setX(558.4082376415505);
        workflowStartPosition.setY(123.72387429194112);
        workflowStartNode.setPosition(workflowStartPosition);
        workflowStartNode.setVersion(481L);

        // 添加workflowStart的inputs
        List<FlowNodeInputItemType> workflowStartInputs = new ArrayList<>();

        FlowNodeInputItemType userChatInput = new FlowNodeInputItemType();
        userChatInput.setKey("userChatInput");
        userChatInput.setRenderTypeList(List.of("reference", "textarea"));
        userChatInput.setValueType("string");
        userChatInput.setLabel("common:core.module.input.label.user question");
        userChatInput.setRequired(true);
        userChatInput.setToolDescription("common:core.module.input.label.user question");
        workflowStartInputs.add(userChatInput);

        workflowStartNode.setInputs(workflowStartInputs);
        workflowStartNode.setOutputs(new ArrayList<>());
        modules.add(workflowStartNode);

        createAppDTO.setModules(modules);

        // 构造edges数据
        List<StoreEdgeItemType> edges = new ArrayList<>();
        createAppDTO.setEdges(edges);

        // 设置chatConfig
        createAppDTO.setChatConfig(new AppChatConfigType());

        // 执行创建请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/create")
                        .content(JsonUtil.toJsonString(createAppDTO))
        );

        // 验证结果并获取应用ID
        MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/app/create接口的返回结果: {}", contentAsString);

        // 从响应中提取应用ID
        ObjectMapper mapper = new ObjectMapper();
        TEST_APP_ID = Long.valueOf(mapper.readTree(contentAsString).path("data").asText());
    }

    /**
     * 删除应用
     */
    @SneakyThrows
    private void deleteApp() {
        if (TEST_APP_ID != null) {
            try {
                // 执行删除请求
                ResultActions perform = mockMvc.perform(
                        postMockRequestForAdmin("/mind/api/core/app/del")
                                .param("appId", String.valueOf(TEST_APP_ID))
                );

                // 验证结果
                perform.andExpect(status().isOk());

                log.info("/app/del接口调用成功，删除应用ID: {}", TEST_APP_ID);
            } catch (Exception e) {
                log.warn("删除应用时出现异常，可能是应用不存在：{}", e.getMessage());
            }
        }
    }

    /**
     * 构造发布应用DTO
     */
    private PublishAppDTO createPublishAppDTO() {
        PublishAppDTO publishAppDTO = new PublishAppDTO();
        
        // 构造nodes数据
        List<StoreNodeItemType> nodes = new ArrayList<>();

        // 添加userGuide节点
        StoreNodeItemType userGuideNode = new StoreNodeItemType();
        userGuideNode.setNodeId("userGuide");
        userGuideNode.setName("common:core.module.template.system_config");
        userGuideNode.setIntro("common:core.module.template.config_params");
        userGuideNode.setAvatar("core/workflow/template/systemConfig");
        userGuideNode.setFlowNodeType("userGuide");

        PositionInfo userGuidePosition = new PositionInfo();
        userGuidePosition.setX(531.2422736065552);
        userGuidePosition.setY(-486.7611729549753);
        userGuideNode.setPosition(userGuidePosition);
        userGuideNode.setVersion(481L);

        // 添加userGuide的inputs
        List<FlowNodeInputItemType> userGuideInputs = new ArrayList<>();

        FlowNodeInputItemType welcomeTextInput = new FlowNodeInputItemType();
        welcomeTextInput.setKey("welcomeText");
        welcomeTextInput.setRenderTypeList(List.of("hidden"));
        welcomeTextInput.setValueType("string");
        welcomeTextInput.setLabel("core.app.Welcome Text");
        welcomeTextInput.setValue("欢迎使用测试应用");
        userGuideInputs.add(welcomeTextInput);

        FlowNodeInputItemType variablesInput = new FlowNodeInputItemType();
        variablesInput.setKey("variables");
        variablesInput.setRenderTypeList(List.of("hidden"));
        variablesInput.setValueType("any");
        variablesInput.setLabel("core.app.Chat Variable");
        variablesInput.setValue(new ArrayList<>());
        userGuideInputs.add(variablesInput);

        userGuideNode.setInputs(userGuideInputs);
        userGuideNode.setOutputs(new ArrayList<>());
        nodes.add(userGuideNode);

        // 添加workflowStart节点
        StoreNodeItemType workflowStartNode = new StoreNodeItemType();
        workflowStartNode.setNodeId("448745");
        workflowStartNode.setName("common:core.module.template.work_start");
        workflowStartNode.setIntro("");
        workflowStartNode.setAvatar("core/workflow/template/workflowStart");
        workflowStartNode.setFlowNodeType("workflowStart");

        PositionInfo workflowStartPosition = new PositionInfo();
        workflowStartPosition.setX(558.4082376415505);
        workflowStartPosition.setY(123.72387429194112);
        workflowStartNode.setPosition(workflowStartPosition);
        workflowStartNode.setVersion(481L);

        // 添加workflowStart的inputs
        List<FlowNodeInputItemType> workflowStartInputs = new ArrayList<>();

        FlowNodeInputItemType userChatInput = new FlowNodeInputItemType();
        userChatInput.setKey("userChatInput");
        userChatInput.setRenderTypeList(List.of("reference", "textarea"));
        userChatInput.setValueType("string");
        userChatInput.setLabel("common:core.module.input.label.user question");
        userChatInput.setRequired(true);
        userChatInput.setToolDescription("common:core.module.input.label.user question");
        workflowStartInputs.add(userChatInput);

        workflowStartNode.setInputs(workflowStartInputs);
        workflowStartNode.setOutputs(new ArrayList<>());
        nodes.add(workflowStartNode);

        publishAppDTO.setNodes(nodes);

        // 构造edges数据
        List<StoreEdgeItemType> edges = new ArrayList<>();
        publishAppDTO.setEdges(edges);

        // 设置chatConfig
        publishAppDTO.setChatConfig(new AppChatConfigType());
        
        // 设置发布参数
        publishAppDTO.setIsPublish(true);
        publishAppDTO.setVersionName("测试版本v1.0");
        publishAppDTO.setAutoSave(false);

        return publishAppDTO;
    }
}