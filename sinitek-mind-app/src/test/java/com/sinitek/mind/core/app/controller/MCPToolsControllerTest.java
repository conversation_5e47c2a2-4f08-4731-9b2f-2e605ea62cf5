package com.sinitek.mind.core.app.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.core.app.dto.CreateMCPToolsDTO;
import com.sinitek.mind.core.app.dto.UpdateMCPToolsDTO;
import com.sinitek.mind.core.app.model.JSONSchemaInputType;
import com.sinitek.mind.core.app.model.McpToolConfig;
import com.sinitek.mind.core.app.model.SecretValue;
import com.sinitek.mind.core.workflow.model.PropertiesValue;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.*;

import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * MCPToolsController - 单元测试
 *
 * <AUTHOR>
 */
@Slf4j
public class MCPToolsControllerTest extends MindBaseTestApplication {

    private static final String TEST_MCP_TOOLS_NAME = "高德MCP服务";
    private static final String TEST_MCP_TOOLS_AVATAR = "core/app/type/mcpToolsFill";
    private static final String TEST_MCP_TOOLS_URL = "https://mcp.amap.com/sse?key=a08cebad0306f9c3147c6f22835f39d5";
    private static final String UPDATED_MCP_TOOLS_NAME = "更新后的高德MCP服务";

    /**
     * 测试MCP工具集CRUD接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试MCP工具集CRUD接口")
    public void testMCPToolsCrud() {
        // 创建MCP工具集
        Long mcpToolsId = createMCPTools();

        // 更新MCP工具集
        updateMCPTools(mcpToolsId);

        // 删除操作在实际业务中可能通过其他接口实现，这里暂不测试
    }

    /**
     * 测试健康检查接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试健康检查接口")
    public void testHealthCheck() {
        // 执行健康检查请求
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/app/mcpTools/health")
        );

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.status", is("UP")))
                .andExpect(jsonPath("$.service", is("MCP Tools Service")))
                .andExpect(jsonPath("$.timestamp", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/mcpTools/health接口的返回结果: {}", contentAsString);
    }

    /**
     * 创建MCP工具集
     */
    @SneakyThrows
    private Long createMCPTools() {
        // 构造创建MCP工具集请求
        CreateMCPToolsDTO createDTO = buildCreateMCPToolsDTO();

        // 执行创建请求
        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/app/mcpTools/create")
                .content(JsonUtil.toJsonString(createDTO));

        ResultActions perform = mockMvc.perform(request);

        // 验证结果并获取MCP工具集ID
        MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data.mcpToolsId", notNullValue()))
                .andExpect(jsonPath("$.data.status", is("success")))
                .andExpect(jsonPath("$.data.message", is("MCP工具集创建成功")))
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/mcpTools/create接口的返回结果: {}", contentAsString);

        // 从响应中提取MCP工具集ID
        ObjectMapper mapper = new ObjectMapper();
        String mcpToolsId = mapper.readTree(contentAsString).path("data").path("mcpToolsId").asText();
        return Long.valueOf(mcpToolsId);
    }

    /**
     * 更新MCP工具集
     */
    @SneakyThrows
    private void updateMCPTools(Long appId) {
        // 构造更新MCP工具集请求
        UpdateMCPToolsDTO updateDTO = buildUpdateMCPToolsDTO(appId);

        // 执行更新请求
        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/app/mcpTools/update")
                .content(JsonUtil.toJsonString(updateDTO));

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data.appId", is(appId)))
                .andExpect(jsonPath("$.data.status", is("success")))
                .andExpect(jsonPath("$.data.message", is("MCP工具集更新成功")))
                .andReturn().getResponse().getContentAsString();

        log.info("/mcpTools/update接口的返回结果: {}", contentAsString);
    }

    /**
     * 构造创建MCP工具集请求数据
     */
    private CreateMCPToolsDTO buildCreateMCPToolsDTO() {
        CreateMCPToolsDTO createDTO = new CreateMCPToolsDTO();
        createDTO.setName(TEST_MCP_TOOLS_NAME);
        createDTO.setAvatar(TEST_MCP_TOOLS_AVATAR);
        createDTO.setUrl(TEST_MCP_TOOLS_URL);
        createDTO.setHeaderSecret(buildHeaderSecret());
        createDTO.setToolList(buildToolList());

        return createDTO;
    }

    /**
     * 构造更新MCP工具集请求数据
     */
    private UpdateMCPToolsDTO buildUpdateMCPToolsDTO(Long appId) {
        UpdateMCPToolsDTO updateDTO = new UpdateMCPToolsDTO();
        updateDTO.setAppId(appId);
        updateDTO.setUrl(TEST_MCP_TOOLS_URL);
        updateDTO.setHeaderSecret(buildHeaderSecret());
        updateDTO.setToolList(buildUpdatedToolList());

        return updateDTO;
    }

    /**
     * 构造请求头密钥配置
     */
    private Map<String, SecretValue> buildHeaderSecret() {
        Map<String, SecretValue> headerSecret = new HashMap<>();
        // 这里可以根据实际需要添加密钥配置
        return headerSecret;
    }

    /**
     * 构造工具列表
     */
    private List<McpToolConfig> buildToolList() {
        List<McpToolConfig> toolList = new ArrayList<>();

        // 添加骑行路径规划工具
        toolList.add(buildBicyclingDirectionTool());

        // 添加驾车路径规划工具
        toolList.add(buildDrivingDirectionTool());

        // 添加地理编码工具
        toolList.add(buildGeocodeTool());

        return toolList;
    }

    /**
     * 构造更新后的工具列表（用于测试更新功能）
     */
    private List<McpToolConfig> buildUpdatedToolList() {
        List<McpToolConfig> toolList = new ArrayList<>();

        // 保留原有工具
        toolList.add(buildBicyclingDirectionTool());
        toolList.add(buildDrivingDirectionTool());

        // 添加新工具
        toolList.add(buildWeatherTool());

        return toolList;
    }

    /**
     * 构造骑行路径规划工具
     */
    private McpToolConfig buildBicyclingDirectionTool() {
        Map<String, PropertiesValue> properties = new HashMap<>();

        PropertiesValue originProp = new PropertiesValue();
        originProp.setType("string");
        originProp.setDescription("出发点经纬度，坐标格式为：经度，纬度");
        properties.put("origin", originProp);

        PropertiesValue destinationProp = new PropertiesValue();
        destinationProp.setType("string");
        destinationProp.setDescription("目的地经纬度，坐标格式为：经度，纬度");
        properties.put("destination", destinationProp);

        JSONSchemaInputType inputSchema = JSONSchemaInputType.builder()
                .type("object")
                .properties(properties)
                .required(Arrays.asList("origin", "destination"))
                .build();

        return McpToolConfig.builder()
                .name("maps_direction_bicycling")
                .description("骑行路径规划用于规划骑行通勤方案，规划时会考虑天桥、单行线、封路等情况。最大支持 500km 的骑行路线规划")
                .inputSchema(inputSchema)
                .build();
    }

    /**
     * 构造驾车路径规划工具
     */
    private McpToolConfig buildDrivingDirectionTool() {
        Map<String, PropertiesValue> properties = new HashMap<>();

        PropertiesValue originProp = new PropertiesValue();
        originProp.setType("string");
        originProp.setDescription("出发点经纬度，坐标格式为：经度，纬度");
        properties.put("origin", originProp);

        PropertiesValue destinationProp = new PropertiesValue();
        destinationProp.setType("string");
        destinationProp.setDescription("目的地经纬度，坐标格式为：经度，纬度");
        properties.put("destination", destinationProp);
        JSONSchemaInputType inputSchema = JSONSchemaInputType.builder()
                .type("object")
                .properties(properties)
                .required(Arrays.asList("origin", "destination"))
                .build();

        return McpToolConfig.builder()
                .name("maps_direction_driving")
                .description("驾车路径规划 API 可以根据用户起终点经纬度坐标规划以小客车、轿车通勤出行的方案，并且返回通勤方案的数据。")
                .inputSchema(inputSchema)
                .build();
    }

    /**
     * 构造地理编码工具
     */
    private McpToolConfig buildGeocodeTool() {
        Map<String, PropertiesValue> properties = new HashMap<>();

        PropertiesValue addressProp = new PropertiesValue();
        addressProp.setType("string");
        addressProp.setDescription("待解析的结构化地址信息");
        properties.put("address", addressProp);

        PropertiesValue cityProp = new PropertiesValue();
        cityProp.setType("string");
        cityProp.setDescription("指定查询的城市");
        properties.put("city", cityProp);

        JSONSchemaInputType inputSchema = JSONSchemaInputType.builder()
                .type("object")
                .properties(properties)
                .required(Arrays.asList("address"))
                .build();

        return McpToolConfig.builder()
                .name("maps_geo")
                .description("将详细的结构化地址转换为经纬度坐标。支持对地标性名胜景区、建筑物名称解析为经纬度坐标")
                .inputSchema(inputSchema)
                .build();
    }

    /**
     * 构造天气查询工具
     */
    private McpToolConfig buildWeatherTool() {
        Map<String, PropertiesValue> properties = new HashMap<>();

        PropertiesValue cityProp = new PropertiesValue();
        cityProp.setType("string");
        cityProp.setDescription("城市名称或者adcode");
        properties.put("city", cityProp);

        JSONSchemaInputType inputSchema = JSONSchemaInputType.builder()
                .type("object")
                .properties(properties)
                .required(Arrays.asList("city"))
                .build();

        return McpToolConfig.builder()
                .name("maps_weather")
                .description("根据城市名称或者标准adcode查询指定城市的天气")
                .inputSchema(inputSchema)
                .build();
    }
}