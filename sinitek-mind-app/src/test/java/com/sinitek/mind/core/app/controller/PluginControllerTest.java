package com.sinitek.mind.core.app.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.dto.GetVersionListRequest;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.PositionInfo;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * PluginController - 单元测试
 * 使用真实数据进行测试
 * 
 * <AUTHOR>
 */
@Slf4j
public class PluginControllerTest extends MindBaseTestApplication {

    private static String TEST_APP_ID;
    private static String TEST_APP_VERSION;
    private static final String TEST_SEARCH_KEY = "test";
    private static final String TEST_PARENT_ID = "";

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 每个单元测试执行前初始化测试数据
     */
    @BeforeEach
    public void initTestData() {
        createTestApp();
    }

    /**
     * 每个单元测试执行后清理测试数据
     */
    @AfterEach
    public void cleanupTestData() {
        deleteTestApp();
    }

    /**
     * 创建测试应用
     */
    @SneakyThrows
    private void createTestApp() {
        // 构造创建应用请求
        CreateAppDTO createAppDTO = new CreateAppDTO();
        createAppDTO.setParentId(null);
        createAppDTO.setAvatar("core/app/type/simpleFill");
        createAppDTO.setName("测试应用" + System.currentTimeMillis());
        createAppDTO.setType(AppTypeEnum.SIMPLE.getValue());
        
        // 构造modules数据
        List<StoreNodeItemType> modules = new ArrayList<>();
        
        // 添加userGuide节点
        StoreNodeItemType userGuideNode = new StoreNodeItemType();
        userGuideNode.setNodeId("userGuide");
        userGuideNode.setName("common:core.module.template.system_config");
        userGuideNode.setIntro("common:core.module.template.config_params");
        userGuideNode.setAvatar("core/workflow/template/systemConfig");
        userGuideNode.setFlowNodeType("userGuide");
        
        PositionInfo userGuidePosition = new PositionInfo();
        userGuidePosition.setX(531.2422736065552);
        userGuidePosition.setY(-486.7611729549753);
        userGuideNode.setPosition(userGuidePosition);
        userGuideNode.setVersion("481");
        
        // 添加userGuide的inputs
        List<FlowNodeInputItemType> userGuideInputs = new ArrayList<>();
        
        // welcomeText输入项
        FlowNodeInputItemType welcomeTextInput = new FlowNodeInputItemType();
        welcomeTextInput.setKey("welcomeText");
        welcomeTextInput.setRenderTypeList(List.of("hidden"));
        welcomeTextInput.setValueType("string");
        welcomeTextInput.setLabel("core.app.Welcome Text");
        welcomeTextInput.setValue("");
        userGuideInputs.add(welcomeTextInput);
        
        // variables输入项
        FlowNodeInputItemType variablesInput = new FlowNodeInputItemType();
        variablesInput.setKey("variables");
        variablesInput.setRenderTypeList(List.of("hidden"));
        variablesInput.setValueType("any");
        variablesInput.setLabel("core.app.Chat Variable");
        variablesInput.setValue(new ArrayList<>());
        userGuideInputs.add(variablesInput);
        
        userGuideNode.setInputs(userGuideInputs);
        userGuideNode.setOutputs(new ArrayList<>());
        modules.add(userGuideNode);
        
        // 添加workflowStart节点
        StoreNodeItemType workflowStartNode = new StoreNodeItemType();
        workflowStartNode.setNodeId("448745");
        workflowStartNode.setName("common:core.module.template.work_start");
        workflowStartNode.setIntro("");
        workflowStartNode.setAvatar("core/workflow/template/workflowStart");
        workflowStartNode.setFlowNodeType("workflowStart");
        
        PositionInfo workflowStartPosition = new PositionInfo();
        workflowStartPosition.setX(558.4082376415505);
        workflowStartPosition.setY(123.72387429194112);
        workflowStartNode.setPosition(workflowStartPosition);
        workflowStartNode.setVersion("481");
        
        // 添加workflowStart的inputs
        List<FlowNodeInputItemType> workflowStartInputs = new ArrayList<>();
        
        FlowNodeInputItemType userChatInput = new FlowNodeInputItemType();
        userChatInput.setKey("userChatInput");
        userChatInput.setRenderTypeList(List.of("reference", "textarea"));
        userChatInput.setValueType("string");
        userChatInput.setLabel("common:core.module.input.label.user question");
        userChatInput.setRequired(true);
        userChatInput.setToolDescription("common:core.module.input.label.user question");
        workflowStartInputs.add(userChatInput);
        
        workflowStartNode.setInputs(workflowStartInputs);
        workflowStartNode.setOutputs(new ArrayList<>());
        modules.add(workflowStartNode);
        
        createAppDTO.setModules(modules);
        
        // 构造edges数据
        List<StoreEdgeItemType> edges = new ArrayList<>();
        createAppDTO.setEdges(edges);
        
        // 设置chatConfig
        createAppDTO.setChatConfig(new AppChatConfigType());

        // 执行创建请求
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/app/create")
                        .content(JsonUtil.toJsonString(createAppDTO))
        );

        // 验证结果并获取应用ID
        MvcResult mvcResult = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info("/app/create接口的返回结果: {}", contentAsString);

        // 从响应中提取应用ID
        TEST_APP_ID = objectMapper.readTree(contentAsString).path("data").asText();
        log.info("创建测试应用成功，应用ID: {}", TEST_APP_ID);
        
        // 获取应用详情，提取version字段
        getAppDetails();
    }

    /**
     * 获取应用详情
     */
    @SneakyThrows
    private void getAppDetails() {
        MockHttpServletRequestBuilder request = getMockRequestForAdmin("/mind/api/core/app/detail")
                .param("appId", TEST_APP_ID);

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/app/detail接口的返回结果: {}", contentAsString);

        // 从响应中提取version字段
        TEST_APP_VERSION = objectMapper.readTree(contentAsString).path("data").path("version").asText();
        log.info("获取应用详情成功，应用版本: {}", TEST_APP_VERSION);
    }

    /**
     * 删除测试应用
     */
    @SneakyThrows
    private void deleteTestApp() {
        if (TEST_APP_ID != null) {
            try {
                // 执行删除请求
                ResultActions perform = mockMvc.perform(
                        postMockRequestForAdmin("/mind/api/core/app/del")
                                .param("appId", TEST_APP_ID)
                );

                // 验证结果
                perform.andExpect(status().isOk());

                log.info("/app/del接口调用成功，删除应用ID: {}", TEST_APP_ID);
                
                // 重置变量
                TEST_APP_ID = null;
                TEST_APP_VERSION = null;
            } catch (Exception e) {
                log.warn("删除应用时出现异常，可能是应用不存在：{}", e.getMessage());
                
                // 重置变量
                TEST_APP_ID = null;
                TEST_APP_VERSION = null;
            }
        }
    }

    /**
     * 测试获取插件分组
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取插件分组")
    public void testGetPluginGroups() {
        MockHttpServletRequestBuilder request = getMockRequestForAdmin("/mind/api/core/app/plugin/getPluginGroups");

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data", hasSize(1)))
                .andExpect(jsonPath("$.data[0].groupId", is("systemPlugin")))
                .andExpect(jsonPath("$.data[0].groupName", is("common:core.module.template.System Plugin")))
                .andExpect(jsonPath("$.data[0].groupAvatar", is("core/app/type/pluginLight")))
                .andExpect(jsonPath("$.data[0].groupTypes", hasSize(12)))
                .andExpect(jsonPath("$.data[0].groupOrder", is(0)))
                .andReturn().getResponse().getContentAsString();

        log.info("/getPluginGroups接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试获取插件预览节点 - 成功情况
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取插件预览节点 - 成功情况")
    public void testGetPreviewNodeSuccess() {
        MockHttpServletRequestBuilder request = getMockRequestForAdmin("/mind/api/core/app/plugin/getPreviewNode")
                .param("appId", TEST_APP_ID);

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/getPreviewNode接口的返回结果: {}", contentAsString);
    }


    /**
     * 测试获取插件预览节点 - 缺少必要参数
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取插件预览节点 - 缺少必要参数")
    public void testGetPreviewNodeMissingRequiredParam() {
        MockHttpServletRequestBuilder request = getMockRequestForAdmin("/mind/api/core/app/plugin/getPreviewNode");

        ResultActions perform = mockMvc.perform(request);

        // 验证结果 - 应该返回400错误
        perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.success", equalTo(false)));
    }

    /**
     * 测试获取系统插件模板列表 - 成功情况
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取系统插件模板列表 - 成功情况")
    public void testGetSystemPluginTemplatesSuccess() {
        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/app/plugin/getSystemPluginTemplates")
                .param("searchKey", TEST_SEARCH_KEY)
                .param("parentId", TEST_PARENT_ID);

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/getSystemPluginTemplates接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试获取系统插件模板列表 - 无参数
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取系统插件模板列表 - 无参数")
    public void testGetSystemPluginTemplatesNoParams() {
        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/app/plugin/getSystemPluginTemplates");

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/getSystemPluginTemplates接口无参数的返回结果: {}", contentAsString);
    }

    /**
     * 测试获取插件版本列表 - 成功情况
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取插件版本列表 - 成功情况")
    public void testGetVersionListSuccess() {
        // 构造请求体
        GetVersionListRequest request = new GetVersionListRequest();
        request.setToolId(TEST_APP_ID);
        request.setOffset(0);
        request.setPageSize(10);

        MockHttpServletRequestBuilder mockRequest = postMockRequestForAdmin("/mind/api/core/app/plugin/getVersionList")
                .content(JsonUtil.toJsonString(request));

        ResultActions perform = mockMvc.perform(mockRequest);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/getVersionList接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试获取插件版本列表 - 使用默认分页参数
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取插件版本列表 - 使用默认分页参数")
    public void testGetVersionListDefaultParams() {
        // 构造请求体
        GetVersionListRequest request = new GetVersionListRequest();
        request.setToolId(TEST_APP_ID);
        // 不设置offset和pageSize，使用默认值

        MockHttpServletRequestBuilder mockRequest = postMockRequestForAdmin("/mind/api/core/app/plugin/getVersionList")
                .content(JsonUtil.toJsonString(request));

        ResultActions perform = mockMvc.perform(mockRequest);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/getVersionList接口默认参数的返回结果: {}", contentAsString);
    }

    /**
     * 测试健康检查接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试健康检查接口")
    public void testHealth() {
        MockHttpServletRequestBuilder request = getMockRequestForAdmin("/mind/api/core/app/plugin/health");

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", is("Plugin service is healthy")))
                .andReturn().getResponse().getContentAsString();

        log.info("/health接口的返回结果: {}", contentAsString);
    }

    /**
     * 测试获取插件预览节点 - 使用真实的应用ID进行完整流程测试
     */
    @Test
    @SneakyThrows
    @DisplayName("测试获取插件预览节点 - 完整流程测试")
    public void testGetPreviewNodeFullFlow() {
        // 使用真实创建的应用ID进行测试
        MockHttpServletRequestBuilder request = getMockRequestForAdmin("/mind/api/core/app/plugin/getPreviewNode")
                .param("appId", TEST_APP_ID);

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/getPreviewNode接口完整流程测试的返回结果: {}", contentAsString);
    }

}