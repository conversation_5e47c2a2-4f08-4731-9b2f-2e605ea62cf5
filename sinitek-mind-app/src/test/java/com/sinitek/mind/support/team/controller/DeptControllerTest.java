package com.sinitek.mind.support.team.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.team.dto.DeptDTO;
import com.sinitek.mind.support.team.dto.DeptListRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * DeptController-单元测试
 *
 * <AUTHOR>
 * date 2025/7/7
 */
public class DeptControllerTest extends MindBaseTestApplication {

    public static final String ROOT_ORG_ID = "99999";
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("测试获取组织列表")
    public void getOrgList() throws Exception {
        DeptListRequest request = DeptListRequest.builder()
                .orgId(ROOT_ORG_ID)
                .withPermission(true)
                .searchKey("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/org/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<List<DeptDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<List<DeptDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取组织列表失败");
        assertNotNull(apiResponse.getData(), "组织列表数据为空");
    }

    @Test
    @DisplayName("测试获取组织列表-不带权限信息")
    public void getOrgListWithoutPermission() throws Exception {
        DeptListRequest request = DeptListRequest.builder()
                .orgId(ROOT_ORG_ID)
                .withPermission(false)
                .searchKey("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/org/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<List<DeptDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<List<DeptDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取组织列表失败");
        assertNotNull(apiResponse.getData(), "组织列表数据为空");
    }

    @Test
    @DisplayName("测试获取组织列表-带搜索关键词")
    public void getOrgListWithSearchKey() throws Exception {
        DeptListRequest request = DeptListRequest.builder()
                .orgId(ROOT_ORG_ID)
                .withPermission(true)
                .searchKey("test")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/org/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<List<DeptDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<List<DeptDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取组织列表失败");
        assertNotNull(apiResponse.getData(), "组织列表数据为空");
    }

    @Test
    @DisplayName("测试获取组织列表-不带父组织ID")
    public void getOrgListWithoutOrgId() throws Exception {
        DeptListRequest request = DeptListRequest.builder()
                .withPermission(true)
                .searchKey("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/org/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<List<DeptDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<List<DeptDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取组织列表失败");
        assertNotNull(apiResponse.getData(), "组织列表数据为空");
    }

    @Test
    @DisplayName("测试获取组织列表-空请求")
    public void getOrgListWithEmptyRequest() throws Exception {
        DeptListRequest request = new DeptListRequest();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/org/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<List<DeptDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<List<DeptDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取组织列表失败");
        assertNotNull(apiResponse.getData(), "组织列表数据为空");
    }
}