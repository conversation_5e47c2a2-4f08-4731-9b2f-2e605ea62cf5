package com.sinitek.mind.support.team.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.support.team.dto.TeamMemberDTO;
import com.sinitek.mind.support.team.dto.TeamMemberListRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * TeamMemberController-单元测试
 *
 * <AUTHOR>
 * date 2025/7/7
 */
public class TeamMemberControllerTest extends MindBaseTestApplication {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("测试获取团队成员列表")
    public void getTeamMemberList() throws Exception {
        TeamMemberListRequest request = TeamMemberListRequest.builder()
                .status("active")
                .withOrgs(true)
                .withPermission(true)
                .searchKey("")
                .orgId("")
                .groupId("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/member/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<PageResult<TeamMemberDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<PageResult<TeamMemberDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取团队成员列表失败");
        assertNotNull(apiResponse.getData(), "团队成员列表数据为空");
    }

    @Test
    @DisplayName("测试获取团队成员列表-不带组织信息")
    public void getTeamMemberListWithoutOrgs() throws Exception {
        TeamMemberListRequest request = TeamMemberListRequest.builder()
                .status("active")
                .withOrgs(false)
                .withPermission(true)
                .searchKey("")
                .orgId("")
                .groupId("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/member/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<PageResult<TeamMemberDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<PageResult<TeamMemberDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取团队成员列表失败");
        assertNotNull(apiResponse.getData(), "团队成员列表数据为空");
    }

    @Test
    @DisplayName("测试获取团队成员列表-不带权限信息")
    public void getTeamMemberListWithoutPermission() throws Exception {
        TeamMemberListRequest request = TeamMemberListRequest.builder()
                .status("active")
                .withOrgs(true)
                .withPermission(false)
                .searchKey("")
                .orgId("")
                .groupId("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/member/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<PageResult<TeamMemberDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<PageResult<TeamMemberDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取团队成员列表失败");
        assertNotNull(apiResponse.getData(), "团队成员列表数据为空");
    }

    @Test
    @DisplayName("测试获取团队成员列表-带搜索关键词")
    public void getTeamMemberListWithSearchKey() throws Exception {
        TeamMemberListRequest request = TeamMemberListRequest.builder()
                .status("active")
                .withOrgs(true)
                .withPermission(true)
                .searchKey("test")
                .orgId("")
                .groupId("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/member/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<PageResult<TeamMemberDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<PageResult<TeamMemberDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取团队成员列表失败");
        assertNotNull(apiResponse.getData(), "团队成员列表数据为空");
    }

    @Test
    @DisplayName("测试获取团队成员列表-按组织筛选")
    public void getTeamMemberListWithOrgFilter() throws Exception {
        TeamMemberListRequest request = TeamMemberListRequest.builder()
                .status("active")
                .withOrgs(true)
                .withPermission(true)
                .searchKey("")
                .orgId("org123")
                .groupId("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/member/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<PageResult<TeamMemberDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<PageResult<TeamMemberDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取团队成员列表失败");
        assertNotNull(apiResponse.getData(), "团队成员列表数据为空");
    }

    @Test
    @DisplayName("测试获取团队成员列表-按群组筛选")
    public void getTeamMemberListWithGroupFilter() throws Exception {
        TeamMemberListRequest request = TeamMemberListRequest.builder()
                .status("active")
                .withOrgs(true)
                .withPermission(true)
                .searchKey("")
                .orgId("")
                .groupId("group123")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/member/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<PageResult<TeamMemberDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<PageResult<TeamMemberDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取团队成员列表失败");
        assertNotNull(apiResponse.getData(), "团队成员列表数据为空");
    }

    @Test
    @DisplayName("测试获取团队成员列表-非活跃状态")
    public void getTeamMemberListWithInactiveStatus() throws Exception {
        TeamMemberListRequest request = TeamMemberListRequest.builder()
                .status("inactive")
                .withOrgs(true)
                .withPermission(true)
                .searchKey("")
                .orgId("")
                .groupId("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/member/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<PageResult<TeamMemberDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<PageResult<TeamMemberDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取团队成员列表失败");
        assertNotNull(apiResponse.getData(), "团队成员列表数据为空");
    }

    @Test
    @DisplayName("测试获取团队成员列表-空请求")
    public void getTeamMemberListWithEmptyRequest() throws Exception {
        TeamMemberListRequest request = new TeamMemberListRequest();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/member/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<PageResult<TeamMemberDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<PageResult<TeamMemberDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取团队成员列表失败");
        assertNotNull(apiResponse.getData(), "团队成员列表数据为空");
    }

    @Test
    @DisplayName("测试获取团队成员列表-带分页参数")
    public void getTeamMemberListWithPagination() throws Exception {
        TeamMemberListRequest request = TeamMemberListRequest.builder()
                .status("active")
                .withOrgs(true)
                .withPermission(true)
                .searchKey("")
                .orgId("")
                .groupId("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/member/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<PageResult<TeamMemberDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<PageResult<TeamMemberDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取团队成员列表失败");
        assertNotNull(apiResponse.getData(), "团队成员列表数据为空");
    }
}