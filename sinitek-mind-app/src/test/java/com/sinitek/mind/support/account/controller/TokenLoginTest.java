package com.sinitek.mind.support.account.controller;

import com.sinitek.mind.MindBaseTestApplication;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * TokenLogin 控制器测试类
 *
 * <AUTHOR>
 * date 2025/08/18
 */
public class TokenLoginTest extends MindBaseTestApplication {

    @Test
    @DisplayName("测试token登录获取用户信息")
    public void testTokenLogin() throws Exception {
        String url = "/mind/api/support/user/account/tokenLogin";
        
        MvcResult mvcResult = mockMvc.perform(getMockRequestForAdmin(url))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.id").exists())
                .andExpect(jsonPath("$.data.username").exists())
                .andExpect(jsonPath("$.data.timezone").value("Asia/Shanghai"))
                .andExpect(jsonPath("$.data.team").exists())
                .andExpect(jsonPath("$.data.permission").exists())
                .andReturn();
        
        String content = mvcResult.getResponse().getContentAsString();
        System.out.println("接口返回值: " + content);
    }

    @Test
    @DisplayName("测试未登录直接访问tokenLogin")
    public void testTokenLoginWithoutLogin() throws Exception {
        String url = "/mind/api/support/user/account/tokenLogin";
        
        // 不带token直接访问
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get(url))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.data").doesNotExist())
                .andReturn();
        
        String content = mvcResult.getResponse().getContentAsString();
        System.out.println("未登录访问接口返回值: " + content);
    }
}