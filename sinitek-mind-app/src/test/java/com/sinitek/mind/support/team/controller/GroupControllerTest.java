package com.sinitek.mind.support.team.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.team.dto.GroupDTO;
import com.sinitek.mind.support.team.dto.GroupListRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * GroupController-单元测试
 *
 * <AUTHOR>
 * date 2025/7/7
 */
public class GroupControllerTest extends MindBaseTestApplication {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("测试获取群组列表")
    public void getGroupList() throws Exception {
        GroupListRequest request = GroupListRequest.builder()
                .withMembers(true)
                .searchKey("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/group/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<List<GroupDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<List<GroupDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取群组列表失败");
        assertNotNull(apiResponse.getData(), "群组列表数据为空");
    }

    @Test
    @DisplayName("测试获取群组列表-不带成员信息")
    public void getGroupListWithoutMembers() throws Exception {
        GroupListRequest request = GroupListRequest.builder()
                .withMembers(false)
                .searchKey("")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/group/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<List<GroupDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<List<GroupDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取群组列表失败");
        assertNotNull(apiResponse.getData(), "群组列表数据为空");
    }

    @Test
    @DisplayName("测试获取群组列表-带搜索关键词")
    public void getGroupListWithSearchKey() throws Exception {
        GroupListRequest request = GroupListRequest.builder()
                .withMembers(true)
                .searchKey("test")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/group/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<List<GroupDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<List<GroupDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取群组列表失败");
        assertNotNull(apiResponse.getData(), "群组列表数据为空");
    }

    @Test
    @DisplayName("测试获取群组列表-空请求")
    public void getGroupListWithEmptyRequest() throws Exception {
        GroupListRequest request = new GroupListRequest();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/group/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<List<GroupDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<List<GroupDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取群组列表失败");
        assertNotNull(apiResponse.getData(), "群组列表数据为空");
    }

    @Test
    @DisplayName("测试获取群组列表-仅带搜索关键词")
    public void getGroupListOnlyWithSearchKey() throws Exception {
        GroupListRequest request = GroupListRequest.builder()
                .searchKey("群组")
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/group/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<List<GroupDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<List<GroupDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取群组列表失败");
        assertNotNull(apiResponse.getData(), "群组列表数据为空");
    }

    @Test
    @DisplayName("测试获取群组列表-仅带成员标识")
    public void getGroupListOnlyWithMembers() throws Exception {
        GroupListRequest request = GroupListRequest.builder()
                .withMembers(true)
                .build();

        MockHttpServletRequestBuilder httpRequest = postMockRequestForAdmin("/mind/api/support/user/team/group/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request));

        MvcResult result = mockMvc.perform(httpRequest)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<List<GroupDTO>> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<List<GroupDTO>>>() {});
        assertTrue(apiResponse.isSuccess(), "获取群组列表失败");
        assertNotNull(apiResponse.getData(), "群组列表数据为空");
    }
}