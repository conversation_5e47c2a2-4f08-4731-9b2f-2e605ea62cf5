package com.sinitek.mind.support.openapi.controller;

import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.common.dto.IdDTO;
import com.sinitek.mind.support.openapi.dto.CreateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.dto.UpdateApiKeyRequestDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.ResultActions;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * OpenApiController - 单元测试
 *
 * <AUTHOR>
 * date 2025-08-08
 */
@Slf4j
public class OpenApiControllerTest extends MindBaseTestApplication {

    private static final String TEST_API_KEY_ID = "test-api-key-id";
    private static final String TEST_API_KEY_NAME = "测试API密钥";
    private static final Long TEST_APP_ID = 1231321123132131L;
    
    private List<String> createdApiKeyIds = new ArrayList<>();

    /**
     * 每个单元测试执行前自动初始化测试数据
     */
    @BeforeEach
    public void initTestData() {
        createdApiKeyIds.clear();
        clearInitData();
        createInitData();
    }

    /**
     * 每个单元测试执行后清理测试数据
     */
    @AfterEach
    public void cleanTestData() {
        cleanAllCreatedApiKeys();
    }

    @Test
    @SneakyThrows
    @DisplayName("测试创建API密钥接口")
    public void testCreateApiKey() {
        // 创建请求DTO
        CreateApiKeyRequestDTO requestDTO = createTestCreateApiKeyRequest();
        
        // 执行创建操作
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/openapi/create")
                        .content(JsonUtil.toJsonString(requestDTO))
        );

        // 验证创建结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data", isA(String.class)))
                .andReturn().getResponse().getContentAsString();

        log.info("/openapi/create接口的返回结果: {}", contentAsString);
        
        // 记录创建的API密钥ID，用于后续清理
        createdApiKeyIds.addAll(getApiKeyIdByName(TEST_API_KEY_NAME));

    }

    @Test
    @SneakyThrows
    @DisplayName("测试获取API密钥列表接口")
    public void testListApiKeys() {
        // 先创建一个API密钥
        String apiKeyId = createTestApiKey();
        
        // 执行列表查询测试
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/support/openapi/list")
                        .param("appId", String.valueOf(TEST_APP_ID))
        );

        // 验证查询结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data", isA(List.class)))
                .andReturn().getResponse().getContentAsString();

        log.info("/openapi/list接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试更新API密钥接口")
    public void testUpdateApiKey() {
        // 先创建一个API密钥
        String apiKeyId = createTestApiKey();
        
        // 创建更新请求DTO
        UpdateApiKeyRequestDTO updateRequestDTO = createTestUpdateApiKeyRequest(apiKeyId);
        
        // 执行更新操作
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/openapi/update")
                        .content(JsonUtil.toJsonString(updateRequestDTO))
        );

        // 验证更新结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", is("更新成功")))
                .andReturn().getResponse().getContentAsString();

        log.info("/openapi/update接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试删除API密钥接口")
    public void testDeleteApiKey() {
        // 先创建一个API密钥
        String apiKeyId = createTestApiKey();
        
        // 创建删除请求DTO
        IdDTO deleteRequest = new IdDTO();
        deleteRequest.setId(apiKeyId);
        
        // 执行删除操作
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/openapi/delete")
                        .content(JsonUtil.toJsonString(deleteRequest))
        );

        // 验证删除结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", is("删除成功")))
                .andReturn().getResponse().getContentAsString();

        log.info("/openapi/delete接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试API密钥完整CRUD流程")
    public void testApiKeyCrud() {
        // 创建API密钥
        String apiKeyId = createTestApiKey();
        
        // 更新API密钥
        updateApiKey(apiKeyId);
        
        // 查询API密钥列表
        listApiKeys();
        
        // 删除API密钥
        deleteApiKey(apiKeyId);
        
        // 从清理列表中移除，因为已经手动删除了
        createdApiKeyIds.remove(apiKeyId);
    }

    @SneakyThrows
    private String createTestApiKey() {
        CreateApiKeyRequestDTO requestDTO = createTestCreateApiKeyRequest();
        
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/openapi/create")
                        .content(JsonUtil.toJsonString(requestDTO))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn().getResponse().getContentAsString();

        log.info("创建API密钥接口的返回结果: {}", contentAsString);
        
        // 解析返回的API密钥
        java.util.Map<String, Object> responseMap = JsonUtil.toJavaObject(contentAsString, java.util.Map.class);
        String apiKey = (String) responseMap.get("data");
        
        // 通过列表查询获取对应的ID
        String apiKeyId = getApiKeyIdByApiKey(apiKey);
        
        // 记录创建的API密钥ID，用于后续清理
        if (apiKeyId != null) {
            createdApiKeyIds.add(apiKeyId);
        }
        
        return apiKeyId;
    }

    @SneakyThrows
    private String getApiKeyIdByApiKey(String apiKey) {
        // 等待一小段时间确保数据同步到数据库
        Thread.sleep(100);
        
        // 查询API密钥列表
        ResultActions listPerform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/support/openapi/list")
                        .param("appId", String.valueOf(TEST_APP_ID))
        );

        String listContent = listPerform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn().getResponse().getContentAsString();

        log.info("查询API密钥列表接口的返回结果: {}", listContent);
        
        // 解析列表结果，找到对应的API密钥ID
        java.util.Map<String, Object> listResponseMap = JsonUtil.toJavaObject(listContent, java.util.Map.class);
        java.util.List<java.util.Map<String, Object>> apiKeys = (java.util.List<java.util.Map<String, Object>>) listResponseMap.get("data");
        
        // 通过名称匹配找到我们创建的API密钥
        if (apiKeys != null && !apiKeys.isEmpty()) {
            for (java.util.Map<String, Object> apiKeyInfo : apiKeys) {
                String name = (String) apiKeyInfo.get("name");
                if (TEST_API_KEY_NAME.equals(name)) {
                    String id = (String) apiKeyInfo.get("id");
                    log.info("通过名称匹配获取到API密钥ID: {}", id);
                    return id;
                }
            }
        }
        
        throw new RuntimeException("无法找到API密钥对应的ID，名称: " + TEST_API_KEY_NAME);
    }

    @SneakyThrows
    private List<String> getApiKeyIdByName(String apiKeyName) {
        try {
            // 等待一小段时间确保数据同步到数据库
            Thread.sleep(100);

            // 查询API密钥列表
            ResultActions listPerform = mockMvc.perform(
                    getMockRequestForAdmin("/mind/api/support/openapi/list")
                            .param("appId", String.valueOf(TEST_APP_ID))
            );

            String listContent = listPerform.andExpect(status().isOk())
                    .andExpect(jsonPathSuccessIsTrue())
                    .andReturn().getResponse().getContentAsString();

            // 解析列表结果，找到对应的API密钥ID
            java.util.Map<String, Object> listResponseMap = JsonUtil.toJavaObject(listContent, java.util.Map.class);
            java.util.List<java.util.Map<String, Object>> apiKeys = (java.util.List<java.util.Map<String, Object>>) listResponseMap.get("data");

            // 通过名称匹配找到我们创建的API密钥
            List<String> list = new LinkedList<>();
            if (apiKeys != null && !apiKeys.isEmpty()) {
                for (java.util.Map<String, Object> apiKeyInfo : apiKeys) {
                    String name = (String) apiKeyInfo.get("name");
                    if (apiKeyName.equals(name)) {
                        String id = (String) apiKeyInfo.get("id");
                        log.info("通过名称匹配获取到API密钥ID: {}", id);
                        list.add(id);
                    }
                }
            }

            return list;
        } catch (Exception e) {
            log.warn("获取API密钥ID时发生异常: {}", e.getMessage());
            return List.of();
        }
    }

    @SneakyThrows
    private void updateApiKey(String apiKeyId) {
        UpdateApiKeyRequestDTO updateRequestDTO = createTestUpdateApiKeyRequest(apiKeyId);
        
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/openapi/update")
                        .content(JsonUtil.toJsonString(updateRequestDTO))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn().getResponse().getContentAsString();

        log.info("更新API密钥接口的返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void listApiKeys() {
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/support/openapi/list")
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn().getResponse().getContentAsString();

        log.info("查询API密钥列表接口的返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void deleteApiKey(String apiKeyId) {
        // 创建删除请求DTO
        IdDTO deleteRequest = new IdDTO();
        deleteRequest.setId(apiKeyId);
        
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/openapi/delete")
                        .content(JsonUtil.toJsonString(deleteRequest))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn().getResponse().getContentAsString();

        log.info("删除API密钥接口的返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void clearInitData() {
        try {
            // 尝试删除测试数据（如果存在）
            IdDTO deleteRequest = new IdDTO();
            deleteRequest.setId(TEST_API_KEY_ID);
            
            mockMvc.perform(
                    postMockRequestForAdmin("/mind/api/support/openapi/delete")
                            .content(JsonUtil.toJsonString(deleteRequest))
            );
        } catch (Exception e) {
            log.info("清理测试数据时发生异常，可能数据不存在: {}", e.getMessage());
        }
    }

    @SneakyThrows
    private void cleanAllCreatedApiKeys() {
        if (createdApiKeyIds.isEmpty()) {
            return;
        }

        List<String> distinctList = createdApiKeyIds.stream().distinct().toList();
        log.info("开始清理测试创建的API密钥，数量: {}", distinctList.size());
        
        for (String apiKeyId : distinctList) {
            try {
                IdDTO deleteRequest = new IdDTO();
                deleteRequest.setId(apiKeyId);
                
                mockMvc.perform(
                        postMockRequestForAdmin("/mind/api/support/openapi/delete")
                                .content(JsonUtil.toJsonString(deleteRequest))
                ).andExpect(status().isOk());
                
                log.info("成功删除API密钥，ID: {}", apiKeyId);
            } catch (Exception e) {
                log.warn("删除API密钥时发生异常，ID: {}，异常: {}", apiKeyId, e.getMessage());
            }
        }
        
        createdApiKeyIds.clear();
        log.info("完成测试创建的API密钥清理");
    }

    @SneakyThrows
    private void createInitData() {
        // 创建测试用的API密钥
        CreateApiKeyRequestDTO requestDTO = createTestCreateApiKeyRequest();
        mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/openapi/create")
                        .content(JsonUtil.toJsonString(requestDTO))
        ).andExpect(status().isOk());
    }

    /**
     * 创建测试用的创建API密钥请求DTO
     */
    private CreateApiKeyRequestDTO createTestCreateApiKeyRequest() {
        CreateApiKeyRequestDTO requestDTO = new CreateApiKeyRequestDTO();
        requestDTO.setAppId(TEST_APP_ID);
        requestDTO.setName(TEST_API_KEY_NAME);
        
        CreateApiKeyRequestDTO.LimitDTO limitDTO = new CreateApiKeyRequestDTO.LimitDTO();
        limitDTO.setExpiredTime(new Date(System.currentTimeMillis() + 86400000L)); // 24小时后过期
        limitDTO.setMaxUsagePoints(1000L);
        
        requestDTO.setLimit(limitDTO);
        
        return requestDTO;
    }

    /**
     * 创建测试用的更新API密钥请求DTO
     */
    private UpdateApiKeyRequestDTO createTestUpdateApiKeyRequest(String apiKeyId) {
        UpdateApiKeyRequestDTO requestDTO = new UpdateApiKeyRequestDTO();
        requestDTO.setId(apiKeyId);
        requestDTO.setName(TEST_API_KEY_NAME + "_updated");
        
        UpdateApiKeyRequestDTO.LimitDTO limitDTO = new UpdateApiKeyRequestDTO.LimitDTO();
        limitDTO.setExpiredTime(new Date(System.currentTimeMillis() + 172800000L)); // 48小时后过期
        limitDTO.setMaxUsagePoints(2000L);
        
        requestDTO.setLimit(limitDTO);
        
        return requestDTO;
    }
}