package com.sinitek.mind.support.team.controller;

import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.CollaboratorDeleteDTO;
import com.sinitek.mind.support.permission.dto.CollaboratorUpdateDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * TeamCollaboratorController - 单元测试
 *
 * <AUTHOR>
 */
@Slf4j
public class TeamCollaboratorControllerTest extends MindBaseTestApplication {

    private static final String TEST_ORG_ID = "13";
    private static final String TEST_MEMBER_ID = "999000001";
    private static final String TEST_GROUP_ID = "14";

    /**
     * 每个单元测试执行前自动初始化测试数据
     */
    @BeforeEach
    public void initTestData() {
        clearInitData();
    }

    /**
     * 每个单元测试执行后清理测试数据
     */
    @AfterEach
    public void cleanupTestData() {
        clearInitData();
    }

    /**
     * 清理初始化数据
     */
    @SneakyThrows
    private void clearInitData() {
        // 删除可能存在的测试数据
        CollaboratorDeleteDTO deleteDTO = new CollaboratorDeleteDTO();
        deleteDTO.setOrgId(TEST_ORG_ID);

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/support/user/team/collaborator/delete")
                .content(JsonUtil.toJsonString(deleteDTO));

        try {
            mockMvc.perform(request);
        } catch (Exception e) {
            // 忽略错误，因为可能不存在要删除的数据
            log.info("清理初始化数据时出现异常，可能是数据不存在：{}", e.getMessage());
        }
    }

    /**
     * 测试团队协作者CRUD接口
     */
    @Test
    @SneakyThrows
    @DisplayName("测试团队协作者CRUD接口")
    public void testTeamCollaboratorCrud() {
        // 获取协作者列表
        getCollaboratorList();

        // 更新协作者
        updateCollaborator();

        // 删除协作者
        deleteCollaborator();
    }

    /**
     * 获取团队协作者列表
     */
    @SneakyThrows
    private void getCollaboratorList() {
        MockHttpServletRequestBuilder request = getMockRequestForAdmin("/mind/api/support/user/team/collaborator/list");

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/team/collaborator/list接口的返回结果: {}", contentAsString);
    }

    /**
     * 更新团队协作者
     */
    @SneakyThrows
    private void updateCollaborator() {
        // 构造更新协作者请求
        CollaboratorUpdateDTO updateDTO = new CollaboratorUpdateDTO();
        updateDTO.setPermission(PermissionConstant.WRITE_PER);
        
        List<String> orgs = new ArrayList<>();
        orgs.add(TEST_ORG_ID);
        updateDTO.setOrgs(orgs);
        
        List<String> members = new ArrayList<>();
        members.add(TEST_MEMBER_ID);
        updateDTO.setMembers(members);
        
        List<String> groups = new ArrayList<>();
        groups.add(TEST_GROUP_ID);
        updateDTO.setGroups(groups);

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/support/user/team/collaborator/update")
                .content(JsonUtil.toJsonString(updateDTO));

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/team/collaborator/update接口的返回结果: {}", contentAsString);

        // 验证更新后的列表
        getCollaboratorList();
    }

    /**
     * 删除团队协作者
     */
    @SneakyThrows
    private void deleteCollaborator() {
        // 构造删除协作者请求 - 测试删除组织
        CollaboratorDeleteDTO deleteDTO = new CollaboratorDeleteDTO();
        deleteDTO.setOrgId(TEST_ORG_ID);

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/support/user/team/collaborator/delete")
                .content(JsonUtil.toJsonString(deleteDTO));

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/team/collaborator/delete接口的返回结果: {}", contentAsString);

        // 验证删除后的列表
        getCollaboratorList();
    }

    /**
     * 测试删除团队成员协作者
     */
    @Test
    @SneakyThrows
    @DisplayName("测试删除团队成员协作者")
    public void testDeleteTeamMemberCollaborator() {
        // 先添加一个团队成员协作者
        CollaboratorUpdateDTO updateDTO = new CollaboratorUpdateDTO();
        updateDTO.setPermission(PermissionConstant.READ_PER);
        
        List<String> members = new ArrayList<>();
        members.add(TEST_MEMBER_ID);
        updateDTO.setMembers(members);

        MockHttpServletRequestBuilder updateRequest = postMockRequestForAdmin("/mind/api/support/user/team/collaborator/update")
                .content(JsonUtil.toJsonString(updateDTO));

        mockMvc.perform(updateRequest)
                .andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200());

        // 然后删除该团队成员协作者
        CollaboratorDeleteDTO deleteDTO = new CollaboratorDeleteDTO();
        deleteDTO.setTmbId(TEST_MEMBER_ID);

        MockHttpServletRequestBuilder deleteRequest = postMockRequestForAdmin("/mind/api/support/user/team/collaborator/delete")
                .content(JsonUtil.toJsonString(deleteDTO));

        ResultActions perform = mockMvc.perform(deleteRequest);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/team/collaborator/delete接口删除团队成员的返回结果: {}", contentAsString);
    }

    /**
     * 测试删除群组协作者
     */
    @Test
    @SneakyThrows
    @DisplayName("测试删除群组协作者")
    public void testDeleteTeamGroupCollaborator() {
        // 先添加一个群组协作者
        CollaboratorUpdateDTO updateDTO = new CollaboratorUpdateDTO();
        updateDTO.setPermission(PermissionConstant.MANAGER_PER);
        
        List<String> groups = new ArrayList<>();
        groups.add(TEST_GROUP_ID);
        updateDTO.setGroups(groups);

        MockHttpServletRequestBuilder updateRequest = postMockRequestForAdmin("/mind/api/support/user/team/collaborator/update")
                .content(JsonUtil.toJsonString(updateDTO));

        mockMvc.perform(updateRequest)
                .andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200());

        // 然后删除该群组协作者
        CollaboratorDeleteDTO deleteDTO = new CollaboratorDeleteDTO();
        deleteDTO.setGroupId(TEST_GROUP_ID);

        MockHttpServletRequestBuilder deleteRequest = postMockRequestForAdmin("/mind/api/support/user/team/collaborator/delete")
                .content(JsonUtil.toJsonString(deleteDTO));

        ResultActions perform = mockMvc.perform(deleteRequest);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/team/collaborator/delete接口删除群组的返回结果: {}", contentAsString);
    }

    /**
     * 测试更新协作者权限为只读
     */
    @Test
    @SneakyThrows
    @DisplayName("测试更新协作者权限为只读")
    public void testUpdateCollaboratorWithReadPermission() {
        CollaboratorUpdateDTO updateDTO = new CollaboratorUpdateDTO();
        updateDTO.setPermission(PermissionConstant.READ_PER);
        
        List<String> orgs = new ArrayList<>();
        orgs.add(TEST_ORG_ID);
        updateDTO.setOrgs(orgs);

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/support/user/team/collaborator/update")
                .content(JsonUtil.toJsonString(updateDTO));

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/team/collaborator/update接口设置只读权限的返回结果: {}", contentAsString);
    }

    /**
     * 测试更新协作者权限为管理
     */
    @Test
    @SneakyThrows
    @DisplayName("测试更新协作者权限为管理")
    public void testUpdateCollaboratorWithManagePermission() {
        CollaboratorUpdateDTO updateDTO = new CollaboratorUpdateDTO();
        updateDTO.setPermission(PermissionConstant.MANAGER_PER);
        
        List<String> members = new ArrayList<>();
        members.add(TEST_MEMBER_ID);
        updateDTO.setMembers(members);

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/support/user/team/collaborator/update")
                .content(JsonUtil.toJsonString(updateDTO));

        ResultActions perform = mockMvc.perform(request);

        // 验证结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/team/collaborator/update接口设置管理权限的返回结果: {}", contentAsString);
    }

}