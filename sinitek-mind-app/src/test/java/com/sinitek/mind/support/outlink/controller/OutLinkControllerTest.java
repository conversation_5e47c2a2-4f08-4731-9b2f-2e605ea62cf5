package com.sinitek.mind.support.outlink.controller;

import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.core.app.model.Limit;
import com.sinitek.mind.support.outlink.dto.CreateOutLinkRequest;
import com.sinitek.mind.support.outlink.dto.UpdateOutLinkRequest;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.ResultActions;

import java.util.Date;
import java.util.List;

import static org.hamcrest.Matchers.isA;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * OutLinkController - 单元测试
 *
 * <AUTHOR>
 * date 2025-08-11
 */
@Slf4j
public class OutLinkControllerTest extends MindBaseTestApplication {

    private static final String TEST_APP_ID = "68945982e06509362dce9d46";
    private static final String TEST_TYPE = "share";
    private static final String TEST_NAME = "测试";

    /**
     * 每个单元测试执行前自动初始化测试数据
     */
    @BeforeEach
    public void initTestData() {
        clearInitData();
        createInitData();
    }

    @Test
    @SneakyThrows
    @DisplayName("测试创建免登录链接接口")
    public void testCreateOutLink() {
        // 创建请求DTO
        CreateOutLinkRequest requestDTO = createTestCreateOutLinkRequest();
        
        // 执行创建操作
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/outLink/create")
                        .content(JsonUtil.toJsonString(requestDTO))
        );

        // 验证创建结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data", isA(String.class)))
                .andReturn().getResponse().getContentAsString();

        log.info("/outLink/create接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试获取免登录链接列表接口")
    public void testGetOutLinkList() {
        // 先创建一个免登录链接
        String shareId = createTestOutLink();
        
        // 执行列表查询测试
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/support/outLink/list")
                        .param("appId", TEST_APP_ID)
                        .param("type", TEST_TYPE)
        );

        // 验证查询结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data", isA(List.class)))
                .andReturn().getResponse().getContentAsString();

        log.info("/outLink/list接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试更新免登录链接接口")
    public void testUpdateOutLink() {
        // 先创建一个免登录链接
        String shareId = createTestOutLink();
        String outLinkId = getOutLinkIdByShareId(shareId);
        
        // 创建更新请求DTO
        UpdateOutLinkRequest updateRequestDTO = createTestUpdateOutLinkRequest(outLinkId);
        
        // 执行更新操作
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/outLink/update")
                        .content(JsonUtil.toJsonString(updateRequestDTO))
        );

        // 验证更新结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/outLink/update接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试删除免登录链接接口")
    public void testDeleteOutLink() {
        // 先创建一个免登录链接
        String shareId = createTestOutLink();
        String outLinkId = getOutLinkIdByShareId(shareId);
        
        // 执行删除操作
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/outLink/delete")
                        .param("id", outLinkId)
        );

        // 验证删除结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("/outLink/delete接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试根据shareId获取应用信息接口")
    public void testGetAppByShareId() {
        // 先创建一个免登录链接
        String shareId = createTestOutLink();
        
        // 执行获取应用信息测试
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/support/outLink/app")
                        .param("shareId", shareId)
        );

        // 验证查询结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/outLink/app接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试免登录链接完整CRUD流程")
    public void testOutLinkCrud() {
        // 创建免登录链接
        String shareId = createTestOutLink();
        
        // 查询免登录链接列表
        getOutLinkList();
        
        // 根据shareId获取应用信息
        getAppByShareId(shareId);
        
        // 获取链接ID并更新
        String outLinkId = getOutLinkIdByShareId(shareId);
        updateOutLink(outLinkId);
        
        // 删除免登录链接
        deleteOutLink(outLinkId);
    }

    @SneakyThrows
    private String createTestOutLink() {
        CreateOutLinkRequest requestDTO = createTestCreateOutLinkRequest();
        
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/outLink/create")
                        .content(JsonUtil.toJsonString(requestDTO))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("创建免登录链接接口的返回结果: {}", contentAsString);
        
        // 解析返回的shareId
        java.util.Map<String, Object> responseMap = JsonUtil.toJavaObject(contentAsString, java.util.Map.class);
        String shareId = (String) responseMap.get("data");
        
        return shareId;
    }

    @SneakyThrows
    private String getOutLinkIdByShareId(String shareId) {
        // 等待一小段时间确保数据同步到数据库
        Thread.sleep(100);
        
        // 查询免登录链接列表
        ResultActions listPerform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/support/outLink/list")
                        .param("appId", TEST_APP_ID)
                        .param("type", TEST_TYPE)
        );

        String listContent = listPerform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("查询免登录链接列表接口的返回结果: {}", listContent);
        
        // 解析列表结果，找到对应的链接ID
        java.util.Map<String, Object> listResponseMap = JsonUtil.toJavaObject(listContent, java.util.Map.class);
        java.util.List<java.util.Map<String, Object>> outLinks = (java.util.List<java.util.Map<String, Object>>) listResponseMap.get("data");
        
        // 通过名称匹配找到我们创建的免登录链接
        if (outLinks != null && !outLinks.isEmpty()) {
            for (java.util.Map<String, Object> outLink : outLinks) {
                String name = (String) outLink.get("name");
                if (TEST_NAME.equals(name)) {
                    String id = (String) outLink.get("_id");
                    log.info("通过名称匹配获取到免登录链接ID: {}", id);
                    return id;
                }
            }
        }
        
        throw new RuntimeException("无法找到免登录链接对应的ID，名称: " + TEST_NAME);
    }

    @SneakyThrows
    private void getOutLinkList() {
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/support/outLink/list")
                        .param("appId", TEST_APP_ID)
                        .param("type", TEST_TYPE)
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("查询免登录链接列表接口的返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void getAppByShareId(String shareId) {
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/support/outLink/app")
                        .param("shareId", shareId)
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("根据shareId获取应用信息接口的返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void updateOutLink(String outLinkId) {
        UpdateOutLinkRequest updateRequestDTO = createTestUpdateOutLinkRequest(outLinkId);
        
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/outLink/update")
                        .content(JsonUtil.toJsonString(updateRequestDTO))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("更新免登录链接接口的返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void deleteOutLink(String outLinkId) {
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/outLink/delete")
                        .param("id", outLinkId)
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathCodeIs200())
                .andReturn().getResponse().getContentAsString();

        log.info("删除免登录链接接口的返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void clearInitData() {
        try {
            // 查询现有的测试数据
            ResultActions listPerform = mockMvc.perform(
                    getMockRequestForAdmin("/mind/api/support/outLink/list")
                            .param("appId", TEST_APP_ID)
                            .param("type", TEST_TYPE)
            );

            String listContent = listPerform.andExpect(status().isOk())
                    .andExpect(jsonPathCodeIs200())
                    .andReturn().getResponse().getContentAsString();

            // 解析列表结果，找到测试数据并删除
            java.util.Map<String, Object> listResponseMap = JsonUtil.toJavaObject(listContent, java.util.Map.class);
            java.util.List<java.util.Map<String, Object>> outLinks = (java.util.List<java.util.Map<String, Object>>) listResponseMap.get("data");
            
            if (outLinks != null && !outLinks.isEmpty()) {
                for (java.util.Map<String, Object> outLink : outLinks) {
                    String name = (String) outLink.get("name");
                    if (TEST_NAME.equals(name)) {
                        String id = (String) outLink.get("_id");
                        log.info("清理测试数据，删除免登录链接ID: {}", id);
                        mockMvc.perform(
                                postMockRequestForAdmin("/mind/api/support/outLink/delete")
                                        .param("id", id)
                        );
                    }
                }
            }
        } catch (Exception e) {
            log.info("清理测试数据时发生异常，可能数据不存在: {}", e.getMessage());
        }
    }

    @SneakyThrows
    private void createInitData() {
        // 创建测试用的免登录链接
        CreateOutLinkRequest requestDTO = createTestCreateOutLinkRequest();
        mockMvc.perform(
                postMockRequestForAdmin("/mind/api/support/outLink/create")
                        .content(JsonUtil.toJsonString(requestDTO))
        ).andExpect(status().isOk());
    }

    /**
     * 创建测试用的创建免登录链接请求DTO
     */
    private CreateOutLinkRequest createTestCreateOutLinkRequest() {
        CreateOutLinkRequest requestDTO = new CreateOutLinkRequest();
        requestDTO.setAppId(TEST_APP_ID);
        requestDTO.setType(TEST_TYPE);
        requestDTO.setName(TEST_NAME);
        requestDTO.setResponseDetail(true);
        requestDTO.setShowNodeStatus(true);
        requestDTO.setShowRawSource(false);
        requestDTO.setImmediateResponse("立即响应内容");
        requestDTO.setDefaultResponse("默认响应内容");
        
        Limit limit = new Limit();
        limit.setExpiredTime(new Date(System.currentTimeMillis() + 86400000L)); // 24小时后过期
        limit.setMaxUsagePoints(1000);
        limit.setQPM(100);
        
        requestDTO.setLimit(limit);
        
        return requestDTO;
    }

    /**
     * 创建测试用的更新免登录链接请求DTO
     */
    private UpdateOutLinkRequest createTestUpdateOutLinkRequest(String outLinkId) {
        UpdateOutLinkRequest requestDTO = new UpdateOutLinkRequest();
        requestDTO.set_id(outLinkId);
        requestDTO.setName(TEST_NAME + "_updated");
        requestDTO.setResponseDetail(false);
        requestDTO.setShowNodeStatus(false);
        requestDTO.setShowRawSource(true);
        requestDTO.setImmediateResponse("更新后的立即响应内容");
        requestDTO.setDefaultResponse("更新后的默认响应内容");
        
        Limit limit = new Limit();
        limit.setExpiredTime(new Date(System.currentTimeMillis() + 172800000L)); // 48小时后过期
        limit.setMaxUsagePoints(2000);
        limit.setQPM(200);
        
        requestDTO.setLimit(limit);
        
        return requestDTO;
    }
}