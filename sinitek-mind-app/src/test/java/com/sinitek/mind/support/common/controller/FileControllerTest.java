package com.sinitek.mind.support.common.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.common.dto.FileTokenQuery;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.util.MindFileTokenUtil;
import com.sinitek.mind.support.common.dto.UploadFileResponse;
import com.sinitek.mind.support.common.dto.UploadImageRequest;
import com.sinitek.mind.support.common.service.IFileService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * FileController unit test
 *
 * <AUTHOR>
 * date 2025-08-18
 */
@AutoConfigureMockMvc
@DisplayName("文件控制器测试")
class FileControllerTest extends MindBaseTestApplication {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private IFileService fileService;

    @Autowired
    private MindFileTokenUtil mindFileTokenUtil;

    private static final String TEST_FILE_PATH = "src/test/java/com/sinitek/mind/support/common/controller/text.txt";
    private static final String DOWNLOAD_DIR = "src/test/java/com/sinitek/mind/support/common/controller/downloads/";
    // 只添加了dataset的桶
    private static final String TEST_BUCKET_NAME = "dataset";

    /**
     * 上传文件并返回文件ID
     */
    private String uploadFileAndGetId() throws Exception {
        // Prepare test file
        File testFile = new File(TEST_FILE_PATH);
        assertTrue(testFile.exists(), "Test file should exist: " + TEST_FILE_PATH);
        
        // Read file content
        byte[] fileContent = Files.readAllBytes(testFile.toPath());
        MockMultipartFile multipartFile = new MockMultipartFile(
                "file", 
                "text.txt", 
                MediaType.TEXT_PLAIN_VALUE, 
                fileContent
        );

        // Prepare metadata
        Map<String, String> metadata = new HashMap<>();
        metadata.put("testKey", "testValue");
        metadata.put("description", "Test file upload");
        String data = objectMapper.writeValueAsString(metadata);

        // Execute upload request
        MvcResult result = mockMvc.perform(multipart("/mind/api/common/file/upload")
                        .file(multipartFile)
                        .param("bucketName", TEST_BUCKET_NAME)
                        .param("data", data)
                        .header(ACCESS_TOKEN_NAME, adminToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.fileId").exists())
                .andExpect(jsonPath("$.data.previewUrl").exists())
                .andReturn();

        // Parse response
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("Upload file response: " + responseContent);
        ApiResponse<UploadFileResponse> response = objectMapper.readValue(
                responseContent, 
                new TypeReference<ApiResponse<UploadFileResponse>>() {}
        );
        
        UploadFileResponse uploadResponse = response.getData();
        assertNotNull(uploadResponse.getFileId());
        assertNotNull(uploadResponse.getPreviewUrl());
        System.out.println("File uploaded successfully, fileId: " + uploadResponse.getFileId());
        
        return uploadResponse.getFileId();
    }

    @Test
    @DisplayName("测试真实文件上传成功")
    void testUploadFileSuccess() throws Exception {
        String fileId = uploadFileAndGetId();
        assertNotNull(fileId);
        System.out.println("File upload test completed");
    }

    @Test
    @DisplayName("测试不带元数据的文件上传")
    void testUploadFileWithoutMetadata() throws Exception {
        // Prepare test file
        File testFile = new File(TEST_FILE_PATH);
        assertTrue(testFile.exists(), "Test file should exist: " + TEST_FILE_PATH);
        
        // Read file content
        byte[] fileContent = Files.readAllBytes(testFile.toPath());
        MockMultipartFile multipartFile = new MockMultipartFile(
                "file", 
                "text.txt", 
                MediaType.TEXT_PLAIN_VALUE, 
                fileContent
        );

        // Execute upload request (without data parameter)
        MvcResult result = mockMvc.perform(multipart("/mind/api/common/file/upload")
                        .file(multipartFile)
                        .param("bucketName", TEST_BUCKET_NAME)
                        .header(ACCESS_TOKEN_NAME, adminToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.fileId").exists())
                .andReturn();

        // Parse response
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("Upload file without metadata response: " + responseContent);
        ApiResponse<UploadFileResponse> response = objectMapper.readValue(
                responseContent, 
                new TypeReference<ApiResponse<UploadFileResponse>>() {}
        );
        
        UploadFileResponse uploadResponse = response.getData();
        assertNotNull(uploadResponse.getFileId());
        System.out.println("File uploaded successfully (without metadata), fileId: " + uploadResponse.getFileId());
    }

    @Test
    @DisplayName("测试完整的文件上传下载流程")
    void testCompleteFileUploadDownloadFlow() throws Exception {
        // 1. Upload file and get fileId
        String fileId = uploadFileAndGetId();
        assertNotNull(fileId);
        
        // 2. Generate file access token
        FileTokenQuery tokenQuery = new FileTokenQuery();
        tokenQuery.setBucketName(TEST_BUCKET_NAME);
        tokenQuery.setFileId(fileId);
        tokenQuery.setTeamId("test-team");
        tokenQuery.setUid("test-user");
        
        String token = mindFileTokenUtil.createFileToken(tokenQuery);
        assertNotNull(token);
        System.out.println("Generated file access token: " + token);
        
        // 3. Download file through readFile interface
        MvcResult result = mockMvc.perform(get("/mind/api/common/file/read")
                        .param("token", token)
                        .header(ACCESS_TOKEN_NAME, adminToken))
                .andExpect(status().isOk())
                .andReturn();
        
        // Output response content
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("Read file response: " + responseContent);
        
        // 4. Save downloaded file to local
        byte[] downloadedContent = result.getResponse().getContentAsByteArray();
        Path downloadPath = Paths.get(DOWNLOAD_DIR + "downloaded_text.txt");
        Files.createDirectories(downloadPath.getParent());
        Files.write(downloadPath, downloadedContent);
        
        // 5. Verify downloaded file content
        File originalFile = new File(TEST_FILE_PATH);
        byte[] originalContent = Files.readAllBytes(originalFile.toPath());
        
        assertArrayEquals(originalContent, downloadedContent, "Downloaded file content should match original file");
        System.out.println("File downloaded successfully, saved to: " + downloadPath);
        
        // 6. Verify file is actually created
        assertTrue(Files.exists(downloadPath), "Downloaded file should exist");
    }

    @Test
    @DisplayName("测试文件读取功能")
    void testReadFileFunctionality() throws Exception {
        // 1. Upload file
        String fileId = uploadFileAndGetId();
        
        // 2. Generate token
        FileTokenQuery tokenQuery = new FileTokenQuery();
        tokenQuery.setBucketName(TEST_BUCKET_NAME);
        tokenQuery.setFileId(fileId);
        tokenQuery.setTeamId("test-team");
        tokenQuery.setUid("test-user");
        
        String token = mindFileTokenUtil.createFileToken(tokenQuery);
        
        // 3. Read file
        MvcResult result = mockMvc.perform(get("/mind/api/common/file/read")
                        .param("token", token)
                        .header(ACCESS_TOKEN_NAME, adminToken))
                .andExpect(status().isOk())
                .andReturn();
        
        // Output response content
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("Read file functionality response: " + responseContent);
        
        System.out.println("File read functionality test completed");
    }

    @Test
    @DisplayName("测试使用无效token读取文件")
    void testReadFileWithInvalidToken() throws Exception {
        String invalidToken = "invalid-token-12345";
        
        MvcResult result = mockMvc.perform(get("/mind/api/common/file/read")
                        .param("token", invalidToken)
                        .header(ACCESS_TOKEN_NAME, adminToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andReturn();
        
        // Output response content
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("Read file with invalid token response: " + responseContent);
    }

    @Test
    @DisplayName("测试使用空token读取文件")
    void testReadFileWithEmptyToken() throws Exception {
        MvcResult result = mockMvc.perform(get("/mind/api/common/file/read")
                        .param("token", "")
                        .header(ACCESS_TOKEN_NAME, adminToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andReturn();
        
        // Output response content
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("Read file with empty token response: " + responseContent);
    }

    @Test
    @DisplayName("测试不带token参数读取文件")
    void testReadFileWithoutToken() throws Exception {
        MvcResult result = mockMvc.perform(get("/mind/api/common/file/read")
                        .header(ACCESS_TOKEN_NAME, adminToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andReturn();
        
        // Output response content
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("Read file without token response: " + responseContent);
    }

    @Test
    @DisplayName("测试图片上传功能")
    void testUploadImage() throws Exception {
        // Create a simple base64 image data (1x1 pixel red PNG)
        String base64ImageData = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==";
        
        UploadImageRequest request = new UploadImageRequest();
        request.setBase64Img(base64ImageData);

        // Execute upload request
        MvcResult result = mockMvc.perform(post("/mind/api/common/file/uploadImage")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .header(ACCESS_TOKEN_NAME, adminToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists())
                .andReturn();
        
        // Output response content
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("Upload image response: " + responseContent);
        
        System.out.println("Image upload test completed");
    }

    @Test
    @DisplayName("测试文件上传错误处理")
    void testUploadFileErrorHandling() throws Exception {
        // Test empty file upload
        MockMultipartFile emptyFile = new MockMultipartFile("file", "", MediaType.TEXT_PLAIN_VALUE, new byte[0]);
        
        MvcResult result = mockMvc.perform(multipart("/mind/api/common/file/upload")
                        .file(emptyFile)
                        .param("bucketName", TEST_BUCKET_NAME)
                        .header(ACCESS_TOKEN_NAME, adminToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andReturn();
        
        // Output response content
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("Upload file error handling response: " + responseContent);
        
        System.out.println("File upload error handling test completed");
    }
}