package com.sinitek.mind.model.controller;

import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.model.dto.ModelDeleteDTO;
import com.sinitek.mind.model.dto.ModelUpdateDefaultRequest;
import com.sinitek.mind.model.dto.ModelUpdateRequest;
import com.sinitek.mind.model.dto.ModelUpdateWithJsonDTO;
import com.sinitek.mind.model.utils.ModelTestDataUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.ResultActions;

import java.util.HashMap;
import java.util.Map;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * ModelController - 单元测试
 *
 * <AUTHOR>
 * date 2025-07-29
 */
@Slf4j
public class ModelControllerTest extends MindBaseTestApplication {

    private static final String LIST_TEST_MODEL_ID = "Qwen3-32B-AWQ";
    private static final String CRUD_TEST_MODEL_ID = "gpt-4.1";

    /**
     * 每个单元测试执行前自动初始化测试数据
     */
    @BeforeEach
    public void initTestData() {
        clearInitData();
        createInitData();
    }

    @Test
    @SneakyThrows
    @DisplayName("测试更新配置文件接口")
    public void testUpdateWithJson() {
        // 先获取当前配置
        ResultActions getPerform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/ai/model/getConfigJson")
        );
        String configJson = getPerform.andReturn().getResponse().getContentAsString();
        Map<String, Object> responseMap = JsonUtil.toJavaObject(configJson, Map.class);
        String configStr = (String) responseMap.get("data");
        log.info("/model/getConfigJson接口的返回结果: {}", configJson);

        // 执行更新操作
        ModelUpdateWithJsonDTO updateDTO = new ModelUpdateWithJsonDTO();
        updateDTO.setConfig(configStr);

        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/updateWithJson")
                        .content(JsonUtil.toJsonString(updateDTO))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn().getResponse().getContentAsString();

        log.info("/model/updateWithJson接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试模型测试接口")
    public void testTestModel() {
        // 使用列表测试模型进行测试
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/ai/model/test")
                        .param("model", LIST_TEST_MODEL_ID)
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn().getResponse().getContentAsString();

        log.info("/model/test接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试模型CRUD接口")
    public void testModelCrud() {
        // 新增模型
        String modelId = saveModel();

        // 更新模型
        updateModel(modelId);

        // 查看模型详情
        getModelDetail(modelId);

        // 删除模型
        deleteModel(modelId);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试模型列表查询接口")
    public void testModelList() {
        // 执行列表查询测试
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/ai/model/list")
        );

        // 验证查询结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data[*].model", hasItem(LIST_TEST_MODEL_ID)))
                .andReturn().getResponse().getContentAsString();

        log.info("/model/list接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试更新默认模型接口")
    public void testUpdateDefault() {
        // 设置请求参数
        ModelUpdateDefaultRequest updateRequest = new ModelUpdateDefaultRequest();
        updateRequest.setLlm("Qwen3-32B-AWQ");
        updateRequest.setEmbedding("bge-m3");
        updateRequest.setDatasetTextLLM("deepseek-r1:latest");
        updateRequest.setDatasetImageLLM("glm-4v-flash");

        // 执行更新操作
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/updateDefault")
                        .content(JsonUtil.toJsonString(updateRequest))
        );

        // 验证更新结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andReturn().getResponse().getContentAsString();

        log.info("/model/updateDefault接口的返回结果: {}", contentAsString);

        // 获取更新后的配置并验证
        ResultActions configPerform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/common/system/getInitData")
        );

        String configContent = configPerform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data.defaultModels.llm.model", is("Qwen3-32B-AWQ")))
                .andReturn().getResponse().getContentAsString();

        log.info("/system/getInitData接口的返回结果: {}", configContent);
    }

    @SneakyThrows
    private String saveModel() {
        ModelUpdateRequest crudTestModel = createCrudTestModel();
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/update")
                        .content(JsonUtil.toJsonString(crudTestModel))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn().getResponse().getContentAsString();

        log.info("/model/update接口的新增返回结果: {}", contentAsString);
        return CRUD_TEST_MODEL_ID;
    }

    @SneakyThrows
    private void updateModel(String modelId) {
        ModelUpdateRequest updateRequest = createCrudTestModel();
        updateRequest.setModel(modelId);
        updateRequest.getMetadata().put("test", "单元测试");
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/update")
                        .content(JsonUtil.toJsonString(updateRequest))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn().getResponse().getContentAsString();

        log.info("/model/update接口的更新返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void getModelDetail(String modelId) {
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/ai/model/detail")
                        .param("model", modelId)
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data.model", is(modelId)))
                .andReturn().getResponse().getContentAsString();

        log.info("/model/detail接口的返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void deleteModel(String modelId) {
        ModelDeleteDTO modelDeleteDTO = new ModelDeleteDTO();
        modelDeleteDTO.setModel(modelId);
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/delete")
                        .content(JsonUtil.toJsonString(modelDeleteDTO))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn().getResponse().getContentAsString();

        log.info("/model/delete接口的返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void clearInitData() {
        try {
            ModelDeleteDTO modelDeleteDTO = new ModelDeleteDTO();
            modelDeleteDTO.setModel(LIST_TEST_MODEL_ID);
            mockMvc.perform(
                    postMockRequestForAdmin("/mind/api/core/ai/model/delete")
                            .content(JsonUtil.toJsonString(modelDeleteDTO))
            );
        } catch (Exception e) {
            log.info("清理列表测试模型数据时发生异常，可能模型不存在: {}", e.getMessage());
        }
    }

    @SneakyThrows
    private void createInitData() {
        ModelUpdateRequest modelUpdateRequest = ModelTestDataUtil.createLLMModel();
        mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/update")
                        .content(JsonUtil.toJsonString(modelUpdateRequest))
        ).andExpect(status().isOk());
    }

    /**
     * CRUD测试数据
     * @return
     */
    private ModelUpdateRequest createCrudTestModel() {
        ModelUpdateRequest modelUpdateRequest = new ModelUpdateRequest();
        modelUpdateRequest.setModel(CRUD_TEST_MODEL_ID);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", "gpt-4.1");
        metadata.put("provider", "OpenAI");
        metadata.put("type", "llm");
        metadata.put("isActive", true);
        metadata.put("isCustom", true);
        metadata.put("maxContext", 1000000);
        metadata.put("maxResponse", 32000);
        metadata.put("quoteMaxToken", 1000000);
        metadata.put("maxTemperature", 1.2);
        metadata.put("showTopP", true);
        metadata.put("vision", true);
        metadata.put("datasetProcess", true);
        metadata.put("usedInClassify", true);
        metadata.put("usedInExtractFields", true);
        metadata.put("usedInToolCall", true);
        metadata.put("functionCall", true);
        metadata.put("toolChoice", true);
        modelUpdateRequest.setMetadata(metadata);

        return modelUpdateRequest;
    }
}