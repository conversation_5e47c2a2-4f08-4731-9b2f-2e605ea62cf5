package com.sinitek.mind.model.core.llm.service;

import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.dataset.constant.DatasetTestConstant;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.model.core.rerank.IModelRerankService;
import com.sinitek.mind.model.core.rerank.RerankModelFactory;
import com.sinitek.mind.model.core.rerank.RerankModelSupport;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.model.scoring.ScoringModel;
import dev.langchain4j.rag.content.Content;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.content.Media;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.MimeTypeUtils;

import java.io.File;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

/**
 * IModelChatService-单元测试
 *
 * <AUTHOR>
 * date 2025-07-25
 */
@Slf4j
public class ModelChatServiceTest extends MindBaseTestApplication {

    @Autowired
    private IModelChatService modelChatService;

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private IModelRerankService modelRerankService;

    @Autowired
    private RerankModelSupport rerankModelSupport;

    @Autowired
    private RerankModelFactory rerankModelFactory;

    String model = "Qwen3-14B-AWQ";

    @Test
    @DisplayName("测试基础聊天方法")
    public void testChatWithString() {
        String message = "你好，这是一个测试消息，收到给我返回yes";
        String response = modelChatService.chat(message, model);

        assertThat(response).isNotBlank();
        log.info("基础聊天方法返回结果: {}", response);
    }

    @SneakyThrows
    @Test
    @DisplayName("测试GLM多模态聊天方法")
    public void testChatWithString2() {
        ClassPathResource classPathResource = new ClassPathResource("/data/model/model_v_test.jpg");
        File file = classPathResource.getFile();

        ChatResponse chatResponse = modelChatService.recognizeImageContent(file, "glm-4v-flash");
        String response2 = chatResponse.getResult().getOutput().getText();
        assertThat(response2).isNotBlank();
        log.info("GLM多模态聊天方法返回结果: {}", response2);
    }

    @Test
    @DisplayName("测试知识库验证")
    public void testChatWithPrompt() {
        String message = "Spring漏洞CVE-2024-38819是什么";
        String datasetId = datasetService.getDatasetIdByName(DatasetTestConstant.DATASET_UNIT_NAME);
        String response = modelChatService.chat(message, model, Collections.singletonList(datasetId));

        assertThat(response).isNotBlank();
        log.info("测试知识库验证方法返回结果: {}", response);
    }

    @Test
    @DisplayName("测试重排模型效果")
    public void testRerankModel() {
        // 准备测试数据
        String query = "机器学习的应用";
        List<Document> documents = Arrays.asList(
                new Document("机器学习是人工智能的一个分支，它专注于开发能够从数据中学习的算法和模型"),
                new Document("深度学习是机器学习的一个子领域，它使用神经网络进行学习"),
                new Document("自然语言处理是机器学习在文本分析中的应用"),
                new Document("计算机视觉是机器学习在图像分析中的应用")
        );

        String rerankModelId = "bge-reranker-large";

        // 调用重排方法
        Response<List<Double>> response = modelRerankService.rerank(query, documents, rerankModelId);
        List<Double> content = response.content();

        // 验证结果
        assertThat(response).isNotNull();
        assertThat(content).isNotNull();
        assertThat(content.size()).isEqualTo(documents.size());
        log.info("重排模型测试结果: {}", response);

        ScoringModel rerankModel = rerankModelFactory.getRerankModel(rerankModelId);
        List<Content> aggregate = rerankModelSupport.rerankDocuments(query, documents, rerankModel, 0.5);
        log.info("测试重排模型的查询: {}", aggregate);

    }
}