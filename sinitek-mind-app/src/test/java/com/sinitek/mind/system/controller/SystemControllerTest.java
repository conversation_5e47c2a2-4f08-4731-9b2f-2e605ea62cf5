package com.sinitek.mind.system.controller;

import com.sinitek.mind.MindBaseTestApplication;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.ResultActions;

import static org.hamcrest.Matchers.isA;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * SystemController - 单元测试
 *
 * <AUTHOR>
 * date 2025-07-18
 */
@Slf4j
public class SystemControllerTest extends MindBaseTestApplication {

    @Test
    @SneakyThrows
    @DisplayName("测试获取系统初始化数据接口 - 无bufferId参数")
    public void testGetInitDataWithoutBufferId() {
        // 执行请求
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/common/system/getInitData")
        );

        // 验证响应
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data.systemVersion", notNullValue()))
                .andExpect(jsonPath("$.data.feConfigs", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/system/getInitData接口(无bufferId)的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试获取系统初始化数据接口 - 有bufferId参数但不匹配")
    public void testGetInitDataWithMismatchedBufferId() {
        // 执行请求 - 使用不匹配的bufferId
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/common/system/getInitData")
                        .param("bufferId", "mismatched-buffer-id")
        );

        // 验证响应 - 应该返回完整数据
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data.systemVersion", notNullValue()))
                .andExpect(jsonPath("$.data.feConfigs", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/system/getInitData接口(不匹配bufferId)的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试获取系统初始化数据接口 - 空bufferId参数")
    public void testGetInitDataWithEmptyBufferId() {
        // 执行请求 - 使用空bufferId
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/common/system/getInitData")
                        .param("bufferId", "")
        );

        // 验证响应 - 应该返回完整数据
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data.systemVersion", notNullValue()))
                .andExpect(jsonPath("$.data.feConfigs", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/system/getInitData接口(空bufferId)的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试获取系统初始化数据接口 - 验证响应数据结构")
    public void testGetInitDataResponseStructure() {
        // 执行请求
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/common/system/getInitData")
        );

        // 验证响应数据结构
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data.systemVersion", isA(String.class)))
                .andExpect(jsonPath("$.data.feConfigs", isA(Object.class)))
                .andReturn().getResponse().getContentAsString();

        log.info("/system/getInitData接口(数据结构验证)的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试获取系统初始化数据接口 - 验证请求头处理")
    public void testGetInitDataWithRequestHeaders() {
        // 执行请求 - 添加自定义请求头
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/common/system/getInitData")
                        .header("referer", "http://localhost:3000")
        );

        // 验证响应
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/system/getInitData接口(带请求头)的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试获取系统初始化数据接口 - 匹配的bufferId场景")
    public void testGetInitDataWithMatchingBufferId() {
        // 首先获取当前的bufferId
        ResultActions firstPerform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/common/system/getInitData")
        );
        String firstContent = firstPerform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andReturn().getResponse().getContentAsString();
        
        // 提取bufferId (这里简化处理，实际应该解析JSON获取)
        String bufferId = "test-buffer-id"; // 实际应该从响应中提取
        
        // 使用匹配的bufferId再次请求
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/common/system/getInitData")
                        .param("bufferId", bufferId)
        );

        // 验证响应
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data.systemVersion", notNullValue()))
                .andReturn().getResponse().getContentAsString();

        log.info("/system/getInitData接口(匹配bufferId)的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试获取系统初始化数据接口 - 异常处理")
    public void testGetInitDataExceptionHandling() {
        // 执行请求
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/common/system/getInitData")
        );

        // 验证响应 - 即使发生异常也应该返回适当的错误响应
        try {
            String contentAsString = perform.andExpect(status().isOk())
                    .andReturn().getResponse().getContentAsString();
            
            // 检查是否为成功响应
            if (contentAsString.contains("\"code\":200")) {
                log.info("/system/getInitData接口(正常响应)的返回结果: {}", contentAsString);
            } else {
                log.info("/system/getInitData接口(异常响应)的返回结果: {}", contentAsString);
            }
        } catch (Exception e) {
            log.info("/system/getInitData接口执行异常: {}", e.getMessage());
        }
    }
}