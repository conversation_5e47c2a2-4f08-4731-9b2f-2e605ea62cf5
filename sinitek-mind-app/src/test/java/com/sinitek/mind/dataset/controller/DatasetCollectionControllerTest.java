package com.sinitek.mind.dataset.controller;

import com.drew.lang.Charsets;
import com.jayway.jsonpath.JsonPath;
import com.sinitek.mind.dataset.constant.DatasetTestConstant;
import com.sinitek.mind.dataset.core.DatasetTrainingVectorTask;
import com.sinitek.mind.dataset.core.vector.IDatasetVectorStore;
import com.sinitek.mind.dataset.dto.DatasetCollectionDTO;
import com.sinitek.mind.dataset.dto.DatasetDataPageParamDTO;
import com.sinitek.mind.dataset.dto.PreviewChunksRequestDTO;
import com.sinitek.mind.dataset.enumerate.PreviewChunksTypeEnum;
import com.sinitek.mind.dataset.service.IDatasetCollectionService;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * DatasetCollectionController-单元测试
 *
 * <AUTHOR>
 * date 2025-07-22
 */
@Slf4j
public class DatasetCollectionControllerTest extends CommonDatasetTest {

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private DatasetTrainingVectorTask datasetTrainingVectorTask;

    @Autowired
    private IDatasetVectorStore datasetVectorStore;

    @Autowired
    private IDatasetCollectionService datasetCollectionService;

    @Test
    @SneakyThrows
    @DisplayName("测试基于文件ID创建知识库集合（docx上传）")
    public void testCreateByFileId() {
        // 创建单元测试目录
        String parentId = testCreateFolderCollection();

        String datasetId = datasetService.getDatasetIdByName(DatasetTestConstant.DATASET_UNIT_NAME);

        // 1. 上传docx文件，获取fileId
        File file = ResourceUtils.getFile("classpath:data/dataset/collection/携宁SiniCube开放开发平台软件V1.3（软件著作权申请表）.docx");
        String fileId = this.uploadDatasetFile(file, datasetId);

        // 2. 调用预览分块接口，验证文件分块功能
        PreviewChunksRequestDTO previewRequest = new PreviewChunksRequestDTO();
        fillBaseCollectionParams(previewRequest, null, datasetId);
        previewRequest.setOverlapRatio(BigDecimal.valueOf(0.2));
        previewRequest.setType(PreviewChunksTypeEnum.FILE_LOCAL.getValue());
        previewRequest.setSourceId(fileId);
        this.checkPreviewChunks(previewRequest);

        // 3. 构造创建集合参数，调用/create/fileId
        String name = "单元测试之docx文件导入";
        String collectionId = super.createCollectionByFileId(datasetId, fileId, name, parentId);

        // 公共校验
        verifyCollectionDetailAndDataList(collectionId, name);
      }

    @Test
    @SneakyThrows
    @DisplayName(value = "测试文本数据集之自定义文本导入和查询全流程")
    public void testCreateByText() {
        // 创建文本数据集之自定义文本
        String datasetId = datasetService.getDatasetIdByName(DatasetTestConstant.DATASET_UNIT_NAME);
        String name = "单元测试之Spring安全漏洞";
        com.sinitek.mind.dataset.dto.TextCreateDatasetCollectionParamsDTO textCreateDatasetCollectionParamsDTO = new com.sinitek.mind.dataset.dto.TextCreateDatasetCollectionParamsDTO();
        fillBaseCollectionParams(textCreateDatasetCollectionParamsDTO, name, datasetId);

        File file = ResourceUtils.getFile("classpath:data/dataset/collection/Spring安全漏洞.txt");
        String text = FileUtils.readFileToString(file, Charsets.UTF_8);
        textCreateDatasetCollectionParamsDTO.setText(text);

        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/dataset/collection/create/text")
                        .content(JsonUtil.toJsonString(textCreateDatasetCollectionParamsDTO))
        );
        String contentAsString = perform.andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        String collectionId = JsonPath.read(contentAsString, "$.data.collectionId");
        assertThat(collectionId).isNotBlank();
        log.info("/collection/create/text接口的返回结果: {}", contentAsString);

        // 公共校验
        verifyCollectionDetailAndDataList(collectionId, name);

        // === 新增：向量化并检索 ===
        // 1. 触发一次向量化任务
        datasetTrainingVectorTask.processPendingTrainings();

        // 2. 进行向量检索
        String query = "Spring漏洞CVE-2024-38819是什么";
        List<Document> results = datasetVectorStore.similaritySearch(query, Collections.singletonList(datasetId));
        assertThat(results.size()).isGreaterThan(0);
        boolean hasFix = results.stream().anyMatch(doc -> Objects.requireNonNull(doc.getText()).contains("CVE-2024-38819"));
        assertThat(hasFix).isTrue();
    }

    /**
     * 检查分块预览文件的效果
     */
    @SneakyThrows
    public void checkPreviewChunks(PreviewChunksRequestDTO previewRequest) {
        ResultActions previewAction = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/dataset/preview-chunks")
                        .content(JsonUtil.toJsonString(previewRequest))
        );
        String previewResp = previewAction.andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        log.info("预览分块接口返回内容: {}", previewResp);
        Integer totalChunks = JsonPath.read(previewResp, "$.data.total");
        assertThat(totalChunks).isGreaterThan(0);
    }


    @SneakyThrows
    @DisplayName("测试创建集合文件夹")
    private String testCreateFolderCollection() {
        // 准备创建文件夹的参数
        String datasetId = datasetService.getDatasetIdByName(DatasetTestConstant.DATASET_UNIT_NAME);
        String folderName = "单元测试目录";
        String dbId = datasetCollectionService.getIdByName(folderName);
        if (StringUtils.isNotBlank(dbId)) {
            return dbId;
        }


        DatasetCollectionDTO dto = new DatasetCollectionDTO();
        dto.setParentId("");
        dto.setDatasetId(datasetId);
        dto.setName(folderName);
        dto.setType("folder");

        // 调用创建接口
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/dataset/collection/create")
                        .content(JsonUtil.toJsonString(dto))
        );

        // 验证响应
        String contentAsString = perform.andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        String collectionId = JsonPath.read(contentAsString, "$.data");
        assertThat(collectionId).isNotBlank();
        log.info("创建文件夹集合的返回结果: {}", contentAsString);

        // 测试paths接口
        ResultActions pathsPerform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/dataset/collection/paths")
                        .param("parentId", collectionId)
        );

        // 验证响应
        pathsPerform.andExpect(status().isOk())
                .andExpect(jsonPathSuccessIsTrue())
                .andExpect(jsonPath("$.data").isArray());

        String pathsContent = pathsPerform.andReturn().getResponse().getContentAsString();
        log.info("/collection/paths接口的返回结果: {}", pathsContent);

        return collectionId;
    }

    /**
     * 公共方法：校验集合详情和数据列表
     */
    private void verifyCollectionDetailAndDataList(String collectionId, String expectedName) throws Exception {
        // 获取集合详情
        ResultActions detailPerform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/dataset/collection/detail")
                        .param("id", collectionId)
        );
        String detailContent = detailPerform.andExpect(status().isOk())
                .andExpect(jsonPath("$.data.name", containsString(expectedName)))
                .andExpect(jsonPath("$.data.tmbId", notNullValue()))
                .andExpect(jsonPath("$.data.file", notNullValue()))
                .andReturn().getResponse().getContentAsString();
        log.info("/collection/detail接口的返回结果: {}", detailContent);

        // 分页获取集合数据列表
        DatasetDataPageParamDTO datasetDataPageParamDTO = new DatasetDataPageParamDTO();
        datasetDataPageParamDTO.setCollectionId(collectionId);
        ResultActions dataListPerform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/dataset/data/v2/list")
                        .content(JsonUtil.toJsonString(datasetDataPageParamDTO))
        );
        String dataListContent = dataListPerform.andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        log.info("/data/v2/list接口的返回结果: {}", dataListContent);

        // 提取第一个数据项的id
        List<String> dataIds = JsonPath.read(dataListContent, "$.data.list[*]._id");
        if (!dataIds.isEmpty()) {
            String firstDataId = dataIds.get(0);
            log.info("获取到第一个数据项的id: {}", firstDataId);

            // 调用detail接口获取第一个数据项的详情
            ResultActions firstDataDetailPerform = mockMvc.perform(
                    getMockRequestForAdmin("/mind/api/core/dataset/data/detail")
                            .param("id", firstDataId)
            );

            // 验证返回值中的q不为空，indexes不为null
            String dataDetailContent = firstDataDetailPerform.andExpect(status().isOk())
                    .andExpect(jsonPath("$.data.q", notNullValue()))
                    .andExpect(jsonPath("$.data.indexes", notNullValue()))
                    .andReturn().getResponse().getContentAsString();

            log.info("验证第一个数据项详情成功，返回值: {}", dataDetailContent);
        } else {
            log.warn("集合数据列表为空，无法验证第一个数据项详情");
        }
    }
}
