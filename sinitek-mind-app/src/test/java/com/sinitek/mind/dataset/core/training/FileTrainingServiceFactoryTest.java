package com.sinitek.mind.dataset.core.training;

import com.sinitek.mind.dataset.core.training.impl.FileTypeTrainingService;
import com.sinitek.mind.dataset.core.training.impl.ImageTypeTrainingService;
import com.sinitek.mind.dataset.enumerate.DatasetCollectionTypeEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件训练服务工厂测试类
 *
 * <AUTHOR>
 * date 2025-08-22
 */
@SpringBootTest
class FileTrainingServiceFactoryTest {

    @Autowired
    private FileTrainingServiceFactory fileTrainingServiceFactory;

    @Test
    void testGetFileTypeService() {
        IFileTrainingService service = fileTrainingServiceFactory.getService(DatasetCollectionTypeEnum.FILE.getCode());
        assertNotNull(service);
        assertEquals(DatasetCollectionTypeEnum.FILE.getCode(), service.getSupportedType());
        assertTrue(service instanceof FileTypeTrainingService);
    }

    @Test
    void testGetImageTypeService() {
        IFileTrainingService service = fileTrainingServiceFactory.getService(DatasetCollectionTypeEnum.IMAGES.getCode());
        assertNotNull(service);
        assertEquals(DatasetCollectionTypeEnum.IMAGES.getCode(), service.getSupportedType());
        assertTrue(service instanceof ImageTypeTrainingService);
    }

    @Test
    void testIsSupported() {
        assertTrue(fileTrainingServiceFactory.isSupported(DatasetCollectionTypeEnum.FILE.getCode()));
        assertTrue(fileTrainingServiceFactory.isSupported(DatasetCollectionTypeEnum.IMAGES.getCode()));
        assertFalse(fileTrainingServiceFactory.isSupported("unsupported_type"));
    }

    @Test
    void testGetUnsupportedService() {
        assertThrows(IllegalArgumentException.class, () -> {
            fileTrainingServiceFactory.getService("unsupported_type");
        });
    }
}
