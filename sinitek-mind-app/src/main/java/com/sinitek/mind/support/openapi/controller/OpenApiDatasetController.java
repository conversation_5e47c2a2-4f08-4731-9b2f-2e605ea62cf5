package com.sinitek.mind.support.openapi.controller;

import com.sinitek.mind.common.support.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 数据集对应的OpenApi接口
 */
@Slf4j
@RestController
@RequestMapping("/mind/open-api/core/dataset")
@RequiredArgsConstructor
@Tag(name = "知识库管理", description = "知识库相关接口")
public class OpenApiDatasetController {

    @PostMapping("/create")
    @Operation(summary = "创建知识库", description = "创建知识库")
    public ApiResponse<?> createDataset() {
        return ApiResponse.success();
    }

    @PostMapping("/list")
    @Operation(summary = "获取知识库列表", description = "获取知识库列表")
    public ApiResponse<?> getDatasetList() {
        return ApiResponse.success();
    }

    @GetMapping("/detail")
    @Operation(summary = "获取知识库详情", description = "获取知识库详情")
    public ApiResponse<?> getDatasetDetail() {
        return ApiResponse.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除知识库", description = "删除知识库")
    public ApiResponse<?> deleteDataset() {
        return ApiResponse.success();
    }

    // 集合相关接口
    @PostMapping("/collection/create")
    @Operation(summary = "创建空集合", description = "创建空集合")
    public ApiResponse<?> createCollection() {
        return ApiResponse.success();
    }

    @PostMapping("/collection/create/text")
    @Operation(summary = "创建纯文本集合", description = "创建纯文本集合")
    public ApiResponse<?> createTextCollection() {
        return ApiResponse.success();
    }

    @PostMapping("/collection/create/link")
    @Operation(summary = "创建链接集合", description = "创建链接集合")
    public ApiResponse<?> createLinkCollection() {
        return ApiResponse.success();
    }

    @PostMapping("/collection/create/localFile")
    @Operation(summary = "创建文件集合", description = "创建文件集合")
    public ApiResponse<?> createFileCollection() {
        return ApiResponse.success();
    }

    @PostMapping("/collection/create/apiCollection")
    @Operation(summary = "创建API集合", description = "创建API集合")
    public ApiResponse<?> createApiCollection() {
        return ApiResponse.success();
    }

    @PostMapping("/collection/listV2")
    @Operation(summary = "获取集合列表（新版）", description = "获取集合列表（新版）")
    public ApiResponse<?> getCollectionListV2() {
        return ApiResponse.success();
    }

    @PostMapping("/collection/list")
    @Operation(summary = "获取集合列表（旧版）", description = "获取集合列表（旧版）")
    public ApiResponse<?> getCollectionList() {
        return ApiResponse.success();
    }

    @GetMapping("/collection/detail")
    @Operation(summary = "获取集合详情", description = "获取集合详情")
    public ApiResponse<?> getCollectionDetail() {
        return ApiResponse.success();
    }

    @PutMapping("/collection/update")
    @Operation(summary = "修改集合信息", description = "修改集合信息（通过集合ID或外部文件ID）")
    public ApiResponse<?> updateCollection() {
        return ApiResponse.success();
    }

    @PostMapping("/collection/delete")
    @Operation(summary = "删除集合", description = "删除集合")
    public ApiResponse<?> deleteCollection() {
        return ApiResponse.success();
    }

    // 数据相关接口
    @PostMapping("/data/pushData")
    @Operation(summary = "为集合批量添加数据", description = "为集合批量添加数据")
    public ApiResponse<?> pushData() {
        return ApiResponse.success();
    }

    @PostMapping("/data/v2/list")
    @Operation(summary = "获取集合数据列表（新版）", description = "获取集合数据列表（新版）")
    public ApiResponse<?> getDataListV2() {
        return ApiResponse.success();
    }

    @PostMapping("/data/list")
    @Operation(summary = "获取集合数据列表（旧版）", description = "获取集合数据列表（旧版）")
    public ApiResponse<?> getDataList() {
        return ApiResponse.success();
    }

    @GetMapping("/data/detail")
    @Operation(summary = "获取单条数据详情", description = "获取单条数据详情")
    public ApiResponse<?> getDataDetail() {
        return ApiResponse.success();
    }

    @PutMapping("/data/update")
    @Operation(summary = "修改单条数据", description = "修改单条数据")
    public ApiResponse<?> updateData() {
        return ApiResponse.success();
    }

    @PostMapping("/data/delete")
    @Operation(summary = "删除单条数据", description = "删除单条数据")
    public ApiResponse<?> deleteData() {
        return ApiResponse.success();
    }

    @PostMapping("/searchTest")
    @Operation(summary = "搜索测试", description = "搜索测试")
    public ApiResponse<?> searchTest() {
        return ApiResponse.success();
    }
}