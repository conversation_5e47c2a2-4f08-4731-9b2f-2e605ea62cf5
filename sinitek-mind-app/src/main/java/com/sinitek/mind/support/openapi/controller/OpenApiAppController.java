package com.sinitek.mind.support.openapi.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.model.AppDetailType;
import com.sinitek.mind.core.app.service.IAppService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.chat.service.IChatLogService;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * app相关的OpenApi接口
 */
@Slf4j
@RestController
@RequestMapping("/mind/open-api/core/app")
@RequiredArgsConstructor
@Tag(name = "app管理", description = "app相关接口")
public class OpenApiAppController {

    private final IAppService appService;
    private final IAuthService authService;
    private final IChatLogService chatLogService;
    private final IPermissionService permissionService;
    private final IAuthAppService authAppService;


    @PostMapping("/list")
    @Operation(summary = "获取应用列表", description = "根据条件获取应用列表")
    public ApiResponse<List<AppListItemDTO>> getAppList(@RequestBody ListAppDTO request) {
        List<AppListItemDTO> appList = appService.getAppList(request);
        return ApiResponse.success(appList);

    }

    @PostMapping("/create")
    @Operation(summary = "创建应用", description = "创建新的app")
    public ApiResponse<String> createApp(@RequestBody CreateAppDTO body) {
        String appId = appService.createApp(body);
        return ApiResponse.success(appId);
    }

    @PostMapping("/simple-create")
    @Operation(summary = "简单创建应用", description = "创建新的app-目前支持工作流类型")
    public ApiResponse<String> createSimpleApp(@RequestBody SimpleCreateAppRequestDTO body) {
        String appId = appService.createSimpleApp(body);
        return ApiResponse.success(appId);
    }

    @GetMapping("/detail")
    @Operation(summary = "应用详情", description = "根据appId获取app详情")
    public ApiResponse<AppDetailType> getAppDetail(@RequestParam("appId") String appId) {
        AppDetailType appDetail = appService.getAppDetail(appId);
        return ApiResponse.success(appDetail);
    }

    @PostMapping("/del")
    @Operation(summary = "删除应用", description = "根据appId删除应用")
    public ApiResponse<Void> deleteApp(@RequestParam("appId") String appId) {
        appService.deleteApp(appId);
        return ApiResponse.success();
    }

    @PostMapping("/del-by-namespace")
    @Operation(summary = "删除应用-通过namespace字段", description = "根据namespace删除应用")
    public ApiResponse<Void> deleteAppByNamespace(@RequestBody DelAppByNamespaceRequest request) {
        appService.deleteAppByNamespace(request.getNamespaceList());
        return ApiResponse.success();
    }

    @GetMapping("/copy")
    @Operation(summary = "复制应用", description = "根据appId复制一个相同的应用")
    public ApiResponse<String> copyApp(@RequestParam("appId") String appId) {
        String appIdNew = appService.copyApp(appId);
        return ApiResponse.success(appIdNew);
    }

    @PostMapping("/update")
    @Operation(summary = "更新应用")
    public ApiResponse<App> updateApp(@RequestParam String appId, @RequestBody AppUpdateDTO params) {
        params.setAppId(appId);
        App app = appService.updateApp(params);
        return ApiResponse.success(app);
    }

}
