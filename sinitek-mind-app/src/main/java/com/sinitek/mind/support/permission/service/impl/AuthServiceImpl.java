package com.sinitek.mind.support.permission.service.impl;

import com.sinitek.mind.core.chat.enumerate.AuthUserTypeEnum;
import com.sinitek.mind.support.openapi.context.OpenApiContext;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.constant.PermissionErrorCodeConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.dto.AuthMemberDTO;
import com.sinitek.mind.support.permission.dto.TeamPermissionDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.dto.TeamMemberDTO;
import com.sinitek.mind.support.team.service.ITeamMemberService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements IAuthService {

    private final IOrgService orgService;

    private final ITeamMemberService teamMemberService;

    private final IPermissionService permissionService;

    @Override
    public AuthDTO authCert() {
        AuthDTO authDTO = new AuthDTO();

        String orgId = CurrentUserFactory.getOrgId();
        if (StringUtils.isNotBlank(orgId)) {
            // 从上下文进行获取
            authDTO.setUserId(CurrentUserFactory.getUserId());
            authDTO.setTmbId(CurrentUserFactory.getOrgId());
            authDTO.setTeamId(CurrentUserFactory.getTenantId());
            authDTO.setAuthType(AuthUserTypeEnum.TOKEN);
        } else {
            // 在从openApi上下文获取
            authDTO.setTmbId(OpenApiContext.getTmbId());
            authDTO.setTeamId(OpenApiContext.getTeamId());
            authDTO.setAuthType(OpenApiContext.getAuthType());

            Employee employee = orgService.getEmployeeById(OpenApiContext.getTmbId());
            if (!Objects.isNull(employee)) {
                authDTO.setUserId(employee.getUserId());
            }
        }

        // 用户是管理员，则认为他是团队拥有者
        authDTO.setIsRoot(orgService.isAdmin(authDTO.getTmbId()));
        // 设置一个默认值
        if (authDTO.getTeamId() == null) {
            authDTO.setTeamId("");
        }
        return authDTO;
    }

    @Override
    public AuthMemberDTO authUserPer(long per) {
        AuthDTO result = authCert();
        TeamMemberDTO tmb = teamMemberService.getById(result.getTmbId());

        AuthMemberDTO authMemberDTO = new AuthMemberDTO();
        BeanUtils.copyProperties(result, authMemberDTO);
        authMemberDTO.setTmb(tmb);

        if (result.getIsRoot()) {
            TeamPermissionDTO teamPermissionDTO = new TeamPermissionDTO();
            teamPermissionDTO.setValue(PermissionConstant.OWNER_PERMISSION_VAL);
            authMemberDTO.setPermission(teamPermissionDTO);
            return authMemberDTO;
        }

        if (!tmb.getPermission().checkPer(per)) {
            throw new BussinessException(PermissionErrorCodeConstant.MEMBER_NO_PERMISSION);
        }

        authMemberDTO.setPermission(tmb.getPermission());
        return authMemberDTO;
    }
}
