package com.sinitek.mind.support.outlink.service.impl;

import com.sinitek.mind.support.outlink.constant.OutLinkErrorCodeConstant;
import com.sinitek.mind.support.outlink.dto.AuthHookRequestDTO;
import com.sinitek.mind.support.outlink.dto.AuthHookResponseDTO;
import com.sinitek.mind.support.outlink.dto.AuthOutLinkDTO;
import com.sinitek.mind.support.outlink.entity.OutLink;
import com.sinitek.mind.support.outlink.repository.OutLinkRepository;
import com.sinitek.mind.support.outlink.service.IAuthOutLinkService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/15
 */
@Slf4j
@Service
public class AuthOutLinkServiceImpl implements IAuthOutLinkService {

    @Autowired
    private OutLinkRepository outLinkRepository;

    @Autowired
    private RestTemplate restTemplate;

    private static final String HOOK_PATH_INIT = "/shareAuth/init";
    private static final String HOOK_PATH_START = "/shareAuth/start";
    private static final String HOOK_PATH_FINISH = "/shareAuth/finish";

    /**
     * 通用的Hook接口调用方法
     *
     * @param hookUrl    Hook基础URL
     * @param requestDTO 请求参数
     * @return HTTP响应
     */
    private ResponseEntity<AuthHookResponseDTO> callHookApi(String hookUrl, AuthHookRequestDTO requestDTO) {
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<AuthHookRequestDTO> requestEntity = new HttpEntity<>(requestDTO, headers);
        
        // 发送POST请求
        log.info("调用Hook接口: {}", hookUrl);
        return restTemplate.exchange(hookUrl, HttpMethod.POST, requestEntity, AuthHookResponseDTO.class);
    }

    /**
     * 获取外链配置并验证
     *
     * @param shareId 外链ID
     * @return OutLink对象
     */
    private OutLink getAndValidateOutLink(String shareId) {
        Optional<OutLink> outLinkOpt = outLinkRepository.findByShareId(shareId);
        if (outLinkOpt.isEmpty()) {
            throw new BussinessException(OutLinkErrorCodeConstant.SHARE_LINK_NOT_FOUND_OR_EXPIRED);
        }
        return outLinkOpt.get();
    }

    /**
     * 进行外链聊天初始化验证，调用身份验证接口，获取实际uid
     *
     * @param shareId 外链id
     * @param authToken 外链用户id
     * @return 外链验证后的用户id-验证接口返回的uid
     */
    @Override
    public void authOutLinkChatInit(String shareId, String authToken) {
        log.info("开始外链聊天初始化验证，shareId: {}, authToken: {}", shareId, authToken);
        
        OutLink outLink = getAndValidateOutLink(shareId);

        if (outLink.getLimit() == null || StringUtils.isBlank(outLink.getLimit().getHookUrl())) {
            // 没有配置hookUrl，不需要校验
            log.info("outLink未配置hookUrl，不需要进行身份验证: {}", authToken);
            return;
        }

        String hookUrl = outLink.getLimit().getHookUrl();
        
        try {
            // 准备请求参数
            AuthHookRequestDTO requestDTO = new AuthHookRequestDTO();
            requestDTO.setToken(authToken);
            
            // 调用Hook接口
            ResponseEntity<AuthHookResponseDTO> response = callHookApi(
                hookUrl,
                requestDTO
            );
            
            AuthHookResponseDTO responseBody = response.getBody();
            if (responseBody == null) {
                throw new BussinessException(OutLinkErrorCodeConstant.AUTH_INTERFACE_EMPTY_RESPONSE, hookUrl);
            }
            if (!responseBody.getSuccess()) {
                // 校验失败，输出错误原因
                throw new BussinessException(OutLinkErrorCodeConstant.AUTH_FAILED, getErrorMsg(responseBody));
            }
        } catch (Exception e) {
            log.error("免登录窗口初始化验证失败", e);
            if (e instanceof BussinessException) {
                throw e;
            } else {
                throw new BussinessException(OutLinkErrorCodeConstant.AUTH_INIT_FAILED, hookUrl);
            }
        }
    }

    @Override
    public AuthOutLinkDTO authOutLink(String shareId, String outLinkUid) {
        if (StringUtils.isBlank(outLinkUid)) {
            throw new BussinessException(OutLinkErrorCodeConstant.OUTLINK_INVALID);
        }

        OutLink outLink = getAndValidateOutLink(shareId);

        AuthOutLinkDTO authOutLinkDTO = new AuthOutLinkDTO();
        authOutLinkDTO.setAppId(outLink.getAppId());
        authOutLinkDTO.setOutLinkConfig(outLink);
        authOutLinkDTO.setUid(outLinkUid);
        return authOutLinkDTO;
    }

    private String getErrorMsg(AuthHookResponseDTO responseDTO) {
        if (Objects.isNull(responseDTO)) {
            return "认证接口返回数据为空";
        }
        String finalMsg = responseDTO.getMessage();
        if (StringUtils.isBlank(finalMsg)) {
            finalMsg = responseDTO.getMsg();
        }
        return finalMsg;
    }

}
