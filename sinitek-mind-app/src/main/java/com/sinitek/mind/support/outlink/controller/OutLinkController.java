package com.sinitek.mind.support.outlink.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.outlink.dto.AppInfoDTO;
import com.sinitek.mind.support.outlink.dto.CreateOutLinkRequest;
import com.sinitek.mind.support.outlink.dto.OutLinkResponse;
import com.sinitek.mind.support.outlink.dto.UpdateOutLinkRequest;
import com.sinitek.mind.support.outlink.service.IOutLinkService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 免登录链接管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/mind/api/support/outLink")
@RequiredArgsConstructor
@Validated
@Tag(name = "免登录链接管理", description = "免登录链接的创建、查询、更新、删除操作")
public class OutLinkController {

    private final IOutLinkService outLinkService;

    @PostMapping("/create")
    @Operation(summary = "创建免登录链接", description = "为指定应用创建一个新的免登录分享链接")
    public ApiResponse<String> createOutLink(@Valid @RequestBody CreateOutLinkRequest request) {
        log.info("创建免登录链接，请求参数：{}", request);
        String shareId = outLinkService.createOutLink(request);
        return ApiResponse.success(shareId);
    }

    @GetMapping("/list")
    @Operation(summary = "查询免登录链接列表", description = "获取指定应用的所有免登录链接列表")
    public ApiResponse<List<OutLinkResponse>> getOutLinkList(
            @Parameter(description = "应用ID", required = true)
            @RequestParam @NotBlank(message = "应用ID不能为空") String appId,
            @Parameter(description = "发布渠道类型", required = true)
            @RequestParam @NotBlank(message = "发布渠道类型不能为空") String type) {
        log.info("查询免登录链接列表，appId：{}，type：{}", appId, type);
        List<OutLinkResponse> outLinks = outLinkService.getOutLinkList(appId, type);
        return ApiResponse.success(outLinks);
    }

    @PostMapping("/update")
    @Operation(summary = "更新免登录链接", description = "更新指定的免登录链接配置")
    public ApiResponse<Void> updateOutLink(@Valid @RequestBody UpdateOutLinkRequest request) {
        log.info("更新免登录链接，请求参数：{}", request);
        outLinkService.updateOutLink(request);
        return ApiResponse.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除免登录链接", description = "删除指定的免登录链接")
    public ApiResponse<Void> deleteOutLink(
            @Parameter(description = "链接数据库ID", required = true)
            @RequestParam @NotBlank(message = "链接ID不能为空") String id) {
        log.info("删除免登录链接，id：{}", id);
        outLinkService.deleteOutLink(id);
        return ApiResponse.success();
    }

    @GetMapping("/app")
    @Operation(summary = "根据shareId获取应用信息", description = "通过分享链接ID获取关联的应用数据对象")
    public ApiResponse<AppInfoDTO> getAppByShareId(
            @Parameter(description = "分享链接ID", required = true)
            @RequestParam @NotBlank(message = "分享链接ID不能为空") String shareId) {
        log.info("根据shareId获取应用信息，shareId：{}", shareId);
        AppInfoDTO app = outLinkService.getAppByShareId(shareId);
        return ApiResponse.success(app);
    }
} 