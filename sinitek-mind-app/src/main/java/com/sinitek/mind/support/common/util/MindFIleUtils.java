package com.sinitek.mind.support.common.util;

import com.mongodb.client.gridfs.model.GridFSFile;
import com.sinitek.mind.common.constant.CommonErrorCodeConstant;
import com.sinitek.mind.support.common.constant.FileConstant;
import com.sinitek.sirm.common.utils.HttpUtils;
import com.sinitek.sirm.common.utils.SpringMvcUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import jakarta.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

/**
 * Mind文件工具类
 *
 * <AUTHOR>
 * date 2025-07-18
 */
@Component
public class MindFIleUtils {

    @Autowired
    @Qualifier("datasetGridFsTemplate")
    private GridFsTemplate datasetGridFsTemplate;

    @Autowired
    @Qualifier("chatGridFsTemplate")
    private GridFsTemplate chatGridFsTemplate;

    /**
     * 根据bucketName找到对应的GridFsTemplate
     * @param bucketName
     * @return
     */
    public GridFsTemplate getGridFsTemplate(String bucketName) {
        if (FileConstant.DATASET_NAME.equals(bucketName)) {
            return datasetGridFsTemplate;
        } else if (FileConstant.CHAT_NAME.equals(bucketName)) {
            return chatGridFsTemplate;
        }
        return null;
    }

    /**
     * 根据fileId获取到GridFSFile
     * @param fileId
     * @param gridFsTemplate
     * @return
     */
    public GridFSFile getGridFSFileByFileId(String fileId, GridFsTemplate gridFsTemplate) {
        Query query = Query.query(Criteria.where("_id").is(new ObjectId(fileId)));
        return gridFsTemplate.findOne(query);
    }

    /**
     * 上传文件
     * @param fileName
     * @param contentType
     * @param inputStream
     * @param gridFsTemplate
     * @return
     */
    public String store(String fileName,String contentType, InputStream inputStream, Map<String, String> metadata, GridFsTemplate gridFsTemplate) {
        try (inputStream) {
            ObjectId store = gridFsTemplate.store(inputStream, fileName, contentType, metadata);
            return store.toString();
        } catch (IOException e) {
            throw new BussinessException(CommonErrorCodeConstant.FILE_UPLOAD_FAILED, e);
        }
    }

    /**
     * 根据gridFSFile下载文件
     * @param gridFSFile
     * @param gridFsTemplate
     */
    public void downloadGridFSFile(GridFSFile gridFSFile, GridFsTemplate gridFsTemplate) {
        String fileName = gridFSFile.getFilename();
        HttpServletResponse response = SpringMvcUtil.getHttpServletResponse();

        try (InputStream inputStream = gridFsTemplate.getResource(gridFSFile).getInputStream()){
            HttpUtils.download(response, fileName, inputStream);
        } catch (IOException e) {
            throw new BussinessException(CommonErrorCodeConstant.FILE_DOWNLOAD_FAILED, e);
        }
    }
}
