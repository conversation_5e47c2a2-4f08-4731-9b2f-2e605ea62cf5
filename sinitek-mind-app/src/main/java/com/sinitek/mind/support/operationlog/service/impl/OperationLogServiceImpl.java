package com.sinitek.mind.support.operationlog.service.impl;

import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.sirm.business.log.service.IBusinessLogExtService;
import com.sinitek.sirm.common.log.enumrate.BusinLogType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 操作记录接口实现类，使用框架的业务日志进行实现
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Slf4j
@Service
public class OperationLogServiceImpl implements IOperationLogService {

    @Autowired
    private IBusinessLogExtService businessLogExtService;

    @Autowired
    private IAuthService authService;

    /* 字段对应关系
     * event-moduleName
     * params-desc(JSON格式)
     * 业务日志的operateType默认为1
     */
    @Override
    public void addOperationLog(BusinLogType event, String desc) {
        // 存储orgId，获取时也是orgId
        AuthDTO authDTO = authService.authCert();
        String userId = authDTO.getUserId();
        String tenantId = authDTO.getTeamId();
        businessLogExtService.simpleSaveBusinessLog(userId, tenantId, event.getModuleName(), event.getOperateType(),
                desc);
    }

}
