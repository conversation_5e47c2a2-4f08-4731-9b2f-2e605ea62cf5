package com.sinitek.mind.support.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.mind.support.openapi.entity.OpenApi;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@Mapper
public interface OpenApiMapper extends BaseMapper<OpenApi> {
    
    /**
     * 根据API密钥查找
     */
    OpenApi getByApiKey(String apiKey);
    
    /**
     * 根据用户ID查找，且appId为null
     */
    List<OpenApi> findByOrgIdAndAppIdIsNull(String orgId);
    
    /**
     * 根据用户ID和应用ID查找
     */
    List<OpenApi> findByOrgIdAndAppId(String orgId, String appId);
    
    /**
     * 更新最后使用时间
     */
    @Update("UPDATE open_api SET last_used_time = #{lastUsedTime} WHERE id = #{id}")
    void updateLastUsedTime(@Param("id") Long id, @Param("lastUsedTime") Date lastUsedTime);

    /**
     * 根据应用ID删除
     */
    int deleteByAppId(String appId);
}
