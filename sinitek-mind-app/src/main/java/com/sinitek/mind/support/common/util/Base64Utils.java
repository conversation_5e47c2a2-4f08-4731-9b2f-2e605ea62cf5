package com.sinitek.mind.support.common.util;

import cn.hutool.core.codec.Base64;
import com.sinitek.mind.common.constant.CommonErrorCodeConstant;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.util.UUID;

/**
 * Base64工具类
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
public class Base64Utils {

    /**
     * 将base64字符串转换为文件
     *
     * @param base64Data base64数据 (格式: data:image/jpeg;base64,/9j/...)
     * @return File对象
     */
    public static File convertBase64ToFile(String base64Data) {
        if (!StringUtils.hasText(base64Data)) {
            throw new BussinessException(CommonErrorCodeConstant.BASE64_DATA_EMPTY);
        }

        try {
            // 解析base64数据，提取内容类型和数据
            String contentType = "image/jpeg"; // 默认类型
            String actualBase64;
            
            if (base64Data.contains(",")) {
                String[] parts = base64Data.split(",");
                String header = parts[0]; // data:image/jpeg;base64
                actualBase64 = parts[1];
                
                // 提取内容类型
                if (header.startsWith("data:") && header.contains(";")) {
                    contentType = header.substring(5, header.indexOf(";"));
                }
            } else {
                actualBase64 = base64Data;
            }

            // 根据内容类型确定文件扩展名
            String fileExtension = getFileExtension(contentType);
            
            // 生成文件名
            String fileName = generateFileName(fileExtension);
            
            // 获取临时目录
            String tempDir = SettingUtils.getTempDir();
            String filePath = tempDir + File.separator + fileName;

            // 使用hutool解码base64
            byte[] bytes = Base64.decode(actualBase64);
            
            // 创建文件
            File file = new File(filePath);
            
            // 确保父目录存在
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(file)) {
                fos.write(bytes);
                fos.flush();
            }
            
            log.info("base64转换为文件成功: {}, 类型: {}, 大小: {} 字节", filePath, contentType, bytes.length);
            return file;
            
        } catch (Exception e) {
            log.error("base64转换为文件失败: {}", e.getMessage(), e);
            throw new BussinessException(CommonErrorCodeConstant.BASE64_CONVERT_FAILED, e);
        }
    }
    
    /**
     * 根据内容类型获取文件扩展名
     */
    private static String getFileExtension(String contentType) {
        switch (contentType.toLowerCase()) {
            case "image/jpeg":
            case "image/jpg":
                return ".jpg";
            case "image/png":
                return ".png";
            case "image/gif":
                return ".gif";
            case "image/webp":
                return ".webp";
            case "image/bmp":
                return ".bmp";
            case "image/svg+xml":
                return ".svg";
            case "image/tiff":
                return ".tiff";
            case "application/pdf":
                return ".pdf";
            case "text/plain":
                return ".txt";
            default:
                return ".jpg"; // 默认为jpg
        }
    }
    
    /**
     * 生成唯一文件名
     */
    private static String generateFileName(String extension) {
        return "upload_" + System.currentTimeMillis() + "_" + 
               UUID.randomUUID().toString().replace("-", "").substring(0, 8) + extension;
    }
} 