package com.sinitek.mind.support.openapi.service.impl;

import com.sinitek.mind.support.openapi.constant.OpenApiErrorCodeConstant;
import com.sinitek.mind.support.openapi.dto.OpenApiAuthResultDTO;
import com.sinitek.mind.support.openapi.entity.OpenApi;
import com.sinitek.mind.support.openapi.service.IOpenApiAuthService;
import com.sinitek.mind.support.openapi.service.IOpenApiService;
import com.sinitek.mind.support.openapi.util.ApiKeyUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.service.IOrgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

/**
 * OpenAPI认证服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@Slf4j
public class OpenApiAuthServiceImpl implements IOpenApiAuthService {
    
    @Autowired
    private IOpenApiService openApiService;

    @Autowired
    private IOrgService orgService;

    /**
     * 认证API密钥
     */
    @Override
    public OpenApiAuthResultDTO authenticateApiKey(String apiKey) {
        // 1. 验证API Key格式
        if (!ApiKeyUtil.isValidApiKeyFormat(apiKey)) {
            throw new BussinessException(OpenApiErrorCodeConstant.INVALID_API_KEY_FORMAT);
        }
        
        // 2. 查询API Key
        Optional<OpenApi> openApiOpt = openApiService.getByApiKey(apiKey.trim());
        if (!openApiOpt.isPresent()) {
            throw new BussinessException(OpenApiErrorCodeConstant.INVALID_API_KEY);
        }
        
        OpenApi openApi = openApiOpt.get();
        
        // 3. 检查过期时间
        validateExpiration(openApi);

        // 4. 异步更新最后使用时间
        openApiService.updateLastUsedTime(openApi.getId());
        
        return OpenApiAuthResultDTO.builder()
            .tmbId(openApi.getOrgId())
            .appId(openApi.getAppId() != null ? openApi.getAppId() : 0L)
            .sourceName(openApi.getName())
            .apiKey(apiKey)
            .build();
    }

    /**
     * 验证过期时间
     */
    private void validateExpiration(OpenApi openApi) {
        if (openApi != null &&
            openApi.getExpiredTime() != null &&
            openApi.getExpiredTime().before(new Date())) {
            throw new BussinessException(OpenApiErrorCodeConstant.API_KEY_EXPIRED);
        }
    }

} 