package com.sinitek.mind.support.openapi.service.impl;

import com.sinitek.mind.support.openapi.constant.OpenApiErrorCodeConstant;
import com.sinitek.mind.support.openapi.dto.OpenApiAuthResultDTO;
import com.sinitek.mind.support.openapi.entity.OpenApi;
import com.sinitek.mind.support.openapi.service.IOpenApiAuthService;
import com.sinitek.mind.support.openapi.service.IOpenApiService;
import com.sinitek.mind.support.openapi.util.ApiKeyUtil;
import com.sinitek.sirm.common.um.RequestUser;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

/**
 * OpenAPI认证服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@Slf4j
public class OpenApiAuthServiceImpl implements IOpenApiAuthService {
    
    @Autowired
    private IOpenApiService openApiService;

    @Autowired
    private IOrgService orgService;

    /**
     * 认证API密钥
     */
    @Override
    public OpenApiAuthResultDTO authenticateApiKey(String apiKey) {
        // 1. 验证API Key格式
        if (!ApiKeyUtil.isValidApiKeyFormat(apiKey)) {
            throw new BussinessException(OpenApiErrorCodeConstant.INVALID_API_KEY_FORMAT);
        }
        
        // 2. 查询API Key
        Optional<OpenApi> openApiOpt = openApiService.getByApiKey(apiKey.trim());
        if (!openApiOpt.isPresent()) {
            throw new BussinessException(OpenApiErrorCodeConstant.INVALID_API_KEY);
        }
        
        OpenApi openApi = openApiOpt.get();
        
        // 3. 检查过期时间
        validateExpiration(openApi);
        
        // 4. 检查使用量限制
        checkUsageLimit(openApi);

        
        // 5. 异步更新最后使用时间
        openApiService.updateLastUsedTime(openApi.getId());
        
        return OpenApiAuthResultDTO.builder()
            .teamId(openApi.getTeamId())
            .tmbId(openApi.getTmbId())
            .appId(openApi.getAppId() != null ? openApi.getAppId() : "")
            .sourceName(openApi.getName())
            .apiKey(apiKey)
            .build();
    }

    @Override
    public RequestUser generateUserInfo(String apiKey) {
        // 1. 验证API Key格式
        if (!ApiKeyUtil.isValidApiKeyFormat(apiKey)) {
            throw new BussinessException(OpenApiErrorCodeConstant.INVALID_API_KEY_FORMAT);
        }

        // 2. 查询API Key
        Optional<OpenApi> openApiOpt = openApiService.getByApiKey(apiKey.trim());
        if (!openApiOpt.isPresent()) {
            throw new BussinessException(OpenApiErrorCodeConstant.INVALID_API_KEY);
        }

        OpenApi openApi = openApiOpt.get();
        RequestUser user = new RequestUser();

        user.setOrgId(openApi.getTmbId());
        user.setTenantId(openApi.getTeamId());

        Employee employee = orgService.getEmployeeById(openApi.getTmbId());
        if (employee != null) {
            user.setUserId(employee.getUserId());
            user.setName(employee.getUserName());
            user.setDisplayName(employee.getEmpName());
            user.setUserProperties(employee.getAllUserProperties());
        }

        return user;
    }

    /**
     * 验证过期时间
     */
    private void validateExpiration(OpenApi openApi) {
        if (openApi.getLimit() != null && 
            openApi.getLimit().getExpiredTime() != null &&
            openApi.getLimit().getExpiredTime().before(new Date())) {
            throw new BussinessException(OpenApiErrorCodeConstant.API_KEY_EXPIRED);
        }
    }
    
    /**
     * 检查使用量限制
     */
    private void checkUsageLimit(OpenApi openApi) {
        if (openApi.getLimit() == null) {
            return; // 无限制
        }
        
        Long maxUsagePoints = openApi.getLimit().getMaxUsagePoints();
        if (maxUsagePoints != null && maxUsagePoints > 0) {
            Long currentUsage = openApi.getUsagePoints();
            if (currentUsage >= maxUsagePoints) {
                throw new BussinessException(OpenApiErrorCodeConstant.USAGE_LIMIT_EXCEEDED, 
                    currentUsage, maxUsagePoints);
            }
        }
    }
} 