package com.sinitek.mind.support.permission.dto;

import com.sinitek.mind.support.permission.constant.PermissionConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.util.Map;

/**
 * 权限DTO
 * <AUTHOR>
 * @date 2025/7/8
 */
@Getter
@Schema(description = "权限DTO")
public class PermissionDTO {

    @Schema(description = "是否有管理权限")
    protected Boolean hasManagePer = false;

    @Schema(description = "是否有读权限")
    protected Boolean hasReadPer = false;

    @Schema(description = "是否有写权限")
    protected Boolean hasWritePer = false;

    @Schema(description = "是否拥有者")
    protected Boolean isOwner = false;

    @Schema(description = "权限值,使用基本类型，防止npe")
    protected long value = PermissionConstant.NULL_PERMISSION;

    @Schema(description = "具体的权限信息")
    protected Map<String, PermissionItemDTO> _permissionList = PermissionConstant.PERMISSION_LIST;

    public PermissionDTO() {
    }

    public PermissionDTO(long value) {
        setValue(value);
    }

    public PermissionDTO(long value, boolean isOwner) {
        if (isOwner) {
            setValue(PermissionConstant.OWNER_PERMISSION_VAL);
        } else {
            setValue(value);
        }
    }

    /**
     * 权限校验，判断是否有对应的权限
     * @param per 需要判断的权限值
     * @return true-有权限，false-无权限
     */
    public boolean checkPer(long per) {
        if (per == PermissionConstant.OWNER_PERMISSION_VAL) {
            return this.value == PermissionConstant.OWNER_PERMISSION_VAL;
        }

        return (per & this.value) == per;
    }

    /**
     * 通过设置权限值，自动设置其他字段
     * @param per 权限值
     */
    public void setValue(long per) {
        this.value = per;
        if (per == PermissionConstant.OWNER_PERMISSION_VAL) {
            this.hasReadPer = true;
            this.hasWritePer = true;
            this.hasManagePer = true;
            this.isOwner = true;
        } else {
            this.hasReadPer = (per & PermissionConstant.READ_PER) == PermissionConstant.READ_PER;
            this.hasWritePer = (per & PermissionConstant.WRITE_PER) == PermissionConstant.WRITE_PER;
            this.hasManagePer = (per & PermissionConstant.MANAGER_PER) == PermissionConstant.MANAGER_PER;
            this.isOwner = false;
        }
    }
}
