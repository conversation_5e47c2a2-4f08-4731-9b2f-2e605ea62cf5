package com.sinitek.mind.support.team.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.permission.dto.CollaboratorDTO;
import com.sinitek.mind.support.permission.dto.CollaboratorDeleteDTO;
import com.sinitek.mind.support.permission.dto.CollaboratorUpdateDTO;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.ICollaboratorService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedList;
import java.util.List;

/**
 * 团队协作者/权限tab页 Controller 层
 *
 * <AUTHOR>
 * @date 2025/7/9
 */
@RestController
@RequestMapping("/mind/api/support/user/team/collaborator")
@Tag(name = "部门/组织管理")
@RequiredArgsConstructor
public class TeamCollaboratorController {

    @Autowired
    private ICollaboratorService collaboratorService;
    
    @GetMapping("/list")
    @Operation(summary = "获取团队协作者列表")
    public ApiResponse<List<CollaboratorDTO>> getCollaboratorList() {
        String tenantId = CurrentUserFactory.getTenantId();
        return ApiResponse.success(collaboratorService.findClb(tenantId, ResourceTypeEnum.TEAM));
    }

    @PostMapping("/update")
    @Operation(summary = "更新团队协作者")
    public ApiResponse<Void> updateCollaborator(@RequestBody CollaboratorUpdateDTO updateDTO) {
        String tenantId = CurrentUserFactory.getTenantId();

        List<String> orgIdList = new LinkedList<>();
        orgIdList.addAll(updateDTO.getOrgs());
        orgIdList.addAll(updateDTO.getMembers());
        orgIdList.addAll(updateDTO.getGroups());
        collaboratorService.updateClb(tenantId, ResourceTypeEnum.TEAM, orgIdList, updateDTO.getPermission());
        return ApiResponse.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除团队协作者")
    public ApiResponse<Void> updateCollaborator(@RequestBody CollaboratorDeleteDTO deleteDTO) {
        String tenantId = CurrentUserFactory.getTenantId();

        String orgId = "";
        if (StringUtils.isNotBlank(deleteDTO.getTmbId())) {
            orgId = deleteDTO.getTmbId();
        } else if (StringUtils.isNotBlank(deleteDTO.getGroupId())) {
            orgId = deleteDTO.getGroupId();
        } else if (StringUtils.isNotBlank(deleteDTO.getOrgId())) {
            orgId = deleteDTO.getOrgId();
        }

        collaboratorService.deleteClb(tenantId, ResourceTypeEnum.TEAM, orgId);
        return ApiResponse.success();
    }

}
