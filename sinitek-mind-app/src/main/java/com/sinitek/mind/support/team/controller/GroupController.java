package com.sinitek.mind.support.team.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.team.dto.GroupDTO;
import com.sinitek.mind.support.team.dto.GroupListRequest;
import com.sinitek.mind.support.team.service.IGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 群组管理 Controller 层
 *
 * <AUTHOR>
 * @date 2025/7/7
 * 描述：群组管理相关接口
 */
@RestController
@RequestMapping("/mind/api/support/user/team/group")
@Tag(name = "群组管理")
@RequiredArgsConstructor
public class GroupController {
    
    private final IGroupService groupService;
    
    @PostMapping("/list")
    @Operation(summary = "获取群组列表")
    public ApiResponse<List<GroupDTO>> getGroupList(
            @RequestBody GroupListRequest request) {
        List<GroupDTO> response = groupService.getGroupList(request);
        return ApiResponse.success(response);
    }
    
//    @PostMapping("/create")
//    @Operation(summary = "创建群组")
//    public ApiResponse<Void> createGroup(
//            @RequestBody CreateGroupRequest request) {
//        groupService.createGroup(request);
//        return ApiResponse.success();
//    }
//
//    @PostMapping("/delete")
//    @Operation(summary = "删除群组")
//    public ApiResponse<Void> deleteGroup(
//            @RequestBody DeleteGroupRequest request) {
//        groupService.deleteGroup(request);
//        return ApiResponse.success();
//    }
//
//    @PostMapping("/update")
//    @Operation(summary = "更新群组信息")
//    public ApiResponse<Void> updateGroup(
//            @RequestBody UpdateGroupRequest request) {
//        groupService.updateGroup(request);
//        return ApiResponse.success();
//    }
//
//    @GetMapping("/members")
//    @Operation(summary = "获取群组成员")
//    public ApiResponse<List<GroupMemberDTO>> getGroupMembers(
//            @Parameter(description = "群组ID") @RequestParam String groupId) {
//        List<GroupMemberDTO> response = groupService.findGroupMembers(groupId);
//        return ApiResponse.success(response);
//    }
//
//    @PostMapping("/changeOwner")
//    @Operation(summary = "转让群组所有权")
//    public ApiResponse<Void> changeGroupOwner(
//            @RequestBody ChangeGroupOwnerRequest request) {
//        groupService.changeGroupOwner(request);
//        return ApiResponse.success();
//    }
} 