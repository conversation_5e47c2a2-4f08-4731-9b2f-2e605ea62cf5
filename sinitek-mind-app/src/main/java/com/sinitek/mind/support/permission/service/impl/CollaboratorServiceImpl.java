package com.sinitek.mind.support.permission.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.mind.core.app.dao.AppDAO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.dataset.repository.DatasetRepository;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.permission.constant.PermissionErrorCodeConstant;
import com.sinitek.mind.support.permission.dto.*;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.ICollaboratorService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.dto.OrgObjectDTO;
import com.sinitek.sirm.org.enumerate.OrgType;
import com.sinitek.sirm.org.service.IOrgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CollaboratorServiceImpl implements ICollaboratorService {

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private IAccountService accountService;

    private final AppDAO appDAO;

    @Autowired
    private DatasetRepository datasetRepository;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IAuthService authService;

    @Override
    public List<CollaboratorPermissionDTO> getResourceClbsAndGroups(String resourceId, ResourceTypeEnum resourceType) {
        Map<String, Permission> allAuthedOrg = permissionService.findAllAuthedOrg(resourceId, resourceType);

        if (CollUtil.isEmpty(allAuthedOrg)) {
            return List.of();
        }

        return allAuthedOrg.entrySet().stream()
                .map(entry -> {
                    String orgId = entry.getKey();
                    Permission permission = entry.getValue();

                    CollaboratorPermissionDTO dto = new CollaboratorPermissionDTO();
                    dto.setOrgId(orgId);
                    dto.setPer(permission.getPermission());
                    return dto;
                })
                .toList();
    }

    @Override
    public void syncCollaborators(String resourceId, ResourceTypeEnum resourceType, List<CollaboratorPermissionDTO> collaborators) {
        permissionService.deletePermission(resourceId, resourceType);

        if (CollUtil.isEmpty(collaborators)) {
            // 没有需要同步的协作者
            return;
        }

        // 按照权限值进行分组后，在进行保存操作
        collaborators.stream()
                .collect(Collectors.groupingBy(CollaboratorPermissionDTO::getPer))
                .forEach((per, list) -> {
                    List<String> orgIdList = list.stream()
                            .map(CollaboratorPermissionDTO::getOrgId)
                            .toList();
                    permissionService.savePermission(resourceId, resourceType, orgIdList, per);
                });
    }

    @Override
    public void syncChildrenPermission(App app, List<String> folderTypeList, List<CollaboratorPermissionDTO> collaborators) {
        // only folder has permission
        boolean isFolder = folderTypeList.contains(app.getType());

        if (!isFolder) {
            return;
        }

        // get all folders and the resource permission of the app
        List<App> allFolders = appDAO.findByTypeInAndInheritPermissionTrue(folderTypeList);

        // bfs to get all children
        Queue<String> queue = new LinkedList<>();
        queue.offer(String.valueOf(app.getId()));
        List<String> children = new LinkedList<>();
        while (!queue.isEmpty()) {
            String parentId = queue.poll();
            List<String> list = allFolders.stream()
                    .map(App::getId)
                    .filter(id -> id.equals(parentId))
                    .map(String::valueOf)
                    .toList();
            children.addAll(list);
            list.forEach(queue::offer);
        }
        if (children.isEmpty()) {
            return;
        }

        // sync the resource permission
        if (CollUtil.isEmpty(collaborators)) {
            return;
        }

        // 同步所有下级
        children.forEach(childId -> {
            CompletableFuture.runAsync(() -> {
                syncCollaborators(childId, ResourceTypeEnum.APP, collaborators);
            });
        });
    }

    @Override
    public void syncChildrenPermission(Dataset dataset, List<String> folderTypeList, List<CollaboratorPermissionDTO> collaborators) {
// only folder has permission
        boolean isFolder = folderTypeList.contains(dataset.getType());

        if (!isFolder) {
            return;
        }

        // get all folders and the resource permission of the app
        List<Dataset> allFolders = datasetRepository.findByTypeInAndInheritPermissionTrue(folderTypeList);

        // bfs to get all children
        Queue<String> queue = new LinkedList<>();
        queue.offer(dataset.getId());
        List<String> children = new LinkedList<>();
        while (!queue.isEmpty()) {
            String parentId = queue.poll();
            List<String> list = allFolders.stream()
                    .map(Dataset::getId)
                    .filter(id -> id.equals(parentId))
                    .toList();
            children.addAll(list);
            list.forEach(queue::offer);
        }
        if (children.isEmpty()) {
            return;
        }

        // sync the resource permission
        if (CollUtil.isEmpty(collaborators)) {
            return;
        }

        // 同步所有下级
        children.forEach(childId -> {
            CompletableFuture.runAsync(() -> {
                syncCollaborators(childId, ResourceTypeEnum.DATASET, collaborators);
            });
        });
    }


    /**
     * 获取指定资源的协作者 只有app和dataset可用
     *
     * @param resourceId       资源id
     * @param resourceTypeEnum 资源类型
     */
    @Override
    public List<CollaboratorDTO> findClb(String resourceId,
                                         ResourceTypeEnum resourceTypeEnum) {
        Map<String, Permission> allAuthedOrg = permissionService.findAllAuthedOrg(
                resourceId, resourceTypeEnum);

        if (CollUtil.isEmpty(allAuthedOrg)) {
            return List.of();
        }

        AuthDTO authDTO = authService.authCert();

        List<String> orgIdList = allAuthedOrg.keySet().stream().toList();
        List<OrgObjectDTO> orgObjectDTOList = orgService.findOrgObjectsByOrgIds(orgIdList);
        Map<String, OrgObjectDTO> orgIdMap = orgObjectDTOList.stream()
                .collect(Collectors.toMap(OrgObjectDTO::getOrgId, Function.identity()));

        return allAuthedOrg.entrySet().stream()
                .map(entry -> {
                    CollaboratorDTO dto = new CollaboratorDTO();
                    dto.setTeamId(authDTO.getTeamId());
                    PermissionDTO permissionDTO = new PermissionDTO();
                    permissionDTO.setValue(entry.getValue().getPermission());
                    dto.setPermission(permissionDTO);

                    OrgObjectDTO orgObjectDTO = MapUtils.getObject(orgIdMap, entry.getKey(), null);
                    if (orgObjectDTO == null) {
                        log.error("orgId:{}，找不到对应的组织", entry.getKey());
                        throw new BussinessException(PermissionErrorCodeConstant.ORG_NOT_FOUND_BY_ORGID);
                    }

                    int orgType = orgObjectDTO.getOrgType();
                    String orgId = orgObjectDTO.getOrgId();
                    if (orgType == OrgType.DEPT.getEnumItemValue()) {
                        // 部门
                        dto.setOrgId(orgId);
                        dto.setName(orgObjectDTO.getOrgName());
                    } else if (orgType == OrgType.ROLE.getEnumItemValue()) {
                        // 角色
                        dto.setGroupId(orgId);
                        dto.setName(orgObjectDTO.getOrgName());
                    } else {
                        // 成员
                        dto.setTmbId(orgId);
                        dto.setName(orgObjectDTO.getOrgName());
                        dto.setAvatar(accountService.getAvatarByOrgId(orgId));
                    }
                    return dto;
                })
                .toList();
    }

    /**
     * 更新协作者 当资源类型为team时，资源id则为teamId
     *
     * @param resourceId       资源id
     * @param resourceTypeEnum 资源类型
     * @param permission       权限值-需要设置的权限值，可查看PermissionConstant中的定义
     * @param orgIdList        授权的对象，成员id、部门id、群组id
     */
    @Override
    public void updateClb(String resourceId, ResourceTypeEnum resourceTypeEnum, List<String> orgIdList, long permission) {
        permissionService.savePermission(resourceId, resourceTypeEnum, orgIdList, permission);
    }

    /**
     * 删除协作者 当资源类型为team时，资源id则为teamId
     *
     * @param resourceId       资源id
     * @param resourceTypeEnum 资源类型
     * @param orgId            需要删除的对象，成员id、部门id、群组id
     */
    @Override
    public void deleteClb(String resourceId, ResourceTypeEnum resourceTypeEnum,
                          String orgId) {
        permissionService.deletePermission(resourceId, resourceTypeEnum, List.of(orgId));
    }

    @Override
    public Map<String, Integer> findClbNumberMap(List<String> resourceIdList, ResourceTypeEnum resourceTypeEnum) {
        return permissionService.findAllAuthedOrgNumber(resourceIdList, resourceTypeEnum);
    }
}