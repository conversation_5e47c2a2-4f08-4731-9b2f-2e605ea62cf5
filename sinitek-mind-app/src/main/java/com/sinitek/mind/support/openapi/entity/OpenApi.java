package com.sinitek.mind.support.openapi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * OpenAPI密钥实体类
 * 对应FastGPT中的OpenApiSchema
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Document(collection = "openapis")
@TableName("mind_open_api")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenApi extends BaseEntity {

    @Schema(description = "所属用户")
    private String orgId;
    
    /**
     * API密钥，格式：sinitek-xxxx
     */
    @Schema(description = "api密钥")
    private String apiKey;

    /**
     * 关联的应用ID（可选）
     */
    @Schema(description = "关联的应用ID（可选）")
    private Long appId;
    
    /**
     * 密钥名称
     */
    @Schema(description = "密钥名称")
    private String name;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private Date expiredTime;

    /**
     * 最后使用时间
     */
    @Schema(description = "最后使用时间")
    private Date lastUsedTime;

} 