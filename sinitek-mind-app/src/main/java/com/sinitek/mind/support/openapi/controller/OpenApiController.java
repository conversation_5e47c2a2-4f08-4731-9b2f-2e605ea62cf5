package com.sinitek.mind.support.openapi.controller;

import com.sinitek.mind.common.dto.IdDTO;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.openapi.dto.ApiKeySearchResultDTO;
import com.sinitek.mind.support.openapi.dto.CreateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.dto.UpdateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.service.IOpenApiService;
import com.sinitek.mind.support.permission.service.IAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * OpenAPI管理控制器
 * 与FastGPT的API接口保持一致
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/mind/api/support/openapi")
@Tag(name = "API密钥管理")
@Slf4j
public class OpenApiController {
    
    @Autowired
    private IOpenApiService openApiService;

    @Autowired
    private IAuthService authService;

    /**
     * 创建API密钥
     * 对应 FastGPT: POST /support/openapi/create
     */
    @PostMapping("/create")
    @Operation(summary = "创建API密钥")
    public ApiResponse<String> createApiKey(@RequestBody CreateApiKeyRequestDTO request) {
        String apiKey = openApiService.createApiKey(request);
        return ApiResponse.success(apiKey);
    }
    
    /**
     * 获取API密钥列表
     * 对应 FastGPT: GET /support/openapi/list?appId=xxx
     */
    @GetMapping("/list")
    @Operation(summary = "获取API密钥列表")
    public ApiResponse<List<ApiKeySearchResultDTO>> listApiKeys(
            @RequestParam(required = false) String appId) {
        List<ApiKeySearchResultDTO> apiKeys = openApiService.listApiKeys(appId);
        return ApiResponse.success(apiKeys);
    }
    
    /**
     * 更新API密钥
     * 对应 FastGPT: PUT /support/openapi/update
     */
    @PostMapping("/update")
    @Operation(summary = "更新API密钥")
    public ApiResponse<String> updateApiKey(@RequestBody UpdateApiKeyRequestDTO request) {
        openApiService.updateApiKey(request);
        return ApiResponse.success("更新成功");
    }
    
    /**
     * 删除API密钥
     * 对应 FastGPT: DELETE /support/openapi/delete?id=xxx
     */
    @PostMapping("/delete")
    @Operation(summary = "删除API密钥")
    public ApiResponse<String> deleteApiKey(@RequestBody IdDTO dto) {
        openApiService.deleteApiKey(dto.getId());
        return ApiResponse.success("删除成功");
    }
} 