package com.sinitek.mind.support.common.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 文件处理模块错误码常量类
 * 
 * 错误码规则：30-13-XXXX
 * - 30: Mind项目固定前缀
 * - 13: 文件处理模块编码
 * - XXXX: 具体错误编码，从0001开始递增
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public final class FileErrorCodeConstant {

    // ==================== 文件处理基础错误 (301301XX) ====================

    /**
     * 处理文件URL时发生错误
     */
    public static final String PROCESS_FILE_URL_ERROR = "30130101";

    /**
     * 从HTTP URL获取文件失败
     */
    public static final String FETCH_FILE_FROM_HTTP_ERROR = "30130102";

    /**
     * 获取原始文本缓冲区失败
     */
    public static final String GET_RAW_TEXT_BUFFER_ERROR = "30130103";

    /**
     * 从缓冲区读取文件内容失败
     */
    public static final String READ_FILE_FROM_BUFFER_ERROR = "30130104";

    /**
     * 从缓冲区创建临时文件失败
     */
    public static final String CREATE_TEMP_FILE_FROM_BUFFER_ERROR = "30130105";

    /**
     * 添加原始文本缓冲区失败
     */
    public static final String ADD_RAW_TEXT_BUFFER_ERROR = "30130106";

    /**
     * sourceId格式错误
     */
    public static final String SOURCE_ID_FORMAT_ERROR = "30130107";

    /**
     * 文件未找到
     */
    public static final String FILE_NOT_FOUND = "30130108";

    /**
     * 文件名URL解码失败
     */
    public static final String FILENAME_URL_DECODE_ERROR = "30130109";

    /**
     * 从bucketName获取文件失败
     */
    public static final String GET_FILE_FROM_BUCKET_ERROR = "30130110";

    /**
     * 从bucketName获取基础文件信息失败
     */
    public static final String GET_FILE_INFO_FROM_BUCKET_ERROR = "30130111";

    /**
     * 上传文件到bucketName失败
     */
    public static final String UPLOAD_FILE_TO_BUCKET_ERROR = "30130112";

    /**
     * 从bucketName获取文件信息失败
     */
    public static final String GET_FILE_METADATA_FROM_BUCKET_ERROR = "30130113";

    /**
     * 文件令牌验证失败
     */
    public static final String FILE_TOKEN_VERIFY_ERROR = "30130114";

}