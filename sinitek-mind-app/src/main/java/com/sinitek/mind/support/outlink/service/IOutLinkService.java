package com.sinitek.mind.support.outlink.service;

import com.sinitek.mind.support.outlink.dto.*;
import com.sinitek.mind.support.outlink.entity.OutLink;

import java.util.List;

/**
 * 免登录链接服务接口
 */
public interface IOutLinkService {

    /**
     * 创建免登录链接
     *
     * @param request 创建请求
     * @return 24位随机生成的shareId
     */
    String createOutLink(CreateOutLinkRequest request);

    /**
     * 查询免登录链接列表
     *
     * @param appId 应用ID
     * @param type 发布渠道类型
     * @return 免登录链接列表
     */
    List<OutLinkResponse> getOutLinkList(String appId, String type);

    /**
     * 更新免登录链接
     *
     * @param request 更新请求
     */
    void updateOutLink(UpdateOutLinkRequest request);

    /**
     * 删除免登录链接
     *
     * @param id 链接数据库ID
     */
    void deleteOutLink(String id);

    /**
     * 初始化免登录聊天
     *
     * @param shareId 分享链接ID
     * @param outLinkUid 外链用户ID
     * @param chatId 聊天ID（可选）
     * @return 聊天初始化响应
     */
    ChatInitResponse initChat(String shareId, String outLinkUid, String chatId, String authToke);

    /**
     * 根据shareId获取应用信息
     *
     * @param shareId 分享链接ID
     * @return 应用信息DTO
     */
    AppInfoDTO getAppByShareId(String shareId);

    /**
     * 添加外链消耗
     * @param shareId
     * @param totalPoints
     */
    void addOutLinkUsage(String shareId, int totalPoints);

    /**
     * 获取outlink
     * @param shareId
     * @return
     */
    OutLink getById(String shareId);
} 