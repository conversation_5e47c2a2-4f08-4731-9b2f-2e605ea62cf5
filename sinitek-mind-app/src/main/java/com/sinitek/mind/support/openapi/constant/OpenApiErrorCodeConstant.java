package com.sinitek.mind.support.openapi.constant;

/**
 * OpenApi模块错误码常量类
 * 
 * 错误码规则：30-10-YYYY
 * 30 - Mind项目固定前缀
 * 10 - OpenApi模块编码
 * YYYY - 具体错误编码
 *
 * <AUTHOR>
 * date 2025-08-15
 */
public class OpenApiErrorCodeConstant {

    /**
     * API密钥不存在
     */
    public static final String API_KEY_NOT_FOUND = "30100001";

    /**
     * 无效的API密钥格式
     */
    public static final String INVALID_API_KEY_FORMAT = "30100002";

    /**
     * 无效的API密钥
     */
    public static final String INVALID_API_KEY = "30100003";

    /**
     * API密钥已过期
     */
    public static final String API_KEY_EXPIRED = "30100004";

    /**
     * 使用量超过限制
     */
    public static final String USAGE_LIMIT_EXCEEDED = "30100005";

    /**
     * 缺少必要参数
     */
    public static final String MISSING_REQUIRED_PARAMETER = "30100006";

    /**
     * 应用不存在
     */
    public static final String APP_NOT_FOUND = "30100007";

    /**
     * 缺少API密钥认证头
     */
    public static final String MISSING_API_KEY_HEADER = "30100008";

}