package com.sinitek.mind.support.account.service;

import com.sinitek.mind.support.account.dto.SourceMemberDTO;

import java.util.List;
import java.util.Map;

/**
 * 账户服务接口
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
public interface IAccountService {

    /**
     * 使用tmbId（员工的orgId）获取头像
     * @param orgId orgId
     * @return 头像
     */
    String getAvatarByOrgId(String orgId);

    /**
     * 使用tmbId（员工的orgId）获取sourceMember
     * @return sourceMember
     */
    SourceMemberDTO getSourceMemberByOrgId(String orgId);

    /**
     * 批量通过tmbId（员工的orgId）获取sourceMember
     * @return sourceMember
     */
    Map<String, SourceMemberDTO> getSourceMemberMapByOrgIds(List<String> orgIds);
}