package com.sinitek.mind.support.team.constant;

/**
 * 团队模块错误码常量类
 * <p>
 * 模块编码：30-12
 * 错误码范围：30120001-30129999
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
public final class TeamErrorCodeConstant {

    /**
     * 私有构造方法，防止实例化
     */
    private TeamErrorCodeConstant() {
    }

    /**
     * 部门没有岗位
     * <p>
     * 当部门下没有岗位时抛出此异常
     * 错误码：30120001
     * </p>
     */
    public static final String DEPT_NO_POSITION = "30120001";

    /**
     * 群组不存在
     * <p>
     * 当根据ID查询群组，但群组不存在时抛出此异常
     * 错误码：30120002
     * </p>
     */
    public static final String GROUP_NOT_FOUND = "30120002";

    /**
     * 成员不存在
     * <p>
     * 当根据ID查询成员，但成员不存在时抛出此异常
     * 错误码：30120003
     * </p>
     */
    public static final String MEMBER_NOT_FOUND = "30120003";

    /**
     * 成员不存在(带ID)
     * <p>
     * 当需要显示具体成员ID的错误信息时使用此错误码
     * 支持参数化消息，使用{0}占位符显示成员ID
     * 错误码：30120004
     * </p>
     */
    public static final String MEMBER_NOT_FOUND_WITH_ID = "30120004";

    /**
     * 群组管理员不能修改
     * <p>
     * 当尝试修改管理员角色时抛出此异常
     * 错误码：30120005
     * </p>
     */
    public static final String GROUP_ADMIN_CANNOT_UPDATE = "30120005";

}