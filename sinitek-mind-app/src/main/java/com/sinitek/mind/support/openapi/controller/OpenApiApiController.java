package com.sinitek.mind.support.openapi.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.openapi.dto.CreateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.service.IOpenApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * OpenApi相关的OpenApi接口
 */
@Slf4j
@RestController
@RequestMapping("/mind/open-api/support/openapi")
@RequiredArgsConstructor
@Tag(name = "对话管理", description = "聊天相关接口")
public class OpenApiApiController {

    @Autowired
    private IOpenApiService openApiService;

    /**
     * 创建API密钥
     * 对应 FastGPT: POST /support/openapi/create
     */
    @PostMapping("/create")
    @Operation(summary = "创建API密钥")
    public ApiResponse<String> createApiKey(@RequestBody CreateApiKeyRequestDTO request) {
        String apiKey = openApiService.createApiKey(request);
        return ApiResponse.success(apiKey);
    }

}