package com.sinitek.mind.support.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 图片上传请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@Schema(description = "图片上传请求")
public class UploadImageRequest {

    @NotBlank(message = "{validation.base64.image.not.empty}")
    @Schema(description = "base64编码的图片数据，格式：data:image/jpeg;base64,/9j/...", required = true)
    private String base64Img;
} 