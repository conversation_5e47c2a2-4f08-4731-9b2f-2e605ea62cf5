package com.sinitek.mind.support.outlink.entity;

import com.sinitek.mind.core.app.model.Limit;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.util.Date;

@Data
@Document(collection = "outlinks")
public class OutLink {
    @Id
    private String id;

    private String shareId;

    private String teamId;

    private String tmbId;

    @Field(targetType = FieldType.OBJECT_ID)
    private String appId;

    private String type;

    private String name;

    private int usagePoints = 0;

    private Date lastTime;

    private Boolean responseDetail = false;

    private Boolean showNodeStatus = false;

    private Boolean showRawSource = false;

    private Limit limit;

    private Object app; // 可根据具体类型替换

    private String immediateResponse;

    private String defaultResponse;
}
