package com.sinitek.mind.support.outlink.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mind_out_link")
@Schema(description = "免登录链接实体")
public class OutLink extends BaseEntity {

    @Schema(description = "分享链接ID")
    private String shareId;

    @Schema(description = "组织ID")
    private String orgId;

    @Schema(description = "应用ID")
    private Long appId;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "最后使用时间")
    private Date lastTime;

    @Schema(description = "是否响应详情")
    private Boolean responseDetail = false;

    @Schema(description = "是否显示节点状态")
    private Boolean showNodeStatus = false;

    @Schema(description = "是否显示原始来源")
    private Boolean showRawSource = false;

    @Schema(description = "过期时间")
    private Date expiredTime;

    @Schema(description = "每分钟查询限制")
    @JsonProperty("QPM")
    private int qpm = 1000;

    @Schema(description = "钩子URL")
    private String hookUrl;

    @Schema(description = "立即响应")
    private String immediateResponse;

    @Schema(description = "默认响应")
    private String defaultResponse;
}
