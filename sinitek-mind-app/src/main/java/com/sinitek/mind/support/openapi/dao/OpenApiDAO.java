package com.sinitek.mind.support.openapi.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.mind.support.openapi.entity.OpenApi;
import com.sinitek.mind.support.openapi.mapper.OpenApiMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class OpenApiDAO extends ServiceImpl<OpenApiMapper, OpenApi> {

    public List<OpenApi> findByOrgIdAndAppId(String orgId, String appId) {
        return baseMapper.findByOrgIdAndAppId(orgId, appId);
    }

    public List<OpenApi> findByOrgIdAndAppIdIsNull(String orgId) {
        return baseMapper.findByOrgIdAndAppIdIsNull(orgId);
    }

    public Optional<OpenApi> getByApiKey(String apiKey) {
        return Optional.ofNullable(baseMapper.getByApiKey(apiKey));
    }

    public void updateLastUsedTime(Long id, Date date) {
        baseMapper.updateLastUsedTime(id, date);
    }

    public void deleteByAppId(String appId) {
        if (StringUtils.isBlank(appId)) {
            return;
        }
        LambdaQueryWrapper<OpenApi> queryWrapper = Wrappers.lambdaQuery(OpenApi.class);
        queryWrapper.eq(OpenApi::getAppId, appId);

        remove(queryWrapper);
    }
}