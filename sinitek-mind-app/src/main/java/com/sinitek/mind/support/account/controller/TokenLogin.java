package com.sinitek.mind.support.account.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.account.constant.AccountErrorCodeConstant;
import com.sinitek.mind.support.account.dto.UserDTO;
import com.sinitek.mind.support.permission.dto.TeamPermissionDTO;
import com.sinitek.mind.support.team.dto.TeamMemberDTO;
import com.sinitek.mind.support.team.service.ITeamMemberService;
import com.sinitek.sirm.common.user.CurrentUserInfo;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/mind/api/support/user/account")
@Tag(name = "token登录")
public class TokenLogin {

    @Autowired
    private ITeamMemberService teamMemberService;

    @GetMapping("/tokenLogin")
    @Operation(summary = "获取用户信息")
    public ApiResponse<UserDTO> tokenLogin() {
        CurrentUserInfo currentUserInfo = CurrentUserFactory.getCurrentUserInfo();
        if (currentUserInfo == null) {
            log.error("用户未登录或会话已过期");
            throw new BussinessException(AccountErrorCodeConstant.USER_NOT_LOGIN_OR_SESSION_EXPIRED);
        }

        TeamMemberDTO teamMemberDTO = teamMemberService.getById(currentUserInfo.getOrgId());

        UserDTO res = UserDTO.builder()
                .id(teamMemberDTO.getUserId())
                .username(teamMemberDTO.getMemberName())
                .timezone("Asia/Shanghai")
                .contact(teamMemberDTO.getContact())
                .team(teamMemberDTO)
                .avatar(teamMemberDTO.getAvatar())
                .permission((TeamPermissionDTO) teamMemberDTO.getPermission())
                .notificationAccount(teamMemberDTO.getContact())
                .build();

        return ApiResponse.success(res);
    }
}
