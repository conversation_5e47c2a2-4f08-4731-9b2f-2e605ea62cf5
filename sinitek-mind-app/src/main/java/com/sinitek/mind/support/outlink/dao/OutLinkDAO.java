package com.sinitek.mind.support.outlink.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.mind.support.outlink.entity.OutLink;
import com.sinitek.mind.support.outlink.mapper.OutLinkMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

@Component
public class OutLinkDAO extends ServiceImpl<OutLinkMapper, OutLink> {

    /**
     * 根据应用ID删除免登录链接
     *
     * @param appId 应用ID
     */
    public void deleteByAppId(String appId) {
        if (!StringUtils.hasText(appId)) {
            return;
        }
        LambdaQueryWrapper<OutLink> queryWrapper = Wrappers.lambdaQuery(OutLink.class);
        queryWrapper.eq(OutLink::getAppId, appId);

        this.baseMapper.delete(queryWrapper);
    }

    /**
     * 根据应用ID和类型查询免登录链接列表
     *
     * @param appId 应用ID
     * @param type  发布渠道类型
     * @return 免登录链接列表
     */
    public List<OutLink> findByAppIdAndType(String appId, String type) {
        LambdaQueryWrapper<OutLink> queryWrapper = Wrappers.lambdaQuery(OutLink.class);
        queryWrapper.eq(OutLink::getAppId, appId);
        queryWrapper.eq(OutLink::getType, type);

        return list(queryWrapper);
    }

    /**
     * 根据shareId查询免登录链接
     *
     * @param shareId 分享链接ID
     * @return 免登录链接
     */
    public OutLink getByShareId(String shareId) {
        LambdaQueryWrapper<OutLink> queryWrapper = Wrappers.lambdaQuery(OutLink.class);
        queryWrapper.eq(OutLink::getShareId, shareId);
        return getOne(queryWrapper);
    }

}
