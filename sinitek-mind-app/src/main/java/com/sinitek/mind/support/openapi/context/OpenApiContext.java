package com.sinitek.mind.support.openapi.context;

import com.sinitek.mind.core.chat.enumerate.AuthUserTypeEnum;
import com.sinitek.mind.support.openapi.dto.OpenApiAuthResultDTO;
import org.springframework.core.NamedInheritableThreadLocal;

/**
 * OpenAPI认证上下文
 * 使用ThreadLocal存储当前线程的认证信息
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public class OpenApiContext {
    
    private static final ThreadLocal<OpenApiAuthResultDTO> CONTEXT = new NamedInheritableThreadLocal<>("OpenApi");

    private static final ThreadLocal<AuthUserTypeEnum> AUTH_TYPE = new NamedInheritableThreadLocal<>("OpenAPI-authType");

    /**
     * 设置当前线程的认证信息
     */
    public static void setAuth(OpenApiAuthResultDTO authResult, AuthUserTypeEnum authType) {
        CONTEXT.set(authResult);
        AUTH_TYPE.set(authType);
    }

    public static AuthUserTypeEnum getAuthType() {
        return AUTH_TYPE.get();
    }

    /**
     * 获取当前线程的认证信息
     */
    public static OpenApiAuthResultDTO getAuth() {
        return CONTEXT.get();
    }
    
    /**
     * 获取当前API密钥
     */
    public static String getApiKey() {
        OpenApiAuthResultDTO auth = getAuth();
        return auth != null ? auth.getApiKey() : null;
    }
    
    /**
     * 获取当前团队ID
     */
    public static String getTeamId() {
        OpenApiAuthResultDTO auth = getAuth();
        return auth != null ? auth.getTeamId() : null;
    }
    
    /**
     * 获取当前团队成员ID
     */
    public static String getTmbId() {
        OpenApiAuthResultDTO auth = getAuth();
        return auth != null ? auth.getTmbId() : null;
    }
    
    /**
     * 获取当前应用ID
     */
    public static Long getAppId() {
        OpenApiAuthResultDTO auth = getAuth();
        return auth != null ? auth.getAppId() : null;
    }
    
    /**
     * 获取当前来源名称
     */
    public static String getSourceName() {
        OpenApiAuthResultDTO auth = getAuth();
        return auth != null ? auth.getSourceName() : null;
    }
    
    /**
     * 清除当前线程的认证信息
     */
    public static void clear() {
        CONTEXT.remove();
        AUTH_TYPE.remove();
    }

} 