package com.sinitek.mind.support.openapi.util;

import cn.hutool.core.codec.Base64;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * API密钥工具类
 * 
 * 提供统一的API密钥生成和验证功能，使用UUID v4保证唯一性
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public class ApiKeyUtil {
    
    /**
     * API密钥前缀
     */
    private static final String API_KEY_PREFIX = "sinitek-";
    
    /**
     * 生成唯一的API密钥
     * 
     * 格式: sinitek-{uuid}
     * 示例: sinitek-a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
     * 
     * @return 生成的API密钥
     */
    public static String generateApiKey() {
        return API_KEY_PREFIX + UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 验证API密钥格式是否正确
     * 
     * @param apiKey 待验证的API密钥
     * @return 格式是否正确
     */
    public static boolean isValidApiKeyFormat(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return false;
        }
        
        String trimmedKey = apiKey.trim();
        return trimmedKey.startsWith(API_KEY_PREFIX) && 
               trimmedKey.length() == API_KEY_PREFIX.length() + 32;
    }
    
    /**
     * 获取API密钥前缀
     * 
     * @return API密钥前缀
     */
    public static String getApiKeyPrefix() {
        return API_KEY_PREFIX;
    }

    /**
     * 从请求头中提取API密钥
     */
    public static String extractApiKey(HttpServletRequest request) {
        String authorization = request.getHeader("Authorization");
        if (authorization != null) {
            // 支持 "Bearer sinitek-xxx" 格式
            if (authorization.startsWith("Bearer ")) {
                return authorization.substring(7);
            }
            // 也支持直接传递API密钥
            if (authorization.startsWith(ApiKeyUtil.getApiKeyPrefix())) {
                return authorization;
            }
        }
        return null;
    }

    /**
     * 脱敏显示API密钥
     */
    public static String maskApiKey(String apiKey) {
        if (StringUtils.isBlank(apiKey)) {
            return "apikey不存在";
        }
        return Base64.encode(apiKey.getBytes(StandardCharsets.UTF_8));
    }
} 