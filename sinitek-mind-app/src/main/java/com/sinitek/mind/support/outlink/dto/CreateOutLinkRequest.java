package com.sinitek.mind.support.outlink.dto;

import com.sinitek.mind.core.app.model.Limit;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "创建免登录链接请求")
public class CreateOutLinkRequest {

    @NotNull(message = "应用ID不能为空")
    @Schema(description = "应用ID")
    private Long appId;

    @NotBlank(message = "发布渠道类型不能为空")
    @Schema(description = "发布渠道类型")
    private String type;

    @NotBlank(message = "链接名称不能为空")
    @Schema(description = "链接名称")
    private String name;

    @Schema(description = "是否显示详细响应")
    private Boolean responseDetail = false;

    @Schema(description = "是否显示节点状态")
    private Boolean showNodeStatus = true;

    @Schema(description = "是否显示原始引用")
    private Boolean showRawSource;

    @Schema(description = "立即响应内容")
    private String immediateResponse;

    @Schema(description = "默认响应内容")
    private String defaultResponse;

    @Schema(description = "限制配置")
    private Limit limit;

    @Schema(description = "第三方应用配置")
    private Object app;
} 