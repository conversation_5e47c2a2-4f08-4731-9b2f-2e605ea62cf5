package com.sinitek.mind.support.mcp.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.model.GetToolsRequest;
import com.sinitek.mind.core.app.model.RunMCPToolBody;
import com.sinitek.mind.support.mcp.model.McpToolConfigType;
import com.sinitek.mind.support.mcp.service.IMCPClientService;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.McpSyncClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
import io.modelcontextprotocol.client.transport.HttpClientStreamableHttpTransport;
import io.modelcontextprotocol.spec.McpClientTransport;
import io.modelcontextprotocol.spec.McpSchema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MCPClientServiceImpl implements IMCPClientService {


    @Override
    public List<McpToolConfigType> getTools(GetToolsRequest request) {
        String url = request.getUrl();

        URI uri = URI.create(url);
        String baseUrl = uri.getScheme() + "://" + uri.getHost();
        if (uri.getPort() != -1) {
            baseUrl += ":" + uri.getPort();
        }
        String endpoint = uri.getPath();
        if (uri.getQuery() != null) {
            endpoint += "?" + uri.getQuery();
        }
        McpClientTransport transport = null;
        if (endpoint.startsWith("/mcp")) {
//            if (endpoint.endsWith("mcp")) {
//                endpoint = endpoint + "/";
//            }
            // 创建Streamable HTTP传输
            transport = HttpClientStreamableHttpTransport
                    .builder(baseUrl)
                    .endpoint(endpoint)
                    .objectMapper(new ObjectMapper())
                    .build();
        } else {
            transport = HttpClientSseClientTransport.builder(baseUrl)
                    .sseEndpoint(endpoint)
                    .objectMapper(new ObjectMapper()).build();
        }
        if (transport != null) {
            McpClient.SyncSpec capabilities = McpClient.sync(transport)
                    .initializationTimeout(Duration.ofSeconds(10))
                    .requestTimeout(Duration.ofSeconds(10))
                    .roots(new ArrayList<>());

            // Create a sync client with custom configuration
            McpSyncClient client = capabilities.build();
            try {
                McpSchema.ListToolsResult listToolsResult = client.listTools();
                List<McpSchema.Tool> tools = listToolsResult.tools();
                List<McpToolConfigType> finalTools = new ArrayList<>();
                tools.stream().forEach(item -> {
                    McpToolConfigType mcpToolConfigType = McpToolConfigType.builder()
                            .name(item.name())
                            .description(item.description())
                            .inputSchema(item.inputSchema())
                            .build();
                    finalTools.add(mcpToolConfigType);
                });
                return finalTools;
            } catch (Exception e) {
                log.error(e.getMessage());
            } finally {
                client.close();
            }
            throw new BussinessException(AppErrorCodeConstant.MCP_CONNECTION_FAILED);
        }
        log.error("传入的URL格式不正确");
        throw new BussinessException(AppErrorCodeConstant.MCP_URL_FORMAT_ERROR);
    }

    @Override
    public McpSchema.CallToolResult runTool(RunMCPToolBody body) {
        String url = body.getUrl();
        String toolName = body.getToolName();
        Map<String, Object> params = body.getParams();

        URI uri = URI.create(url);
        String baseUrl = uri.getScheme() + "://" + uri.getHost();
        if (uri.getPort() != -1) {
            baseUrl += ":" + uri.getPort();
        }
        String endpoint = uri.getPath();
        if (uri.getQuery() != null) {
            endpoint += "?" + uri.getQuery();
        }
        McpClientTransport transport = null;
        if (endpoint.startsWith("/mcp")) {
            if (endpoint.endsWith("mcp")) {
                endpoint = endpoint + "/";
            }
            // 创建Streamable HTTP传输
            transport = HttpClientStreamableHttpTransport
                    .builder(baseUrl)
                    .endpoint(endpoint)
                    .objectMapper(new ObjectMapper())
                    .build();
        } else {
            transport = HttpClientSseClientTransport.builder(baseUrl)
                    .sseEndpoint(endpoint)
                    .objectMapper(new ObjectMapper()).build();
        }
        if (transport != null) {
            McpClient.SyncSpec capabilities = McpClient.sync(transport)
                    .initializationTimeout(Duration.ofSeconds(10))
                    .requestTimeout(Duration.ofSeconds(10))
                    .roots(new ArrayList<>());

            // Create a sync client with custom configuration
            McpSyncClient client = capabilities.build();
            try {
                McpSchema.CallToolRequest callToolRequest = new McpSchema.CallToolRequest(toolName, params);

                return client.callTool(callToolRequest);
            } catch (Exception e) {
                log.error(e.getMessage());
            } finally {
                client.close();
            }
            throw new BussinessException(AppErrorCodeConstant.MCP_CONNECTION_FAILED);
        }
        log.error("传入的URL格式不正确");
        throw new BussinessException(AppErrorCodeConstant.MCP_URL_FORMAT_ERROR);
    }
}
