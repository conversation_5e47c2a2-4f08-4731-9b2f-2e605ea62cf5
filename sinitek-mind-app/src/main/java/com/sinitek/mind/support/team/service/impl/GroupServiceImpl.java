package com.sinitek.mind.support.team.service.impl;

import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.constant.TeamErrorCodeConstant;
import com.sinitek.mind.support.team.dto.*;
import com.sinitek.mind.support.team.service.IGroupService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.entity.Role;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.org.service.IOrgUpdaterService;
import com.sinitek.spirit.org.core.IOrgFinder;
import com.sinitek.spirit.org.core.constant.OrgConstant;
import com.sinitek.spirit.org.core.dto.OrgSpiritObjectDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 群组服务实现类
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GroupServiceImpl implements IGroupService {

    /**
     * fastgpt的群组使用框架中的角色进行实现
     * 小组拥有者这个无法实现，直接去除
     */

    @Autowired
    private IOrgFinder orgFinder;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IOrgUpdaterService orgUpdaterService;

    @Autowired
    private IAccountService accountService;

    @Autowired
    private IPermissionService permissionService;

    @Override
    public List<GroupDTO> getGroupList(GroupListRequest request) {
        String orgId = orgFinder.getRoot().getId();
        List<OrgSpiritObjectDTO> teamList = orgService.findRolesByFromObjectId(orgId);
        
        return teamList.stream()
            .map(this::convertTeamDTO2GroupDTO)
            .collect(Collectors.toList());
    }

    @Override
    public void createGroup(CreateGroupRequest request) {
        Role role = new Role();
        role.setName(request.getName());
        role.setTenantId(CurrentUserFactory.getTenantId());
        orgUpdaterService.saveRole(role);
    }
    
    @Override
    public void deleteGroup(DeleteGroupRequest request) {
        orgUpdaterService.deleteRolesByIds(request.getGroupId());
    }
    
    @Override
    public void updateGroup(UpdateGroupRequest request) {
        Role role = orgService.getRoleById(request.getGroupId());
        if (role == null) {
            log.error("群组不存在，groupId: {}", request.getGroupId());
            throw new BussinessException(TeamErrorCodeConstant.GROUP_NOT_FOUND);
        }

        // 如果是管理员角色则不能修改
        if(OrgConstant.ROLE_ADMIN.equals(role.getName())){
            log.error("群组管理员不能修改，groupId: {}", request.getGroupId());
            throw new BussinessException(TeamErrorCodeConstant.GROUP_ADMIN_CANNOT_UPDATE);
        }

        if (StringUtils.isNotBlank(request.getName())) {
            role.setName(request.getName());
            orgUpdaterService.saveRole(role);
        }

        List<GroupMemberDTO> memberList = request.getMemberList();
        // memberList为空，说明去除所有的成员
        if (memberList != null) {
            // 先删除全部的群组成员
            List<GroupMemberDTO> existMemberList = findGroupMembers(request.getGroupId());
            List<String> existEmpList = existMemberList.stream()
                .map(GroupMemberDTO::getTmbId)
                .collect(Collectors.toList());
            orgUpdaterService.removeUnitEmoloyee(request.getGroupId(), existEmpList);

            // 添加群组成员
            List<String> empIdList = memberList.stream()
                .map(GroupMemberDTO::getTmbId)
                .collect(Collectors.toList());
            orgUpdaterService.addOrgEmployee(request.getGroupId(), empIdList);
        }
    }

    @Override
    public List<GroupMemberDTO> findGroupMembers(String groupId) {
        List<Employee> employeeList = orgService.findEmployeeByRoleId(groupId);
        return employeeList.stream()
            .map(emp -> {
                GroupMemberDTO dto = new GroupMemberDTO();
                dto.setTmbId(emp.getId());
                dto.setName(emp.getEmpName());
                dto.setAvatar(accountService.getAvatarByOrgId(emp.getId()));
                return dto;
            })
            .collect(Collectors.toList());
    }
    
    @Override
    public void changeGroupOwner(ChangeGroupOwnerRequest request) {
        // 框架的角色不支持设置小组长，功能不进行实现
//        orgUpdaterService.setTeamer(request.getGroupId(), request.getTmbId());
    }

    private GroupDTO convertTeamDTO2GroupDTO(OrgSpiritObjectDTO dto) {
        GroupDTO groupDTO = new GroupDTO();
        groupDTO.set_id(dto.getId());
        groupDTO.setTeamId(dto.getTenantId());
        groupDTO.setName(dto.getName());
        groupDTO.setAvatar("");
        groupDTO.setPermission(permissionService.getTeamPermissionResultDTO(dto.getId()));
        groupDTO.setMembers(findGroupMembers(dto.getId()));

        return groupDTO;
    }
}