package com.sinitek.mind.support.openapi.service.impl;

import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.support.openapi.constant.OpenApiErrorCodeConstant;
import com.sinitek.mind.support.openapi.dao.OpenApiDAO;
import com.sinitek.mind.support.openapi.dto.ApiKeySearchResultDTO;
import com.sinitek.mind.support.openapi.dto.CreateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.dto.UpdateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.entity.OpenApi;
import com.sinitek.mind.support.openapi.service.IOpenApiService;
import com.sinitek.mind.support.openapi.util.ApiKeyConverter;
import com.sinitek.mind.support.openapi.util.ApiKeyUtil;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * OpenAPI服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OpenApiServiceImpl implements IOpenApiService {

    public static final String API_DEFAULT_NAME = "Api Key";
    private final OpenApiDAO openApiDAO;
    private final IOperationLogService operationLogService;
    private final IAuthService authService;

    /**
     * 创建API密钥
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createApiKey(CreateApiKeyRequestDTO request) {
        AuthDTO authDTO = authService.authCert();
        String orgId = authDTO.getTmbId();

        // 2. 生成唯一API Key
        String apiKey = ApiKeyUtil.generateApiKey();

        OpenApi openApi = OpenApi.builder()
                .orgId(orgId)
                .apiKey(apiKey)
                .appId(request.getAppId() != null ? request.getAppId() : null)
                .name(request.getName() != null ? request.getName() : API_DEFAULT_NAME)
                .expiredTime(request.getLimit() != null ? request.getLimit().getExpiredTime() : null)
                .build();

        openApiDAO.save(openApi);

        operationLogService.addOperationLog(MindConstant.API_KEY, String.format("创建API密钥【%s】", openApi.getName()));

        log.info("创建API密钥成功，用户: {}, 密钥: {}", orgId, apiKey);
        return apiKey;
    }
    
    /**
     * 获取API密钥列表（FastGPT格式）
     */
    @Override
    public List<ApiKeySearchResultDTO> listApiKeys(String appId) {
        // 从认证上下文获取用户信息
        AuthDTO authDTO = authService.authCert();
        String orgId = authDTO.getTmbId();
        List<OpenApi> apiKeys;
        if (appId != null && !appId.isEmpty()) {
            apiKeys = openApiDAO.findByOrgIdAndAppId(orgId, appId);
        } else {
            apiKeys = openApiDAO.findByOrgIdAndAppIdIsNull(orgId);
        }
        
        return apiKeys.stream()
            .map(ApiKeyConverter::convertToApiKeySearchResult)
            .collect(Collectors.toList());
    }
    
    /**
     * 更新API密钥
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateApiKey(UpdateApiKeyRequestDTO request) {
        OpenApi openApi = openApiDAO.getById(request.getId());

        if (openApi == null) {
            throw new BussinessException(OpenApiErrorCodeConstant.API_KEY_NOT_FOUND);
        }

        // 更新基本信息
        if (request.getName() != null) {
            openApi.setName(request.getName());
        }
        
        // 更新使用限制
        if (request.getLimit() != null && request.getLimit().getExpiredTime() != null) {
            UpdateApiKeyRequestDTO.LimitDTO limitDTO = request.getLimit();
            openApi.setExpiredTime(limitDTO.getExpiredTime());
        }
        
        openApiDAO.updateById(openApi);

        operationLogService.addOperationLog(MindConstant.API_KEY, String.format("更新API密钥【%s】", openApi.getName()));
        log.info("更新API密钥成功，ID: {}", request.getId());
    }
    
    /**
     * 删除API密钥
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteApiKey(String id) {
         OpenApi openApi = openApiDAO.getById(id);
        
        if (openApi == null) {
            throw new BussinessException(OpenApiErrorCodeConstant.API_KEY_NOT_FOUND);
        }
        
        openApiDAO.removeById(id);
        operationLogService.addOperationLog(MindConstant.API_KEY, String.format("删除API密钥【%s】", openApi.getName()));
        log.info("删除API密钥成功，ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByAppId(String appId) {
        openApiDAO.deleteByAppId(appId);
    }

    /**
     * 根据API密钥查找
     */
    @Override
    public Optional<OpenApi> getByApiKey(String apiKey) {
        return openApiDAO.getByApiKey(apiKey);
    }
    
    /**
     * 更新最后使用时间
     */
    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLastUsedTime(Long id) {
        try {
            openApiDAO.updateLastUsedTime(id, new Date());
        } catch (Exception e) {
            log.warn("更新API密钥最后使用时间失败: {}", id, e);
        }
    }



} 