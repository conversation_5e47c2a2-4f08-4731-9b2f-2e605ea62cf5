package com.sinitek.mind.support.outlink.service;

import com.sinitek.mind.support.outlink.dto.AuthOutLinkDTO;

/**
 * <AUTHOR>
 * @date 2025/7/15
 */
public interface IAuthOutLinkService {

    /**
     * 进行外链初始化验证，调用身份验证接口
     * @param shareId 外链id
     * @param authToken 外链authToken
     */
    void authOutLinkChatInit(String shareId, String authToken);

    AuthOutLinkDTO authOutLink(String shareId, String outLinkUid);
}
