package com.sinitek.mind.support.permission.enumerate;

import lombok.Getter;

@Getter
public enum TeamResourceEnum {

    // Type是固定的，直接构造方法中统一指定资源类型为TEAM
    APP_CREATE("", "appCreate"),
    DATASET_CREATE("", "datasetCreate"),
    APIKEY_CREATE("", "apikeyCreate"),
    MANAGER("", "manager");

    private final String type;
    private final String id;

    TeamResourceEnum(String type, String id) {
        this.type = ResourceTypeEnum.TEAM.getValue();
        this.id = id;
    }
}
