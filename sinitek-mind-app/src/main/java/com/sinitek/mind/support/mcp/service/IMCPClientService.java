package com.sinitek.mind.support.mcp.service;

import com.sinitek.mind.core.app.model.GetToolsRequest;
import com.sinitek.mind.core.app.model.RunMCPToolBody;
import com.sinitek.mind.support.mcp.model.McpToolConfigType;
import io.modelcontextprotocol.spec.McpSchema;

import java.util.List;

public interface IMCPClientService {

    List<McpToolConfigType> getTools(GetToolsRequest request);

    McpSchema.CallToolResult runTool(RunMCPToolBody body);
}
