package com.sinitek.mind.support.outlink.constant;

/**
 * 外链模块错误码常量类
 * 
 * 错误码规则：30-11-YYYY
 * 30 - Mind项目固定前缀
 * 11 - 外链模块编码
 * YYYY - 具体错误编码
 *
 * <AUTHOR>
 * date 2025-08-15
 */
public class OutLinkErrorCodeConstant {

    /**
     * 应用不存在
     */
    public static final String APP_NOT_FOUND = "30110001";

    /**
     * 创建免登录链接失败
     */
    public static final String OUTLINK_CREATE_FAILED = "30110002";

    /**
     * 查询免登录链接列表失败
     */
    public static final String OUTLINK_LIST_QUERY_FAILED = "30110003";

    /**
     * 免登录链接不存在
     */
    public static final String OUTLINK_NOT_FOUND = "30110004";

    /**
     * 更新免登录链接失败
     */
    public static final String OUTLINK_UPDATE_FAILED = "30110005";

    /**
     * 删除免登录链接失败
     */
    public static final String OUTLINK_DELETE_FAILED = "30110006";

    /**
     * 分享链接已过期
     */
    public static final String SHARE_LINK_EXPIRED = "30110007";

    /**
     * 分享链接使用次数已达上限
     */
    public static final String SHARE_LINK_USAGE_LIMIT_EXCEEDED = "30110008";

    /**
     * 分享链接不存在或已过期
     */
    public static final String SHARE_LINK_NOT_FOUND_OR_EXPIRED = "30110009";

    /**
     * 认证接口返回数据为空
     */
    public static final String AUTH_INTERFACE_EMPTY_RESPONSE = "30110010";

    /**
     * 认证失败
     */
    public static final String AUTH_FAILED = "30110011";

    /**
     * 调用免登录窗口初始化验证接口失败
     */
    public static final String AUTH_INIT_FAILED = "30110012";

    /**
     * 外链无效
     */
    public static final String OUTLINK_INVALID = "30110013";

    /**
     * 未知的发布渠道类型
     */
    public static final String PUBLISH_CHANNEL_UNKNOWN = "30110014";

    /**
     * 获取应用信息失败
     */
    public static final String APP_INFO_GET_FAILED = "30110015";

}