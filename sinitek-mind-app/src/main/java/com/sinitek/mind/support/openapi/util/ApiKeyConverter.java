package com.sinitek.mind.support.openapi.util;

import com.sinitek.mind.support.openapi.dto.ApiKeySearchResultDTO;
import com.sinitek.mind.support.openapi.entity.OpenApi;

/**
 * API密钥转换工具类
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
public class ApiKeyConverter {
    
    /**
     * 转换为API密钥搜索结果DTO
     */
    public static ApiKeySearchResultDTO convertToApiKeySearchResult(OpenApi openApi) {
        // 构建限制信息
        ApiKeySearchResultDTO.LimitInfo limitInfo = ApiKeySearchResultDTO.LimitInfo.builder()
            .maxUsagePoints(openApi.getLimit() != null ? 
                openApi.getLimit().getMaxUsagePoints() : -1L)
            .build();
        
        return ApiKeySearchResultDTO.builder()
            .apiKey(ApiKeyUtil.maskApiKey(openApi.getApiKey()))
            .createTime(openApi.getCreateTime())
            .id(openApi.getId())
            ._id(openApi.getId())
            .limit(limitInfo)
            .name(openApi.getName())
            .teamId(openApi.getTeamId())
            .tmbId(openApi.getTmbId())
            .usagePoints(openApi.getUsagePoints() != null ? openApi.getUsagePoints() : 0L)
            .build();
    }
} 