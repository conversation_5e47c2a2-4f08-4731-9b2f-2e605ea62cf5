package com.sinitek.mind.support.openapi.util;

import com.sinitek.mind.support.openapi.dto.ApiKeySearchResultDTO;
import com.sinitek.mind.support.openapi.entity.OpenApi;

/**
 * API密钥转换工具类
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
public class ApiKeyConverter {
    
    /**
     * 转换为API密钥搜索结果DTO
     */
    public static ApiKeySearchResultDTO convertToApiKeySearchResult(OpenApi openApi) {
        return ApiKeySearchResultDTO.builder()
            .apiKey(ApiKeyUtil.maskApiKey(openApi.getApiKey()))
            .createTime(openApi.getCreateTimeStamp())
            .id(String.valueOf(openApi.getId()))
            ._id(String.valueOf(openApi.getId()))
            .name(openApi.getName())
            .tmbId(openApi.getOrgId())
            .usagePoints(0L)
            .build();
    }
} 