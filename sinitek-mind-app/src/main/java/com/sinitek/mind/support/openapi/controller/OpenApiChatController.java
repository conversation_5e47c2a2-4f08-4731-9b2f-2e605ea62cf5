package com.sinitek.mind.support.openapi.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.chat.dto.*;
import com.sinitek.mind.core.chat.entity.Chat;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.service.IChatService;
import com.sinitek.mind.support.openapi.constant.OpenApiErrorCodeConstant;
import com.sinitek.mind.support.outlink.dto.ChatInitResponse;
import com.sinitek.mind.support.outlink.service.IOutLinkService;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 对话相关的OpenApi接口
 */
@Slf4j
@RestController
@RequestMapping("/mind/open-api/core/chat")
@RequiredArgsConstructor
@Tag(name = "对话管理", description = "聊天相关接口")
public class OpenApiChatController {

    private final IChatService chatService;

    private final IOutLinkService outLinkService;

    // 历史管理
    
    @PostMapping("/getHistories")
    @Operation(summary = "获取历史列表", description = "获取聊天历史列表")
    public ApiResponse<PageResult<Chat>> getHistories(
            @Parameter(description = "查询参数")
            @RequestBody GetHistoriesRequest request) {
        PageResult<Chat> response = chatService.getHistories(request);
        return ApiResponse.success(response);
    }

    @PostMapping("/updateHistory")
    @Operation(
            summary = "更新历史标题",
            description = "更新聊天历史标题"
    )
    public ApiResponse<Void> updateHistory(
            @Parameter(description = "更新参数")
            @RequestBody UpdateHistoryRequest request) {
        chatService.updateHistory(request);
        return ApiResponse.success();
    }

    @PostMapping("/delHistory")
    @Operation(summary = "删除历史", description = "删除聊天历史")
    public ApiResponse<Void> delHistory(@RequestBody DelHistoryRequest request) {
        chatService.delHistory(request);
        return ApiResponse.success();
    }

    @PostMapping("/clearHistories")
    @Operation(summary = "清空历史", description = "清空聊天历史")
    public ApiResponse<Void> clearHistories(@RequestBody ClearHistortiesRequest request) {
        chatService.clearHistories(request);
        return ApiResponse.success();
    }
    
    // 聊天记录管理
    
    @GetMapping("/init")
    @Operation(summary = "获取初始化信息", description = "获取聊天初始化信息")
    public ApiResponse<InitChatResponse> initChat(
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "chatId", required = false) String chatId) {

        try {
            // 参数校验
            if (appId == null || appId.trim().isEmpty()) {
                throw new BussinessException(OpenApiErrorCodeConstant.APP_NOT_FOUND);
            }

            InitChatRequest request = new InitChatRequest();
            request.setAppId(appId);
            request.setChatId(chatId);

            InitChatResponse response = chatService.initChat(request);
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("初始化聊天失败: {}", e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @PostMapping("/getPaginationRecords")
    @Operation(summary = "获取分页记录", description = "获取聊天分页记录")
    public ApiResponse<PageResult<ChatItemType>> getPaginationRecords(
            @RequestBody GetPaginationRecordsRequest request) {
        try {
            if (request.getAppId() == null) {
                throw new BussinessException(OpenApiErrorCodeConstant.MISSING_REQUIRED_PARAMETER, "appId");
            }
            if (!StringUtils.hasText(request.getChatId())) {
                throw new BussinessException(OpenApiErrorCodeConstant.MISSING_REQUIRED_PARAMETER, "chatId");
            }
            if (request.getOffset() < 0) {
                request.setOffset(0);
            }
            if (request.getPageSize() <= 0 || request.getPageSize() > 100) {
                request.setPageSize(20);
            }
            PageResult<ChatItemType> result = chatService.getPaginationRecords(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("分页获取聊天记录失败: {}", e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @GetMapping("/getResData")
    @Operation(summary = "获取单个对话记录运行详情", description = "获取单个对话记录运行详情")
    public ApiResponse<?> getResData() {
        return ApiResponse.success();
    }


    @PostMapping("/item/delete")
    @Operation(summary = "删除记录", description = "删除聊天记录")
    public ApiResponse<?> deleteItem() {
        return ApiResponse.success();
    }
    
    // 用户反馈
    
    @PostMapping("/feedback/updateUserFeedback")
    @Operation(summary = "更新用户反馈", description = "更新用户反馈（点赞/点踩）")
    public ApiResponse<?> updateUserFeedback() {
        return ApiResponse.success();
    }

    @GetMapping("/outLink/init")
    @Operation(summary = "初始化免登录聊天", description = "初始化免登录聊天会话，验证链接有效性并返回聊天配置信息")
    public ApiResponse<ChatInitResponse> initChat(
            @Parameter(description = "分享链接ID（24位随机字符串）", required = true)
            @RequestParam @NotBlank(message = "分享链接ID不能为空") String shareId,
            @Parameter(description = "外链用户ID（用于识别用户身份）", required = true)
            @RequestParam @NotBlank(message = "外链用户ID不能为空") String outLinkUid,
            @Parameter(description = "聊天ID（可选，用于恢复已有聊天）")
            @RequestParam(required = false) String chatId,
            @Parameter(description = "authToken（可选，用于验证用户身份）")
            @RequestParam(required = false) String authToken) {
        log.info("初始化免登录聊天，shareId：{}，outLinkUid：{}，chatId：{},authToken:{}", shareId, outLinkUid, chatId, authToken);
        ChatInitResponse response = outLinkService.initChat(shareId, outLinkUid, chatId, authToken);
        return ApiResponse.success(response);
    }
}