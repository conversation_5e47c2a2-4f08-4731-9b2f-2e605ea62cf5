package com.sinitek.mind.support.openapi.service;

import com.sinitek.mind.support.openapi.dto.ApiKeySearchResultDTO;
import com.sinitek.mind.support.openapi.dto.CreateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.dto.UpdateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.entity.OpenApi;

import java.util.List;
import java.util.Optional;

/**
 * OpenAPI服务接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface IOpenApiService {
    
    /**
     * 创建API密钥
     */
    String createApiKey(CreateApiKeyRequestDTO request);
    
    /**
     * 获取API密钥列表
     */
    List<ApiKeySearchResultDTO> listApiKeys(String appId);
    
    /**
     * 更新API密钥
     */
    void updateApiKey(UpdateApiKeyRequestDTO request);
    
    /**
     * 删除API密钥
     */
    void deleteApiKey(String id);
    
    /**
     * 根据API密钥查找
     */
    Optional<OpenApi> getByApiKey(String apiKey);
    
    /**
     * 记录使用量
     */
    void recordUsage(String apiKey, Long usagePoints);
    
    /**
     * 更新最后使用时间
     */
    void updateLastUsedTime(String id);
} 