package com.sinitek.mind.support.mcp.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.core.app.model.GetToolsRequest;
import com.sinitek.mind.core.app.model.RunMCPToolBody;
import com.sinitek.mind.support.mcp.model.McpToolConfigType;
import com.sinitek.mind.support.mcp.service.IMCPClientService;
import io.modelcontextprotocol.spec.McpSchema;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/mind/api/support/mcp/client")
@RequiredArgsConstructor
public class MCPClientController {

    private final IMCPClientService mcpClientService;

    @PostMapping("/getTools")
    @Operation(summary = "获取MCP的工具列表", description = "根据传入的URL信息获取该MCP服务提供的工具列表")
    public ApiResponse<List<McpToolConfigType>> getTools(@RequestBody GetToolsRequest request) {
        List<McpToolConfigType> tools = mcpClientService.getTools(request);
        return ApiResponse.success(tools);
    }

    @PostMapping("/runTool")
    @Operation(summary = "运行MCP工具", description = "根据传入的参数运行该MCP服务指定的工具")
    public ApiResponse<McpSchema.CallToolResult> runTool(@RequestBody RunMCPToolBody body) {
        McpSchema.CallToolResult result = mcpClientService.runTool(body);
        return ApiResponse.success(result);
    }
}
