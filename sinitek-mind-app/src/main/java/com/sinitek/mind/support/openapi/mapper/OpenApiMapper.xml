<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.mind.support.openapi.mapper.OpenApiMapper">

    <!-- 根据API密钥查找 -->
    <select id="getByApiKey" resultType="com.sinitek.mind.support.openapi.entity.OpenApi">
        SELECT * FROM mind_open_api WHERE api_key = #{apiKey}
    </select>

    <!-- 根据团队成员ID查找，且appId为null -->
    <select id="findByOrgIdAndAppIdIsNull" resultType="com.sinitek.mind.support.openapi.entity.OpenApi">
        SELECT * FROM mind_open_api WHERE org_id = #{orgId} AND app_id IS NULL
    </select>

    <!-- 根据团队成员ID和应用ID查找 -->
    <select id="findByOrgIdAndAppId" resultType="com.sinitek.mind.support.openapi.entity.OpenApi">
        SELECT * FROM mind_open_api WHERE org_id = #{orgId} AND app_id = #{appId}
    </select>

    <!-- 根据应用ID删除 -->
    <delete id="deleteByAppId">
        DELETE FROM mind_open_api WHERE app_id = #{appId}
    </delete>

</mapper>