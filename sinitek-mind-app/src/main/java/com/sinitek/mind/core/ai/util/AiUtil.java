package com.sinitek.mind.core.ai.util;

import com.sinitek.mind.model.dto.SystemModelDTO;

public class AiUtil {

    /**
     * 计算响应最大token
     * 对应 TypeScript 中的 computedMaxToken 函数
     *
     * @param maxToken 最大token
     * @param model 模型数据
     * @param min 最小
     * @return 最大token
     */
    public static Integer computedMaxToken(Integer maxToken, SystemModelDTO model, Integer min) {
        if (maxToken == null) return 1;

        int maxTokens = 1;

        maxTokens = Math.min(maxToken, model.getMaxResponse());

        return Math.max(maxTokens, min == null ? 1 : min);
    }
}
