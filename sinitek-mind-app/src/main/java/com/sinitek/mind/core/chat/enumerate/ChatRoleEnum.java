package com.sinitek.mind.core.chat.enumerate;

import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.mind.core.chat.constant.ChatErrorCodeConstant;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Getter;

@Getter
public enum ChatRoleEnum {

    SYSTEM("System", "系统"),
    HUM<PERSON>("Human", "用户"),
    AI("AI", "AI");

    private final String name;
    private final String value;

    ChatRoleEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static ChatRoleEnum getByValue(String value) {
        for (ChatRoleEnum chatRoleEnum : ChatRoleEnum.values()) {
            if (chatRoleEnum.getValue().equals(value)) {
                return chatRoleEnum;
            }
        }
        throw new BussinessException(ChatErrorCodeConstant.ROLE_NOT_FOUND);
    }

    @JsonValue
    @Override
    public String toString() {
        return this.value;
    }
}
