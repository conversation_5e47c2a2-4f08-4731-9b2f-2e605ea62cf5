package com.sinitek.mind.core.app.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.dto.UpdateHttpPluginDTO;
import com.sinitek.mind.core.app.service.IHttpPluginService;
import com.sinitek.mind.core.app.util.SwaggerUtils;
import com.sinitek.mind.core.app.util.SystemUtils;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/mind/api/core/app/httpPlugin")
@RequiredArgsConstructor
@Tag(name = "http插件管理", description = "http插件相关接口")
public class HttpPluginController {

    private final IHttpPluginService httpPluginService;

    @PostMapping("/getApiSchemaByUrl")
    @Operation(summary = "从外部URL加载OpenAPI模式")
    public ApiResponse<?> getApiSchemaByUr(@RequestBody Map<String, String> request) {
        try {
            String apiURL = request.get("url");

            if (apiURL == null || apiURL.isEmpty()) {
                throw new BussinessException(AppErrorCodeConstant.MISSING_PARAMS);
            }

            boolean isInternal = SystemUtils.isInternalAddress(apiURL);

            if (isInternal) {
                throw new BussinessException(AppErrorCodeConstant.HTTP_PLUGIN_URL_CANNOT_BE_EMPTY);
            }

            Object schema = SwaggerUtils.loadOpenAPISchemaFromUrl(apiURL);

            return ApiResponse.success(schema);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 创建HTTP插件
     *
     * @param dto 创建请求
     * @return 创建响应
     */
    @PostMapping("/create")
    @Operation(summary = "创建httpPlugin")
    public ApiResponse<Long> createHttpPlugin(@RequestBody CreateAppDTO dto) {
        return httpPluginService.createHttpPlugin(dto);
    }

    /**
     * 更新HTTP插件
     *
     * @param dto 更新HTTP插件请求
     * @return 响应
     */
    @PostMapping("/update")
    @Operation(summary = "更新httpPlugin")
    public ApiResponse<Void> updateHttpPlugin(@RequestBody UpdateHttpPluginDTO dto) {
        httpPluginService.updateHttpPlugin(dto);
        return ApiResponse.success(null);
    }
}
