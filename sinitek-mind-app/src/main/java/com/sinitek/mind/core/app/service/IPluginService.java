package com.sinitek.mind.core.app.service;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.model.*;
import com.sinitek.mind.core.workflow.model.FlowNodeTemplateType;

import java.util.List;

public interface IPluginService {

    /**
     *
     * @param tempAppId 这个id可能是系统插件id(common-XXX）
     * @param versionId
     * @return
     */
    FlowNodeTemplateType getChildAppPreviewNode(String tempAppId, Long versionId);

    List<NodeTemplateListItem> getSystemPluginTemplates(String searchKey, String parentId);

    PageResult<VersionListItemType> getVersionList(String toolId, int offset, int pageSize, String userId);

    List<ParentTreePathItemType> getPluginPath(Long pluginId, String type);

    /**
     *
     * @param id 这个id可能是系统插件id(common-XXX）
     * @param versionId
     * @return
     */
    PluginRuntimeType getChildAppRuntimeById(String id, Long versionId);

    /**
     *
     * @param id 这个id可能是系统插件id(common-XXX）
     * @param versionId
     * @return
     */
    ChildAppType getSystemPluginTemplateById(String id, Long versionId);
}
