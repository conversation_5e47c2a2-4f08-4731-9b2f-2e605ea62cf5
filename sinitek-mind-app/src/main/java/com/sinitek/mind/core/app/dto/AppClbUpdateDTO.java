package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.support.permission.dto.CollaboratorUpdateDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * App协作者信息更新DTO
 *
 * <AUTHOR>
 * @date 2025/7/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "App协作者更新对象")
public class AppClbUpdateDTO extends CollaboratorUpdateDTO {

    @Schema(description = "app应用id")
    private Long appId;
}