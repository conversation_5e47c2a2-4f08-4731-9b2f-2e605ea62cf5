package com.sinitek.mind.core.workflow.dispatch;

import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class UserGuideDispatcher implements NodeDispatcher {

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.SYSTEM_CONFIG.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchData.getParams();
    }
}
