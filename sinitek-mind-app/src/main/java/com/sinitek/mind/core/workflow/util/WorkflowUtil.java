package com.sinitek.mind.core.workflow.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.app.constant.AppConstant;
import com.sinitek.mind.core.app.enumerate.WorkflowIOValueTypeEnum;
import com.sinitek.mind.core.app.model.*;
import com.sinitek.mind.core.app.util.MCPToolUtil;
import com.sinitek.mind.core.app.util.PluginUtils;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.enumerate.ChatCompletionRequestMessageRoleEnum;
import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.model.*;
import com.sinitek.mind.core.dataset.model.SearchDataResponseItemType;
import com.sinitek.mind.core.workflow.constant.WorkflowConstant;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.SseResponseEventEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.model.sse.GptResponse;
import com.sinitek.mind.core.workflow.model.sse.WorkflowResponseWriter;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.security.SecureRandom;
import java.util.*;
import java.util.function.Consumer;
import java.util.regex.MatchResult;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WorkflowUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取处理ID
     * @param nodeId
     * @param type 只能为source或target
     * @param key
     * @return
     */
    public static String getHandleId(String nodeId, String type, String key) {
        return nodeId + "-" + type + "-"  + key;
    }

    public static int getMaxHistoryLimitFromNodes(List<StoreNodeItemType> nodes) {
        int limit = 10;
        for (StoreNodeItemType node : nodes) {
            for (FlowNodeInputItemType input : node.getInputs()) {
                if (NodeInputKeyEnum.HISTORY.getValue().equals(input.getKey())
                || NodeInputKeyEnum.HISTORY_MAX_AMOUNT.getValue().equals(input.getKey())
                && input.getValue() instanceof Number) {
                    limit = Math.max(limit, ((Number) input.getValue()).intValue());
                }
            }
        }
        return limit * 2;
    }

    /**
     * 从最后一条AI消息中获取交互信息（如果有的话）
     * 功能：
     * 1. 获取交互数据
     * 2. 检查工作流是否从交互节点开始
     *
     * @param histories 聊天历史记录列表
     * @return 工作流交互响应类型，如果没有则返回null
     */
    public static WorkflowInteractiveResponseType getLastInteractiveValue(List<ChatItemType> histories) {
        if (histories == null || histories.isEmpty()) {
            return null;
        }

        // 创建历史记录的副本并反转，查找最后一条AI消息
        List<ChatItemType> reversedHistories = new ArrayList<>(histories);
        Collections.reverse(reversedHistories);

        Optional<ChatItemType> lastAIMessageOpt = reversedHistories.stream()
                .filter(item -> item.getObj() == ChatRoleEnum.AI)
                .findFirst();

        if (lastAIMessageOpt.isEmpty()) {
            return null;
        }

        ChatItemType lastAIMessage = lastAIMessageOpt.get();
        List<ChatItemValueItemType> values = lastAIMessage.getValue();

        if (values == null || values.isEmpty()) {
            return null;
        }

        // 获取最后一个值
        ChatItemValueItemType lastValue = values.get(values.size() - 1);

        // 检查是否为交互类型
        if (lastValue == null ||
                !Objects.equals(lastValue.getType(), ChatItemValueType.INTERACTIVE.getValue()) ||
                lastValue.getInteractive() == null) {
            return null;
        }

        WorkflowInteractiveResponseType interactive = lastValue.getInteractive();
        String interactiveType = interactive.getNodeResponse().getType();

        // 检查子交互或循环交互
        if ("childrenInteractive".equals(interactiveType) ||
                "loopInteractive".equals(interactiveType)) {
            return interactive;
        }

        // 检查用户选择交互
        if ("userSelect".equals(interactiveType)) {
            UserSelectInteractive.Params params = (UserSelectInteractive.Params) interactive.getNodeResponse().getParam();
            if (params == null || params.getUserSelectedVal() == null) {
                return interactive;
            }
        }

        // 检查用户输入交互
        if ("userInput".equals(interactiveType)) {
            UserInputInteractive.Params params = (UserInputInteractive.Params) interactive.getNodeResponse().getParam();
            if (params == null || !params.getSubmitted()) {
                return interactive;
            }
        }

        return null;
    }

    public static List<String> getWorkflowEntryNodeIds(
            List<StoreNodeItemType> nodes,
            WorkflowInteractiveResponseType lastInteractive) {

        // 优先返回交互响应中的入口节点
        if (lastInteractive != null) {
            List<String> entryNodeIds = lastInteractive.getEntryNodeIds();
            if (entryNodeIds != null && !entryNodeIds.isEmpty()) {
                return new ArrayList<>(entryNodeIds);
            }
        }

        // 定义标准入口节点类型
        Set<String> entryTypes = Set.of(
                FlowNodeTypeEnum.SYSTEM_CONFIG.getValue(),
                FlowNodeTypeEnum.WORKFLOW_START.getValue(),
                FlowNodeTypeEnum.PLUGIN_INPUT.getValue()
        );

        // 查找标准入口节点
        List<String> entryNodeIds = nodes.stream()
                .filter(node -> entryTypes.contains(node.getFlowNodeType()))
                .map(StoreNodeItemType::getNodeId)
                .toList();

        // 如果没有标准入口节点，使用工具节点作为入口
        if (entryNodeIds.isEmpty()) {
            entryNodeIds = nodes.stream()
                    .filter(node -> Objects.equals(node.getFlowNodeType(), FlowNodeTypeEnum.TOOL.getValue()))
                    .map(StoreNodeItemType::getNodeId)
                    .toList();
        }

        return entryNodeIds;
    }

    /**
     * 将存储节点转换为运行时节点
     *
     * @param nodes 存储节点列表
     * @param entryNodeIds 入口节点ID列表
     * @return 运行时节点列表
     */
    public static List<RuntimeNodeItemType> storeNodes2RuntimeNodes(
            List<StoreNodeItemType> nodes,
            List<String> entryNodeIds) {

        if (nodes == null) {
            return new ArrayList<>();
        }

        Set<String> entryNodeIdSet = entryNodeIds != null ?
                new HashSet<>(entryNodeIds) : new HashSet<>();

        return nodes.stream()
                .map(node -> {
                    RuntimeNodeItemType runtimeNode = new RuntimeNodeItemType();
                    runtimeNode.setNodeId(node.getNodeId());
                    runtimeNode.setName(node.getName());
                    runtimeNode.setAvatar(node.getAvatar());
                    runtimeNode.setIntro(node.getIntro());
                    runtimeNode.setFlowNodeType(node.getFlowNodeType());
                    runtimeNode.setShowStatus(node.getShowStatus());
                    runtimeNode.setIsEntry(entryNodeIdSet.contains(node.getNodeId()));
                    runtimeNode.setInputs(node.getInputs());
                    runtimeNode.setOutputs(node.getOutputs());
                    runtimeNode.setPluginId(node.getPluginId());
                    runtimeNode.setVersion(node.getAppVersion());
                    return runtimeNode;
                })
                .toList();
    }

    /**
     * 将存储边转换为运行时边
     *
     * @param edges 存储边列表
     * @param lastInteractive 最后的交互响应（可选）
     * @return 运行时边列表
     */
    public static List<RuntimeEdgeItemType> storeEdges2RuntimeEdges(
            List<? extends StoreEdgeItemType> edges,
            WorkflowInteractiveResponseType lastInteractive) {

        // 如果存在最后的交互响应，优先使用其中的记忆边
        if (lastInteractive != null) {
            List<RuntimeEdgeItemType> memoryEdges = lastInteractive.getMemoryEdges();
            if (memoryEdges != null && !memoryEdges.isEmpty()) {
                return memoryEdges;
            }
        }

        // 否则将存储边转换为运行时边，默认状态为 'waiting'
        if (edges == null) {
            return new ArrayList<>();
        }

        return edges.stream()
                .map(edge -> {
                    RuntimeEdgeItemType runtimeEdge = new RuntimeEdgeItemType();
                    runtimeEdge.setSource(edge.getSource());
                    runtimeEdge.setSourceHandle(edge.getSourceHandle());
                    runtimeEdge.setTarget(edge.getTarget());
                    runtimeEdge.setTargetHandle(edge.getTargetHandle());
                    runtimeEdge.setStatus("waiting"); // 默认状态为等待
                    return runtimeEdge;
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据变量更新插件输入节点的值
     * 在运行插件时为插件输入节点添加值
     *
     * @param nodes 运行时节点列表
     * @param variables 变量映射
     * @return 更新后的节点列表
     */
    public static List<RuntimeNodeItemType> updatePluginInputByVariables(
            List<RuntimeNodeItemType> nodes,
            Map<String, Object> variables) {

        if (nodes == null) {
            return new ArrayList<>();
        }

        if (variables == null) {
            variables = new HashMap<>();
        }

        final Map<String, Object> finalVariables = variables;

        return nodes.stream()
                .map(node -> {
                    if (Objects.equals(node.getFlowNodeType(), FlowNodeTypeEnum.PLUGIN_INPUT.getValue())) {
                        // 创建新的节点副本
                        RuntimeNodeItemType updatedNode = new RuntimeNodeItemType();
                        // 复制所有属性
                        BeanUtils.copyProperties(node, updatedNode);

                        // 更新输入项
                        List<FlowNodeInputItemType> updatedInputs = node.getInputs().stream()
                                .map(input -> {
                                    Object parseValue = parseVariableValue(input, finalVariables);

                                    FlowNodeInputItemType updatedInput = new FlowNodeInputItemType();
                                    BeanUtils.copyProperties(input, updatedInput);
                                    updatedInput.setValue(parseValue != null ? parseValue : input.getValue());

                                    return updatedInput;
                                })
                                .toList();

                        updatedNode.setInputs(updatedInputs);
                        return updatedNode;
                    } else {
                        return node;
                    }
                })
                .toList();
    }

    /**
     * 解析变量值
     *
     * @param input 输入项
     * @param variables 变量映射
     * @return 解析后的值
     */
    private static Object parseVariableValue(FlowNodeInputItemType input, Map<String, Object> variables) {
        Object variableValue = variables.get(input.getKey());
        if (variableValue == null) {
            return null;
        }

        try {
            WorkflowIOValueTypeEnum valueType = WorkflowIOValueTypeEnum.fromValue(input.getValueType());

            // 对于基本类型，直接返回变量值
            if (valueType == WorkflowIOValueTypeEnum.STRING ||
                    valueType == WorkflowIOValueTypeEnum.NUMBER ||
                    valueType == WorkflowIOValueTypeEnum.BOOLEAN) {
                return variableValue;
            }

            // 对于复杂类型，尝试 JSON 解析
            if (variableValue instanceof String) {
                return parseJsonValue((String) variableValue);
            }

            return variableValue;

        } catch (Exception e) {
            // 解析失败时返回原始值
            return variableValue;
        }
    }

    /**
     * 解析 JSON 字符串
     *
     * @param jsonString JSON 字符串
     * @return 解析后的对象
     */
    private static Object parseJsonValue(String jsonString) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jsonString, Object.class);
        } catch (Exception e) {
            // JSON 解析失败，返回原始字符串
            return jsonString;
        }
    }

    /**
     * 使用历史交互数据重写节点输出
     * 根据历史记录中的交互数据更新运行时节点的输出值
     *
     * @param runtimeNodes 运行时节点列表
     * @param lastInteractive 最后一次交互节点响应
     * @return 更新后的运行时节点列表
     */
    public static List<RuntimeNodeItemType> rewriteNodeOutputByHistories(
            List<RuntimeNodeItemType> runtimeNodes,
            WorkflowInteractiveResponseType lastInteractive) {

        if (runtimeNodes == null) {
            return new ArrayList<>();
        }

        // 检查是否有有效的交互数据
        if (lastInteractive == null ||
                lastInteractive.getNodeOutputs() == null ||
                lastInteractive.getNodeOutputs().isEmpty()) {
            return new ArrayList<>(runtimeNodes);
        }

        List<NodeOutputItemType> nodeOutputs = lastInteractive.getNodeOutputs();

        return runtimeNodes.stream()
                .map(node -> {
                    // 创建节点副本
                    RuntimeNodeItemType updatedNode = new RuntimeNodeItemType();
                    BeanUtils.copyProperties(node, updatedNode);

                    // 更新输出项
                    List<FlowNodeOutputItemType> updatedOutputs = node.getOutputs().stream()
                            .map(output -> {
                                // 查找匹配的历史输出值
                                Object historyValue = findHistoryOutputValue(
                                        nodeOutputs, node.getNodeId(), output.getKey());

                                FlowNodeOutputItemType updatedOutput = new FlowNodeOutputItemType();
                                BeanUtils.copyProperties(output, updatedOutput);

                                // 使用历史值或原始值
                                updatedOutput.setValue(historyValue != null ? historyValue : output.getValue());

                                return updatedOutput;
                            })
                            .toList();

                    updatedNode.setOutputs(updatedOutputs);
                    return updatedNode;
                })
                .toList();
    }

    /**
     * 查找历史输出值
     *
     * @param nodeOutputs 节点输出列表
     * @param nodeId 节点ID
     * @param key 输出键
     * @return 匹配的输出值，如果未找到返回null
     */
    private static Object findHistoryOutputValue(
            List<NodeOutputItemType> nodeOutputs,
            String nodeId,
            String key) {

        return nodeOutputs.stream()
                .filter(item -> Objects.equals(item.getNodeId(), nodeId) &&
                        Objects.equals(item.getKey(), key))
                .findFirst()
                .map(NodeOutputItemType::getValue)
                .orElse(null);
    }

    /**
     * 重写运行时工作流
     * 将 toolSet 节点展开为多个独立的 tool 节点
     *
     * @param nodes 运行时节点列表
     * @param edges 运行时边列表
     */
    public static void rewriteRuntimeWorkFlow(
            List<RuntimeNodeItemType> nodes,
            List<RuntimeEdgeItemType> edges) {

        // 筛选出所有 toolSet 类型的节点
        List<RuntimeNodeItemType> toolSetNodes = nodes.stream()
                .filter(node -> FlowNodeTypeEnum.TOOL_SET.getValue().equals(node.getFlowNodeType()))
                .toList();

        // 如果没有 toolSet 节点，直接返回
        if (toolSetNodes.isEmpty()) {
            return;
        }

        // 记录需要删除的节点ID
        Set<String> nodeIdsToRemove = new HashSet<>();

        // 处理每个 toolSet 节点
        for (RuntimeNodeItemType toolSetNode : toolSetNodes) {
            nodeIdsToRemove.add(toolSetNode.getNodeId());

            // 获取 toolSet 的配置数据
            McpToolSetDataType toolSetValue = extractToolSetValue(toolSetNode);
            if (toolSetValue == null) {
                continue;
            }

            List<McpToolConfig> toolList = toolSetValue.getToolList();
            String url = toolSetValue.getUrl();
            Map<String, SecretValue> headerSecret = toolSetValue.getHeaderSecret();

            // 找到指向当前 toolSet 节点的所有边
            List<RuntimeEdgeItemType> incomingEdges = edges.stream()
                    .filter(edge -> toolSetNode.getNodeId().equals(edge.getTarget()))
                    .toList();

            // 为每个工具创建新的节点
            for (McpToolConfig tool : toolList) {
                RuntimeNodeItemType newToolNode = MCPToolUtil.getMCPToolRuntimeNode(
                        tool,
                        url,
                        headerSecret,
                        toolSetNode.getAvatar()
                );

                // 设置新节点的名称
                newToolNode.setName(toolSetNode.getName() + "/" + tool.getName());

                // 添加新节点到列表
                nodes.add(newToolNode);

                // 为新节点创建输入边
                for (RuntimeEdgeItemType inEdge : incomingEdges) {
                    RuntimeEdgeItemType newEdge = new RuntimeEdgeItemType();
                    newEdge.setSource(inEdge.getSource());
                    newEdge.setTarget(newToolNode.getNodeId());
                    newEdge.setSourceHandle(inEdge.getSourceHandle());
                    newEdge.setTargetHandle("selectedTools");
                    newEdge.setStatus(inEdge.getStatus());

                    edges.add(newEdge);
                }
            }
        }

        // 删除原有的 toolSet 节点
        nodes.removeIf(node -> nodeIdsToRemove.contains(node.getNodeId()));

        // 删除指向已删除节点的边
        edges.removeIf(edge -> nodeIdsToRemove.contains(edge.getTarget()));
    }

    /**
     * 从 toolSet 节点中提取工具集配置数据
     *
     * @param toolSetNode toolSet 节点
     * @return 工具集配置数据，如果提取失败返回 null
     */
    private static McpToolSetDataType extractToolSetValue(RuntimeNodeItemType toolSetNode) {
        try {
            if (toolSetNode.getInputs() == null || toolSetNode.getInputs().isEmpty()) {
                return null;
            }

            Object value = toolSetNode.getInputs().get(0).getValue();
            if (value instanceof McpToolSetDataType) {
                return (McpToolSetDataType) value;
            } else if (value instanceof Map<?,?>){
                Map<String, Object> valueMap = (Map<String, Object>) value;
                McpToolSetDataType dataType = new McpToolSetDataType();
                String url = (String) valueMap.get("url");
                String name1 = (String) valueMap.get("name");
                String avatar = (String) valueMap.get("avatar");
                dataType.setUrl(url);
                dataType.setName(name1);
                dataType.setAvatar(avatar);
                Map<String, SecretValue> secret = (Map<String, SecretValue>) valueMap.get("headerSecret");
                dataType.setHeaderSecret(secret);
                List<McpToolConfig> tooListObj = new ArrayList<>();
                List<Map<String, Object>> toolList = getListParam(valueMap, "toolList");
                for (Map<String, Object> map : toolList) {
                    McpToolConfig config = new McpToolConfig();
                    String name = (String) map.get("name");
                    String desc = (String) map.get("description");
                    Map<String, Object> schema = (Map<String, Object>) map.get("inputSchema");
                    JSONSchemaInputType itemType = new ObjectMapper().convertValue(schema, JSONSchemaInputType.class);
                    config.setName(name);
                    config.setDescription(desc);
                    config.setInputSchema(itemType);
                    tooListObj.add(config);
                }
                dataType.setToolList(tooListObj);
                return dataType;
            }

            // 如果需要，可以在这里添加类型转换逻辑
            log.warn("无法从 toolSet 节点提取配置数据，节点ID: {}", toolSetNode.getNodeId());
            return null;

        } catch (Exception e) {
            log.error("提取 toolSet 配置数据时发生错误，节点ID: {}", toolSetNode.getNodeId(), e);
            return null;
        }
    }

    /**
     * 获取List参数
     */
    @SuppressWarnings("unchecked")
    private static <T> List<T> getListParam(Map<String, Object> params, String key) {
        Object value = params.get(key);
        if (value instanceof List) {
            return (List<T>) value;
        }
        return new ArrayList<>();
    }

    /**
     * 移除系统变量
     *
     * @param variables 原始变量Map
     * @param removeObj 需要额外移除的变量Map（可选）
     * @return 移除系统变量后的新Map
     */
    public static Map<String, Object> removeSystemVariable(
            Map<String, Object> variables,
            Map<String, String> removeObj) {

        // 创建变量的副本
        Map<String, Object> copyVariables = variables.entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue() != null ?
                                entry.getValue().toString() :
                                "" // 或 ""
                ));

        // 删除系统变量
        copyVariables.remove("userId");
        copyVariables.remove("appId");
        copyVariables.remove("chatId");
        copyVariables.remove("responseChatItemId");
        copyVariables.remove("histories");
        copyVariables.remove("cTime");

        // 删除外部提供者工作流变量
        if (removeObj != null) {
            removeObj.keySet().forEach(copyVariables::remove);
        }

        return copyVariables;
    }

    /**
     * 替换编辑器变量
     */
    public static Object replaceEditorVariable(Object text, List<RuntimeNodeItemType> nodes, Map<String, Object> variables) {
        if (text == null || "null".equals(text)) {
            return null;
        }
        String result = "";
        // 替换普通变量 {{variableName}}
        if (text instanceof String input) {
            result = (String) replaceVariable(input, variables);

            // 匹配 {{$nodeId.id$}} 格式的变量
            Pattern variablePattern = Pattern.compile("\\{\\{\\$([^.]+)\\.([^$]+)\\$\\}\\}");
            Matcher matcher = variablePattern.matcher(result);

            // 查找所有匹配项
            List<String> matches = matcher.results()
                    .map(MatchResult::group)
                    .toList();

            if (matches.isEmpty()) {
                return result;
            }

            for (String match : matches) {
                Matcher m = variablePattern.matcher(match);
                if (!m.find()) continue;

                String nodeId = m.group(1);
                String id = m.group(2);

                Object variableVal = getVariableValue(nodeId, id, nodes, variables);

                String formatVal = valToStr(variableVal);

                // 创建精确匹配模式并替换
                String regex = "\\{\\{\\$" + Pattern.quote(nodeId + "." + id) + "\\$\\}\\}";
                result = result.replaceAll(regex, Matcher.quoteReplacement(formatVal));
            }
        } else {
            return text;
        }

        return result;
    }

    private static Object getVariableValue(String nodeId,
                                           String id,
                                           List<RuntimeNodeItemType> nodes,
                                           Map<String, Object> variables) {
        // 处理全局变量节点
        if ("VARIABLE_NODE_ID".equals(nodeId)) {
            return variables.get(id);
        }

        // 查找节点
        RuntimeNodeItemType node = nodes.stream()
                .filter(n -> nodeId.equals(n.getNodeId()))
                .findFirst()
                .orElse(null);

        if (node == null) {
            return null;
        }

        // 查找输出值
        for (FlowNodeOutputItemType output : node.getOutputs()) {
            if (id.equals(output.getId())) {
                return formatVariableValByType(output.getValue(), output.getValueType());
            }
        }

        // 查找输入值
        for (FlowNodeInputItemType input : node.getInputs()) {
            if (id.equals(input.getKey())) {
                return getReferenceVariableValue(input.getValue(), nodes, variables);
            }
        }

        return null;
    }


    public static Object replaceVariable(Object text, Map<String, Object> variables) {
        if (!(text instanceof String input)) {
            return text;
        }
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String key = entry.getKey();
            Object val = entry.getValue();
            String formatVal = valToStr(val);
            // 转义正则特殊字符并构建模式
            String regex = "\\{\\{" + Pattern.quote(key) + "\\}\\}";
            input = input.replaceAll(regex, Matcher.quoteReplacement(formatVal));
        }
        return input.isEmpty() ? "" : input;
    }

    /**
     * 根据类型格式化变量值
     */
    public static Object formatVariableValByType(Object val, String valueType) {
        // 如果没有指定类型，直接返回原值
        if (valueType == null) {
            return val;
        }

        // 如果值为null或undefined，返回null
        if (val == null) {
            return null;
        }

        // 值类型检查，如果valueType无效，返回null
        // 检查数组类型
        if (valueType.startsWith("array") && !(val instanceof List || val.getClass().isArray())) {
            return null;
        }
        // 布尔类型转换
        if (valueType.equals(WorkflowIOValueTypeEnum.BOOLEAN.getValue())) {
            if (val instanceof Boolean) {
                return (Boolean) val;
            }
            if (val instanceof String) {
                String str = ((String) val).toLowerCase().trim();
                return !str.isEmpty() && !str.equals("false") && !str.equals("0");
            }
            if (val instanceof Number) {
                return ((Number) val).doubleValue() != 0;
            }
            return true;
        }

        // 数字类型转换
        if (valueType.equals(WorkflowIOValueTypeEnum.NUMBER.getValue())) {
            if (val instanceof Number) {
                return (Number) val;
            }
            if (val instanceof String str) {
                try {
                    if (str.contains(".")) {
                        return Double.parseDouble(str);
                    } else {
                        return Long.parseLong(str);
                    }
                } catch (NumberFormatException e) {
                    return Double.NaN;
                }
            }
            if (val instanceof Boolean) {
                return ((Boolean) val) ? 1 : 0;
            }
            return Double.NaN;
        }

        // 字符串类型转换
        if (valueType.equals(WorkflowIOValueTypeEnum.STRING.getValue())) {
            if (val instanceof String) {
                return val;
            } else if (isObjectType(val)) {
                try {
                    return objectMapper.writeValueAsString(val);
                } catch (JsonProcessingException e) {
                    return val.toString();
                }
            } else {
                return val.toString();
            }
        }
        // 对象类型检查
        List<String> objectTypes = Arrays.asList(
                WorkflowIOValueTypeEnum.OBJECT.getValue(),
                WorkflowIOValueTypeEnum.DATASET_QUOTE.getValue(),
                WorkflowIOValueTypeEnum.SELECT_APP.getValue(),
                WorkflowIOValueTypeEnum.SELECT_DATASET.getValue()
        );

        if (objectTypes.contains(valueType) && !isObjectType(val)) {
            return null;
        }

        return val;
    }

    /**
     * 检查值是否为对象类型
     */
    private static boolean isObjectType(Object val) {
        return val != null &&
                !(val instanceof String) &&
                !(val instanceof Number) &&
                !(val instanceof Boolean) &&
                !(val instanceof Character);
    }

    /**
     * value转String
     */
    public static String valToStr(Object val) {
        if (val == null) {
            return "null";
        }
        if (val instanceof String) {
            return (String) val;
        }
        // 处理基础类型
        if (val instanceof Number || val instanceof Boolean) {
            return val.toString();
        }
        try {

            return objectMapper.writeValueAsString(val);
        } catch (JsonProcessingException e) {
            log.error("Json序列化失败");
        }
        return val.toString();
    }

    /**
     * 获取引用变量值
     */
    public static Object getReferenceVariableValue(Object value, List<RuntimeNodeItemType> nodes, Map<String, Object> variables) {
        if (value == null) {
            return null;
        }
        if (value instanceof String text) {
            Matcher matcher = Pattern.compile("\\$\\{([^}]+)\\}").matcher(text);

            while (matcher.find()) {
                String reference = matcher.group(1).trim();
                String[] parts = reference.split("\\.");

                if (parts.length >= 2) {
                    String nodeId = parts[0];
                    String outputKey = parts[1];

                    // 先从全局变量中获取
                    if (WorkflowConstant.VARIABLE_NODE_ID.equals(nodeId)) {
                        if (!StringUtils.hasText(outputKey)) return "";
                        return MapUtils.getObject(variables, outputKey);
                    }

                    // 查找对应的节点
                    Optional<RuntimeNodeItemType> targetNode = nodes.stream()
                            .filter(node -> nodeId.equals(node.getNodeId()))
                            .findFirst();

                    if (targetNode.isPresent()) {
                        // 查找对应的输出
                        Optional<FlowNodeOutputItemType> output = targetNode.get().getOutputs().stream()
                                .filter(out -> outputKey.equals(out.getId()))
                                .findFirst();

                        if (output.isPresent() && output.get().getValue() != null) {
                            FlowNodeOutputItemType itemType = output.get();
                            Object formatedValue = valueTypeFormat(itemType.getValue(), itemType.getValueType());
                            text = text.replace(matcher.group(0), String.valueOf(formatedValue));
                        }
                    }
                }
            }
            return text;
        }

        if (value instanceof List) {
            if (((List<?>) value).size() == 2 && ((List<?>) value).get(0) instanceof String) {

                List<String> values = (List<String>) value;
                String newValue = "${" + values.get(0) + "." + values.get(values.size()-1) + "}";
                return getReferenceVariableValue(newValue, nodes, variables);
            }
        }

        if (value instanceof ReferenceItemValueType item) {
            String nodeId = item.getT1();
            String variableValue = item.getT2();
            String newValue = "${" + nodeId + "." + variableValue + "}";
            return getReferenceVariableValue(newValue, nodes, variables);
        }

        return value;

    }

    /**
     * 值类型格式化
     * @param value 要格式化的值
     * @param type 目标类型
     * @return 格式化后的值
     */
    public static Object valueTypeFormat(Object value, String type) {
        // 1. any值，忽略格式化
        if (value == null) {
            return value;
        }
        if (type == null || type.equals(WorkflowIOValueTypeEnum.ANY.getValue())) {
            return value;
        }

        // 都走一遍类型转换，在进行对比时确保类型都是一致
//        // 2. 如果值已经符合目标类型，直接返回
//        if (isValueMatchType(value, WorkflowIOValueTypeEnum.fromValue(type))) {
//            return value;
//        }

        // 4. 按目标类型，进行格式转化
        // 4.1 基本类型转换
        if (type.equals(WorkflowIOValueTypeEnum.STRING.getValue())) {
            if (value instanceof Map || value instanceof List) {
                try {
                    return objectMapper.writeValueAsString(value);
                } catch (Exception e) {
                    return value.toString();
                }
            }
            return value.toString();
        }

        if (type.equals(WorkflowIOValueTypeEnum.NUMBER.getValue())) {
            // number都默认为double类型（json格式化默认为double），防止出现10.0与10不相等
            try {
                if (value instanceof String) {
                    return Double.parseDouble((String) value);
                }
                return Double.parseDouble(value.toString());
            } catch (Exception e) {
                log.error("value【{}】转化为Double类型失败", value);
                return Double.NaN;
            }
        }

        if (type.equals(WorkflowIOValueTypeEnum.BOOLEAN.getValue())) {
            if (value instanceof String) {
                return "true".equalsIgnoreCase((String) value);
            }
            if (value instanceof Boolean) {
                return value;
            }
            return Boolean.parseBoolean(value.toString());
        }

        // 4.3 字符串转对象
        if ((type.equals(WorkflowIOValueTypeEnum.OBJECT.getValue()) || type.startsWith("array"))
                && value instanceof String) {
            String trimmedValue = ((String) value).trim();
            if (!trimmedValue.isEmpty() && isObjectString(trimmedValue)) {
                try {
                    Object parsed = objectMapper.readValue(trimmedValue, Object.class);
                    // 检测解析结果与目标类型是否一致
                    if (type.startsWith("array") && parsed instanceof List) {
                        return parsed;
                    }
                    if (type.equals(WorkflowIOValueTypeEnum.OBJECT.getValue()) && (parsed instanceof Map || parsed instanceof Object)) {
                        return parsed;
                    }
                } catch (Exception e) {
                    // 解析失败，继续后续处理
                }
            }
        }

        // 4.4 数组类型(这里 value 不是数组类型)
        if (type.startsWith("array")) {
            return List.of(value);
        }

        // 4.5 特殊类型处理
        if (type.equals(WorkflowIOValueTypeEnum.DATASET_QUOTE.getValue()) || type.equals(WorkflowIOValueTypeEnum.SELECT_DATASET.getValue())) {
            if (value instanceof String && isObjectString((String) value)) {
                try {
                    return objectMapper.readValue((String) value, new TypeReference<List<Object>>(){});
                } catch (Exception e) {
                    return new ArrayList<>();
                }
            }
            return new ArrayList<>();
        }

        if ((type.equals(WorkflowIOValueTypeEnum.SELECT_APP.getValue()) || type.equals(WorkflowIOValueTypeEnum.OBJECT.getValue()))
                && value instanceof String) {
            if (isObjectString((String) value)) {
                try {
                    return objectMapper.readValue((String) value, new TypeReference<Map<String, Object>>(){});
                } catch (Exception e) {
                    return new HashMap<>();
                }
            }
            return new HashMap<>();
        }

        // Invalid history type
        if (type.equals(WorkflowIOValueTypeEnum.CHAT_HISTORY.getValue())) {
            return 0;
        }

        // 5. 默认返回原值
        return value;
    }

    /**
     * 合并助手响应答案文本
     */
    public static List<AIChatItemValueItemType> mergeAssistantResponseAnswerText(List<AIChatItemValueItemType> responses) {
        if (responses == null || responses.isEmpty()) {
            return responses;
        }

        List<AIChatItemValueItemType> mergedResponses = new ArrayList<>();
        StringBuilder textBuffer = new StringBuilder();

        for (AIChatItemValueItemType response : responses) {
            if (ChatItemValueType.TEXT.getValue().equals(response.getType()) && response.getText() != null) {
                textBuffer.append(response.getText().getContent());
            } else {
                // 如果有累积的文本，先添加文本响应
                if (!textBuffer.isEmpty()) {
                    AIChatItemValueItemType type = new  AIChatItemValueItemType();
                    type.setType(ChatItemValueType.TEXT.getValue());
                    type.setText(TextContent.builder().content(textBuffer.toString()).build());
                    mergedResponses.add(type);
                    textBuffer.setLength(0);
                }
                // 添加非文本响应
                mergedResponses.add(response);
            }
        }

        // 处理最后的文本缓冲
        if ( !textBuffer.isEmpty()) {
            AIChatItemValueItemType type = new  AIChatItemValueItemType();
            type.setType(ChatItemValueType.TEXT.getValue());
            type.setText(TextContent.builder().content(textBuffer.toString()).build());
            mergedResponses.add(type);
        }

        return mergedResponses;
    }


    /**
     * 检查字符串是否为对象字符串（JSON格式）
     */
    private static boolean isObjectString(String value) {
        if (value == null || "false".equals(value) || "true".equals(value)) {
            return false;
        }
        String trimmedValue = value.trim();
        return (trimmedValue.startsWith("{") && trimmedValue.endsWith("}")) ||
                (trimmedValue.startsWith("[") && trimmedValue.endsWith("]"));
    }


    /**
     * 将文本内容适配为GPT响应格式
     *
     * @param params 参数对象
     * @return GPT响应格式的数据
     */
    public static GptResponse textAdaptGptResponse(TextAdaptGptResponseParams params) {
        return GptResponse.builder()
                .extraData(params.getExtraData())
                .id("")
                .object("")
                .created(0L)
                .model(params.getModel() != null ? params.getModel() : "")
                .choices(List.of(
                        Choice.builder()
                                .delta(Delta.builder()
                                        .role(ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue())
                                        .content(params.getText())
                                        .reasoningContent(params.getReasoningContent())
                                        .build())
                                .index(0)
                                .finish_reason(params.getFinishReason())
                                .build()
                ))
                .build();
    }

    /**
     * 过滤公共节点响应数据
     */
    public static List<ChatHistoryItemResType> filterPublicNodeResponseData(
            List<ChatHistoryItemResType> flowResponses, boolean responseDetail) {

        if (!responseDetail) {
            return flowResponses.stream()
                    .map(response -> ChatHistoryItemResType.builder()
                            .id(response.getId())
                            .nodeId(response.getNodeId())
                            .moduleName(response.getModuleName())
                            .moduleType(response.getModuleType())
                            .runningTime(response.getRunningTime())
                            .query(response.getQuery())
//                            .response(response.getResponse())
                            .build())
                    .collect(Collectors.toList());
        }

        return flowResponses;
    }

    /**
     * 生成随机ID
     */
    public static String getNanoid(int length) {
        // 生成类似nanoid的随机字符串
        String alp = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(alp.charAt(new SecureRandom().nextInt(alp.length())));
        }
        return sb.toString();
    }

    /**
     * 检查边是否形成环路
     *
     * @param edge 要检查的边
     * @param visited 已访问的节点ID集合
     * @param allEdges 所有边列表
     * @param currentNode 当前节点
     * @return 是否形成环路
     */
    private static boolean checkIsCircular(
            RuntimeEdgeItemType edge,
            Set<String> visited,
            List<RuntimeEdgeItemType> allEdges,
            RuntimeNodeItemType currentNode) {

        if (Objects.equals(edge.getSource(), currentNode.getNodeId())) {
            return true; // 检测到环,并且环中包含当前节点
        }

        if (visited.contains(edge.getSource())) {
            return false; // 检测到环,但不包含当前节点(子节点成环)
        }

        visited.add(edge.getSource());

        // 递归检测后面的 edge，如果有其中一个成环，则返回 true
        List<RuntimeEdgeItemType> nextEdges = allEdges.stream()
                .filter(item -> Objects.equals(item.getTarget(), edge.getSource()))
                .toList();

        for (RuntimeEdgeItemType nextEdge : nextEdges) {
            Set<String> newVisited = new HashSet<>(visited);
            if (checkIsCircular(nextEdge, newVisited, allEdges, currentNode)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 过滤工作流边（排除工具选择相关的边）
     *
     * @param edges 边列表
     * @return 过滤后的边列表
     */
    public static List<RuntimeEdgeItemType> filterWorkflowEdges(List<RuntimeEdgeItemType> edges) {
        return edges.stream()
                .filter(edge -> !NodeOutputKeyEnum.SELECTED_TOOLS.getValue().equals(edge.getSourceHandle()) &&
                        !NodeOutputKeyEnum.SELECTED_TOOLS.getValue().equals(edge.getTargetHandle()))
                .collect(Collectors.toList());
    }

    /**
     * 将源边分为工作流边（普通边和递归边）
     *
     * @param sourceEdges 源边列表
     * @param allEdges 所有边列表
     * @param currentNode 当前节点
     * @return 边分类结果
     */
    private static EdgeSplitResult splitEdges2WorkflowEdges(
            List<RuntimeEdgeItemType> sourceEdges,
            List<RuntimeEdgeItemType> allEdges,
            RuntimeNodeItemType currentNode) {

        List<RuntimeEdgeItemType> commonEdges = new ArrayList<>();
        List<RuntimeEdgeItemType> recursiveEdges = new ArrayList<>();

        for (RuntimeEdgeItemType edge : sourceEdges) {
            if (checkIsCircular(edge, new HashSet<>(Collections.singletonList(currentNode.getNodeId())), allEdges, currentNode)) {
                recursiveEdges.add(edge);
            } else {
                commonEdges.add(edge);
            }
        }

        return new EdgeSplitResult(commonEdges, recursiveEdges);
    }

    /**
     * 检查节点运行状态
     */
    public static String checkNodeRunStatus(RuntimeNodeItemType node, List<RuntimeEdgeItemType> runtimeEdges) {
        // 获取指向当前节点的源边
        List<RuntimeEdgeItemType> runtimeNodeSourceEdge = filterWorkflowEdges(runtimeEdges).stream()
                .filter(item -> Objects.equals(item.getTarget(), node.getNodeId()))
                .collect(Collectors.toList());

        // Entry - 如果没有源边，说明是入口节点，直接运行
        if (runtimeNodeSourceEdge.isEmpty()) {
            return "run";
        }

        // 分类边
        EdgeSplitResult edgeResult = splitEdges2WorkflowEdges(
                runtimeNodeSourceEdge,
                runtimeEdges,
                node
        );

        List<RuntimeEdgeItemType> commonEdges = edgeResult.getCommonEdges();
        List<RuntimeEdgeItemType> recursiveEdges = edgeResult.getRecursiveEdges();

        // 检查 active（其中一组边，至少有一个 active，且没有 waiting 即可运行）
        if (!commonEdges.isEmpty() &&
                commonEdges.stream().anyMatch(item -> "active".equals(item.getStatus())) &&
                commonEdges.stream().noneMatch(item -> "waiting".equals(item.getStatus()))) {
            return "run";
        }

        if (!recursiveEdges.isEmpty() &&
                recursiveEdges.stream().anyMatch(item -> "active".equals(item.getStatus())) &&
                recursiveEdges.stream().noneMatch(item -> "waiting".equals(item.getStatus()))) {
            return "run";
        }

        // 检查 skip（其中一组边，全是 skipped 则跳过运行）
        if (!commonEdges.isEmpty() &&
                commonEdges.stream().allMatch(item -> "skipped".equals(item.getStatus()))) {
            return "skip";
        }

        if (!recursiveEdges.isEmpty() &&
                recursiveEdges.stream().allMatch(item -> "skipped".equals(item.getStatus()))) {
            return "skip";
        }

        return "wait";
    }

    /**
     * 检查值是否已经符合目标类型
     */
    private static boolean isValueMatchType(Object value, WorkflowIOValueTypeEnum type) {
            switch (type) {
            case STRING:
                return value instanceof String;
            case NUMBER:
                return value instanceof Number;
            case BOOLEAN:
                return value instanceof Boolean;
            case OBJECT:
                return value instanceof Map || value instanceof Object;
            case CHAT_HISTORY:
                return value instanceof List || value instanceof Number;
            case DATASET_QUOTE:
            case SELECT_DATASET:
                return value instanceof List;
            case SELECT_APP:
                return value instanceof Map;
            default:
                if (type.getValue().startsWith("array")) {
                    return value instanceof List;
                }
                return false;
        }
    }

    public static Consumer<WorkflowStreamResponse> getWorkflowResponseWrite(WorkflowResponseConfig config) {
        return response -> {
            boolean useStreamResponse = config.isStreamResponse();

            // 检查响应是否可用
            if (config.getSseEmitter() == null || !useStreamResponse) {
                return;
            }

            // 禁止显示详情的事件
            Set<String> notDetailEvents = Set.of(
                    SseResponseEventEnum.ANSWER.getValue(),
                    SseResponseEventEnum.FAST_ANSWER.getValue()
            );
            if (!config.isDetail() && !notDetailEvents.contains(response.getEvent())) {
                return;
            }
            // 禁止显示运行状态的事件
            Set<String> statusEvents = Set.of(
                    SseResponseEventEnum.FLOW_NODE_STATUS.getValue(),
                    SseResponseEventEnum.TOOL_CALL.getValue(),
                    SseResponseEventEnum.TOOL_PARAMS.getValue(),
                    SseResponseEventEnum.TOOL_RESPONSE.getValue()
            );

            if (!config.isShowNodeStatus() && statusEvents.contains(response.getEvent())) {
                return;
            }

            WorkflowResponseWriter.write(config.getSseEmitter(), response.getEvent(), response.getData());

            // TODO 确认WorkflowStreamResponse中comsumer函数是否需要，需要就执行下面的逻辑，不需要就直接返回结果了
            // 执行响应写入
//            responseWrite(ResponseWriteRequest.builder()
//                    .sseEmitter(config.getSseEmitter())
//                    .writeFunction(response.getWriteFunction())
//                    .event(config.isDetail() ? response.getEvent() : null)
//                    .data(convertToJson(objectMapper.convertValue(response.getData(), Map.class)))
//                    .build());
        };
    }

    /**
     * 执行响应写入
     * @param request 写入请求
     */
    public static void responseWrite(ResponseWriteRequest request) {
        try {
            Consumer<String> writer = request.getWriteFunction();
            if (writer == null && request.getSseEmitter() != null) {
                // 使用 SseEmitter 写入
                SseEmitter sseEmitter = request.getSseEmitter();

                if (request.getEvent() != null) {
                    sseEmitter.send(SseEmitter.event()
                            .name(request.getEvent())
                            .data(request.getData()));
                } else {
                    sseEmitter.send(request.getData());
                }
            } else if (writer != null) {
                // 使用自定义写入函数
                if (request.getEvent() != null) {
                    writer.accept("event: " + request.getEvent() + "\n");
                }
                writer.accept("data: " + request.getData() + "\n\n");
            }
        } catch (Exception e) {
            log.error("响应写入失败", e);
        }
    }

    /**
     * 将数据转换为JSON字符串
     * @param data 数据对象
     * @return JSON字符串
     */
    private static String convertToJson(Map<String, Object> data) {
        try {
            return objectMapper.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            log.error("JSON转换失败", e);
            return "{}";
        }
    }

    /**
     * 获取插件运行用户查询
     * 对应TypeScript方法: getPluginRunUserQuery
     *
     * @param pluginInputs 插件输入列表
     * @param variables 变量映射
     * @param files 文件列表（可选）
     * @return 包含dataId的用户聊天项
     */
    public static ChatItemType getPluginRunUserQuery(
            List<FlowNodeInputItemType> pluginInputs,
            Map<String, Object> variables,
            List<ChatItemValueItemFileInfo> files) {

        if (files == null) {
            files = new ArrayList<>();
        }

        // 生成24位随机ID（对应TypeScript的getNanoid(24)）
        String dataId = getNanoid(24);

        // 获取插件运行内容
        String pluginContent = PluginUtils.getPluginRunContent(pluginInputs, variables);

        // 构建聊天值
        RuntimePromptType promptType = new RuntimePromptType();
        promptType.setText(pluginContent);
        promptType.setFiles(files);
        List<ChatItemValueItemType> chatItemValueItemTypes = ChatAdaptor.runtimePrompt2ChatsValue(promptType);

        // 返回结果
        ChatItemType result = new ChatItemType();
        result.setDataId(dataId);
        result.setObj(ChatRoleEnum.HUMAN);
        result.setValue(chatItemValueItemTypes);

        return result;
    }

    /**
     * 获取引导模块
     * 对应TypeScript中的getGuideModule函数
     *
     * @param modules 存储节点项类型列表
     * @return 引导模块，如果未找到则返回null
     */
    public static StoreNodeItemType getGuideModule(List<StoreNodeItemType> modules) {
        if (modules == null || modules.isEmpty()) {
            return null;
        }

        return modules.stream()
                .filter(item -> item != null && (
                        FlowNodeTypeEnum.SYSTEM_CONFIG.getValue().equals(item.getFlowNodeType())
                ))
                .findFirst()
                .orElse(null);
    }

    /**
     * 查找输入值的辅助方法
     */
    private static <T> Optional<T> findInputValue(StoreNodeItemType node, NodeInputKeyEnum key, Class<T> type) {
        if (node.getInputs() == null) {
            return Optional.empty();
        }

        return node.getInputs().stream()
                .filter(input -> key.getValue().equals(input.getKey()))
                .map(input -> {
                    Object value = input.getValue();
                    if (type.isInstance(value)) {
                        return type.cast(value);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .findFirst();
    }

    /**
     * 拆分引导模块配置
     * 对应TypeScript中的splitGuideModule函数
     *
     * @param guideModules 引导模块节点
     * @return 拆分后的引导模块配置
     */
    public static GuideModuleConfig splitGuideModule(StoreNodeItemType guideModules) {
        if (guideModules == null || guideModules.getInputs() == null) {
            return GuideModuleConfig.builder()
                    .ttsConfig(AppConstant.DEFAULT_TTS_CONFIG)
                    .questionGuide(AppConstant.DEFAULT_QG_CONFIG)
                    .whisperConfig(AppConstant.DEFAULT_WHISPER_CONFIG)
                    .chatInputGuide(AppConstant.DEFAULT_CHAT_INPUT_GUIDE_CONFIG)
                    .autoExecute(AppConstant.DEFAULT_AUTO_EXECUTE_CONFIG)
                    .build();
        }

        // 欢迎文本
        String welcomeText = findInputValue(guideModules, NodeInputKeyEnum.WELCOME_TEXT, String.class)
                .orElse("");

        // 变量列表
        @SuppressWarnings("unchecked")
        List<VariableItemType> variables = (List<VariableItemType>) findInputValue(
                guideModules, NodeInputKeyEnum.VARIABLES, List.class)
                .orElse(Collections.emptyList());

        // 问题引导配置 - 适配旧版本
        Object questionGuideVal = findInputValue(guideModules, NodeInputKeyEnum.QUESTION_GUIDE, Object.class)
                .orElse(null);
        AppQGConfigType questionGuide = null;
        if (questionGuideVal instanceof Boolean boolQGVal) {
            // 旧版本布尔值适配
            AppQGConfigType appQGConfigType = new AppQGConfigType();
            BeanUtils.copyProperties(AppConstant.DEFAULT_QG_CONFIG, appQGConfigType);
            appQGConfigType.setOpen(boolQGVal);
            questionGuide = appQGConfigType;
        } else {
            questionGuide = AppConstant.DEFAULT_QG_CONFIG;
        }

        // TTS配置
        AppTTSConfigType ttsConfig = findInputValue(guideModules, NodeInputKeyEnum.TTS, AppTTSConfigType.class)
                .orElse(AppConstant.DEFAULT_TTS_CONFIG);

        // Whisper配置
        AppWhisperConfigType whisperConfig = findInputValue(
                guideModules, NodeInputKeyEnum.WHISPER, AppWhisperConfigType.class)
                .orElse(AppConstant.DEFAULT_WHISPER_CONFIG);

        // 定时触发配置
        AppScheduledTriggerConfigType scheduledTriggerConfig = findInputValue(
                guideModules, NodeInputKeyEnum.SCHEDULE_TRIGGER, AppScheduledTriggerConfigType.class)
                .orElse(null);

        // 聊天输入引导配置
        ChatInputGuideConfigType chatInputGuide = findInputValue(
                guideModules, NodeInputKeyEnum.CHAT_INPUT_GUIDE, ChatInputGuideConfigType.class)
                .orElse(AppConstant.DEFAULT_CHAT_INPUT_GUIDE_CONFIG);

        // 指令
        String instruction = findInputValue(guideModules, NodeInputKeyEnum.INSTRUCTION, String.class)
                .orElse("");

        // 自动执行配置
        AppAutoExecuteConfigType autoExecute = findInputValue(
                guideModules, NodeInputKeyEnum.AUTO_EXECUTE, AppAutoExecuteConfigType.class)
                .orElse(AppConstant.DEFAULT_AUTO_EXECUTE_CONFIG);

        return GuideModuleConfig.builder()
                .welcomeText(welcomeText)
                .variables(variables)
                .questionGuide(questionGuide)
                .ttsConfig(ttsConfig)
                .whisperConfig(whisperConfig)
                .scheduledTriggerConfig(scheduledTriggerConfig)
                .chatInputGuide(chatInputGuide)
                .instruction(instruction)
                .autoExecute(autoExecute)
                .build();
    }

    /**
     * 获取应用聊天配置
     * 类似于TypeScript中的getAppChatConfig函数
     */
    public static AppChatConfigType getAppChatConfig(GetAppChatConfigReq req) {
        GuideModuleConfig guideModuleConfig = splitGuideModule(req.getSystemConfigNode());

        AppChatConfigType config = new AppChatConfigType();
        config.setQuestionGuide(guideModuleConfig.getQuestionGuide());
        config.setTtsConfig(guideModuleConfig.getTtsConfig());
        config.setWhisperConfig(guideModuleConfig.getWhisperConfig());
        config.setScheduledTriggerConfig(guideModuleConfig.getScheduledTriggerConfig());
        config.setChatInputGuide(guideModuleConfig.getChatInputGuide());
        config.setInstruction(guideModuleConfig.getInstruction());
        config.setAutoExecute(guideModuleConfig.getAutoExecute());

        if (req.getChatConfig() != null) {
            BeanUtils.copyProperties(req.getChatConfig(), config);
        }

        if (req.getStoreVariables() != null) {
            config.setVariables(req.getStoreVariables());
        } else if (req.getChatConfig() != null && req.getChatConfig().getVariables() != null) {
            config.setVariables(req.getChatConfig().getVariables());
        } else {
            config.setVariables(guideModuleConfig.getVariables());
        }

        if (req.getStoreWelcomeText() != null) {
            config.setWelcomeText(req.getStoreWelcomeText());
        } else if (req.getChatConfig() != null && req.getChatConfig().getWelcomeText() != null) {
            config.setWelcomeText(req.getChatConfig().getWelcomeText());
        } else {
            config.setWelcomeText(guideModuleConfig.getWelcomeText());
        }

        if (!req.isPublicFetch()) {
            config.setScheduledTriggerConfig(null);
        }

        return config;
    }
    /**
     * 验证变量引用有效性
     * 检查变量引用是否为有效的数组格式，并且节点ID存在于节点列表中
     *
     * @param variable 变量引用对象，应为包含[nodeId, variableKey]的数组
     * @param nodeIds 有效的节点ID列表
     * @return true 如果引用有效，false 否则
     */
    public static boolean isValidReferenceValue(Object variable, List<String> nodeIds) {
        if (variable == null) {
            return false;
        }

        if (variable instanceof ReferenceItemValueType reference) {
            // 如果是ReferenceItemValueType类型，直接调用自身方法
            String nodeId = reference.getT1();
            String key = reference.getT2();
            if (!StringUtils.hasText(key)) {
                // 为空，报错
                return false;
            }

            // 检查nodeId是否在有效节点列表中，或者是全局变量节点
            if ("VARIABLE_NODE_ID".equals(nodeId)) {
                return true; // 全局变量总是有效的
            }

            return nodeIds != null && nodeIds.contains(nodeId);
        }

        // 检查是否为数组类型
        if (!(variable instanceof Object[])) {
            return false;
        }

        Object[] varArray = (Object[]) variable;
        
        // 检查数组长度是否为2
        if (varArray.length != 2) {
            return false;
        }

        // 检查第一个元素（nodeId）是否为字符串
        if (!(varArray[0] instanceof String)) {
            return false;
        }

        String nodeId = (String) varArray[0];
        
        // 检查第二个元素（variableKey）是否为字符串且不为空
        if (!(varArray[1] instanceof String) || ((String) varArray[1]).trim().isEmpty()) {
            return false;
        }

        // 检查nodeId是否在有效节点列表中，或者是全局变量节点
        if ("VARIABLE_NODE_ID".equals(nodeId)) {
            return true; // 全局变量总是有效的
        }

        return nodeIds != null && nodeIds.contains(nodeId);
    }

    /**
     * 提取变量引用信息
     * 从变量引用对象中提取节点ID和变量键名，构建VariableReference对象
     *
     * @param variable 变量引用对象，应为包含[nodeId, variableKey]的数组
     * @return VariableReference对象，如果提取失败返回null
     */
    public static VariableReference extractVariableReference(Object variable) {
        if (variable == null) {
            return null;
        }

        // 检查是否为数组类型
        if (!(variable instanceof Object[])) {
            return null;
        }

        Object[] varArray = (Object[]) variable;
        
        // 检查数组长度是否为2
        if (varArray.length != 2) {
            return null;
        }

        // 检查第一个元素（nodeId）是否为字符串
        if (!(varArray[0] instanceof String)) {
            return null;
        }

        String nodeId = (String) varArray[0];
        
        // 检查第二个元素（variableKey）是否为字符串且不为空
        if (!(varArray[1] instanceof String) || ((String) varArray[1]).trim().isEmpty()) {
            return null;
        }

        String variableKey = ((String) varArray[1]).trim();
        
        // 判断是否为全局变量
        boolean isGlobalVariable = "VARIABLE_NODE_ID".equals(nodeId);

        return VariableReference.builder()
                .nodeId(nodeId)
                .variableKey(variableKey)
                .isGlobalVariable(isGlobalVariable)
                .build();
    }

    public static List<SearchDataResponseItemType> filterSearchResultsByMaxChars(List<SearchDataResponseItemType> list, Integer maxTokens) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<SearchDataResponseItemType> results = new ArrayList<>();
        int totalTokens = 0;

        for (SearchDataResponseItemType item : list) {
            // 计算当前项的token数量
            int itemTokens = countPromptTokens(item.getQ() + item.getA());
            totalTokens += itemTokens;

            // 如果超过最大token数量+500，停止添加
            if (totalTokens > maxTokens + 500) {
                break;
            }

            results.add(item);

            // 如果超过最大token数量，停止添加
            if (totalTokens > maxTokens) {
                break;
            }
        }

        // 如果结果为空，返回第一个元素
        return results.isEmpty() ? list.subList(0, Math.min(1, list.size())) : results;
    }

    /**
     * 计算提示词的token数量
     * 这里需要根据实际的token计算逻辑实现
     *
     * @param text 文本内容
     * @return token数量
     */
    public static int countPromptTokens(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        // TODO: 实现实际的token计算逻辑
        // 这里使用简单的字符数估算，实际应该调用相应的token计算服务
        return text.length() / 4; // 粗略估算：平均4个字符约等于1个token
    }

    // 工具值类型列表
    public static final List<ToolValueTypeItem> TOOL_VALUE_TYPE_LIST = Arrays.asList(
            new ToolValueTypeItem(
                    WorkflowIOValueTypeEnum.STRING.getValue(),
                    WorkflowIOValueTypeEnum.STRING,
                    new JsonSchemaPropertiesItemType("string")
            ),
            new ToolValueTypeItem(
                    WorkflowIOValueTypeEnum.NUMBER.getValue(),
                    WorkflowIOValueTypeEnum.NUMBER,
                    new JsonSchemaPropertiesItemType("number")
            ),
            new ToolValueTypeItem(
                    WorkflowIOValueTypeEnum.BOOLEAN.getValue(),
                    WorkflowIOValueTypeEnum.BOOLEAN,
                    new JsonSchemaPropertiesItemType("boolean")
            ),
            new ToolValueTypeItem(
                    "array<string>",
                    WorkflowIOValueTypeEnum.ARRAY_STRING,
                    new JsonSchemaPropertiesItemType("array", new JsonSchemaPropertiesItemType.ItemInfo("string"))
            ),
            new ToolValueTypeItem(
                    "array<number>",
                    WorkflowIOValueTypeEnum.ARRAY_NUMBER,
                    new JsonSchemaPropertiesItemType("array", new JsonSchemaPropertiesItemType.ItemInfo("number"))
            ),
            new ToolValueTypeItem(
                    "array<boolean>",
                    WorkflowIOValueTypeEnum.ARRAY_BOOLEAN,
                    new JsonSchemaPropertiesItemType("array", new JsonSchemaPropertiesItemType.ItemInfo("boolean"))
            )
    );


    /**
     * 创建值类型JSON Schema映射
     * 相当于TypeScript中的reduce操作
     */
    public static Map<String, JsonSchemaPropertiesItemType> createValueTypeJsonSchemaMap() {
        Map<String, JsonSchemaPropertiesItemType> map = new HashMap<>();

        for (ToolValueTypeItem item : TOOL_VALUE_TYPE_LIST) {
            map.put(item.getValue().getValue(), item.getJsonSchema());
        }

        return map;
    }

    /**
     * 格式化节点参数为指定类型
     * @param obj
     * @param t
     * @return
     * @param <T>
     */
    public static <T> T formatNodeParam(Object obj, Class<T> t) {
        try {
            return objectMapper.readValue(objectMapper.writeValueAsString(obj), t);
        } catch (Exception e) {
            log.error("节点参数格式化失败,Class:{}", t, e);
            throw new BussinessException(WorkflowErrorCodeConstant.JSON_CONVERSION_FAILED, t);
        }
    }
}
