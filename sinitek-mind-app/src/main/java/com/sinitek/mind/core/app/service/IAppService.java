package com.sinitek.mind.core.app.service;

import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.model.AppDetailType;

import java.util.List;

public interface IAppService {

    /**
     * 获取应用列表
     *
     * @param request 请求参数
     * @return 应用列表
     */
    List<AppListItemDTO> getAppList(ListAppDTO request);

    /**
     * 创建应用
     * @param body 创建应用请求
     * @return 应用ID
     */
    Long createApp(CreateAppDTO body);

    AppDetailType getAppDetail(Long appId);

    /**
     * 查找应用及其所有子应用
     * 对应TypeScript中的findAppAndAllChildren方法
     */
    List<App> findAppAndAllChildren(String teamId, Long appId);

    /**
     * 根据ID列表获取应用基本信息
     * 对应TypeScript中的getAppBasicInfoByIds方法
     */
    List<AppBasicInfoDTO> getAppBasicInfoByIds(String teamId, List<Long> ids);

    /**
     * 根据团队ID和类型查找应用
     */
    List<App> findByTeamIdAndType(String teamId, String type);


    List<App> findAppAndAllChildren(String teamId, Long appId, String fields);

    void deleteApp(Long appId);

    Long copyApp(Long appId);

    App updateApp(AppUpdateDTO params);

    Long transitionWorkFlow(TransitionWorkflowDTO body);

    Long resumeInheritPermission(Long appId);

    List<BasicInfoResDTO> getBasicInfo(String teamId, List<Long> ids);

    /**
     * 创建应用，但是不需要传入module和edges，方法提供默认的modules和edges
     * @param body
     * @return
     */
    Long createSimpleApp(SimpleCreateAppRequestDTO body);

    /**
     * 根据namespace列表删除app
     * @param namespaceList
     */
    void deleteAppByNamespace(List<String> namespaceList);
}
