package com.sinitek.mind.core.app.service.impl;

import com.mchange.v2.lang.ObjectUtils;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.dao.AppDAO;
import com.sinitek.mind.core.app.dto.AuthAppDTO;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.dto.UpdateHttpPluginDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.PluginData;
import com.sinitek.mind.core.app.service.IAppService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.app.service.IHttpPluginService;
import com.sinitek.mind.core.app.util.HttpPluginUtils;
import com.sinitek.mind.core.app.util.ImageUtils;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class HttpPluginServiceImpl implements IHttpPluginService {

    private final IAppService appService;
    private final AppDAO appDAO;
    private final ImageUtils imageUtils;
    private final IAuthService authService;
    private final IAuthAppService authAppService;

    @Override
    public ApiResponse<Long> createHttpPlugin(CreateAppDTO dto) {
        Long parentId = dto.getParentId();

        // 参数校验
        if (dto.getName() == null || dto.getPluginData() == null) {
            return ApiResponse.error("缺少参数");
        }
        dto.setType(AppTypeEnum.HTTP_PLUGIN.getValue());

        // 权限验证
        AuthDTO authDTO;
        if (parentId != null) {
            authDTO = authAppService.authApp(parentId, PermissionConstant.WRITE_PER);
        } else {
            authDTO = authService.authUserPer(PermissionConstant.APP_CREATE_PER);
        }


        // 事务中创建插件
//            AuthAppDTO finalAuthAppDTO = authAppDTO;
//            String httpPluginId = MongoSessionUtils.mongoSessionRun(() -> {
//                String appId = appService.createApp(dto);
//                // 计算子插件
//                List<CreateAppDTO> childrenPlugins = HttpPluginUtils.httpApiSchema2Plugins(
//                        appId,
//                        dto.getPluginData().getApiSchemaStr(),
//                        dto.getPluginData().getCustomHeaders()
//                );
//                for (CreateAppDTO childDTO : childrenPlugins) {
//                    childDTO.setTeamId(finalAuthAppDTO.getTeamId());
//                    childDTO.setTmbId(finalAuthAppDTO.getTmbId());
//                    appService.createApp(childDTO);
//                }
//
//                return appId;
//            }, transactionManager);


        App app = new App();
        app.setAvatar(dto.getAvatar());
        app.setName(dto.getName());
        app.setIntro(dto.getIntro());

        app.setOrgId(authDTO.getTmbId());
//            app.setModules(dto.getModules());
//            app.setEdges(dto.getEdges());
//            app.setChatConfig(dto.getChatConfig());
        app.setType(dto.getType());
        app.setPluginData(dto.getPluginData());
        app.setUpdateTimeStamp(new Date());

        // 如果有父级ID，设置父级关系
        if (parentId != null) {
            app.setParentId(parentId);
        }
        // 保存应用
        appDAO.save(app);
        Long appId = app.getId();

        // 计算子插件
        List<CreateAppDTO> childrenPlugins = HttpPluginUtils.httpApiSchema2Plugins(
                appId,
                dto.getPluginData().getApiSchemaStr(),
                dto.getPluginData().getCustomHeaders()
        );
        for (CreateAppDTO childDTO : childrenPlugins) {
            childDTO.setTeamId(authDTO.getTeamId());
            childDTO.setTmbId(authDTO.getTmbId());
            appService.createApp(childDTO);
        }

        // TODO 记录业务日志，创建http插件

        return ApiResponse.success(appId);
    }

    @Override
    public void updateHttpPlugin(UpdateHttpPluginDTO dto) {
        // 参数校验
        if (dto.getName() == null || dto.getPluginData() == null) {
            throw new BussinessException(AppErrorCodeConstant.MISSING_PARAMS);
        }
        AuthAppDTO authAppDTO = authAppService.authApp(dto.getAppId(), PermissionConstant.WRITE_PER);
        App app = authAppDTO.getApp();
        PluginData pluginData = dto.getPluginData();

        // 比较存储的数据和更新的数据
        Map<String, String> storeData = new HashMap<>();
        storeData.put("apiSchemaStr", app.getPluginData() != null ? app.getPluginData().getApiSchemaStr() : null);
        storeData.put("customHeaders", app.getPluginData() != null ? app.getPluginData().getCustomHeaders() : null);

        Map<String, String> updateData = new HashMap<>();
        updateData.put("apiSchemaStr", pluginData != null ? pluginData.getApiSchemaStr() : null);
        updateData.put("customHeaders", pluginData != null ? pluginData.getCustomHeaders() : null);

        // 如果数据不相等，更新子插件
        if (!ObjectUtils.eqOrBothNull(storeData, updateData)) {
            updateHttpChildrenPlugin("", app.getOrgId(),app.getId(), pluginData);
        }

        // 更新应用
        if (dto.getName() != null) {
            app.setName(dto.getName());
        }
        if (dto.getAvatar() != null) {
            app.setAvatar(dto.getAvatar());
        }
        if (dto.getIntro() != null) {
            app.setIntro(dto.getIntro());
        }
        if (pluginData != null) {
            app.setPluginData(pluginData);
        }
        appDAO.updateById(app);

        // 刷新头像资源
        imageUtils.refreshSourceAvatar(dto.getAvatar(), app.getAvatar(), null);

    }

    private void updateHttpChildrenPlugin(String teamId, String tmbId, Long parentId, PluginData pluginData) {
        if (pluginData == null || pluginData.getApiSchemaStr() == null) {
            return;
        }
        // 查询数据库中的插件
        List<App> apps = appDAO.listByParentIdAndPluginDataNotNull(parentId);

        // 从Schema生成插件
        List<CreateAppDTO> schemaPlugins = HttpPluginUtils.httpApiSchema2Plugins(
                parentId,
                pluginData.getApiSchemaStr(),
                pluginData.getCustomHeaders()
        );
        // 数据库中存在，schema不存在，删除
        for (App app : apps) {
            boolean found = false;

            for (CreateAppDTO dto : schemaPlugins) {
                if (dto.getName().equals(app.getPluginData().getPluginUniId())) {
                    found = true;
                    break;
                }
            }

            if (!found) {
                appService.deleteApp(app.getId());
            }
        }
        // 数据库中不存在，schema存在，新增
        for (CreateAppDTO dto : schemaPlugins) {
            boolean found = false;

            for (App app : apps) {
                if (app.getPluginData() != null &&
                dto.getName().equals(app.getPluginData().getPluginUniId())) {
                    found = true;
                    break;
                }
            }

            if (!found) {
                dto.setTmbId(teamId);
                dto.setTmbId(tmbId);
                appService.createApp(dto);
            }
        }
        // 数据库中存在，schema存在，更新
        List<App> needUpdateAppList = new LinkedList<>();
        for (CreateAppDTO dto : schemaPlugins) {
            for (App app : apps) {
                if (app.getPluginData() != null && dto.getName().equals(app.getPluginData().getPluginUniId())) {
                    app.setName(dto.getName());
                    app.setIntro(dto.getIntro());
                    needUpdateAppList.add(app);
                }
            }
        }
        appDAO.updateBatchById(needUpdateAppList);
    }
}
