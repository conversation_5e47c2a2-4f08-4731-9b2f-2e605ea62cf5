package com.sinitek.mind.core.app.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.app.entity.AppTemplate;
import com.sinitek.mind.core.app.service.IFileTemplateService;
import com.sinitek.mind.core.workflow.model.SystemPluginTemplateItemType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileTemplateServiceImpl implements IFileTemplateService {

    @Value("${app.templates.path:classpath:templates/}")
    private String templatesPath;

    @Value("${app.templates.plugin.path:classpath:plugins/}")
    private String pluginPath;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final List<String> staticPluginList = List.of(
            "getTime",
            "fetchUrl",
            "feishu",
            "DingTalkWebhook",
            "WeWorkWebhook",
            "google",
            "bing",
            "bocha",
            "delay");

    private final List<String> packagePluginList = List.of(
            "mathExprVal",
            "duckduckgo",
            "duckduckgo/search",
            "duckduckgo/searchImg",
            "duckduckgo/searchNews",
            "duckduckgo/searchVideo",
            "drawing",
            "drawing/baseChart",
            "wiki",
            "databaseConnection",
            "Doc2X",
            "Doc2X/PDF2text",
            "searchXNG",
            "smtpEmail");

    @Override
    public List<AppTemplate> getFileTemplates() {
        List<AppTemplate> templates = new ArrayList<>();

        try {
            // 获取模板目录下的所有template.json文件
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources(templatesPath + "**/template.json");

            for (Resource resource : resources) {
                try {
                    // 读取JSON文件内容
                    AppTemplate template = objectMapper.readValue(resource.getInputStream(), AppTemplate.class);

                    // 从文件路径提取模板名称
                    String templateName = extractTemplateNameFromPath(resource.getURI().toString());

                    // 设置模板ID（对应原始代码中的community前缀）
                    template.setTemplateId("community-" + templateName);

                    // 设置为激活状态
                    template.setIsActive(true);

                    templates.add(template);

                } catch (Exception e) {
                    // 记录错误但继续处理其他模板
                    log.error("Error loading template from {}: {}", resource.getURI(), e.getMessage());
                }
            }

        } catch (IOException e) {
            log.error("Error scanning template files: {}", e.getMessage());
        }

        return templates;
    }

    @Override
    public List<SystemPluginTemplateItemType> getPluginTemplate() {
        List<SystemPluginTemplateItemType> plugins = new ArrayList<>();

        // 将staticPluginList和packagePluginList合并为一个新的List
        List<String> allPluginPathName = Stream.concat(staticPluginList.stream(), packagePluginList.stream())
                .toList();
        for (String pluginPathName : allPluginPathName) {
            try {
                String resourcePath = pluginPath + pluginPathName + "/template.json";
                PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
                Resource[] resources = resolver.getResources(resourcePath);
                for (Resource resource : resources) {
                    SystemPluginTemplateItemType plugin = objectMapper.readValue(resource.getInputStream(), SystemPluginTemplateItemType.class);
                    if (pluginPathName.endsWith("/")) {
                        plugin.setIsFolder(true);
                    }
                    List<String> parentIdList = Arrays.stream(pluginPathName.split("/"))
                            .limit(pluginPathName.split("/").length - 1)
                            .toList();
                    // TODO 应该都是唯一值
//                    plugin.setParentId(!parentIdList.isEmpty() ? \"community-" + String.join("/", parentIdList) : null);
//                    plugin.setId("community-" + pluginPathName);
                    plugin.setIsActive(true);
                    plugin.setIsOfficial(true);
                    plugins.add(plugin);
                }
            } catch (Exception e) {
                log.error("Error loading plugin template from {}: {}", pluginPathName, e.getMessage());
            }
        }

        return plugins;
    }

    /**
     * 从文件路径中提取模板名称
     *
     * @param filePath 文件路径
     * @return 模板名称
     */
    private String extractTemplateNameFromPath(String filePath) {
        try {
            // 从路径中提取模板目录名
            // 例如：/templates/chatbot/template.json -> chatbot
            String[] parts = filePath.split("/");
            for (int i = parts.length - 1; i >= 0; i--) {
                if ("template.json".equals(parts[i]) && i > 0) {
                    return parts[i - 1];
                }
            }
            return "unknown";
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 获取模板名称列表
     * 对应原始的getTemplateNameList函数
     *
     * @return 模板名称列表
     */
    public List<String> getTemplateNameList() {
        List<String> templateNames = new ArrayList<>();

        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources(templatesPath + "**/template.json");

            for (Resource resource : resources) {
                String templateName = extractTemplateNameFromPath(resource.getURI().toString());
                if (!"unknown".equals(templateName)) {
                    templateNames.add(templateName);
                }
            }

        } catch (IOException e) {
            System.err.println("Error getting template name list: " + e.getMessage());
        }

        return templateNames;
    }
}
