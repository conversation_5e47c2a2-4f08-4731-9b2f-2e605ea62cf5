package com.sinitek.mind.core.chat.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.mind.core.chat.dto.*;
import com.sinitek.mind.core.chat.entity.Chat;
import com.sinitek.mind.core.chat.mapper.ChatMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class ChatDAO extends ServiceImpl<ChatMapper, Chat> {
    /**
     * 根据应用ID和聊天ID查找聊天记录
     */
    public Optional<Chat> getByAppIdAndChatId(Long appId, String chatId) {
        LambdaQueryWrapper<Chat> queryWrapper = Wrappers.lambdaQuery(Chat.class);
        queryWrapper.eq(Chat::getAppId, appId);
        queryWrapper.eq(Chat::getChatId, chatId);
        return Optional.ofNullable(getOne(queryWrapper));
    }

    /**
     * 根据appID删除聊天记录
     */
    public void deleteByAppId(Long appId) {
        if (appId == null) {
            return;
        }
        LambdaQueryWrapper<Chat> queryWrapper = Wrappers.lambdaQuery(Chat.class);
        queryWrapper.eq(Chat::getAppId, appId);
        remove(queryWrapper);
    }

    public void deleteByAppIdAndChatId(Long appId, String chatId){
        if (appId == null || chatId == null) {
            return;
        }
        LambdaQueryWrapper<Chat> queryWrapper = Wrappers.lambdaQuery(Chat.class);
        queryWrapper.eq(Chat::getAppId, appId);
        queryWrapper.eq(Chat::getChatId, chatId);
        remove(queryWrapper);
    }

    public Page<Chat> pageByParamDTO(ChatPageParamDTO param, Page<Chat> page) {
        return this.baseMapper.pageByParamDTO(param, page);
    }

    public List<String> findChatIdByAppIdAndOutLinkUid(Long appId, String outLinkUid) {
        LambdaQueryWrapper<Chat> queryWrapper = Wrappers.lambdaQuery(Chat.class);
        queryWrapper.select(Chat::getChatId);
        queryWrapper.eq(Chat::getAppId, appId);
        queryWrapper.eq(Chat::getOutLinkUid, outLinkUid);
        return listObjs(queryWrapper, String::valueOf);
    }

    public List<String> findChatIdByOrgIdAndAppIdAndSource(String orgId, Long appId, String source) {
        LambdaQueryWrapper<Chat> queryWrapper = Wrappers.lambdaQuery(Chat.class);
        queryWrapper.select(Chat::getChatId);
        queryWrapper.eq(Chat::getAppId, appId);
        queryWrapper.eq(Chat::getOrgId, orgId);
        queryWrapper.eq(Chat::getSource, source);
        return listObjs(queryWrapper, String::valueOf);
    }

    public List<String> findChatIdByAppIdAndSource(Long appId, String source) {
        LambdaQueryWrapper<Chat> queryWrapper = Wrappers.lambdaQuery(Chat.class);
        queryWrapper.select(Chat::getChatId);
        queryWrapper.eq(Chat::getAppId, appId);
        queryWrapper.eq(Chat::getSource, source);
        return listObjs(queryWrapper, String::valueOf);
    }

    /**
     * 获取聊天日志分页数据
     * @param params 查询参数
     * @param page 分页参数
     * @return 聊天日志分页数据
     */
    public Page<ChatLogResponse> getChatLogs(ChatLogParamDTO params, Page<ChatLogResponse> page) {
        return this.baseMapper.getChatLogs(params, page);
    }

    /**
     * 获取聊天日志导出数据
     * @param params 查询参数
     * @return 聊天日志导出数据列表
     */
    public List<ChatLogExportDTO> getChatLogsForExport(ChatLogExportParamDTO params) {
        return this.baseMapper.getChatLogsForExport(params);
    }

    }
