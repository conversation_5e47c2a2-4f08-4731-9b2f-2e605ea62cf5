package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.chat.enumerate.AuthUserTypeEnum;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * app权限验证
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AuthAppDTO extends AuthDTO {

    @Schema(description = "appId")
    private String appId;

    @Schema(description = "对应的App")
    private App app;

    @Schema(description = "校验方式")
    private AuthUserTypeEnum authType;
}
