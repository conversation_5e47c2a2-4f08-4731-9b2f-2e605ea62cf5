package com.sinitek.mind.core.app.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.sinitek.data.mybatis.base.BaseEntity;
import com.sinitek.mind.common.typehandler.StoreEdgeItemListTypeHandler;
import com.sinitek.mind.common.typehandler.StoreNodeItemListTypeHandler;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.AppScheduledTriggerConfigType;
import com.sinitek.mind.core.app.model.PluginData;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mind_app", autoResultMap = true)
@Schema(description = "应用实体")
public class App extends BaseEntity {

    @Schema(description = "父应用ID")
    private Long parentId;

    @Schema(description = "组织ID")
    private String orgId;

    @Schema(description = "应用类型")
    private String type;

    @Schema(description = "应用名称")
    private String name;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "简介")
    private String intro;

    @Schema(description = "命名空间")
    private String namespace;

    @TableField(typeHandler = StoreNodeItemListTypeHandler.class)
    @Schema(description = "模块列表")
    private List<StoreNodeItemType> modules;

    @TableField(typeHandler = StoreEdgeItemListTypeHandler.class)
    @Schema(description = "边列表")
    private List<StoreEdgeItemType> edges;

    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "插件数据")
    private PluginData pluginData;

    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "聊天配置")
    private AppChatConfigType chatConfig;

    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "定时触发配置")
    private AppScheduledTriggerConfigType scheduledTriggerConfig;

    @Schema(description = "定时触发下次时间")
    private Date scheduledTriggerNextTime;

    @Schema(description = "是否初始化")
    private Boolean inited;

    @Schema(description = "是否继承权限", defaultValue = "true")
    private Boolean inheritPermission = true;

    @Schema(description = "默认权限")
    private Integer defaultPermission;

    /**
     * 子工具ID列表（用于MCP工具集）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "子工具ID列表（用于MCP工具集）")
    private List<Long> childToolIds;

    /**
     * MCP工具集配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "MCP工具集配置")
    private Map<String, Object> mcpToolsConfig;

    /**
     * 工具配置（用于子工具）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "工具配置（用于子工具）")
    private Map<String, Object> toolConfig;
} 