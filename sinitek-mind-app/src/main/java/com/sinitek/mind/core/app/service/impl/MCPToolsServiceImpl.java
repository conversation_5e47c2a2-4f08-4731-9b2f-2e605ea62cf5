package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.common.util.SecretUtil;
import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.dao.AppDAO;
import com.sinitek.mind.core.app.dao.AppVersionDAO;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.McpToolConfig;
import com.sinitek.mind.core.app.model.SecretValue;
import com.sinitek.mind.core.app.service.IAppService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.app.service.IMCPToolsService;
import com.sinitek.mind.core.app.util.MCPToolUtil;
import com.sinitek.mind.core.app.util.SecurityUtils;
import com.sinitek.mind.core.workflow.model.RuntimeNodeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthMemberDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MCPToolsServiceImpl implements IMCPToolsService {

    private final AppDAO appDAO;
    private final IAppService appService;
    private final AppVersionDAO appVersionDAO;
    private final IAuthService authService;
    private final IAuthAppService authAppService;
    private final IPermissionService permissionService;
    private final SecurityUtils securityUtils;
    private final IOperationLogService logService;

    /**
     * 创建MCP工具集
     * 对应原始的 create.ts 逻辑
     */
    @Transactional
    @Override
    public CreateMCPToolsResDTO createMCPTools(CreateMCPToolsDTO request) {
        try {
            log.info("开始创建MCP工具集: {}", request.getName());
            String name = request.getName();
            String avatar = request.getAvatar();
            List<McpToolConfig> toolList = request.getToolList();
            String url = request.getUrl();
            Map<String, SecretValue> headerSecret = request.getHeaderSecret();
            Long parentId = request.getParentId();
            String teamId = "";
            String tmbId = "";
            if (parentId != null) {
                AuthAppDTO authAppDTO = authAppService.authApp(parentId, PermissionConstant.WRITE_PER);
                teamId = authAppDTO.getTeamId();
                tmbId = authAppDTO.getTmbId();
            } else {
                AuthMemberDTO authMemberDTO = authService.authUserPer(PermissionConstant.APP_CREATE_PER);
                teamId = authMemberDTO.getTeamId();
                tmbId = authMemberDTO.getTmbId();
            }
            // 2. 检查团队应用限制
//            permissionService.checkTeamAppLimit(currentUser.getTeamId());

            // 3. 处理密钥存储
            Map<String, SecretValue> processedHeaderSecret = storeSecretValue(headerSecret);

            CreateAppDTO appDTO = new CreateAppDTO();
            appDTO.setName(name);
            appDTO.setAvatar(avatar);
            appDTO.setParentId(parentId);
            appDTO.setTeamId(teamId);
            appDTO.setTmbId(tmbId);
            appDTO.setType(AppTypeEnum.TOOL_SET.getValue());

            RuntimeNodeItemType mcpToolSetRuntimeNode = MCPToolUtil.getMCPToolSetRuntimeNode(request.getUrl(),
                    request.getToolList(), headerSecret, request.getName(), request.getAvatar());
            StoreNodeItemType storeNodeItemType = new StoreNodeItemType();
            BeanUtils.copyProperties(mcpToolSetRuntimeNode, storeNodeItemType);


            appDTO.setModules(List.of(storeNodeItemType));

            Map<String, Object> mcpToolsConfig = new HashMap<>();
            mcpToolsConfig.put("url", url);
            mcpToolsConfig.put("headerSecret", headerSecret);
            appDTO.setMcpToolsConfig(mcpToolsConfig);

            Long mcpToolsId = appService.createApp(appDTO);
            List<Long> toolIds = new ArrayList<>();
            for (McpToolConfig tool : toolList) {
                CreateAppDTO toolApp = new CreateAppDTO();
                toolApp.setName(tool.getName());
                toolApp.setAvatar(avatar);
                toolApp.setParentId(mcpToolsId);
                toolApp.setTeamId(teamId);
                toolApp.setTmbId(tmbId);
                toolApp.setType(AppTypeEnum.TOOL.getValue());
                toolApp.setIntro(tool.getDescription());

                // 创建MCP工具运行时节点
                RuntimeNodeItemType mcpToolRuntimeNode = MCPToolUtil.getMCPToolRuntimeNode(
                        tool, url, headerSecret, avatar);
                StoreNodeItemType storeNodeItemTypeTool = new StoreNodeItemType();
                BeanUtils.copyProperties(mcpToolRuntimeNode, storeNodeItemTypeTool);
                toolApp.setModules(List.of(storeNodeItemTypeTool));
                // 设置工具配置
                Map<String, Object> toolConfigMap = new HashMap<>();
                toolConfigMap.put("name", tool.getName());
                toolConfigMap.put("description", tool.getDescription());
                toolConfigMap.put("inputSchema", tool.getInputSchema());
                toolApp.setToolConfig(toolConfigMap);
                Long toolId = appService.createApp(toolApp);
                toolIds.add(toolId);
            }

            // 4. 创建主应用（MCP工具集）
//            App mcpToolsApp = createMCPToolsApp(request, teamId, tmbId, userId, processedHeaderSecret);
//
//            App savedMcpApp = appDAO.save(mcpToolsApp);

//            // 5. 创建子工具应用
//            List<String> childToolIds = createChildToolApps(toolList, savedMcpApp, userId);

            // 6. 更新主应用的子工具引用
            updateMCPToolsAppWithChildren(mcpToolsId, toolIds);

            // 7. 记录操作日志
            logService.addOperationLog(MindConstant.APP, String.format("创建应用【%s】", name));

            log.info("MCP工具集创建成功: {}, 子工具数量: {}", mcpToolsId, toolIds.size());
            return CreateMCPToolsResDTO.success(mcpToolsId, toolIds);

        } catch (Exception e) {
            log.error("创建MCP工具集失败: {}", e.getMessage(), e);
            throw new BussinessException(AppErrorCodeConstant.MCP_TOOLS_CREATE_FAILED);
        }
    }

    @Override
    public UpdateMCPToolsResDTO updateMCPTools(UpdateMCPToolsDTO request, String authToken) {
        try {
            log.info("开始更新MCP工具集: {}", request.getAppId());

            // 1. 认证和权限验证
//            User currentUser = authService.authenticateUser(authToken);
//            if (currentUser == null) {
//                throw new BussinessException("认证失败");
//            }

            // 2. 获取现有应用
            App existingApp = appDAO.getById(request.getAppId());

            // 3. 权限检查
//            if (!hasPermission(currentUser, existingApp)) {
//                throw new BussinessException("无权限操作此应用");
//            }

            // 4. 处理密钥存储
            Map<String, String> processedHeaderSecret = processSecretValues(request.getHeaderSecret());

            // 5. 比较工具集数据变化
            boolean hasChanges = hasToolSetChanges(existingApp, request, processedHeaderSecret);

            // 6. 更新子工具 TODO user传值
            MCPChildrenUpdateResult updateResult = updateMCPChildrenTools(
                    existingApp, request.getToolList(), null);

            // 7. 更新主应用
            if (hasChanges || updateResult.hasChanges()) {
                updateMCPToolsApp(existingApp, request, processedHeaderSecret, updateResult.getAllChildIds());
            }

            // 8. 记录操作日志
            logService.addOperationLog(MindConstant.APP, String.format("更新应用信息【%s】", existingApp.getName()));

            log.info("MCP工具集更新成功: {}", request.getAppId());
            return UpdateMCPToolsResDTO.success(
                    request.getAppId(),
                    updateResult.getUpdatedToolIds(),
                    updateResult.getCreatedToolIds(),
                    updateResult.getDeletedToolIds()
            );

        } catch (Exception e) {
            log.error("更新MCP工具集失败: {}", e.getMessage(), e);
            throw new BussinessException(AppErrorCodeConstant.MCP_TOOLS_UPDATE_FAILED);
        }
    }

    public static Map<String, SecretValue> storeSecretValue(
            Map<String, SecretValue> storeSecret) {

        if (storeSecret == null) {
            storeSecret = new HashMap<>();
        }

        return storeSecret.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            try {
                                return new SecretValue(
                                        SecretUtil.encryptSecret(entry.getValue().getValue()),
                                        ""
                                );
                            } catch (Exception e) {
                                throw new BussinessException(AppErrorCodeConstant.SECRET_ENCRYPTION_FAILED);
                            }
                        }
                ));
    }

    /**
     * 处理密钥值
     */
    private Map<String, String> processSecretValues(Map<String, SecretValue> headerSecret) {
        if (headerSecret == null || headerSecret.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, String> processed = new HashMap<>();
        for (Map.Entry<String, SecretValue> entry : headerSecret.entrySet()) {
            SecretValue secretValue = entry.getValue();
            if (secretValue != null) {
                // 如果有明文值，则加密存储；否则使用已加密的值
                String storedValue = StringUtils.isNotBlank(secretValue.getValue())
                        ? securityUtils.encryptSecret(secretValue.getValue())
                        : secretValue.getSecret();
                processed.put(entry.getKey(), storedValue);
            }
        }
        return processed;
    }

    /**
     * 创建MCP工具集应用
     */
    private App createMCPToolsApp(CreateMCPToolsDTO request, String teamId, String tmbId, String userId, Map<String, SecretValue> headerSecret) {

        RuntimeNodeItemType mcpToolSetRuntimeNode = MCPToolUtil.getMCPToolSetRuntimeNode(request.getUrl(),
                request.getToolList(), headerSecret, request.getName(), request.getAvatar());
        StoreNodeItemType storeNodeItemType = new StoreNodeItemType();
        BeanUtils.copyProperties(mcpToolSetRuntimeNode, storeNodeItemType);


        App app = new App();
//        app.setId(generateId());
        app.setOrgId(tmbId);
        app.setName(request.getName());
        app.setAvatar(request.getAvatar());
        app.setIntro(request.getIntro());

        app.setType(AppTypeEnum.TOOL_SET.getValue());
        app.setParentId(request.getParentId());
        app.setModules(List.of(storeNodeItemType));

        return app;
    }

    /**
     * 创建子工具应用
     */
    private List<String> createChildToolApps(List<McpToolConfig> toolList, App parentApp, String userId) {
        List<String> childIds = new ArrayList<>();

        for (McpToolConfig toolConfig : toolList) {
            App childApp = createChildToolApp(toolConfig, parentApp, userId);
            appDAO.save(childApp);
            childIds.add(String.valueOf(childApp.getId()));
        }

        return childIds;
    }

    /**
     * 创建单个子工具应用
     */
    private App createChildToolApp(McpToolConfig toolConfig, App parentApp, String userId) {
        App childApp = new App();
//        childApp.setId(generateId());
        childApp.setOrgId(parentApp.getOrgId());
        childApp.setName(toolConfig.getName());
        childApp.setIntro(toolConfig.getDescription());
        childApp.setType(AppTypeEnum.PLUGIN.getValue());
        childApp.setParentId(parentApp.getId());
        childApp.setUpdateTimeStamp(new Date());

        // 设置工具配置
        Map<String, Object> toolConfigMap = new HashMap<>();
        toolConfigMap.put("name", toolConfig.getName());
        toolConfigMap.put("description", toolConfig.getDescription());
        toolConfigMap.put("inputSchema", toolConfig.getInputSchema());
        childApp.setToolConfig(toolConfigMap);

        // 创建MCP工具运行时节点
        Map<String, Object> mcpToolsConfig = parentApp.getMcpToolsConfig();
        if (mcpToolsConfig != null) {
            String url = (String) mcpToolsConfig.get("url");
            Map<String, SecretValue> headerSecret = (Map<String, SecretValue>) mcpToolsConfig.get("headerSecret");
            RuntimeNodeItemType mcpToolRuntimeNode = MCPToolUtil.getMCPToolRuntimeNode(
                    toolConfig, url, headerSecret, parentApp.getAvatar()
            );
            StoreNodeItemType storeNodeItemType = new StoreNodeItemType();
            BeanUtils.copyProperties(mcpToolRuntimeNode, storeNodeItemType);
            childApp.setModules(List.of(storeNodeItemType));
        }

        return childApp;
    }

    /**
     * 更新MCP工具集的子工具引用
     */
    private void updateMCPToolsAppWithChildren(Long appId, List<Long> childIds) {
        appDAO.lambdaUpdate()
                .eq(App::getId, appId)
                .set(App::getChildToolIds, childIds)
                .update();
    }

    /**
     * 检查工具集数据是否有变化
     */
    private boolean hasToolSetChanges(App existingApp, UpdateMCPToolsDTO request,
                                      Map<String, String> processedHeaderSecret) {
//        Map<String, Object> existingConfig = existingApp.getMcpToolsConfig();
        Map<String, Object> existingConfig = new HashMap<>();
        if (existingConfig == null) {
            return true;
        }

        String existingUrl = (String) existingConfig.get("url");
        Map<String, String> existingHeaderSecret = (Map<String, String>) existingConfig.get("headerSecret");

        return !Objects.equals(existingUrl, request.getUrl()) ||
                !Objects.equals(existingHeaderSecret, processedHeaderSecret);
    }

    /**
     * 更新子工具
     */
    private MCPChildrenUpdateResult updateMCPChildrenTools(App parentApp, List<McpToolConfig> newToolList, String userId) {
        // 获取现有子工具
        List<Long> existingChildIds = parentApp.getChildToolIds() != null ?
                parentApp.getChildToolIds() : new ArrayList<>();
        List<App> existingChildren = appDAO.findByIdIn(existingChildIds);

        Map<String, App> existingToolMap = existingChildren.stream()
                .collect(Collectors.toMap(App::getName, app -> app));

        List<Long> updatedToolIds = new ArrayList<>();
        List<Long> createdToolIds = new ArrayList<>();
        List<Long> allChildIds = new ArrayList<>();

        // 处理新工具列表
        for (McpToolConfig newTool : newToolList) {
            App existingTool = existingToolMap.get(newTool.getName());

            if (existingTool != null) {
                // 更新现有工具
                if (hasToolConfigChanges(existingTool, newTool)) {
                    updateChildToolApp(existingTool, newTool);
                    updatedToolIds.add(existingTool.getId());
                }
                allChildIds.add(existingTool.getId());
                existingToolMap.remove(newTool.getName());
            } else {
                // 创建新工具
                App newChildApp = createChildToolApp(newTool, parentApp, userId);
                appDAO.save(newChildApp);
                createdToolIds.add(newChildApp.getId());
                allChildIds.add(newChildApp.getId());
            }
        }

        // 删除不再需要的工具
        List<Long> deletedToolIds = existingToolMap.values().stream()
                .map(App::getId)
                .toList();
        appDAO.removeBatchByIds(deletedToolIds);

        return new MCPChildrenUpdateResult(updatedToolIds, createdToolIds, deletedToolIds, allChildIds);
    }

    /**
     * 检查工具配置是否有变化
     */
    private boolean hasToolConfigChanges(App existingTool, McpToolConfig newConfig) {
        Map<String, Object> existingConfig = existingTool.getToolConfig();
        if (existingConfig == null) {
            return true;
        }

        return !Objects.equals(existingConfig.get("description"), newConfig.getDescription()) ||
                !Objects.equals(existingConfig.get("inputSchema"), newConfig.getInputSchema());
    }

    /**
     * 更新子工具应用
     */
    private void updateChildToolApp(App childApp, McpToolConfig newConfig) {
        childApp.setIntro(newConfig.getDescription());
        childApp.setUpdateTimeStamp(new Date());

        Map<String, Object> toolConfig = childApp.getToolConfig();
        if (toolConfig == null) {
            toolConfig = new HashMap<>();
        }
        toolConfig.put("description", newConfig.getDescription());
        toolConfig.put("inputSchema", newConfig.getInputSchema());
        childApp.setToolConfig(toolConfig);

        appDAO.save(childApp);
    }

    /**
     * 更新MCP工具集应用
     */
    private void updateMCPToolsApp(App app, UpdateMCPToolsDTO request,
                                   Map<String, String> headerSecret, List<Long> childIds) {
        app.setUpdateTimeStamp(new Date());
        app.setChildToolIds(childIds);

        Map<String, Object> mcpConfig = app.getMcpToolsConfig();
        if (mcpConfig == null) {
            mcpConfig = new HashMap<>();
        }
        mcpConfig.put("url", request.getUrl());
        mcpConfig.put("headerSecret", headerSecret);
        app.setMcpToolsConfig(mcpConfig);

        appDAO.save(app);
    }

    /**
     * 检查用户权限
     */
//    private boolean hasPermission(User user, App app) {
//        return Objects.equals(user.getId(), app.getUserId()) ||
//                Objects.equals(user.getTeamId(), app.getTeamId());
//    }


    /**
     * 生成ID
     */
    private String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 子工具更新结果
     */
    private static class MCPChildrenUpdateResult {
        private final List<Long> updatedToolIds;
        private final List<Long> createdToolIds;
        private final List<Long> deletedToolIds;
        private final List<Long> allChildIds;

        public MCPChildrenUpdateResult(List<Long> updatedToolIds, List<Long> createdToolIds,
                                       List<Long> deletedToolIds, List<Long> allChildIds) {
            this.updatedToolIds = updatedToolIds;
            this.createdToolIds = createdToolIds;
            this.deletedToolIds = deletedToolIds;
            this.allChildIds = allChildIds;
        }

        public boolean hasChanges() {
            return !updatedToolIds.isEmpty() || !createdToolIds.isEmpty() || !deletedToolIds.isEmpty();
        }

        public List<Long> getUpdatedToolIds() { return updatedToolIds; }
        public List<Long> getCreatedToolIds() { return createdToolIds; }
        public List<Long> getDeletedToolIds() { return deletedToolIds; }
        public List<Long> getAllChildIds() { return allChildIds; }
    }
}
