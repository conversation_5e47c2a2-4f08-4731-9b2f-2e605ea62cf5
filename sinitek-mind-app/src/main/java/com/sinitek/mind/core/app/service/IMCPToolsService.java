package com.sinitek.mind.core.app.service;

import com.sinitek.mind.core.app.dto.CreateMCPToolsDTO;
import com.sinitek.mind.core.app.dto.CreateMCPToolsResDTO;
import com.sinitek.mind.core.app.dto.UpdateMCPToolsDTO;
import com.sinitek.mind.core.app.dto.UpdateMCPToolsResDTO;

public interface IMCPToolsService {

    CreateMCPToolsResDTO createMCPTools(CreateMCPToolsDTO request);

    UpdateMCPToolsResDTO updateMCPTools(UpdateMCPToolsDTO request, String authToken);
}
