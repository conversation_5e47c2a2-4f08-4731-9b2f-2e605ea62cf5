package com.sinitek.mind.core.app.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.model.AppDetailType;
import com.sinitek.mind.core.app.service.IAppService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.chat.dto.ChatLogResponse;
import com.sinitek.mind.core.chat.dto.GetChatLogsRequest;
import com.sinitek.mind.core.chat.service.IChatLogService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/mind/api/core/app")
@RequiredArgsConstructor
@Tag(name = "应用管理", description = "应用相关接口")
public class AppController {

    private final IAppService appService;
    private final IAuthService authService;
    private final IChatLogService chatLogService;
    private final IPermissionService permissionService;
    private final IAuthAppService authAppService;

    @PostMapping("/list")
    @Operation(summary = "获取应用列表", description = "根据条件获取应用列表")
    public ApiResponse<List<AppListItemDTO>> getAppList(@RequestBody ListAppDTO request) {
        List<AppListItemDTO> appList = appService.getAppList(request);
        return ApiResponse.success(appList);

    }

    @PostMapping("/create")
    @Operation(summary = "创建应用", description = "创建新的app")
    public ApiResponse<String> createApp(@RequestBody CreateAppDTO body) {
        String appId = appService.createApp(body);
        return ApiResponse.success(appId);
    }

    @GetMapping("/detail")
    @Operation(summary = "应用详情", description = "根据appId获取app详情")
    public ApiResponse<AppDetailType> getAppDetail(@RequestParam("appId") String appId) {
        AppDetailType appDetail = appService.getAppDetail(appId);
        return ApiResponse.success(appDetail);
    }

    @PostMapping("/del")
    @Operation(summary = "删除应用", description = "根据appId删除应用")
    public ApiResponse<Void> deleteApp(@RequestParam String appId) {
        appService.deleteApp(appId);
        return ApiResponse.success();
    }

    @GetMapping("/copy")
    @Operation(summary = "复制应用", description = "根据appId复制一个相同的应用")
    public ApiResponse<String> copyApp(@RequestParam("appId") String appId) {
        String appIdNew = appService.copyApp(appId);
        return ApiResponse.success(appIdNew);
    }

    @PostMapping("/update")
    @Operation(summary = "更新应用")
    public ApiResponse<App> updateApp(@RequestParam String appId, @RequestBody AppUpdateDTO params) {
        params.setAppId(appId);
        App app = appService.updateApp(params);
        return ApiResponse.success(app);
    }

    @PostMapping("/transitionWorkflow")
    @Operation(summary = "转换工作流")
    private ApiResponse<String> transitionWorkFlow(@RequestBody TransitionWorkflowDTO body) {
        String appId = appService.transitionWorkFlow(body);
        return ApiResponse.success(appId);
    }

    @GetMapping("/resumeInheritPermission")
    @Operation(summary = "恢复应用继承权限")
    public ApiResponse<String> resumeInheritPermission(@RequestParam("appId") String appId) {
        String appIdNew = appService.resumeInheritPermission(appId);
        return ApiResponse.success(appIdNew);
    }

    @PostMapping("/getBasicInfo")
    @Operation(summary = "根据id列表获取基本信息")
    public ApiResponse<List<BasicInfoResDTO>> getBasicInfo(@RequestBody List<String> ids) throws Exception {

        AuthDTO authDTO = authService.authCert();

        List<BasicInfoResDTO> basicInfoList = appService.getBasicInfo(authDTO.getTeamId(), ids);

        return ApiResponse.success(basicInfoList);
    }

    @PostMapping("/getChatLogs")
    @Operation(summary = "获取应用聊天日志")
    public ApiResponse<PageResult<ChatLogResponse>> getChatLogs(@RequestBody GetChatLogsRequest request) {
        try {
            // 参数校验
            if (request.getAppId() == null || request.getAppId().trim().isEmpty()) {
                throw new BussinessException(AppErrorCodeConstant.APP_ID_CANNOT_BE_EMPTY);
            }
            // 设置默认值
            if (request.getDateStart() == null) {
                request.setDateStart(Date.from(
                        java.time.ZonedDateTime.now()
                                .minusDays(7)
                                .toInstant()
                ));
            }
            if (request.getDateEnd() == null) {
                request.setDateEnd(new Date());
            }
            AuthAppDTO authAppDTO = authAppService.authApp(request.getAppId(), PermissionConstant.READ_PER);
            PageResult<ChatLogResponse> chatLogs = chatLogService.getChatLogs(request, authAppDTO.getTeamId(), authAppDTO.getTmbId(), authAppDTO.getApp());

            return ApiResponse.success(chatLogs);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 导出聊天日志为CSV格式
     * @param request 导出请求参数
     * @param response HTTP响应
     * @return 响应结果
     */
    @PostMapping("/exportChatLogs")
    public ApiResponse<?> exportChatLogs(
            @RequestBody ExportChatLogsBody request,
            HttpServletResponse response) {

        try {
            // 参数验证
            if (request.getAppId() == null || request.getAppId().trim().isEmpty()) {
                return ApiResponse.error("缺少参数: appId");
            }

            // 设置默认值
            if (request.getDateStart() == null) {
                request.setDateStart(Date.from(
                        java.time.ZonedDateTime.now()
                                .minusDays(7)
                                .toInstant()
                ));
            }
            if (request.getDateEnd() == null) {
                request.setDateEnd(new Date());
            }
            if (request.getTitle() == null || request.getTitle().trim().isEmpty()) {
                request.setTitle("chat_logs");
            }

            // 身份验证
            AuthAppDTO authAppDTO = authAppService.authApp(request.getAppId(), PermissionConstant.READ_PER);

            // 检查应用是否存在
            if (authAppDTO.getApp() == null) {
                return ApiResponse.error("应用不存在");
            }

            // 执行导出
            chatLogService.exportChatLogs(request, authAppDTO.getTeamId(), response);
            return null; // 由于直接写入response，不需要返回ResponseEntity

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResponse.error("导出聊天日志失败: " + e.getMessage());
        }
    }
}
