package com.sinitek.mind.core.workflow.service;

import com.sinitek.mind.core.workflow.model.ChatDispatchProps;
import com.sinitek.mind.core.workflow.model.DispatchFlowResponse;
import com.sinitek.mind.support.outlink.entity.OutLink;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 工作流服务接口
 */
public interface IWorkflowService {

    Map<String, Object> getSystemVariables(ChatDispatchProps props);

    CompletableFuture<DispatchFlowResponse> dispatchWorkFlow(ChatDispatchProps props);

    /**
     * 免登录窗口专用的，限流方法
     * @param dispatchProps
     * @param outLink
     * @return
     */
    CompletableFuture<DispatchFlowResponse> dispatchWorkFlowWithQpmLimit(ChatDispatchProps dispatchProps, OutLink outLink);
}