package com.sinitek.mind.core.workflow.dispatch.tools;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.ai.model.ChatCompletionContentPart;
import com.sinitek.mind.core.ai.model.ChatCompletionContentPartText;
import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.core.ai.model.ChatCompletionMessageToolCall;
import com.sinitek.mind.core.ai.util.AiUtil;
import com.sinitek.mind.core.ai.util.PromptUtil;
import com.sinitek.mind.core.app.mcp.McpToolDataType;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.JSONSchemaInputType;
import com.sinitek.mind.core.app.model.JsonSchemaPropertiesItemType;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.enumerate.ChatCompletionRequestMessageRoleEnum;
import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.model.*;
import com.sinitek.mind.core.chat.util.ChatUtil;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.dispatch.NodeService;
import com.sinitek.mind.core.workflow.dispatch.util.DispatchUtil;
import com.sinitek.mind.core.workflow.enumerate.*;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.model.sse.GptResponse;
import com.sinitek.mind.core.workflow.model.sse.SseMapResponse;
import com.sinitek.mind.core.workflow.model.sse.SseResponseType;
import com.sinitek.mind.core.workflow.util.FileUtil;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.model.core.llm.LLMChatModelFactory;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.enumerate.ModelTypeEnum;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.mind.support.wallet.model.ModelPointsRequest;
import com.sinitek.mind.support.wallet.util.WalletUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.definition.ToolDefinition;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Agent工具调用节点调度器
 * 对应 TypeScript 中的 runTool/index.ts 文件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AgentRunToolsDispatcher implements NodeDispatcher {

    private final ISystemModelService systemModelService;
    private final LLMChatModelFactory llmChatModelFactory;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final NodeService nodeService;

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.TOOLS.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchRunTools(dispatchData);
    }

    /**
     * 执行工具调用节点
     * 对应 TypeScript 中的 dispatchRunTools 函数
     *
     * @param dispatchData 调度数据
     *        ---dispatchData.getParam()包含数据：
     *                    List<ChatItemType>  history
     *                    String userChatInput
     *                    List<String> fileUrlList
     *                    String aiModel
     *                    String systemPrompt
     *                    Double temperature
     *                    Integer maxToken
     *                    boolean aiChatVision
     *                    boolean aiChatReasoning
     *                    Double aiChatTopP
     *                    String aiChatStopSign
     *                    String aiChatResponseFormat
     *                    String aiChatJsonSchema
     *
     * @return 节点执行结果
     */
    private Map<String, Object> dispatchRunTools(ModuleDispatchProps dispatchData) {
        // 提取基本参数
        RuntimeNodeItemType node = dispatchData.getNode();
        String nodeId = node.getNodeId();
        String name = node.getName();
        Boolean isEntry = node.getIsEntry();
        Long version = node.getVersion();
        List<FlowNodeInputItemType> inputs = node.getInputs();

        List<RuntimeNodeItemType> runtimeNodes = dispatchData.getRuntimeNodes();
        List<RuntimeEdgeItemType> runtimeEdges = dispatchData.getRuntimeEdges();
        List<ChatItemType> histories = dispatchData.getHistories();
        List<ChatItemValueItemType> query = dispatchData.getQuery();
        String requestOrigin = dispatchData.getRequestOrigin();
        AppChatConfigType chatConfig = dispatchData.getChatConfig();
        WorkflowInteractiveResponseType lastInteractive = dispatchData.getLastInteractive();
        RunningUserInfo runningUserInfo = dispatchData.getRunningUserInfo();
        ExternalProviderType externalProvider = dispatchData.getExternalProvider();

        // 提取参数
        Map<String, Object> params = dispatchData.getParams();
        String model = (String) params.get("model");
        String systemPrompt = (String) params.get("systemPrompt");
        String userChatInput = (String) params.get("userChatInput");
        Integer history = (Integer) params.getOrDefault("history", 6);
        List<String> fileLinks = parseFileLinks(params);
        Boolean aiChatVision = (Boolean) params.get("aiChatVision");
        Boolean aiChatReasoning = (Boolean) params.get("aiChatReasoning");

        // 获取模型信息
        SystemModelDTO toolModel = systemModelService.getModelDetail(model);
        boolean useVision = Boolean.TRUE.equals(aiChatVision) && Boolean.TRUE.equals(toolModel.getVision());
        List<ChatItemType> chatHistories = DispatchUtil.getHistories(history, histories);

        // 更新参数
        params.put("aiChatVision", aiChatVision && Boolean.TRUE.equals(toolModel.getVision()));
        params.put("aiChatReasoning", aiChatReasoning && (toolModel.getReasoning() != null && toolModel.getReasoning()));

        // 检查文件URL输入
        Optional<FlowNodeInputItemType> fileUrlInput = inputs.stream()
                .filter(item -> NodeInputKeyEnum.FILE_URL_LIST.getValue().equals(item.getKey()))
                .findFirst();
        if (fileUrlInput.isEmpty() || fileUrlInput.get().getValue() == null ||
                (fileUrlInput.get().getValue() instanceof List && ((List<?>) fileUrlInput.get().getValue()).isEmpty())) {
            fileLinks = null;
        }

        // 获取工具节点ID列表
        List<String> toolNodeIds = DispatchUtil.filterToolNodeIdByEdges(nodeId, runtimeEdges);

        // 构建工具节点列表
        List<ToolNodeItemType> toolNodes = toolNodeIds.stream()
                .map(toolNodeId -> runtimeNodes.stream()
                        .filter(runtimeNode -> toolNodeId.equals(runtimeNode.getNodeId()))
                        .findFirst()
                        .orElse(null))
                .filter(Objects::nonNull)
                .map(this::buildToolNodeItem)
                .toList();

        // 设置节点非入口
        node.setIsEntry(false);

        // 检查是否有读取文件工具
        boolean hasReadFilesTool = toolNodes.stream()
                .anyMatch(item -> FlowNodeTypeEnum.READ_FILES.getValue().equals(item.getFlowNodeType()));

        // 获取全局文件
        List<ChatItemValueItemFileInfo> globalFiles = ChatAdaptor.chatValue2RuntimePrompt(query).getFiles();

        // 获取多输入处理结果
        MultiInputResult multiInputResult = getMultiInput(
                runningUserInfo, chatHistories, requestOrigin,
                chatConfig != null && chatConfig.getFileSelectConfig() != null ?
                        chatConfig.getFileSelectConfig().getMaxFiles() : 20,
                chatConfig != null && chatConfig.getFileSelectConfig() != null ?
                        chatConfig.getFileSelectConfig().getCustomPdfParse() : null,
                fileLinks, globalFiles, hasReadFilesTool
        );

        // 构建系统提示词
        String concatenateSystemPrompt = buildSystemPrompt(
                toolModel.getDefaultSystemChatPrompt(), systemPrompt,
                multiInputResult.getDocumentQuoteText(), String.valueOf(version)
        );

        // 构建消息列表
        List<ChatItemType> messages = buildMessages(
                concatenateSystemPrompt, chatHistories, userChatInput,
                multiInputResult.getUserFiles(), hasReadFilesTool, lastInteractive, isEntry
        );

        // 文本审查
        if (Boolean.TRUE.equals(toolModel.getCensor()) &&
                (externalProvider.getOpenaiAccount() == null ||
                        !StringUtils.hasText(externalProvider.getOpenaiAccount().getKey()))) {
            // TODO: 实现文本审查逻辑
            log.debug("Text censor check for: {}", systemPrompt + "\n" + userChatInput);
        }

        RunToolResponse toolResponse = executeToolCall(dispatchData, toolModel, toolNodes, messages, lastInteractive, model);

        ModelPointsRequest request = new ModelPointsRequest();
        request.setModel(model);
        request.setInputTokens(toolResponse.getToolNodeInputTokens());
        request.setOutputTokens(toolResponse.getToolNodeOutputTokens());
        request.setModelType(ModelTypeEnum.LLM.getCode());
        ChatNodeUsageType chatNodeUsageType = WalletUtil.formatModelChars2Points(request);
        double totalPoints = chatNodeUsageType.getTotalPoints();
        String modelName = chatNodeUsageType.getModuleName();
        double toolAIUsage = 0;
        if (externalProvider != null && externalProvider.getOpenaiAccount() != null && externalProvider.getOpenaiAccount().getKey() != null) {
            toolAIUsage = totalPoints;
        }

        List<ChatHistoryItemResType> childToolResponse = toolResponse.getDispatchFlowResponse().stream()
                .flatMap(item -> item.getFlowResponses().stream()).toList();

        Double totalPointsUsage = toolAIUsage + toolResponse.getDispatchFlowResponse().stream()
                .mapToDouble(item ->
                        item.getFlowUsage().stream()
                                .mapToDouble(ChatNodeUsageType::getTotalPoints)
                                .sum()
                )
                .sum();

        List<ChatNodeUsageType> flatUsages = toolResponse.getDispatchFlowResponse().stream()
                .flatMap(item -> item.getFlowUsage().stream())
                .toList();

        List<AIChatItemValueItemType> previewAssistantResponses = DispatchUtil.filterToolResponseToPreview(toolResponse.getAssistantResponses());


        // 计算使用量和积分
        UsageCalculationResult usageResult = calculateUsage(
                model, toolResponse.getToolNodeInputTokens(),
                toolResponse.getToolNodeOutputTokens(), externalProvider
        );

        // 构建返回结果
        return buildResponse(previewAssistantResponses,childToolResponse,flatUsages,totalPointsUsage,
                nodeId, modelName, toolResponse, usageResult, userChatInput,
                useVision, toolResponse.getCompleteMessages()
        );
    }

    private List<String> parseFileLinks(Map<String, Object> params) {
        List<String> fileLinks = new ArrayList<>();
        Object oriLinks = params.get("fileUrlList");
        extractStringsRecursively(oriLinks, fileLinks);
        return fileLinks;
    }

    private void extractStringsRecursively(Object source, List<String> result) {
        if (source instanceof List) {
            for (Object item : (List<?>) source) {
                extractStringsRecursively(item, result);
            }
        } else if (source instanceof String) {
            result.add((String) source);
        }
        // 忽略其他类型（原逻辑）
    }

    /**
     * 构建工具节点项
     */
    private ToolNodeItemType buildToolNodeItem(RuntimeNodeItemType tool) {
        List<FlowNodeInputItemType> toolParams = new ArrayList<>();
        JSONSchemaInputType jsonSchema = null;

        if (!CollectionUtils.isEmpty(tool.getInputs())) {
            for (FlowNodeInputItemType input : tool.getInputs()) {
                if (input.getToolDescription() != null && !input.getToolDescription().isEmpty()) {
                    toolParams.add(input);
                }

                if (NodeInputKeyEnum.TOOL_DATA.getValue().equals(input.getKey()) ||
                        "toolData".equals(input.getKey())) {
                    McpToolDataType value = objectMapper.convertValue(input.getValue(), McpToolDataType.class);
                    jsonSchema = value.getInputSchema();
                }
            }
        }
        ToolNodeItemType.ToolNodeItemTypeBuilder<?, ?> builder = ToolNodeItemType.builder()
                .nodeId(tool.getNodeId())
                .name(tool.getName())
                .avatar(tool.getAvatar())
                .intro(tool.getIntro())
                .flowNodeType(tool.getFlowNodeType())
                .showStatus(tool.getShowStatus())
                .isEntry(tool.getIsEntry())
                .inputs(tool.getInputs())
                .outputs(tool.getOutputs())
                .pluginId(tool.getPluginId())
                .version(tool.getVersion())
                .toolConfig(tool.getToolConfig());

        builder.toolParams(toolParams)
                .jsonSchema(jsonSchema);
        return builder.build();
    }

    /**
     * 获取多输入处理结果   -packages\service\core\workflow\dispatch\agent\runTool\index.ts#getMultiInput
     */
    public static MultiInputResult getMultiInput(
            RunningUserInfo runningUserInfo, List<ChatItemType> histories,
            String requestOrigin, Integer maxFiles, Boolean customPdfParse,
            List<String> fileLinks, List<ChatItemValueItemFileInfo> inputFiles,
            boolean hasReadFilesTool) {

        // 如果没有文件链接或有读取文件工具，直接返回
        if (CollectionUtils.isEmpty(fileLinks) || hasReadFilesTool) {
            return MultiInputResult.builder()
                    .documentQuoteText("")
                    .userFiles(inputFiles != null ? inputFiles : new ArrayList<>())
                    .build();
        }

        List<String> filesFromHistories = DispatchUtil.getHistoryFileLinks(histories);

        List<String> urls = new ArrayList<>();
        urls.addAll(filesFromHistories);
        urls.addAll(fileLinks);

        if (urls.isEmpty()) {
            return MultiInputResult.builder()
                    .documentQuoteText("")
                    .userFiles(new ArrayList<>())
                    .build();
        }

        String text = DispatchUtil.getFileContentFromLinks(urls, requestOrigin, maxFiles, runningUserInfo.getTeamId(), runningUserInfo.getTmbId(), customPdfParse).getText();
        List<ChatItemValueItemFileInfo> userFiles = fileLinks.stream().map(FileUtil::parseUrlToFileType).toList();

        return MultiInputResult.builder()
                .documentQuoteText(text)
                .userFiles(userFiles)
                .build();
    }


    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(String defaultPrompt, String systemPrompt,
                                     String documentQuoteText, String version) {
        List<String> prompts = new ArrayList<>();

        if (StringUtils.hasText(defaultPrompt)) {
            prompts.add(defaultPrompt);
        }
        if (StringUtils.hasText(systemPrompt)) {
            prompts.add(systemPrompt);
        }
        if (StringUtils.hasText(documentQuoteText)) {

            String documentQuotePrompt = PromptUtil.getDocumentQuotePrompt(version);
            String o = (String) WorkflowUtil.replaceVariable(documentQuotePrompt, Map.of("quote", documentQuoteText));
            prompts.add(o);
        }

        return String.join("\n\n===---===---===\n\n", prompts);
    }

    /**
     * 构建消息列表
     */
    private List<ChatItemType> buildMessages(
            String systemPrompt, List<ChatItemType> chatHistories,
            String userChatInput, List<ChatItemValueItemFileInfo> userFiles,
            boolean hasReadFilesTool, WorkflowInteractiveResponseType lastInteractive,
            Boolean isEntry) {

        List<ChatItemType> systemPromptChatItemType = ChatAdaptor.getSystemPrompt_ChatItemType(systemPrompt);
        List<ChatItemType> messages = new ArrayList<>(systemPromptChatItemType);

        // 添加历史消息
        for (ChatItemType history : chatHistories) {
            if (ChatRoleEnum.HUMAN.equals(history.getObj())) {
                // 适配工具调用消息
                ChatItemType chatItemType = new ChatItemType();
                BeanUtils.copyProperties(history, chatItemType);
                List<ChatItemValueItemType> itemTypes = DispatchUtil.toolCallMessagesAdapt(history.getValue(), !hasReadFilesTool);
                chatItemType.setValue(itemTypes);
                messages.add(chatItemType);
            } else {
                messages.add(history);
            }
        }

        // 添加当前用户输入
        RuntimePromptType type = new RuntimePromptType();
        type.setText(userChatInput);
        type.setFiles(userFiles);
        List<ChatItemValueItemType> userInput = ChatAdaptor.runtimePrompt2ChatsValue(type);
        List<ChatItemValueItemType> itemTypes = DispatchUtil.toolCallMessagesAdapt(userInput, !hasReadFilesTool);
        ChatItemType userMessage = new ChatItemType();
        userMessage.setObj(ChatRoleEnum.HUMAN);
        userMessage.setValue(itemTypes);
        messages.add(userMessage);

        // 如果是交互式入口，移除最后两条消息
        if (lastInteractive != null && Boolean.TRUE.equals(isEntry) && messages.size() >= 2) {
            messages = messages.subList(0, messages.size() - 2);
        }

        return messages;
    }

    /**
     * 执行工具调用
     */
    private RunToolResponse executeToolCall(
            ModuleDispatchProps dispatchData, SystemModelDTO toolModel,
            List<ToolNodeItemType> toolNodes, List<ChatItemType> messages,
            WorkflowInteractiveResponseType lastInteractive, String model) {

        String requestOrigin = dispatchData.getRequestOrigin();
        List<RuntimeNodeItemType> runtimeNodes = dispatchData.getRuntimeNodes();
        List<RuntimeEdgeItemType> runtimeEdges = dispatchData.getRuntimeEdges();
        ExternalProviderType externalProvider = dispatchData.getExternalProvider();

        // 提取参数
        Map<String, Object> params = dispatchData.getParams();


        ChatsToGPTMessagesParam param = new ChatsToGPTMessagesParam();
        param.setMessages(messages);
        param.setReserveId(false);

        List<ChatCompletionMessageParam> adaptMessages = ChatAdaptor.chats2GPTMessages(param);

        // TODO: 实现具体的工具调用逻辑
        RunToolResponse toolResponse = null;
        if (toolModel.getToolChoice()!= null && toolModel.getToolChoice()) {
            // 执行工具调用
            RunToolRequest toolRequest = RunToolRequest.builder().build();
            BeanUtils.copyProperties(dispatchData, toolRequest);
            toolRequest.setRuntimeNodes(runtimeNodes);
            toolRequest.setRuntimeEdges(runtimeEdges);
            toolRequest.setToolNodes(toolNodes);
            toolRequest.setToolModel(toolModel);
            toolRequest.setMessages(adaptMessages);
            toolRequest.setInteractiveEntryToolParams(lastInteractive == null ? null : lastInteractive.getToolParams());
            toolRequest.setMaxRunToolTimes(30);
            toolResponse = runToolWithToolChoice(toolRequest);
        } else if (toolModel.getFunctionCall()!= null && toolModel.getFunctionCall()) {
            // function_call
        } else {
            throw new BussinessException("模型不支持工具调用！");
//            ChatCompletionMessageParam lastMessage = adaptMessages.get(adaptMessages.size() - 1);
//
//            if (lastMessage != null) {
//                if (lastMessage.getContent() instanceof String) {
//                    String message = (String) lastMessage.getContent();
//                    Map<String, Object> pa = new HashMap<>();
//                    pa.put("question", message);
//                    lastMessage.setContent(WorkflowUtil.replaceVariable(DispatchUtil.Prompt_Tool_Call, pa));
//                } else if (lastMessage.getContent() instanceof List<?>) {
//                    try {
//                        List<ChatCompletionContentPartText> contentList = (List<ChatCompletionContentPartText>) lastMessage.getContent();
//                        if (!contentList.isEmpty()) {
//                            ChatCompletionContentPartText lastContent = contentList.get(contentList.size() - 1);
//                            Map<String, Object> pa = new HashMap<>();
//                            pa.put("question", lastContent.getText());
//                            lastContent.setText((String) WorkflowUtil.replaceVariable(DispatchUtil.Prompt_Tool_Call, pa));
//                        }
//                    } catch (ClassCastException e) {
//                        log.warn("Prompt call invalid input", e);
//                    }
//                }
//            }
//            // 执行工具调用
//            RunToolRequest toolRequest = RunToolRequest.builder().build();
//            BeanUtils.copyProperties(dispatchData, toolRequest);
//            toolRequest.setRuntimeNodes(runtimeNodes);
//            toolRequest.setRuntimeEdges(runtimeEdges);
//            toolRequest.setToolNodes(toolNodes);
//            toolRequest.setToolModel(toolModel);
//            toolRequest.setMessages(adaptMessages);
//            toolRequest.setInteractiveEntryToolParams(lastInteractive == null ? null : lastInteractive.getToolParams());
//            toolResponse = runToolWithPromptCall(toolRequest, toolResponse);
        }

        log.debug("Tool call execution not fully implemented yet");

        return toolResponse;
    }

    private RunToolResponse runToolWithToolChoice(RunToolRequest request) {

        RunToolResponse response = null;
        try {
            // 1. 提取请求参数
            List<ChatCompletionMessageParam> messages = request.getMessages();
            List<ToolNodeItemType> toolNodes = request.getToolNodes();
            SystemModelDTO toolModel = request.getToolModel();
            int maxRunToolTimes = request.getMaxRunToolTimes() != null ? request.getMaxRunToolTimes() : 10;
            WorkflowInteractiveResponseType.ToolParams interactiveEntryToolParams = request.getInteractiveEntryToolParams();

            SseEmitter res = request.getRes();
            String requestOrigin = request.getRequestOrigin();
            List<RuntimeNodeItemType> runtimeNodes = request.getRuntimeNodes();
            List<RuntimeEdgeItemType> runtimeEdges = request.getRuntimeEdges();
            Boolean stream = request.getStream();

            Boolean retainDatasetCite = request.getRetainDatasetCite();
            retainDatasetCite = true;

            ExternalProviderType externalProvider = request.getExternalProvider();

            Consumer<WorkflowStreamResponse> workflowStreamResponse = request.getWorkflowStreamResponse();

            Map<String, Object> params = request.getParams();
            Double temperature = (Double) params.get("temperature");
            Integer maxToken = (Integer) params.get("maxToken");
            Boolean aiChatVision = (Boolean) params.get("aiChatVision");
            Double aiChatTopP = (Double) params.get("aiChatTopP");
            String aiChatStopSign = (String) params.get("aiChatStopSign");
            String aiChatResponseFormat = (String) params.get("aiChatResponseFormat");
            String aiChatJsonSchema = (String) params.get("aiChatJsonSchema");
            Boolean aiChatReasoning = (Boolean) params.get("aiChatReasoning");

            aiChatReasoning = (aiChatReasoning != null && aiChatReasoning) && (toolModel.getReasoning() != null && toolModel.getReasoning());
            if (maxRunToolTimes <= 0 && response != null) {
                return null;
            }

            if (interactiveEntryToolParams != null) {

            }

            List<AIChatItemValueItemType> assistantResponses = response != null ? response.getAssistantResponses() : new ArrayList<>();

//            // 2. 初始化工具相关数据
            Map<String, JsonSchemaPropertiesItemType> valueTypeJsonSchemaMap = WorkflowUtil.createValueTypeJsonSchemaMap();

            List<ChatCompletionTool> tools = toolNodes.stream().map(item -> {
                // 优先使用jsonSchema，如果没有则从toolParams构建
                if (item.getJsonSchema() != null) {
                    FunctionDefinition functionDef = new FunctionDefinition();
                    functionDef.setName(item.getNodeId());
                    functionDef.setDescription(item.getIntro() == null ? item.getName() : item.getIntro());
                    functionDef.setParameters(item.getJsonSchema());
                    ChatCompletionTool tool = new ChatCompletionTool();
                    tool.setType("function");
                    tool.setFunction(functionDef);
                    return tool;
                } else {

                    Map<String, PropertiesValue> properties = new HashMap<>();

                    List<FlowNodeInputItemType> toolParams = item.getToolParams();

                    toolParams.forEach(param -> {
                        String valueType = param.getValueType();
                        JsonSchemaPropertiesItemType jsonSchema;
                        if (valueType == null) {
                            ToolValueTypeItem toolValueTypeItem = WorkflowUtil.TOOL_VALUE_TYPE_LIST.get(0);
                            jsonSchema = toolValueTypeItem.getJsonSchema();
                        } else {
                            jsonSchema = valueTypeJsonSchemaMap.get(param.getValueType());
                            if (jsonSchema == null) {
                                ToolValueTypeItem toolValueTypeItem = WorkflowUtil.TOOL_VALUE_TYPE_LIST.get(0);
                                jsonSchema = toolValueTypeItem.getJsonSchema();
                            }
                        }
                        List<String> enums = new ArrayList<>();
                        if (param.getEnums() != null) {
                            enums = Arrays.stream(param.getEnums().split("\\R"))
                                    .filter(line -> !line.trim().isEmpty())
                                    .toList();
                        }
                        PropertiesValue propertiesValue = new PropertiesValue();
                        propertiesValue.setDescription(param.getDescription());
                        propertiesValue.setEnums(enums);
                        propertiesValue.setType(jsonSchema.getType());
                        propertiesValue.setItems(jsonSchema.getItems());
                        properties.put(param.getKey(), propertiesValue);
                    });
                    JSONSchemaInputType pa = new JSONSchemaInputType();
                    pa.setType("object");
                    pa.setProperties(properties);
                    pa.setRequired(item.getToolParams().stream().filter(FlowNodeInputItemType::getRequired).map(FlowNodeInputItemType::getKey).toList());
                    FunctionDefinition functionDef = new FunctionDefinition();
                    functionDef.setName(item.getNodeId());
                    functionDef.setDescription(item.getIntro().isEmpty() ? item.getName() : item.getIntro());
                    functionDef.setParameters(pa);
                    ChatCompletionTool tool = new ChatCompletionTool();
                    tool.setType("function");
                    tool.setFunction(functionDef);
                    return tool;
                }
            }).toList();

            // 3. 计算最大token并过滤消息
            Integer max_tokens = AiUtil.computedMaxToken(maxToken, toolModel, 100);
            // 根据maxToken过滤历史
            List<ChatCompletionMessageParam> chatCompletionMessageParams = ChatUtil.filterGPTMessageByMaxContext(messages, toolModel.getMaxContext() - max_tokens);

            List<ChatCompletionMessageParam> filterMessages = chatCompletionMessageParams.stream().map(item -> {
                if ("assistant".equals(item.getRole()) && (item.getToolCalls() != null && !item.getToolCalls().isEmpty())) {
                    List<ChatCompletionMessageToolCall> list = item.getToolCalls().stream().map(tool -> {
                        ChatCompletionMessageToolCall newTool = new ChatCompletionMessageToolCall();
                        newTool.setId(tool.getId());
                        newTool.setType(tool.getType());
                        newTool.setFunction(tool.getFunction());
                        return newTool;
                    }).toList();
                    item.setToolCalls(list);
                    return item;
                }
                return item;
            }).toList();

            // 4. 加载请求消息
            boolean useVision = toolModel.getVision() != null && toolModel.getVision() && aiChatVision;
            CompletableFuture<List<ChatCompletionMessageParam>> requestMessagesCom = ChatUtil.loadRequestMessages(filterMessages, useVision, requestOrigin);
            List<ChatCompletionMessageParam> requestMessages = new ArrayList<>();
            try {
                requestMessages = requestMessagesCom.get();
            } catch (Exception e) {
                requestMessages = new ArrayList<>();
            }

            // 5. 计算输入token使用量
            UsageCalculationResult usageResult = calculateUsage(
                    toolModel.getModel(),
                    estimateTokens(requestMessages),
                    0,
                    request.getExternalProvider()
            );

            // 6. 构建Spring AI聊天模型
            ChatModel chatModel = llmChatModelFactory.createChatModel(toolModel);
            ToolCallback[] springAiTools = buildSpringAiTools(tools, toolNodes, request);
            // 8. 构建聊天选项
            // ChatOptions chatOptions = ToolCallingChatOptions.builder().model(toolModel.getModel())
            //         .temperature(temperature != null ? temperature : 0.7)
            //         .maxTokens(max_tokens)
            //         .toolCallbacks(springAiTools).build();

            // 9. 转换消息格式为Spring AI格式
            List<Message> springAiMessages = convertToSpringAIMessages(requestMessages);
            // 10. 创建提示
            // Prompt prompt = new Prompt(springAiMessages, chatOptions);

            StringBuilder answerBuilder = new StringBuilder();
            StringBuilder reasoningBuilder = new StringBuilder();
            String finishReason = "stop";
            AtomicReference<String> contentStr = new AtomicReference<>("");
            // 11. 执行聊天完成
            ChatClient.Builder chatClientBuilder = ChatClient.builder(chatModel);
            // 注册所有工具回调
            chatClientBuilder.defaultToolCallbacks(springAiTools);
            ChatClient chatClient = chatClientBuilder.build();
            try {
                Flux<ChatResponse> streamResponse = chatClient.prompt()
                        .messages(springAiMessages)
                        .stream().chatResponse();
                Boolean finalAiChatReasoning = aiChatReasoning;
                streamResponse.doOnNext(resp -> {
                            try {
                                // 解析流式响应
                                String content = resp.getResult().getOutput().getText();
                                String reasoningContent = extractReasoningContent(resp);
                                List<AssistantMessage.ToolCall> toolCalls = resp.getResult().getOutput().getToolCalls();
                                System.out.println("toolcalls：" + toolCalls);

                                if (StringUtils.hasText(reasoningContent)) {
                                    reasoningBuilder.append(reasoningContent);
                                    // 发送推理内容
                                    if (finalAiChatReasoning) {
                                        sendStreamResponse(request, SseResponseEventEnum.ANSWER,
                                                createReasoningResponse(reasoningContent));
                                    }
                                }

                                if (StringUtils.hasText(content)) {
                                    contentStr.set(content);
                                    answerBuilder.append(content);
                                    // 发送回答内容
                                    sendStreamResponse(request, SseResponseEventEnum.ANSWER,
                                            createAnswerResponse(content));
                                }
                            } catch (Exception e) {
                                log.error("处理流式响应片段失败", e);
                            }
                        })
                        .doOnComplete(() -> {
                            log.debug("流式响应完成");
                        })
                        .doOnError(error -> {
                            log.error("流式响应错误", error);
                        })
                        .blockLast(); // 等待流式响应完成
            } catch (Exception e) {
                log.error("流式响应错误", e.getMessage());
            }
            String content = contentStr.get();
            // 构建文本内容
            TextContent textInfo = new TextContent();
            textInfo.setContent(content);
            
            // 构建AIChatItemValueItemType
            AIChatItemValueItemType aiValueItem = new AIChatItemValueItemType();
            aiValueItem.setType(ChatItemValueType.TEXT.getValue());
            aiValueItem.setText(textInfo);
            
            // 构建ChatCompletionMessageParam用于completeMessages
            ChatCompletionMessageParam assistantMessage = new ChatCompletionMessageParam();
            assistantMessage.setRole(ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue());
            assistantMessage.setContent(content);
            
            // 构建完整的消息列表
            List<ChatCompletionMessageParam> completeMessages = new ArrayList<>(requestMessages);
            completeMessages.add(assistantMessage);
            
            // 构建响应
            return buildFinalResponse(
                completeMessages,
                content,
                "",
                null,
                List.of(aiValueItem),
                null
            );
        } catch (Exception e) {
            log.error("Error in runToolWithToolChoice", e);
            throw new RuntimeException(e.getMessage());
        }

    }

    private RunToolResponse runToolWithPromptCall(RunToolRequest request, RunToolResponse response) {

        try {
            // 1. 提取请求参数
            List<ChatCompletionMessageParam> messages = request.getMessages();
            List<ToolNodeItemType> toolNodes = request.getToolNodes();
            SystemModelDTO toolModel = request.getToolModel();
            int maxRunToolTimes = request.getMaxRunToolTimes() != null ? request.getMaxRunToolTimes() : 10;
            WorkflowInteractiveResponseType.ToolParams interactiveEntryToolParams = request.getInteractiveEntryToolParams();

            SseEmitter res = request.getRes();
            String requestOrigin = request.getRequestOrigin();
            List<RuntimeNodeItemType> runtimeNodes = request.getRuntimeNodes();
            List<RuntimeEdgeItemType> runtimeEdges = request.getRuntimeEdges();
            Boolean stream = request.getStream();

            Boolean retainDatasetCite = request.getRetainDatasetCite();
            retainDatasetCite = true;

            ExternalProviderType externalProvider = request.getExternalProvider();

            Consumer<WorkflowStreamResponse> workflowStreamResponse = request.getWorkflowStreamResponse();

            Map<String, Object> params = request.getParams();
            Double temperature = (Double) params.get("temperature");
            Integer maxToken = (Integer) params.get("maxToken");
            Boolean aiChatVision = (Boolean) params.get("aiChatVision");
            Double aiChatTopP = (Double) params.get("aiChatTopP");
            String aiChatStopSign = (String) params.get("aiChatStopSign");
            String aiChatResponseFormat = (String) params.get("aiChatResponseFormat");
            String aiChatJsonSchema = (String) params.get("aiChatJsonSchema");
            Boolean aiChatReasoning = (Boolean) params.get("aiChatReasoning");

            aiChatReasoning = (aiChatReasoning != null && aiChatReasoning) && (toolModel.getReasoning() != null && toolModel.getReasoning());
            if (maxRunToolTimes <= 0 && response != null) {
                return null;
            }

            if (interactiveEntryToolParams != null) {

            }

            List<AIChatItemValueItemType> assistantResponses = response != null ? response.getAssistantResponses() : new ArrayList<>();

            // 2. 初始化工具相关数据
            Map<String, JsonSchemaPropertiesItemType> valueTypeJsonSchemaMap = WorkflowUtil.createValueTypeJsonSchemaMap();

            List<ToolPromptDef> tools = toolNodes.stream().map(item -> {
                // 优先使用jsonSchema，如果没有则从toolParams构建
                if (item.getJsonSchema() != null) {
                    ToolPromptDef functionDef = new ToolPromptDef();
                    functionDef.setToolId(item.getNodeId());
                    functionDef.setDescription(item.getIntro());
                    functionDef.setParameters(item.getJsonSchema());
                    return functionDef;
                } else {

                    Map<String, PropertiesValue> properties = new HashMap<>();

                    List<FlowNodeInputItemType> toolParams = item.getToolParams();

                    toolParams.forEach(param -> {
                        String valueType = param.getValueType();
                        JsonSchemaPropertiesItemType jsonSchema;
                        if (valueType == null) {
                            ToolValueTypeItem toolValueTypeItem = WorkflowUtil.TOOL_VALUE_TYPE_LIST.get(0);
                            jsonSchema = toolValueTypeItem.getJsonSchema();
                        } else {
                            jsonSchema = valueTypeJsonSchemaMap.get(param.getValueType());
                            if (jsonSchema == null) {
                                ToolValueTypeItem toolValueTypeItem = WorkflowUtil.TOOL_VALUE_TYPE_LIST.get(0);
                                jsonSchema = toolValueTypeItem.getJsonSchema();
                            }
                        }
                        List<String> enums = new ArrayList<>();
                        if (param.getEnums() != null) {
                            enums = Arrays.stream(param.getEnums().split("\\R"))
                                    .filter(line -> !line.trim().isEmpty())
                                    .toList();
                        }
                        PropertiesValue propertiesValue = new PropertiesValue();
                        propertiesValue.setDescription(param.getToolDescription());
                        propertiesValue.setEnums(enums);
                        propertiesValue.setType(jsonSchema.getType());
                        propertiesValue.setItems(jsonSchema.getItems());
                        properties.put(param.getKey(), propertiesValue);
                    });
                    JSONSchemaInputType pa = new JSONSchemaInputType();
                    pa.setType("object");
                    pa.setProperties(properties);
                    pa.setRequired(item.getToolParams().stream().filter(FlowNodeInputItemType::getRequired).map(FlowNodeInputItemType::getKey).toList());
                    ToolPromptDef tool = new ToolPromptDef();
                    tool.setToolId(item.getNodeId());
                    tool.setDescription(item.getIntro());
                    tool.setParameters(pa);
                    return tool;
                }
            }).toList();

            String toolsPrompt = objectMapper.writeValueAsString(tools);

            ChatCompletionMessageParam lastMessage = messages.get(messages.size() - 1);

            if (lastMessage.getContent() instanceof String) {
                lastMessage.setContent(WorkflowUtil.replaceVariable(lastMessage.getContent(), Map.of("toolsPrompt", toolsPrompt)));
            } else if (lastMessage.getContent() instanceof List<?>) {
                // list的情况下替换最后一个元素就行
                try {
                    List<ChatCompletionContentPartText> contentList = (List<ChatCompletionContentPartText>) lastMessage.getContent();
                    ChatCompletionContentPartText lastText = contentList.get(contentList.size() - 1);
                    lastText.setText((String) WorkflowUtil.replaceVariable(lastText.getText(), Map.of("toolsPrompt", toolsPrompt)));
                } catch (Exception e) {
                    throw new RuntimeException("Prompt call invalid input");
                }

            } else {
                return null;
            }

            // 3. 计算最大token并过滤消息
            Integer max_tokens = AiUtil.computedMaxToken(maxToken, toolModel, 100);
            // 根据maxToken过滤历史
            List<ChatCompletionMessageParam> chatCompletionMessageParams = ChatUtil.filterGPTMessageByMaxContext(messages, toolModel.getMaxContext() - max_tokens);

            List<ChatCompletionMessageParam> filterMessages = chatCompletionMessageParams.stream().map(item -> {
                if ("assistant".equals(item.getRole()) && (item.getToolCalls() != null && !item.getToolCalls().isEmpty())) {
                    List<ChatCompletionMessageToolCall> list = item.getToolCalls().stream().map(tool -> {
                        ChatCompletionMessageToolCall newTool = new ChatCompletionMessageToolCall();
                        newTool.setId(tool.getId());
                        newTool.setType(tool.getType());
                        newTool.setFunction(tool.getFunction());
                        return newTool;
                    }).toList();
                    item.setToolCalls(list);
                    return item;
                }
                return item;
            }).toList();

            // 4. 加载请求消息
            boolean useVision = toolModel.getVision() != null && toolModel.getVision() && aiChatVision;
            CompletableFuture<List<ChatCompletionMessageParam>> requestMessagesCom = ChatUtil.loadRequestMessages(filterMessages, useVision, requestOrigin);
            List<ChatCompletionMessageParam> requestMessages = new ArrayList<>();
            try {
                requestMessages = requestMessagesCom.get();
            } catch (Exception e) {
                requestMessages = new ArrayList<>();
            }

            // 5. 计算输入token使用量
            UsageCalculationResult usageResult = calculateUsage(
                    toolModel.getModel(),
                    estimateTokens(requestMessages),
                    0,
                    request.getExternalProvider()
            );

            // 6. 构建Spring AI聊天模型
            ChatModel chatModel = llmChatModelFactory.createChatModel(toolModel);
            // TODO 修改 null
            ToolCallback[] springAiTools = buildSpringAiTools(null, toolNodes, request);
            // 8. 构建聊天选项
            ChatOptions chatOptions = ToolCallingChatOptions.builder().model(toolModel.getModel())
                    .temperature(temperature != null ? temperature : 0.7)
                    .maxTokens(max_tokens)
                    .toolCallbacks(springAiTools).build();

            // 9. 转换消息格式为Spring AI格式
            List<Message> springAiMessages = convertToSpringAIMessages(requestMessages);
            // 10. 创建提示
            Prompt prompt = new Prompt(springAiMessages, chatOptions);

            // 11. 执行聊天完成
            ChatResponse aiResponse = chatModel.call(prompt);
            String answer = aiResponse.getResult().getOutput().getText();
            String reasoning = "";

            // 解析响应
            ToolCallResult parseResult = parseAnswer(answer);

            if (parseResult.getToolJson() == null) {
                // 没有工具调用，返回最终结果
                return buildFinalResponse(requestMessages, answer, reasoning, response, assistantResponses,
                        aiResponse.getMetadata().getUsage());
            }

            // 执行工具调用
            ToolExecutionResult toolResult = executeToolCall(parseResult.getToolJson(), toolNodes, request);

            // 更新最后一条消息，添加工具响应
            updateLastMessageWithToolResponse(lastMessage, parseResult.getAnswer(), toolResult.getToolResponsePrompt());

            // 检查是否有停止信号或交互响应
            if (toolResult.hasStopSignal || toolResult.getWorkflowInteractiveResponse() != null) {
                return buildToolStopResponse(requestMessages, assistantResponses, toolResult, response,
                        aiResponse.getMetadata().getUsage());
            }

            // 递归调用继续处理
            RunToolRequest nextRequest = RunToolRequest.builder()
                    .messages(messages)
                    .toolNodes(toolNodes)
                    .toolModel(toolModel)
                    .maxRunToolTimes(maxRunToolTimes - 1)
                    .params(params)
                    .build();

            if (toolResult.getDispatchFlowResponse() != null) {
                response.getDispatchFlowResponse().add(toolResult.getDispatchFlowResponse());
            }

            return runToolWithPromptCall(nextRequest, RunToolResponse.builder()
                    .dispatchFlowResponse(response != null ?
                            new ArrayList<>(response.getDispatchFlowResponse()) : new ArrayList<>())
                    .toolNodeInputTokens(response != null ? response.getToolNodeInputTokens() : 0)
                    .toolNodeOutputTokens(response != null ? response.getToolNodeOutputTokens() : 0)
                    .assistantResponses(new ArrayList<>(assistantResponses))
                    .runTimes((response != null ? response.getRunTimes() : 0) + 1)
                    .build());
        } catch (Exception e) {
            log.error("Error in runToolWithToolChoice", e);
            throw new RuntimeException(e.getMessage());
        }

    }

    /**
     * 计算使用量
     */
    private UsageCalculationResult calculateUsage(
            String model, int inputTokens, int outputTokens,
            ExternalProviderType externalProvider) {

        // TODO: 实现积分计算逻辑
        int totalPoints = 0;
        if (externalProvider.getOpenaiAccount() == null ||
                !StringUtils.hasText(externalProvider.getOpenaiAccount().getKey())) {
            // 计算内部积分消耗
            totalPoints = inputTokens + outputTokens; // 简化计算
        }

        return UsageCalculationResult.builder()
                .totalPoints(totalPoints)
                .modelName(model)
                .toolAIUsage(totalPoints)
                .build();
    }

    /**
     * 估算token数量
     */
    private int estimateTokens(List<ChatCompletionMessageParam> messages) {
        int totalTokens = 0;
        for (ChatCompletionMessageParam message : messages) {
            if (message.getContent() != null) {
                // 简单估算：每4个字符约等于1个token
                String string = message.getContent().toString();
                totalTokens += string.length() / 4;
            }
        }
        return totalTokens;
    }

    /**
     * 构建响应结果
     */
    private Map<String, Object> buildResponse(List<AIChatItemValueItemType> previewAssistantResponses,
                                              List<ChatHistoryItemResType> childToolResponse,List<ChatNodeUsageType> flatUsages,
                                              Double totalPointsUsage,
            String nodeId, String name, RunToolResponse toolResponse,
            UsageCalculationResult usageResult, String userChatInput,
            boolean useVision, List<ChatCompletionMessageParam> completeMessages) {

        double childTotalPoints = flatUsages.stream()
                .mapToDouble(ChatNodeUsageType::getTotalPoints)
                .sum();

        Map<String, Object> result = new HashMap<>();

        // 设置运行次数
        result.put(DispatchNodeResponseKeyEnum.RUN_TIMES.getValue(), toolResponse.getRunTimes());

        // 设置答案文本
        String answerText = previewAssistantResponses.stream()
                .filter(response -> response.getText() != null &&
                        StringUtils.hasText(response.getText().getContent()))
                .map(response -> response.getText().getContent())
                .collect(Collectors.joining(""));
        result.put(NodeOutputKeyEnum.ANSWER_TEXT.getValue(), answerText);

        List<ChatItemType> previewData = ChatAdaptor.gptMessages2Chats(completeMessages, false);
        List<DispatchNodeResponseType.HistoryPreviewItem> historyPreview = ChatUtil.getHistoryPreview(previewData, 10000, useVision);
        // 设置助手响应
        result.put(DispatchNodeResponseKeyEnum.ASSISTANT_RESPONSES.getValue(),
                previewAssistantResponses);

        // 设置节点响应
        DispatchNodeResponseType nodeResponse = DispatchNodeResponseType.builder()
                .totalPoints(totalPointsUsage)
                .toolCallInputTokens(toolResponse.getToolNodeInputTokens())
                .toolCallOutputTokens(toolResponse.getToolNodeOutputTokens())
                .childTotalPoints(childTotalPoints)
                .model(usageResult.getModelName())
                .historyPreview(historyPreview)
                .query(userChatInput)
                .historyPreview(historyPreview)
                .toolDetail(childToolResponse)
                .mergeSignId(nodeId)
                .finishReason(toolResponse.getFinishReason())
                .build();
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), nodeResponse);

        // 设置使用量
        List<Map<String, Object>> usages = new ArrayList<>();
        Map<String, Object> usage = new HashMap<>();
        usage.put("moduleName", name);
        usage.put("model", usageResult.getModelName());
        usage.put("totalPoints", usageResult.getToolAIUsage());
        usage.put("inputTokens", toolResponse.getToolNodeInputTokens());
        usage.put("outputTokens", toolResponse.getToolNodeOutputTokens());
        usages.add(usage);

        // 添加工具的消耗
        for (ChatNodeUsageType usagea : flatUsages) {
            Map<String, Object> childUsage = new HashMap<>();
            childUsage.put("moduleName", usagea.getModuleName());
            childUsage.put("model", usagea.getModel());
            childUsage.put("totalPoints", usagea.getTotalPoints());
            childUsage.put("inputTokens", usagea.getInputTokens());
            childUsage.put("outputTokens", usagea.getOutputTokens());
            usages.add(childUsage);
        }

        result.put(DispatchNodeResponseKeyEnum.NODE_DISPATCH_USAGES.getValue(), usages);

        // 设置交互响应
        if (toolResponse.getToolWorkflowInteractiveResponse() != null) {
            result.put(DispatchNodeResponseKeyEnum.INTERACTIVE.getValue(),
                    toolResponse.getToolWorkflowInteractiveResponse());
        }

        return result;
    }

    /**
     * 构建Spring AI工具定义
     */
    private ToolCallback[] buildSpringAiTools(List<ChatCompletionTool> tools, List<ToolNodeItemType> toolNodes, RunToolRequest request) {
        List<ToolCallback> springAiTools = new ArrayList<>();
        
        // 创建日志收集器
        ToolCallLogCollector logCollector = ToolCallLogCollector.builder().build();
        
        // 构建 props 对象
        ModuleDispatchProps props = ModuleDispatchProps.builder().build();
        BeanUtils.copyProperties(request, props);
        
        for (ChatCompletionTool tool : tools) {
            if (tool.getFunction() != null) {
                FunctionDefinition function = tool.getFunction();

                // 创建FunctionToolCallback
                FunctionToolCallback.Builder builder = FunctionToolCallback.builder(
                                function.getName(),
                                (input) -> {
                                    // 执行工具函数并返回结果
                                    String inputStr;
                                    if (input instanceof String) {
                                        inputStr = (String) input;
                                    } else if (input != null) {
                                        try {
                                            inputStr = objectMapper.writeValueAsString(input);
                                        } catch (Exception e) {
                                            log.warn("Failed to convert input to JSON string: {}", input, e);
                                            inputStr = input.toString();
                                        }
                                    } else {
                                        inputStr = "{}";
                                    }
                                    return executeToolFunction(function.getName(), inputStr, toolNodes, request);
                                }
                        )
                        .description(function.getDescription());

                // 根据function的parameters设置正确的inputType
                if (function.getParameters() != null) {
                    // 如果有parameters定义，创建一个对应的类型
                    // 这里我们需要根据JSONSchemaInputType动态创建合适的输入类型
                    // 暂时使用Map.class作为通用的object类型
                    builder.inputType(Map.class);
                } else {
                    // 如果没有parameters，使用String.class
                    builder.inputType(String.class);
                }

                FunctionToolCallback originalCallback = builder.build();
                
                // 查找对应的工具节点
                ToolNodeItemType toolNode = toolNodes.stream()
                        .filter(node -> function.getName().equals(node.getNodeId()))
                        .findFirst()
                        .orElse(null);
                
                // 使用 LoggingToolCallback 包装原始回调
                LoggingToolCallback loggingCallback = new LoggingToolCallback(
                        originalCallback, 
                        logCollector, 
                        props, 
                        toolNode, 
                        this
                );
                
                springAiTools.add(loggingCallback);
            }
        }

        return springAiTools.toArray(new ToolCallback[0]);
    }

    /**
     * 解析JSON字符串参数为Map对象
     */
    private Map<String, Object> parseArguments(String arguments) {
        try {
            if (arguments == null || arguments.trim().isEmpty()) {
                return new HashMap<>();
            }
            return objectMapper.readValue(arguments, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            // 如果解析失败，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 流式响应结果记录
     */
    private record StreamResponseResult(String answerText, String reasoningText, String finishReason) {}

    /**
     * 处理流式响应
     */
    private AgentRunToolsDispatcher.StreamResponseResult handleStreamResponse(
            ChatModel chatModel,
            Prompt prompt,
            ModuleDispatchProps props,
            SystemModelDTO modelConfig,
            boolean aiChatReasoning) {

        StringBuilder answerBuilder = new StringBuilder();
        StringBuilder reasoningBuilder = new StringBuilder();
        String finishReason = "stop";

        try {
            // 使用 ChatModel 的流式调用
            Flux<ChatResponse> streamResponse = chatModel.stream(prompt);

            streamResponse
                    .doOnNext(response -> {
                        try {
                            // 解析流式响应
                            String content = response.getResult().getOutput().getText();
                            String reasoningContent = extractReasoningContent(response);

                            if (StringUtils.hasText(reasoningContent)) {
                                reasoningBuilder.append(reasoningContent);
                                // 发送推理内容
                                if (aiChatReasoning) {
                                    sendStreamResponse(props, SseResponseEventEnum.ANSWER,
                                            createReasoningResponse(reasoningContent));
                                }
                            }

                            if (StringUtils.hasText(content)) {
                                answerBuilder.append(content);
                                // 发送回答内容
                                sendStreamResponse(props, SseResponseEventEnum.ANSWER,
                                        createAnswerResponse(content));
                            }
                        } catch (Exception e) {
                            log.error("处理流式响应片段失败", e);
                        }
                    })
                    .doOnComplete(() -> {
                        log.debug("流式响应完成");
                    })
                    .doOnError(error -> {
                        log.error("流式响应错误", error);
                        // TODO 应该在节点响应中记录错误信息
//                        sendStreamResponse(props, SseResponseEventEnum.ERROR,
//                                Map.of("message", error.getMessage()));
                    })
                    .blockLast(); // 等待流式响应完成

        } catch (Exception e) {
            log.error("流式响应处理失败", e);
            finishReason = "error";
        }

        return new AgentRunToolsDispatcher.StreamResponseResult(
                answerBuilder.toString().trim(),
                reasoningBuilder.toString().trim(),
                finishReason
        );
    }

    /**
     * 发送流式响应
     */
    private void sendStreamResponse(ModuleDispatchProps props, SseResponseEventEnum event, SseResponseType data) {
        if (props.getWorkflowStreamResponse() != null) {
            WorkflowStreamResponse streamResponse = WorkflowStreamResponse.builder()
                    .event(event.getValue())
                    .data(data)
                    .build();
            props.getWorkflowStreamResponse().accept(streamResponse);
        }
    }


    /**
     * 发送工具事件
     */
    private void sendToolEvent(ModuleDispatchProps props, SseResponseEventEnum event, Object eventData) {
        if (props == null || props.getWorkflowStreamResponse() == null) {
            return;
        }
        
        try {
            SseResponseType responseData;
            if (eventData instanceof SseResponseType) {
                responseData = (SseResponseType) eventData;
            } else if (eventData instanceof Map) {
                responseData = new SseMapResponse<>((Map<String, Object>) eventData);
            } else {
                responseData = new SseMapResponse<>(Map.of("data", eventData));
            }
            
            sendStreamResponse(props, event, responseData);
            log.debug("工具事件已发送: {} - {}", event, eventData);
        } catch (Exception e) {
            log.warn("发送工具事件失败: {}", event, e);
        }
    }

    /**
     * 工具调用日志实体
     */
    @Data
    @Builder
    private static class ToolCallLog {
        private String toolId;
        private String toolName;
        private String toolAvatar;
        private String arguments;
        private long startTime;
        private long endTime;
        private String result;
        private boolean success;
        private String errorMessage;
        
        public long getDuration() {
            return endTime - startTime;
        }
    }

    /**
     * 工具调用日志收集器
     */
    @Data
    @Builder
    private static class ToolCallLogCollector {
        private final List<ToolCallLog> logs = new ArrayList<>();
        
        public void addLog(ToolCallLog log) {
            logs.add(log);
        }
        
        public List<ToolCallLog> getLogs() {
            return new ArrayList<>(logs);
        }
        
        public void clear() {
            logs.clear();
        }
    }

    /**
     * 装饰器类：包装 FunctionToolCallback 以添加日志和事件功能
     */
    private static class LoggingToolCallback implements ToolCallback {
        private final FunctionToolCallback originalCallback;
        private final ToolCallLogCollector logCollector;
        private final ModuleDispatchProps props;
        private final ToolNodeItemType toolNode;
        private final AgentRunToolsDispatcher dispatcher;

        public LoggingToolCallback(FunctionToolCallback originalCallback,
                                 ToolCallLogCollector logCollector,
                                 ModuleDispatchProps props,
                                 ToolNodeItemType toolNode,
                                 AgentRunToolsDispatcher dispatcher) {
            this.originalCallback = originalCallback;
            this.logCollector = logCollector;
            this.props = props;
            this.toolNode = toolNode;
            this.dispatcher = dispatcher;
        }

        @Override
        public ToolDefinition getToolDefinition() {
            return originalCallback.getToolDefinition();
        }

        @Override
        public String call(String arguments) {
            long startTime = System.currentTimeMillis();
            String toolId = toolNode != null ? toolNode.getNodeId() : getToolDefinition().name();
            String toolName = toolNode != null ? toolNode.getName() : getToolDefinition().name();
            String toolAvatar = toolNode != null ? toolNode.getAvatar() : "";

            // 发送工具调用开始事件
            try {
                ToolModuleResponseItemType toolCallEvent = new ToolModuleResponseItemType();
                toolCallEvent.setId(toolId);
                toolCallEvent.setToolName(toolName);
                toolCallEvent.setToolAvatar(toolAvatar);
                toolCallEvent.setParams(arguments);
                toolCallEvent.setResponse("");

                dispatcher.sendToolEvent(props, SseResponseEventEnum.TOOL_CALL,
                    new SseMapResponse<>(Map.of("tool", toolCallEvent)));
            } catch (Exception e) {
                log.warn("发送工具调用事件失败", e);
            }

            String result = null;
            boolean success = false;
            String errorMessage = null;

            try {
                result = originalCallback.call(arguments);
                success = true;
            } catch (Exception e) {
                success = false;
                errorMessage = e.getMessage();
                result = "{\"error\": \"" + e.getMessage() + "\"}";
                log.error("工具调用执行失败: {}", toolName, e);
            }

            long endTime = System.currentTimeMillis();

            // 记录日志
            ToolCallLog callLog = ToolCallLog.builder()
                .toolId(toolId)
                .toolName(toolName)
                .toolAvatar(toolAvatar)
                .arguments(arguments)
                .startTime(startTime)
                .endTime(endTime)
                .result(result)
                .success(success)
                .errorMessage(errorMessage)
                .build();
            logCollector.addLog(callLog);

            // 发送工具响应事件
            try {
                ToolModuleResponseItemType toolResponseEvent = new ToolModuleResponseItemType();
                toolResponseEvent.setId(toolId);
                toolResponseEvent.setToolName(toolName);
                toolResponseEvent.setToolAvatar(toolAvatar);
                toolResponseEvent.setParams(arguments);
                toolResponseEvent.setResponse(result);

                dispatcher.sendToolEvent(props, SseResponseEventEnum.TOOL_RESPONSE,
                    new SseMapResponse<>(Map.of("tool", toolResponseEvent)));
            } catch (Exception e) {
                log.warn("发送工具响应事件失败", e);
            }

            return result;
        }
    }

    /**
     * 创建回答内容响应
     */
    private GptResponse createAnswerResponse(String content) {
        return WorkflowUtil.textAdaptGptResponse(
                TextAdaptGptResponseParams.builder()
                        .text(content)
                        .build());
    }

    /**
     * 创建推理内容响应
     */
    private GptResponse createReasoningResponse(String reasoningContent) {
        return WorkflowUtil.textAdaptGptResponse(
                TextAdaptGptResponseParams.builder()
                        .reasoningContent(reasoningContent)
                        .build());
    }

    /**
     * 提取推理内容
     */
    private String extractReasoningContent(ChatResponse response) {
        // 根据模型响应格式提取推理内容
        try {
            Object reasoningContent = response.getMetadata().get("reasoning_content");
            if (reasoningContent != null) {
                return reasoningContent.toString();
            }

            // 尝试从其他可能的字段获取推理内容
            Object reasoning = response.getMetadata().get("reasoning");
            if (reasoning != null) {
                return reasoning.toString();
            }
        } catch (Exception e) {
            log.debug("提取推理内容失败", e);
        }
        return "";
    }

    /**
     * 执行工具函数
     */
    private String executeToolFunction(String functionName, String input, List<ToolNodeItemType> toolNodes, RunToolRequest request) {
        try {
            // 查找对应的工具节点
            ToolNodeItemType targetTool = toolNodes.stream()
                    .filter(tool -> functionName.equals(tool.getNodeId()))
                    .findFirst()
                    .orElse(null);

            if (targetTool == null) {
                return "{\"error\": \"Tool not found: " + functionName + "\"}";
            }

            // 解析输入参数
            Map<String, Object> params = new HashMap<>();

            targetTool.getInputs().forEach(item -> {
                String key = item.getKey();
                String valueType = item.getValueType();
                Object valueObj = item.getValue();
                if (valueType.equals("string")) {
                    params.put(key, (String) valueObj);
                } else if ("boolean".equals(valueType)) {
                    params.put(key, (Boolean) valueObj);
                } else if ("object".equals(valueType)) {
                    params.put(key, objectMapper.convertValue(valueObj, McpToolDataType.class));
                } else {
                    params.put(key, valueObj);
                }
            });

            Map<String, Object> objectMap = parseToolInput(input);
            params.putAll(objectMap);

            ModuleDispatchProps props = ModuleDispatchProps.builder().build();
            BeanUtils.copyProperties(request, props);
            props.setNode(targetTool);
            props.setParams(params);

            Map<String, Object> stringObjectMap = nodeService.processNode(targetTool.getFlowNodeType(), props);

            // 返回结果
            return convertToolResultToString(stringObjectMap);

        } catch (Exception e) {
            log.error("Error executing tool function: {}", functionName, e);
            return "{\"error\": \"Tool execution failed: " + e.getMessage() + "\"}";
        }
    }

    /**
     * 解析工具输入参数
     */
    private Map<String, Object> parseToolInput(String input) {
        try {
            return objectMapper.readValue(input, Map.class);
        } catch (Exception e) {
            log.warn("Failed to parse tool input as JSON: {}", input);
            Map<String, Object> result = new HashMap<>();
            result.put("input", input);
            return result;
        }
    }



    /**
     * 将工具结果转换为字符串
     */
    private String convertToolResultToString(Map<String, Object> result) {
        try {
            return objectMapper.writeValueAsString(result);
        } catch (Exception e) {
            log.error("Failed to convert tool result to string", e);
            return "{\"error\": \"Failed to serialize tool result\"}";
        }
    }

    /**
     * 转换消息格式为Spring AI格式
     */
    private List<Message> convertToSpringAIMessages(List<ChatCompletionMessageParam> messages) {
        List<Message> springAiMessages = new ArrayList<>();

        for (ChatCompletionMessageParam msg : messages) {
            Object content = msg.getContent();
            if (content instanceof ChatCompletionContentPart) {
                Message springAiMessage = convertSingleMessage(msg);
                if (springAiMessage != null) {
                    springAiMessages.add(springAiMessage);
                }
            } else if (content instanceof List<?>) {
                List<ChatCompletionContentPartText> contentList = (List<ChatCompletionContentPartText>) content;
                for (ChatCompletionContentPartText partText : contentList) {
                    String text = partText.getText();
                    ChatCompletionMessageParam newParam = new ChatCompletionMessageParam();
                    newParam.setRole(msg.getRole());
                    newParam.setContent(text);
                    Message springAiMessage = convertSingleMessage(newParam);
                    if (springAiMessage != null) {
                        springAiMessages.add(springAiMessage);
                    }
                }
            }
        }

        return springAiMessages;
    }

    /**
     * 转换单个消息
     */
    private Message convertSingleMessage(ChatCompletionMessageParam msg) {
        if (msg == null || msg.getRole() == null) {
            return null;
        }

        ChatCompletionContentPartText content = msg.getContent() != null ? (ChatCompletionContentPartText)msg.getContent() : null;

        switch (msg.getRole().toLowerCase()) {
            case "system":
                return new SystemMessage(content.getText());
            case "user":
                return new UserMessage(content.getText());
            case "assistant":
                AssistantMessage assistantMsg = new AssistantMessage(content.getText());
                // 处理工具调用
                if (msg.getToolCalls() != null && !msg.getToolCalls().isEmpty()) {
                    List<AssistantMessage.ToolCall> toolCalls = new ArrayList<>();
                    for (ChatCompletionMessageToolCall toolCall : msg.getToolCalls()) {
                        if (toolCall.getFunction() != null) {
                            AssistantMessage.ToolCall springToolCall = new AssistantMessage.ToolCall(
                                    toolCall.getId(),
                                    "function",
                                    toolCall.getFunction().getName(),
                                    toolCall.getFunction().getArguments()
                            );
                            toolCalls.add(springToolCall);
                        }
                    }
                    assistantMsg = new AssistantMessage(content.getText(), Map.of(), toolCalls);
                }
                return assistantMsg;
//            case "tool":
//                return new ToolResponseMessage(content, msg.getToolCallId());
            default:
                log.warn("Unknown message role: {}", msg.getRole());
                return new UserMessage(content.getText());
        }
    }

    // 内部数据类

    @Data
    @Builder
    private static class UsageCalculationResult {
        private int totalPoints;
        private String modelName;
        private int toolAIUsage;
    }

    @Data
    @Builder
    private static class RunToolResponse {
        private List<DispatchFlowResponse> dispatchFlowResponse;
        private int toolNodeInputTokens;
        private int toolNodeOutputTokens;
        private List<ChatCompletionMessageParam> completeMessages;
        private List<AIChatItemValueItemType> assistantResponses;
        private WorkflowInteractiveResponseType toolWorkflowInteractiveResponse;
        private int runTimes;
        private String finishReason;
    }

    @Data
    @Builder
    private static class ToolCallResult {
        private String answer;
        private FunctionCallCompletion toolJson;

        public ToolCallResult(String answer, FunctionCallCompletion toolJson) {
            this.answer = answer;
            this.toolJson = toolJson;
        }
    }

    @Data
    @Builder
    private static class ToolExecutionResult {
        private String toolResponsePrompt;
        private boolean hasStopSignal;
        private WorkflowInteractiveResponseType workflowInteractiveResponse;
        private DispatchFlowResponse dispatchFlowResponse;
    }

    @Data
    private static class FunctionCallCompletion {
        private String id;
        private String name;
        private String arguments;
        private String toolName;
        private String toolAvatar;
    }

    /**
     * 构建工具提示
     */
    private String buildToolsPrompt(List<ToolNodeItemType> toolNodes) {
        try {
            Map<String, JsonSchemaPropertiesItemType> valueTypeJsonSchemaMap = WorkflowUtil.createValueTypeJsonSchemaMap();

            List<ToolPromptDef> tools = toolNodes.stream().map(item -> {
                if (item.getJsonSchema() != null) {
                    ToolPromptDef functionDef = new ToolPromptDef();
                    functionDef.setToolId(item.getNodeId());
                    functionDef.setDescription(item.getIntro());
                    functionDef.setParameters(item.getJsonSchema());
                    return functionDef;
                } else {
                    Map<String, PropertiesValue> properties = new HashMap<>();
                    List<FlowNodeInputItemType> toolParams = item.getToolParams();

                    toolParams.forEach(param -> {
                        String valueType = param.getValueType();
                        JsonSchemaPropertiesItemType jsonSchema;
                        if (valueType == null) {
                            ToolValueTypeItem toolValueTypeItem = WorkflowUtil.TOOL_VALUE_TYPE_LIST.get(0);
                            jsonSchema = toolValueTypeItem.getJsonSchema();
                        } else {
                            jsonSchema = valueTypeJsonSchemaMap.get(param.getValueType());
                            if (jsonSchema == null) {
                                ToolValueTypeItem toolValueTypeItem = WorkflowUtil.TOOL_VALUE_TYPE_LIST.get(0);
                                jsonSchema = toolValueTypeItem.getJsonSchema();
                            }
                        }
                        List<String> enums = new ArrayList<>();
                        if (param.getEnums() != null) {
                            enums = Arrays.stream(param.getEnums().split("\\R"))
                                    .filter(line -> !line.trim().isEmpty())
                                    .toList();
                        }
                        PropertiesValue propertiesValue = new PropertiesValue();
                        propertiesValue.setDescription(param.getToolDescription());
                        propertiesValue.setEnums(enums);
                        propertiesValue.setType(jsonSchema.getType());
                        propertiesValue.setItems(jsonSchema.getItems());
                        properties.put(param.getKey(), propertiesValue);
                    });

                    JSONSchemaInputType pa = new JSONSchemaInputType();
                    pa.setType("object");
                    pa.setProperties(properties);
                    pa.setRequired(item.getToolParams().stream()
                            .filter(FlowNodeInputItemType::getRequired)
                            .map(FlowNodeInputItemType::getKey)
                            .toList());

                    ToolPromptDef tool = new ToolPromptDef();
                    tool.setToolId(item.getNodeId());
                    tool.setDescription(item.getIntro());
                    tool.setParameters(pa);
                    return tool;
                }
            }).toList();

            return objectMapper.writeValueAsString(tools);
        } catch (Exception e) {
            log.error("Error building tools prompt", e);
            return "[]";
        }
    }

    /**
     * 在消息中替换工具提示
     */
    private void replaceToolsPromptInMessage(ChatCompletionMessageParam message, String toolsPrompt) {
        if (message.getContent() instanceof String) {
            message.setContent(WorkflowUtil.replaceVariable(message.getContent(), Map.of("toolsPrompt", toolsPrompt)));
        } else if (message.getContent() instanceof List<?>) {
            try {
                List<ChatCompletionContentPartText> contentList = (List<ChatCompletionContentPartText>) message.getContent();
                ChatCompletionContentPartText lastText = contentList.get(contentList.size() - 1);
                lastText.setText((String) WorkflowUtil.replaceVariable(lastText.getText(), Map.of("toolsPrompt", toolsPrompt)));
            } catch (Exception e) {
                throw new RuntimeException("Prompt call invalid input");
            }
        } else {
            throw new RuntimeException("Prompt call invalid input");
        }
    }

    /**
     * 解析AI响应，提取工具调用信息
     */
    private ToolCallResult parseAnswer(String answer) {
        answer = answer.trim();

        // 检查是否包含工具调用标识 "1:"
        if (answer.matches(".*1[:：].*")) {
            String toolString = extractJsonFromString(answer);

            try {
                Map<String, Object> toolCall = objectMapper.readValue(toolString, Map.class);

                FunctionCallCompletion functionCall = new FunctionCallCompletion();
                functionCall.setId(WorkflowUtil.getNanoid(10));
                functionCall.setName((String) toolCall.get("toolId"));

                Object arguments = toolCall.get("arguments");
                if (arguments == null) {
                    arguments = toolCall.get("parameters");
                }
                functionCall.setArguments(objectMapper.writeValueAsString(arguments != null ? arguments : new HashMap<>()));

                return new ToolCallResult("1: " + toolString, functionCall);
            } catch (Exception e) {
                if (answer.matches("^1[:：].*")) {
                    return new ToolCallResult("Tool run error", null);
                } else {
                    return new ToolCallResult(answer, null);
                }
            }
        } else {
            // 提取 "0:" 后的内容作为答案
            int firstIndex = answer.indexOf("0:");
            if (firstIndex == -1) {
                firstIndex = answer.indexOf("0：");
            }
            if (firstIndex != -1) {
                answer = answer.substring(firstIndex + 2).trim();
            }
            return new ToolCallResult(answer, null);
        }
    }

    /**
     * 从字符串中提取JSON
     */
    private String extractJsonFromString(String str) {
        // 简单的JSON提取逻辑，寻找第一个 { 到最后一个 } 的内容
        int start = str.indexOf('{');
        int end = str.lastIndexOf('}');
        if (start != -1 && end != -1 && end > start) {
            return str.substring(start, end + 1);
        }
        return "{}";
    }

    /**
     * 执行工具调用
     */
    private ToolExecutionResult executeToolCall(FunctionCallCompletion toolCall,
                                                List<ToolNodeItemType> toolNodes,
                                                RunToolRequest request) {
        try {
            // 查找工具节点
            ToolNodeItemType toolNode = toolNodes.stream()
                    .filter(item -> item.getNodeId().equals(toolCall.getName()))
                    .findFirst()
                    .orElse(null);

            if (toolNode == null) {
                throw new RuntimeException("Tool not found: " + toolCall.getName());
            }

            toolCall.setToolName(toolNode.getName());
            toolCall.setToolAvatar(toolNode.getAvatar());

            // 解析工具参数
            Map<String, Object> startParams;
            try {
                startParams = objectMapper.readValue(toolCall.getArguments(), Map.class);
            } catch (Exception e) {
                startParams = new HashMap<>();
            }

            // TODO: 实现具体的工具执行逻辑
            // 这里应该调用实际的工具执行方法，类似于 dispatchWorkFlow
            // DispatchFlowResponse toolResponse = dispatchWorkFlow(workflowProps, toolNode, startParams);

            // 暂时返回模拟结果
            String toolResponsePrompt = "Tool executed successfully with params: " + toolCall.getArguments();

            return ToolExecutionResult.builder()
                    .toolResponsePrompt(toolResponsePrompt)
                    .hasStopSignal(false)
                    .workflowInteractiveResponse(null)
                    .dispatchFlowResponse(null)
                    .build();

        } catch (Exception e) {
            log.error("Error executing tool call: {}", toolCall.getName(), e);
            return ToolExecutionResult.builder()
                    .toolResponsePrompt("Tool execution failed: " + e.getMessage())
                    .hasStopSignal(false)
                    .workflowInteractiveResponse(null)
                    .dispatchFlowResponse(null)
                    .build();
        }
    }

    /**
     * 更新最后一条消息，添加工具响应
     */
    private void updateLastMessageWithToolResponse(ChatCompletionMessageParam lastMessage,
                                                   String answer, String toolResponse) {
        String toolResponseText = String.format("%s\nTOOL_RESPONSE: \"\"\"\n%s\n\"\"\"\nANSWER: ",
                answer, toolResponse);

        if (lastMessage.getContent() instanceof String) {
            lastMessage.setContent(lastMessage.getContent() + toolResponseText);
        } else if (lastMessage.getContent() instanceof List<?>) {
            try {
                List<ChatCompletionContentPartText> contentList = (List<ChatCompletionContentPartText>) lastMessage.getContent();
                if (!contentList.isEmpty()) {
                    ChatCompletionContentPartText lastContent = contentList.get(contentList.size() - 1);
                    lastContent.setText(lastContent.getText() + toolResponseText);
                }
            } catch (ClassCastException e) {
                log.warn("Failed to update message content", e);
            }
        }
    }

    /**
     * 构建最终响应（无工具调用）
     */
    private RunToolResponse buildFinalResponse(List<ChatCompletionMessageParam> messages,
                                               String answer, String reasoning,
                                               RunToolResponse previousResponse,
                                               List<AIChatItemValueItemType> assistantResponses,
                                               org.springframework.ai.chat.metadata.Usage usage) {
        ChatCompletionMessageParam gptAssistantResponse = new ChatCompletionMessageParam();
        gptAssistantResponse.setRole(ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue());
        gptAssistantResponse.setContent(answer);

        List<ChatCompletionMessageParam> completeMessages = new ArrayList<>(messages);
        completeMessages.add(gptAssistantResponse);

        int inputTokens = usage != null ? usage.getPromptTokens() : 0;
        int outputTokens = usage != null ? usage.getCompletionTokens() : 0;

        return RunToolResponse.builder()
                .dispatchFlowResponse(previousResponse != null ? previousResponse.getDispatchFlowResponse() : new ArrayList<>())
                .toolNodeInputTokens((previousResponse != null ? previousResponse.getToolNodeInputTokens() : 0) + inputTokens)
                .toolNodeOutputTokens((previousResponse != null ? previousResponse.getToolNodeOutputTokens() : 0) + outputTokens)
                .completeMessages(completeMessages)
                .assistantResponses(assistantResponses)
                .runTimes((previousResponse != null ? previousResponse.getRunTimes() : 0) + 1)
                .finishReason("stop")
                .build();
    }

    /**
     * 构建工具停止响应
     */
    private RunToolResponse buildToolStopResponse(List<ChatCompletionMessageParam> messages,
                                                  List<AIChatItemValueItemType> assistantResponses,
                                                  ToolExecutionResult toolResult,
                                                  RunToolResponse previousResponse,
                                                  org.springframework.ai.chat.metadata.Usage usage) {
        int inputTokens = usage != null ? usage.getPromptTokens() : 0;
        int outputTokens = usage != null ? usage.getCompletionTokens() : 0;

        return RunToolResponse.builder()
                .dispatchFlowResponse(previousResponse != null ?
                        new ArrayList<>(previousResponse.getDispatchFlowResponse()) : new ArrayList<>())
                .toolNodeInputTokens((previousResponse != null ? previousResponse.getToolNodeInputTokens() : 0) + inputTokens)
                .toolNodeOutputTokens((previousResponse != null ? previousResponse.getToolNodeOutputTokens() : 0) + outputTokens)
                .completeMessages(messages)
                .assistantResponses(assistantResponses)
                .runTimes((previousResponse != null ? previousResponse.getRunTimes() : 0) + 1)
                .toolWorkflowInteractiveResponse(toolResult.getWorkflowInteractiveResponse())
                .finishReason(toolResult.hasStopSignal ? "tool_stop" : "stop")
                .build();
    }
}
