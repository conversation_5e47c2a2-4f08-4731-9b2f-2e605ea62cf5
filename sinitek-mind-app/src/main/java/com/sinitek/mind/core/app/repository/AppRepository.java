package com.sinitek.mind.core.app.repository;

import com.sinitek.mind.core.app.entity.App;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.Update;

import java.util.List;
import java.util.Optional;

public interface AppRepository extends MongoRepository<App, String> {
    // 基础的CRUD操作由MongoRepository提供
    @NotNull
    Optional<App> findById(@NotNull String id);

    /**
     * 根据团队ID查找应用
     */
    List<App> findByTeamId(String teamId);

    /**
     * 根据团队ID和父级ID查找子应用
     */
    List<App> findByTeamIdAndParentId(String teamId, String parentId);

    /**
     * 根据团队ID和类型查找应用
     */
    List<App> findByTeamIdAndType(String teamId, String type);

    /**
     * 根据团队ID查找应用，按更新时间倒序排列
     */
    List<App> findByTeamIdOrderByUpdateTimeDesc(String teamId);

    /**
     * 根据团队ID和多个ID查找应用
     */
    List<App> findByTeamIdAndIdIn(String teamId, List<String> ids);

    /**
     * 根据团队ID和父级ID查找子应用，只返回指定字段
     */
    @Query(value = "{'teamId': ?0, 'parentId': ?1}", fields = "{'id': 1, 'name': 1, 'avatar': 1}")
    List<App> findChildrenByTeamIdAndParentId(String teamId, String parentId);

    /**
     * 根据团队ID和多个ID查找应用，只返回指定字段
     */
    @Query(value = "{'teamId': ?0, '_id': {'$in': ?1}}", fields = "{'id': 1, 'name': 1, 'avatar': 1}")
    List<App> findBasicInfoByTeamIdAndIds(String teamId, List<String> ids);

    @Query("{ 'parentId': ?1, 'teamId': ?2 }")
    void updateAvatarByParentIdAndTeamId(String avatar, String parentId, String teamId);

    /**
     * 根据应用ID更新tmbId
     */
    @Query("{ '_id': ?0 }")
    @Update("{ '$set': { 'tmbId': ?1 } }")
    void updateTmbIdById(String appId, String tmbId);

//    void updateTypeById(String appId, String type);

    List<App> findByTypeInAndInheritPermissionTrue(List<String> types);

    void deleteByNamespaceIn(List<String> namespaceList);
}
