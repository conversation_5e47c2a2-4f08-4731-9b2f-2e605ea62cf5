package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.core.app.model.McpToolConfig;
import com.sinitek.mind.core.app.model.PluginData;
import com.sinitek.mind.core.app.model.SecretValue;
import com.sinitek.mind.core.app.model.ShortUrlParams;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 创建MCP工具集请求
 * 对应原始的 createMCPToolsBody
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateMCPToolsDTO {

    // 从 CreateAppDTO 继承的字段 (排除 type/modules/edges/chatConfig)
    /**
     * 父级应用ID（可选）
     */
    private String parentId;
    /**
     * 工具集名称
     */
    @NotBlank(message = "工具集名称不能为空")
    private String name;
    /**
     * 工具集头像
     */
    private String avatar;
    private ShortUrlParams utmParams;
    /**
     * 工具集介绍
     */
    private String intro;
    private String teamId;
    private String tmbId;
    private PluginData pluginData;
    private String username;
    private String userAvatar;
    private String namespace;

    // 新增字段
    /**
     * MCP服务器URL
     */
    @NotBlank(message = "MCP服务器URL不能为空")
    private String url;                        // 对应 TS 的 url
    /**
     * 请求头密钥配置
     */
    private Map<String, SecretValue> headerSecret; // 对应 TS 的可选 headerSecret
    /**
     * 工具列表
     */
    @NotEmpty(message = "工具列表不能为空")
    @Valid
    private List<McpToolConfig> toolList; // 对应 TS 的 toolList
}