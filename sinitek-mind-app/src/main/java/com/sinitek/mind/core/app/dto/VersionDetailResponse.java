package com.sinitek.mind.core.app.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 版本详情响应DTO
 * 对应原始接口的AppVersionSchemaType返回类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VersionDetailResponse {
    
    /**
     * 版本ID
     */
    @JsonProperty("_id")
    private String _id;
    
    /**
     * 应用ID
     */
    private Long appId;
    
    /**
     * 创建时间
     */
    private Date time;
    /**
     * 工作流节点列表
     */
    private List<StoreNodeItemType> nodes;


    /**
     * 工作流边列表
     */
    private List<StoreEdgeItemType> edges;

    /**
     * 聊天配置
     */
    private AppChatConfigType chatConfig;
    /**
     * 是否已发布
     */
    private Boolean isPublish;
    
    /**
     * 版本名称
     */
    private String versionName;
    
    /**
     * 团队成员ID
     */
    private String tmbId;

}