package com.sinitek.mind.core.chat.dto;

import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class V2ChatCompletionRequest {

    // FastGptWebChatProps 相关属性
    @Schema(description = "存在时，会获取从数据库中获取记录作为上下文")
    String chatId;

    @Schema(description = "使用token调用时，需要传入应用id")
    String appId;

    @Schema(description = "自定义元数据，只用于存储")
    Map<String, Object> metadata;

    // OutLinkChatAuthProps 相关属性
    @Schema(description = "免登录窗口shareId与outLinkUid搭配使用")
    String shareId;

    @Schema(description = "免登录窗口outLinkUid可自定义，存储时按照这个值进行存储聊天记录")
    String outLinkUid;

    @Schema(description = "如果免登录窗口配置了身份验证链接，则会将authToken传入验证接口")
    String authToken;

    @Schema(description = "暂时无用")
    String teamId;

    @Schema(description = "暂时无用")
    String teamToken;

    @Schema(description = "消息内容")
    List<ChatCompletionMessageParam> messages;

    @Schema(description = "自定义返回聊天itemId-即ai回复内容对应chatItem实体的id")
    String responseChatItemId;

    @Schema(description = "是否流式输出-只能为true")
    Boolean stream = true;

    @Schema(description = "是否输出详情-节点状态和返回值")
    Boolean detail = false;

    @Schema(description = "是否保留数据集引用-需要")
    Boolean retainDatasetCite = false;

    @Schema(description = "工作流全局变量")
    Map<String, Object> variables;

}