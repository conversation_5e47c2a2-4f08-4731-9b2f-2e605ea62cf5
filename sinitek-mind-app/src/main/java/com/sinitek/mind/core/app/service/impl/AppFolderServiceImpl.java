package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.dao.AppDAO;
import com.sinitek.mind.core.app.dao.AppVersionDAO;
import com.sinitek.mind.core.app.dto.AuthAppDTO;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.dto.GetPathDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.ParentTreePathItemType;
import com.sinitek.mind.core.app.service.IAppFolderService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthMemberDTO;
import com.sinitek.mind.support.permission.dto.CollaboratorPermissionDTO;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.ICollaboratorService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppFolderServiceImpl implements IAppFolderService {

    private final IPermissionService permissionService;

    private final AppVersionDAO appVersionDAO;

    private final AppDAO appDAO;

    private final ICollaboratorService collaboratorService;

    private final IOperationLogService operationLogService;

    private final IAuthAppService authAppService;

    private final IAuthService authService;

    private static final List<AppTypeEnum> APP_FOLDER_TYPE_LIST = List.of(AppTypeEnum.FOLDER);

    private static final String FOLDER_IMG_URL = "/imgs/files/folder.svg";

    @Override
    public String createFolder(CreateAppDTO body) {
        Long parentId = body.getParentId();
        String name = body.getName();
        String intro = body.getIntro();

        if (StringUtils.isBlank(name)) {
            throw new BussinessException(AppErrorCodeConstant.APP_NAME_CANNOT_BE_EMPTY);
        }

        String tmbId;
        if (parentId !=null ) {
            AuthAppDTO authAppDTO = authAppService.authApp(parentId, PermissionConstant.WRITE_PER);
            tmbId = authAppDTO.getTmbId();
        } else {
            AuthMemberDTO authMemberDTO = authService.authUserPer(PermissionConstant.APP_CREATE_PER);
            tmbId = authMemberDTO.getTmbId();
        }

        App app = new App();
        app.setParentId(parentId);
        app.setAvatar(FOLDER_IMG_URL);
        app.setOrgId(tmbId);
        app.setType(AppTypeEnum.FOLDER.getValue());
        app.setName(name);
        app.setIntro(intro);
        app.setUpdateTimeStamp(new Date());
        appDAO.save(app);

        if (parentId != null) {
            // 同步上级的权限
            List<CollaboratorPermissionDTO> parentClbsAndGroups = collaboratorService.getResourceClbsAndGroups(String.valueOf(parentId), ResourceTypeEnum.APP);

            collaboratorService.syncCollaborators(String.valueOf(app.getId()), ResourceTypeEnum.APP, parentClbsAndGroups);
        }

        operationLogService.addOperationLog(MindConstant.APP,
                String.format("创建文件夹【%s】", body.getName()));

        return String.valueOf(app.getId());
    }

    @Override
    public List<ParentTreePathItemType> folderPath(GetPathDTO props) throws Exception {
        if (props.getSourceId() == null) {
            return List.of();
        }
        AuthAppDTO authAppDTO = authAppService.authApp(props.getSourceId(), PermissionConstant.READ_PER);
        App app = authAppDTO.getApp();
        return getParents("current".equals(props.getType()) ? props.getSourceId() : app.getParentId());
    }

    private List<ParentTreePathItemType> getParents(Long parentId) {
        if (parentId == null) {
            return new LinkedList<>();
        }
        App parent = appDAO.getById(parentId);
        if (parent == null) {
            return new LinkedList<>();
        }
        List<ParentTreePathItemType> parentsPath = getParents(parent.getParentId());

        parentsPath.add(new ParentTreePathItemType(parentId, parent.getName()));
        return parentsPath;
    }
}
