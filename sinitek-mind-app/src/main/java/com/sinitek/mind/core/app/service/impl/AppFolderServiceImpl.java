package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.dto.AuthAppDTO;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.dto.GetPathDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.entity.AppVersion;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.ParentTreePathItemType;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.core.app.repository.AppVersionRepository;
import com.sinitek.mind.core.app.service.IAppFolderService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthMemberDTO;
import com.sinitek.mind.support.permission.dto.CollaboratorPermissionDTO;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.ICollaboratorService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppFolderServiceImpl implements IAppFolderService {

    private final IPermissionService permissionService;

    private final AppVersionRepository appVersionRepository;

    private final AppRepository appRepository;

    private final ICollaboratorService collaboratorService;

    private final IOperationLogService operationLogService;

    private final IAuthAppService authAppService;

    private final IAuthService authService;

    private static final List<AppTypeEnum> APP_FOLDER_TYPE_LIST = List.of(AppTypeEnum.FOLDER);

    private static final String FOLDER_IMG_URL = "/imgs/files/folder.svg";

    @Override
    public String createFolder(CreateAppDTO body) {
        String parentId = body.getParentId();
        String name = body.getName();
        String intro = body.getIntro();

        if (StringUtils.isBlank(name)) {
            throw new BussinessException(AppErrorCodeConstant.APP_NAME_CANNOT_BE_EMPTY);
        }

        String teamId;
        String tmbId;
        if (StringUtils.isNotBlank(parentId)) {
            AuthAppDTO authAppDTO = authAppService.authApp(parentId, PermissionConstant.WRITE_PER);
            teamId = authAppDTO.getTeamId();
            tmbId = authAppDTO.getTmbId();
        } else {
            AuthMemberDTO authMemberDTO = authService.authUserPer(PermissionConstant.APP_CREATE_PER);
            teamId = authMemberDTO.getTeamId();
            tmbId = authMemberDTO.getTmbId();
        }

        App app = new App();
        app.setParentId(StringUtils.isNotBlank(parentId) ? parentId : null);
        app.setAvatar(FOLDER_IMG_URL);
        app.setTeamId(teamId);
        app.setTmbId(tmbId);
        app.setType(AppTypeEnum.FOLDER.getValue());
        app.setName(name);
        app.setIntro(intro);
        app.setUpdateTime(new Date());
        App savedApp = appRepository.save(app);

        if (StringUtils.isNotBlank(parentId)) {
            // 同步上级的权限
            List<CollaboratorPermissionDTO> parentClbsAndGroups = collaboratorService.getResourceClbsAndGroups(parentId, ResourceTypeEnum.APP);

            collaboratorService.syncCollaborators(savedApp.getId(), ResourceTypeEnum.APP, parentClbsAndGroups);
        }

        operationLogService.addOperationLog(MindConstant.APP,
                String.format("创建文件夹【%s】", body.getName()));

        return savedApp.getId();
    }

    @Override
    public List<ParentTreePathItemType> folderPath(GetPathDTO props) throws Exception {
        if (StringUtils.isBlank(props.getSourceId())) {
            return List.of();
        }
        AuthAppDTO authAppDTO = authAppService.authApp(props.getSourceId(), PermissionConstant.READ_PER);
        App app = authAppDTO.getApp();
        return getParents("current".equals(props.getType()) ? props.getSourceId() : app.getParentId());
    }

    private List<ParentTreePathItemType> getParents(String parentId) {
        if (parentId == null || StringUtils.isBlank(parentId)) {
            return new LinkedList<>();
        }
        App parent = appRepository.findById(parentId).orElse(null);
        if (parent == null) {
            return new LinkedList<>();
        }
        List<ParentTreePathItemType> parentsPath = getParents(parent.getParentId());

        parentsPath.add(new ParentTreePathItemType(parentId, parent.getName()));
        return parentsPath;
    }

    private String onCreateApp(CreateAppDTO body) {
        App app = new App();
        app.setName(body.getName());
        app.setAvatar(body.getAvatar());
        app.setType(body.getType());
        app.setModules(body.getModules());
        app.setEdges(body.getEdges());
        app.setChatConfig(body.getChatConfig());
        app.setVersion("v2");

        // 如果有父级ID，设置父级关系
        if (body.getParentId() != null) {
            app.setParentId(body.getParentId());
        }
        // 保存应用
        App savedApp = appRepository.save(app);
        String appId = savedApp.getId();

        if (!APP_FOLDER_TYPE_LIST.contains(AppTypeEnum.fromValue(savedApp.getType()))) {
            AppVersion appVersion = new AppVersion();
            appVersion.setAppId(appId);
            appVersion.setNodes(body.getModules());
            appVersion.setEdges(body.getEdges());
            appVersion.setChatConfig(body.getChatConfig());
            appVersion.setVersionName(body.getName());

            appVersionRepository.save(appVersion);
        }

        return appId;
    }
}
