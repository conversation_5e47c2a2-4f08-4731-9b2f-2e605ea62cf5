package com.sinitek.mind.core.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "获取应用列表请求参数")
public class ListAppDTO {

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "应用类型")
    private List<String> type; // AppTypeEnum

    @Schema(description = "是否获取最近聊天的应用")
    private Boolean getRecentlyChat;

    @Schema(description = "搜索关键词")
    private String searchKey;

    @Schema(description = "命名空间")
    private String namespace;
}