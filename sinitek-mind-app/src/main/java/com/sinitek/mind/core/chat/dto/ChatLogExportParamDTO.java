package com.sinitek.mind.core.chat.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 聊天日志导出查询参数DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatLogExportParamDTO {
    
    /**
     * 应用ID
     */
    private Long appId;
    
    /**
     * 开始日期
     */
    private Date dateStart;
    
    /**
     * 结束日期
     */
    private Date dateEnd;
    
    /**
     * 来源列表
     */
    private List<String> sources;
    
    /**
     * 日志标题（用于搜索）
     */
    private String logTitle;
}