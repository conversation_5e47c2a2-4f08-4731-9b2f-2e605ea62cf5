package com.sinitek.mind.core.chat.service.impl;

import com.sinitek.mind.core.chat.dao.ChatInputGuideDAO;
import com.sinitek.mind.core.chat.service.IChatInputGuideService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 聊天输入引导服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatInputGuideServiceImpl implements IChatInputGuideService {

    private final ChatInputGuideDAO chatInputGuideDAO;

    @Override
    public Integer countTotal(String appId) {
        // 参数验证
        if (!StringUtils.hasText(appId)) {
            return 0;
        }

        // 统计数量
        long total = chatInputGuideDAO.countByAppId(appId);
        
        return (int) total;
    }
}