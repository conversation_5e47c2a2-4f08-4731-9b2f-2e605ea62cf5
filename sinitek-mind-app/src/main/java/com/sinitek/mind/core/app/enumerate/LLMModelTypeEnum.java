package com.sinitek.mind.core.app.enumerate;

import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Getter;

@Getter
public enum LLMModelTypeEnum {

    ALL("all"),
    CLASSIFY("classify"),
    EXTRACT_FIELDS("extractFields"),
    TOOL_CALL("toolCall");

    private final String value;

    LLMModelTypeEnum(String value) {
        this.value = value;
    }

    // 根据字符串值查找枚举
    public static LLMModelTypeEnum fromValue(String value) {
        for (LLMModelTypeEnum type : LLMModelTypeEnum.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new BussinessException(AppErrorCodeConstant.UNKNOWN_LLM_MODEL_TYPE, value);
    }
}
