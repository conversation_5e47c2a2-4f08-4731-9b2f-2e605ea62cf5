package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.dao.AppDAO;
import com.sinitek.mind.core.app.dto.AuthAppDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.AppDetailType;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.dto.PermissionDTO;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.dto.TeamMemberDTO;
import com.sinitek.mind.support.team.service.ITeamMemberService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class AuthAppServiceImpl implements IAuthAppService {

    private final IAuthService authService;

    private final ITeamMemberService memberService;

    private final AppDAO appDAO;

    private final IPermissionService permissionService;

    @Override
    public AuthAppDTO authApp(Long appId, long per) {

        if (appId == null) {
            throw new BussinessException(AppErrorCodeConstant.APP_ID_CANNOT_BE_EMPTY);
        }

        AuthDTO authDTO = authService.authCert();
        String tmbId = authDTO.getTmbId();

        AppDetailType appDetailType = authAppByTmbId(tmbId, appId, per, authDTO.getIsRoot());

        AuthAppDTO authAppDTO = new AuthAppDTO();
        BeanUtils.copyProperties(authDTO, authAppDTO);

        // 设置结果
        authAppDTO.setPermission(appDetailType.getPermission());
        authAppDTO.setApp(appDetailType);
        authAppDTO.setAppId(appId);
        return authAppDTO;
    }

    @Override
    public AppDetailType authAppByTmbId(String tmbId, Long appId, long per, boolean isRoot) {
        TeamMemberDTO teamMemberDTO = memberService.getById(tmbId);

        if (Objects.isNull(teamMemberDTO)) {
            throw new BussinessException(AppErrorCodeConstant.NO_PERMISSION_TO_ACCESS_APP);
        }

        String teamId = teamMemberDTO.getTeamId();
        PermissionDTO tmbPer = teamMemberDTO.getPermission();

        AppDetailType appDetailType = new AppDetailType();

        App app = appDAO.getById(appId);


        if (Objects.isNull(app)) {
            throw new BussinessException(AppErrorCodeConstant.APP_NOT_FOUND);
        }

        // 设置app的属性
        BeanUtils.copyProperties(app, appDetailType);

        if (isRoot) {
            PermissionDTO permissionDTO = new PermissionDTO();
            permissionDTO.setValue(PermissionConstant.OWNER_PERMISSION_VAL);
            appDetailType.setPermission(permissionDTO);
            return appDetailType;
        }

//        if (!Objects.equals(teamId, app.getTeamId())) {
//            throw new BussinessException("未授权的App，拒绝访问");
//        }

        boolean isOwner = tmbPer.getIsOwner() || Objects.equals(app.getOrgId(), tmbId);

        PermissionDTO permission = new PermissionDTO();
        if (isOwner) {
            permission.setValue(PermissionConstant.OWNER_PERMISSION_VAL);
        } else {
            List<String> appFolderTypeList = List.of(AppTypeEnum.FOLDER.getValue(), AppTypeEnum.HTTP_PLUGIN.getValue());
            if (appFolderTypeList.contains(app.getType()) || !app.getInheritPermission() || Objects.isNull(app.getParentId())) {
                // 1. is a folder. (Folders have compeletely permission)
                // 2. inheritPermission is false.
                // 3. is root folder/app.
                long appPer = permissionService.getResourcePerByOrgId(tmbId, String.valueOf(appId), ResourceTypeEnum.APP, true);
                permission.setValue(appPer);
            } else {
                // is not folder and inheritPermission is true and is not root folder.
                AppDetailType parentAppDetailType = authAppByTmbId(tmbId, app.getParentId(), per, false);
                permission.setValue(parentAppDetailType.getPermission().getValue());
            }
        }

        if (!permission.checkPer(per)) {
            throw new BussinessException(AppErrorCodeConstant.NO_PERMISSION_TO_ACCESS_APP);
        }

        appDetailType.setPermission(permission);
        return appDetailType;
    }


}
