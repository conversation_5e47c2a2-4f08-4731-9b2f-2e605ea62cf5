package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import lombok.Data;

import java.util.List;

/**
 * 更新app，传入值DTO，对应ts中type:AppUpdateParams
 */
@Data
public class AppUpdateDTO {

    private Long appId;

    private Long parentId;

    private String name;

    private String type; // AppTypeEnum

    private String avatar;

    private String intro;

    private List<StoreNodeItemType> nodes;

    private List<StoreEdgeItemType> edges;

    private AppChatConfigType chatConfig;

    private List<String> teamTags;

    private String namespace;
}
