package com.sinitek.mind.core.workflow.dispatch.tools;

import com.sinitek.mind.core.workflow.constant.WorkflowConstant;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.SseResponseEventEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.model.sse.SseMapResponse;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 变量更新调度器
 * 对应 TypeScript 中的 dispatchUpdateVariable 函数
 * 用于处理工作流中的变量更新操作
 */
@Slf4j
@Component
public class UpdateVarDispatcher implements NodeDispatcher {

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.VARIABLE_UPDATE.getValue();
    }

    /**
     * 调度方法 - 主要入口点
     * 
     * @param dispatchData 调度数据，包含执行所需的所有参数
     * @return 执行结果映射，包含更新后的变量状态和响应数据
     */
    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        // 1. 参数提取和验证逻辑 - 从 ModuleDispatchProps 中获取 updateList
        Map<String, Object> params = dispatchData.getParams();
        Map<String, Object> variables = dispatchData.getVariables();
        List<RuntimeNodeItemType> runtimeNodes = dispatchData.getRuntimeNodes();
        RunningAppInfo runningAppInfo = dispatchData.getRunningAppInfo();
        ExternalProviderType externalProvider = dispatchData.getExternalProvider();

        List<String> nodeIds = runtimeNodes.stream()
                .map(RuntimeNodeItemType::getNodeId)
                .toList();

        // 获取更新列表参数
        List<Object> updateObjList = (List<Object>) params.get(NodeInputKeyEnum.UPDATE_LIST.getValue());

        // 进行类型转换
        List<UpdateListItem> updateList = updateObjList.stream()
                .map(item -> WorkflowUtil.formatNodeParam(item, UpdateListItem.class))
                .filter(Objects::nonNull)
                .toList();

        List<Object> result = updateList.stream()
                .map(item -> {
                    ReferenceItemValueType variable = item.getVariable();

                    if (!WorkflowUtil.isValidReferenceValue(variable, nodeIds)) {
                        return null;
                    }

                    String varNodeId = variable.getT1();
                    String varKey = variable.getT2();

                    if (Objects.isNull(varKey)) {
                        return null;
                    }

                    Object value;
                    // If first item is empty, it means it is a input value
                    if (!StringUtils.hasText(item.getValue().getT1())) {
                        Object temp;
                        if (varKey instanceof String) {
                            temp = WorkflowUtil.replaceEditorVariable(item.getValue().getT2(), runtimeNodes, variables);
                        } else {
                            temp = item.getValue().getT2();
                        }
                        value = WorkflowUtil.valueTypeFormat(temp, item.getValueType());
                    } else {
                        value = WorkflowUtil.getReferenceVariableValue(item.getValue(), runtimeNodes, variables);
                    }

                    // Update node output
                    // Global variable
                    if (Objects.equals(varNodeId, WorkflowConstant.VARIABLE_NODE_ID)) {
                        variables.put((String) varKey, value);
                    } else {
                        // Other nodes
                        runtimeNodes.stream()
                                .filter(node -> node.getNodeId().equals(varNodeId))
                                .findFirst()
                                .ifPresent(node ->
                                        node.getOutputs().stream()
                                                .filter(output -> output.getId().equals(varKey))
                                                .findFirst()
                                                .ifPresent(output -> output.setValue(value))
                                );
                    }

                    return value;
                })
                .toList();

        if (runningAppInfo.getIsChildApp() == null || !runningAppInfo.getIsChildApp()) {
            WorkflowStreamResponse workflowStreamResponse = WorkflowStreamResponse.builder()
                    .event(SseResponseEventEnum.UPDATE_VARIABLES.getValue())
                    .data(new SseMapResponse<>(WorkflowUtil.removeSystemVariable(variables, externalProvider.getExternalWorkflowVariables())))
                    .build();
            dispatchData.getWorkflowStreamResponse()
                    .accept(workflowStreamResponse);
        }

        // 构建响应结构
        Map<String, Object> nodeResult = new HashMap<>();
        nodeResult.put(DispatchNodeResponseKeyEnum.NEW_VARIABLES.getValue(), variables);
        nodeResult.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                .updateVarResult(result)
                .build());
        return nodeResult;
    }

}