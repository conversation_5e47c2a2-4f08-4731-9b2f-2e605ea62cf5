package com.sinitek.mind.core.workflow.dispatch.tools;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.common.util.TikTokenUtil;
import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.core.ai.util.PromptUtil;
import com.sinitek.mind.core.app.model.JSONSchemaInputType;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemTextInfo;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.chat.model.ChatsToGPTMessagesParam;
import com.sinitek.mind.core.chat.util.ChatUtil;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.dispatch.util.DispatchUtil;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.model.core.llm.service.IModelChatService;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.micrometer.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ContentExtractDispatcher implements NodeDispatcher {

    @Autowired
    private IModelChatService modelChatService;

    @Autowired
    private ISystemModelService systemModelService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.CONTENT_EXTRACT.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchContentExtract(dispatchData);
    }

    private Map<String, Object> dispatchContentExtract(ModuleDispatchProps props) {
        // TODO ai转写的逻辑 需要落实对应
        // 1. 提取参数
        Map<String, Object> params = props.getParams();
        List<ChatItemType> histories = props.getHistories();
        ExternalProviderType externalProvider = props.getExternalProvider();
        RunningAppInfo runningAppInfo = props.getRunningAppInfo();
        RuntimeNodeItemType node = props.getNode();

        String content = getStringParam(params, "content", "");
        String model = getStringParam(params, "model", "");
        String description = getStringParam(params, "description", "");
        Integer history = getIntegerParam(params, "history", 6);

        // 获取提取字段配置
        List<ContextExtractAgentItemType> extractKeys = getListParam(params, "extractKeys");

        if (StringUtils.isBlank(content)) {
            throw new BussinessException(WorkflowErrorCodeConstant.INPUT_IS_EMPTY);
        }

        // 2. 获取模型信息
        SystemModelDTO extractModel = systemModelService.findModelByModelId(model);
        if (extractModel == null) {
            throw new BussinessException(WorkflowErrorCodeConstant.MODEL_NOT_FOUND);
        }

        // 3. 获取聊天历史
        List<ChatItemType> chatHistories = DispatchUtil.getHistories(history, histories);

        // 4. 获取记忆
        String memoryKey = runningAppInfo.getId() + "-" + node.getNodeId();
        Map<String, Object> lastMemory = getLastMemory(chatHistories, memoryKey);

        // 5. 执行提取
        ExtractResult extractResult;
        if (extractModel.getToolChoice() != null && extractModel.getToolChoice()) {
            ActionPropsCE actionProps = new ActionPropsCE();
            actionProps.setHistories(chatHistories);
            actionProps.setExternalProvider(externalProvider);
            actionProps.setExtractModel(extractModel);
            actionProps.setParams(props.getParams());
            actionProps.setLastMemory(lastMemory);
            extractResult = toolChoice(actionProps);
        } else {
            extractResult = completions(props, extractModel, chatHistories, content, description, extractKeys, lastMemory);
        }

        Map<String, Object> arg = extractResult.getArg();
        int inputTokens = extractResult.getInputTokens();
        int outputTokens = extractResult.getOutputTokens();

        // 6. 清理无效字段
        removeInvalidKeys(arg, extractKeys);

        // 7. 自动填充必填字段
        autoFillRequiredFields(arg, extractKeys);

        // 8. 验证字段
        boolean success = validateFields(arg, extractKeys);

        // 9. 构建响应
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        try {
            result.put("contextExtractFields", objectMapper.writeValueAsString(arg));
        } catch (Exception e) {
            log.warn("Failed to serialize extract fields", e);
            result.put("contextExtractFields", "{}");
        }

        // 添加记忆
        Map<String, Object> memories = new HashMap<>();
        memories.put(memoryKey, arg);
        result.put("memories", memories);

        // 添加提取的字段到结果中
        result.putAll(arg);

        // 添加节点响应信息
        DispatchNodeResponseType nodeResponse = DispatchNodeResponseType.builder()
                .totalPoints(0.0)// TODO: 计算实际消耗
                .model(extractModel.getModel())
                .query(content)
                .inputTokens(inputTokens)
                .outputTokens(outputTokens)
                .extractDescription(description)
                .extractResult(arg)
                .contextTotalLen(chatHistories.size() + 2)
                .build();
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), nodeResponse);

        return result;
    }

    // 辅助方法
    private String getStringParam(Map<String, Object> params, String key, String defaultValue) {
        Object value = params.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    private Integer getIntegerParam(Map<String, Object> params, String key, Integer defaultValue) {
        Object value = params.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    private Map<String, Object> getLastMemory(List<ChatItemType> chatHistories, String memoryKey) {
        if (chatHistories.isEmpty()) {
            return new HashMap<>();
        }

        // 从聊天历史中获取最后的记忆信息
        for (int i = chatHistories.size() - 1; i >= 0; i--) {
            ChatItemType history = chatHistories.get(i);
            if (ChatRoleEnum.AI.equals(history.getObj())) {
                // 检查是否包含记忆信息
                if (history.getValue() != null && !history.getValue().isEmpty()) {
                    ChatItemValueItemType valueItem = history.getValue().get(0);
                    if (valueItem.getText() != null &&
                            StringUtils.isNotBlank(valueItem.getText().getContent())) {
                        String content = valueItem.getText().getContent();

                        // 尝试从内容中提取记忆信息（假设记忆以特定格式存储）
                        Map<String, Object> memory = extractMemoryFromContent(content);
                        if (!memory.isEmpty()) {
                            return memory;
                        }
                    }
                }
            }
        }

        return new HashMap<>();
    }

    private Map<String, Object> extractMemoryFromContent(String content) {
        // 简单的记忆提取逻辑，可以根据实际需求调整
        try {
            // 查找JSON格式的记忆信息
            String jsonStr = extractJsonFromText(content);
            if (StringUtils.isNotBlank(jsonStr)) {
                Map<String, Object> parsed = objectMapper.readValue(jsonStr, new TypeReference<Map<String, Object>>() {});
                // 检查是否包含记忆相关的键
                if (parsed.containsKey("memory") || parsed.containsKey("记忆") ||
                        parsed.containsKey("context") || parsed.containsKey("上下文")) {
                    return parsed;
                }
            }
        } catch (Exception e) {
            log.debug("Failed to extract memory from content: {}", content, e);
        }

        return new HashMap<>();
    }

    private void removeInvalidKeys(Map<String, Object> arg, List<ContextExtractAgentItemType> extractKeys) {
        Set<String> validKeys = extractKeys.stream()
                .map(item -> (String) item.getKey())
                .collect(Collectors.toSet());

        arg.entrySet().removeIf(entry ->
                !validKeys.contains(entry.getKey()) ||
                        (entry.getValue() instanceof String && ((String) entry.getValue()).isEmpty())
        );
    }

    private void autoFillRequiredFields(Map<String, Object> arg, List<ContextExtractAgentItemType> extractKeys) {
        for (ContextExtractAgentItemType item : extractKeys) {
            boolean required = item.isRequired();
            String key = item.getKey();
            Object defaultValue = item.getDefaultValue();

            if (required && !arg.containsKey(key)) {
                arg.put(key, defaultValue != null ? defaultValue : "");
            }
        }
    }

    private boolean validateFields(Map<String, Object> arg, List<ContextExtractAgentItemType> extractKeys) {
        // 检查是否所有必填字段都存在
        for (ContextExtractAgentItemType item : extractKeys) {
            String key = (String) item.getKey();
            if (!arg.containsKey(key)) {
                return false;
            }
        }

        // 检查是否所有字段都有效
        for (String key : arg.keySet()) {
            boolean found = extractKeys.stream()
                    .anyMatch(item -> key.equals(item.getKey()));
            if (!found) {
                return false;
            }
        }

        return true;
    }

    private Map<String, Object> getJsonSchema(List<Map<String, Object>> extractKeys) {
        Map<String, Object> properties = new HashMap<>();

        for (Map<String, Object> item : extractKeys) {
            String key = (String) item.get("key");
            String desc = (String) item.get("desc");
            String valueType = (String) item.get("valueType");
            String enumStr = (String) item.get("enum");

            Map<String, Object> property = new HashMap<>();
            property.put("type", getJsonSchemaType(valueType));
            property.put("description", desc);

            if (StringUtils.isNotBlank(enumStr)) {
                List<String> enumValues = Arrays.stream(enumStr.split("\n"))
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                property.put("enum", enumValues);
            }

            properties.put(key, property);
        }

        return properties;
    }

    private String getJsonSchemaType(String valueType) {
        if (StringUtils.isBlank(valueType)) {
            return "string";
        }

        return switch (valueType.toLowerCase()) {
            case "number", "integer" -> "number";
            case "boolean" -> "boolean";
            case "array" -> "array";
            case "object" -> "object";
            default -> "string";
        };
    }

    private ExtractResult toolChoice(ActionPropsCE actionProps) {
        try {

            ExternalProviderType externalProvider = actionProps.getExternalProvider();
            SystemModelDTO extractModel = actionProps.getExtractModel();
            List<ChatItemType> histories = actionProps.getHistories();
            Map<String, Object> params = actionProps.getParams();
            Map<String, Object> lastMemory = actionProps.getLastMemory();
            String content = getStringParam(params, "content", "");
            String description = getStringParam(params, "description", "");
            // 构建消息
            List<ChatItemType> messages = new ArrayList<>();

            // 系统消息
            String lastMemoryStr = lastMemory.isEmpty() ? null : objectMapper.writeValueAsString(lastMemory);
            String systemPrompt = PromptUtil.getExtractJsonToolPrompt(description, lastMemoryStr);
            ChatItemType systemMessage = new ChatItemType();
            systemMessage.setObj(ChatRoleEnum.SYSTEM);
            ChatItemValueItemType sysItemType = new ChatItemValueItemType();
            sysItemType.setType(ChatItemValueType.TEXT.getValue());
            ChatItemValueItemTextInfo sysTextInfo = new ChatItemValueItemTextInfo();
            sysTextInfo.setContent(systemPrompt);
            sysItemType.setText(sysTextInfo);
            systemMessage.setValue(List.of(sysItemType));
            messages.add(systemMessage);

            // 历史消息
            messages.addAll(histories);

            // 用户消息
            ChatItemType userMessage = new ChatItemType();
            userMessage.setObj(ChatRoleEnum.HUMAN);
            ChatItemValueItemType userItemType = new ChatItemValueItemType();
            userItemType.setType(ChatItemValueType.TEXT.getValue());
            ChatItemValueItemTextInfo userTextInfo = new ChatItemValueItemTextInfo();
            userTextInfo.setContent(content);
            userItemType.setText(userTextInfo);
            userMessage.setValue(List.of(userItemType));
            messages.add(userMessage);

            ChatsToGPTMessagesParam param = new ChatsToGPTMessagesParam();
            param.setMessages(messages);
            param.setReserveId(false);
            List<ChatCompletionMessageParam> adaptMessages = ChatAdaptor.chats2GPTMessages(param);

            List<ChatCompletionMessageParam> filterMessages = ChatUtil.filterGPTMessageByMaxContext(adaptMessages, extractModel.getMaxContext());

            CompletableFuture<List<ChatCompletionMessageParam>> requestMessages = ChatUtil.loadRequestMessages(filterMessages, false, "");

            Map<String, PropertiesValue> schema = DispatchUtil.getJsonSchema(actionProps);

            ChatCompletionTool tool = new ChatCompletionTool();
            FunctionDefinition func = new FunctionDefinition();
            func.setName("request_function");
            func.setDescription("需要执行的函数");
            JSONSchemaInputType pa = new JSONSchemaInputType();
            pa.setType("object");
            pa.setProperties(schema);
            pa.setRequired(new ArrayList<>());
            func.setParameters(pa);
            tool.setFunction(func);

            List<ChatCompletionTool> tools = List.of(tool);

            // TODO 请求大模型获取返回值

            // 创建Prompt
//            Prompt prompt = new Prompt(messages);

            // 调用模型
//            ChatResponse response = modelChatService.chat(prompt, extractModel.getModel());
//            String responseText = response.getResult().getOutput().getText();

            // 解析响应
//            Map<String, Object> arg = parseJsonResponse(responseText);

            // 计算token
//            int inputTokens = calculateInputTokens(messages);
//            int outputTokens = TikTokenUtil.countPromptTokens(responseText, null);

//            return new ExtractResult(arg, inputTokens, outputTokens);
            return null;
        } catch (Exception e) {
            log.error("Tool choice extraction failed", e);
            return new ExtractResult(new HashMap<>(), 0, 0);
        }
    }

    private ExtractResult completions(ModuleDispatchProps props, SystemModelDTO extractModel,
                                      List<ChatItemType> histories, String content, String description,
                                      List<ContextExtractAgentItemType> extractKeys, Map<String, Object> lastMemory) {
        try {
            // 构建消息
            List<Message> messages = new ArrayList<>();

            // 系统消息
//            String systemPrompt = getExtractJsonPrompt(description, lastMemory, getJsonSchema(extractKeys));
//            messages.add(new SystemMessage(systemPrompt));

            // 历史消息
            for (ChatItemType history : histories) {
                if (ChatRoleEnum.HUMAN.equals(history.getObj())) {
                    messages.add(new UserMessage(getTextFromChatItem(history)));
                } else if (ChatRoleEnum.AI.equals(history.getObj())) {
                    // TODO: 添加AI消息处理
                }
            }

            // 用户消息
            messages.add(new UserMessage(content));

            // 创建Prompt
            Prompt prompt = new Prompt(messages);

            // 调用模型
            ChatResponse response = modelChatService.chat(prompt, extractModel.getModel());
            String responseText = response.getResult().getOutput().getText();

            // 解析响应
            Map<String, Object> arg = parseJsonResponse(responseText);

            // 计算token
            int inputTokens = calculateInputTokens(messages);
            int outputTokens = TikTokenUtil.countPromptTokens(responseText, null);

            return new ExtractResult(arg, inputTokens, outputTokens);

        } catch (Exception e) {
            log.error("Completions extraction failed", e);
            return new ExtractResult(new HashMap<>(), 0, 0);
        }
    }

    private String getTextFromChatItem(ChatItemType chatItem) {
        if (chatItem.getValue() == null || chatItem.getValue().isEmpty()) {
            return "";
        }

        ChatItemValueItemType firstValue = chatItem.getValue().get(0);
        if (firstValue.getText() != null) {
            return firstValue.getText().getContent();
        }

        return "";
    }

    private Map<String, Object> parseJsonResponse(String responseText) {
        if (StringUtils.isBlank(responseText)) {
            return new HashMap<>();
        }

        try {
            // 尝试提取JSON字符串
            String jsonStr = extractJsonFromText(responseText);
            if (StringUtils.isNotBlank(jsonStr)) {
                return objectMapper.readValue(jsonStr, new TypeReference<Map<String, Object>>() {});
            }
        } catch (Exception e) {
            log.warn("Failed to parse JSON response: {}", responseText, e);
        }

        return new HashMap<>();
    }

    private String extractJsonFromText(String text) {
        // 简单的JSON提取逻辑，寻找第一个完整的JSON对象
        int start = text.indexOf('{');
        if (start == -1) {
            return null;
        }

        int braceCount = 0;
        int end = start;

        for (int i = start; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == '{') {
                braceCount++;
            } else if (c == '}') {
                braceCount--;
                if (braceCount == 0) {
                    end = i;
                    break;
                }
            }
        }

        if (braceCount == 0 && end > start) {
            return text.substring(start, end + 1);
        }

        return null;
    }

    private int calculateInputTokens(List<Message> messages) {
        int totalTokens = 0;
        for (Message message : messages) {
            String content = message.getText();
            totalTokens += TikTokenUtil.countPromptTokens(content, null);
        }
        return totalTokens;
    }

    /**
     * 获取List参数
     */
    @SuppressWarnings("unchecked")
    private static <T> List<T> getListParam(Map<String, Object> params, String key) {
        Object value = params.get(key);
        if (value instanceof List) {
            return (List<T>) value;
        }
        return new ArrayList<>();
    }

}
