package com.sinitek.mind.core.workflow.constant;

/**
 * 工作流模块错误码常量类
 * 
 * 错误码规则：30-50-YYYY
 * 30 - 固定前缀
 * 50 - 工作流模块
 * YYYY - 具体错误码
 *
 * <AUTHOR>
 * date 2025-08-15
 */
public class WorkflowErrorCodeConstant {

    // ==================== LLM and Model related ====================
    
    /**
     * 请确保已正确配置大模型
     */
    public static final String PLEASE_CONFIRM_LLM_CONFIG = "30050001";
    
    /**
     * 请确认所选的AI模型【{0}】配置正确
     */
    public static final String MODEL_CONFIG_NOT_FOUND = "30050002";
    
    /**
     * AI模型返回值为空，请确认AI模型可以调用
     */
    public static final String AI_MODEL_RESPONSE_EMPTY = "30050003";
    
    /**
     * 未找到模型: {0}
     */
    public static final String MODEL_NOT_FOUND = "30050004";
    
    // ==================== Node type related ====================
    
    /**
     * 未知的节点类型: {0}
     */
    public static final String UNKNOWN_NODE_TYPE = "30050005";
    
    /**
     * 不支持的节点类型: {0}
     */
    public static final String UNSUPPORTED_NODE_TYPE = "30050006";
    
    // ==================== AI Enum related ====================
    
    /**
     * 未知的CompletionFinishReason: {0}
     */
    public static final String UNKNOWN_COMPLETION_FINISH_REASON = "30050044";
    
    /**
     * 未知的ChatMessageTypeEnum: {0}
     */
    public static final String UNKNOWN_CHAT_MESSAGE_TYPE = "30050045";
    
    // ==================== Input validation ====================
    
    /**
     * 输入不能为空
     */
    public static final String INPUT_IS_EMPTY = "30050007";
    
    /**
     * 插件ID不能为空
     */
    public static final String PLUGIN_ID_IS_EMPTY = "30050008";
    
    /**
     * 问题不能为空
     */
    public static final String QUESTION_IS_EMPTY = "30050009";
    
    /**
     * HTTP URL不能为空
     */
    public static final String HTTP_URL_IS_EMPTY = "30050010";
    
    /**
     * LOOP_INPUT_ARRAY必须是一个List
     */
    public static final String LOOP_INPUT_NOT_LIST = "30050011";
    
    /**
     * 循环输入数组不能为空
     */
    public static final String LOOP_INPUT_EMPTY = "30050012";
    
    // ==================== Configuration related ====================
    
    /**
     * 在mind属性中未找到sandboxUrl
     */
    public static final String SANDBOX_URL_NOT_FOUND = "30050013";
    
    /**
     * 无效的代码类型: {0}
     */
    public static final String INVALID_CODE_TYPE = "30050014";
    
    /**
     * 无效的JSON请求体: {0}
     */
    public static final String INVALID_JSON_BODY = "30050015";
    
    /**
     * 对象不是一个Map
     */
    public static final String OBJECT_NOT_MAP = "30050016";
    
    // ==================== Dataset related ====================
    
    /**
     * 数据集列表不能为空
     */
    public static final String DATASET_LIST_EMPTY = "30050017";
    
    /**
     * 过滤数据集失败
     */
    public static final String DATASET_FILTER_FAILED = "30050018";
    
    // ==================== Plugin related ====================
    
    /**
     * 未找到工具配置数据
     */
    public static final String PLUGIN_CONFIG_NOT_FOUND = "30050019";
    
    /**
     * 工具调用失败: {0}
     */
    public static final String PLUGIN_RUN_FAILED = "30050020";
    
    /**
     * Stdio URL格式错误，应为: stdio://command arg1 arg2
     */
    public static final String STDIO_URL_FORMAT_ERROR = "30050021";
    
    /**
     * 不支持的协议: {0}. 支持的协议: http, https, stdio
     */
    public static final String UNSUPPORTED_PROTOCOL = "30050022";
    
    /**
     * 创建MCP客户端失败: {0}
     */
    public static final String MCP_CLIENT_CREATE_FAILED = "30050023";
    
    /**
     * 工具调用返回错误
     */
    public static final String TOOL_CALL_RETURN_ERROR = "30050024";
    
    // ==================== Code related ====================
    
    /**
     * 运行代码失败
     */
    public static final String RUN_CODE_FAILED = "30050025";
    
    // ==================== Conversion related ====================
    
    /**
     * 转换失败
     */
    public static final String CONVERSION_FAILED = "30050026";
    
    // ==================== Dispatch failures ====================
    
    /**
     * 问题分类调度失败: {0}
     */
    public static final String CLASSIFY_QUESTION_DISPATCH_FAILED = "30050027";
    
    /**
     * 插件模块调度失败: {0}
     */
    public static final String PLUGIN_MODULE_DISPATCH_FAILED = "30050028";
    
    /**
     * 查询扩展失败: {0}
     */
    public static final String QUERY_EXTENSION_FAILED = "30050029";
    
    /**
     * 查询扩展AI调用失败: {0}
     */
    public static final String QUERY_EXTENSION_AI_CALL_FAILED = "30050030";
    
    // ==================== Workflow execution related ====================
    
    /**
     * 工作流执行失败
     */
    public static final String WORKFLOW_EXECUTION_FAILED = "30050031";
    
    /**
     * 节点执行失败: {0}
     */
    public static final String NODE_EXECUTION_FAILED = "30050032";
    
    /**
     * 工作流初始化失败
     */
    public static final String WORKFLOW_INIT_FAILED = "30050033";
    
    /**
     * 设置SSE响应头失败
     */
    public static final String SSE_HEADER_SET_FAILED = "30050034";
    
    /**
     * 发送心跳消息失败
     */
    public static final String HEARTBEAT_SEND_FAILED = "30050035";
    
    /**
     * 流式响应处理失败
     */
    public static final String STREAM_RESPONSE_PROCESS_FAILED = "30050036";
    
    /**
     * 流式响应错误
     */
    public static final String STREAM_RESPONSE_ERROR = "30050037";
    
    /**
     * 无效的JSON请求体
     */
    public static final String INVALID_JSON_REQUEST_BODY = "30050038";
    
    /**
     * SSE消息发送失败
     */
    public static final String SSE_MESSAGE_SEND_FAILED = "30050039";
    
    /**
     * 响应写入失败
     */
    public static final String RESPONSE_WRITE_FAILED = "30050040";
    
    /**
     * Json序列化失败
     */
    public static final String JSON_SERIALIZATION_FAILED = "30050041";
    
    /**
     * JSON转换失败:{0}
     */
    public static final String JSON_CONVERSION_FAILED = "30050042";
    
    /**
     * 提取 toolSet 配置数据时发生错误，节点ID: {0}
     */
    public static final String TOOLSET_CONFIG_EXTRACTION_FAILED = "30050043";
    
    private WorkflowErrorCodeConstant() {
        // 工具类，不允许实例化
    }
}