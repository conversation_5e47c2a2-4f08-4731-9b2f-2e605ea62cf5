<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.mind.core.app.mapper.AppMapper">

    <select id="pageByParamDTO" resultType="com.sinitek.mind.core.app.entity.App">
        select
            *
        from mind_app
        where
            (org_id = #{params.orgId}
            <if test="@org.apache.commons.collections.CollectionUtils@isNotEmpty(params.ids)" >
                or id in
                <foreach collection="params.ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            )
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(params.namespace)">
                and namespace = #{params.namespace}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(params.searchKey)">
                and (
                    name like concat("%", #{params.searchKey}, "%")
                    or intro like concat("%", #{params.searchKey}, "%")
                )
            </if>
            <choose>
                <when test="params.parentId != null">
                    and parent_id = #{params.parentId}
                </when>
                <otherwise>
                    and parent_id = 0
                </otherwise>
            </choose>
            <if test="@org.apache.commons.collections.CollectionUtils@isNotEmpty(params.types)">
                and type in
                <foreach collection="params.types" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            order by updatetimestamp desc
            limit #{params.offset}, #{params.limit}
    </select>

</mapper>