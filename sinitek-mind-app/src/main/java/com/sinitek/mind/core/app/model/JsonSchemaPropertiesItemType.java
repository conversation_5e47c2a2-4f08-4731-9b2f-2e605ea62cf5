package com.sinitek.mind.core.app.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class JsonSchemaPropertiesItemType {

    private String description;

    private String type; // 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object'

    private List<String> enums;

    private Integer minimum;
    private Integer maximum;
    private ItemInfo items;

    public JsonSchemaPropertiesItemType (String type) {
        this.type = type;
    }

    public JsonSchemaPropertiesItemType (String type, ItemInfo items) {
        this.type = type;
        this.items = items;
    }

    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ItemInfo {
        private String type; // 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object'

        public ItemInfo (String type) {
            this.type = type;
        }
    }
}
