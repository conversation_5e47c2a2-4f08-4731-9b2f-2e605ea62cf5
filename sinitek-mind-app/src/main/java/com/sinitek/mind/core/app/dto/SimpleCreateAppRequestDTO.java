package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.core.app.model.VariableItemType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "简单创建应用-工作流")
public class SimpleCreateAppRequestDTO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "命名空间")
    private String namespace;

    @Schema(description = "介绍")
    private String intro;

    @Schema(description = "全局变量")
    private List<VariableItemType> globalVariables;
}
