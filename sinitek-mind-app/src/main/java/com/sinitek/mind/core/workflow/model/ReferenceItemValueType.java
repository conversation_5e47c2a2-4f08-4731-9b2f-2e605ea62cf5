package com.sinitek.mind.core.workflow.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 对应ts中的ReferenceItemValueType，元组数据结构
 */
@Data
@NoArgsConstructor
public class ReferenceItemValueType {

    private String t1;

    // 可能为null
    private String t2;

    /**
     * 让 Jackson 把 JSON 数组直接映射成对象
     */
    @JsonCreator
    public ReferenceItemValueType(List<String> arr) {
        if (arr == null || arr.isEmpty() || arr.size() > 2) {
            throw new BussinessException(WorkflowErrorCodeConstant.JSON_CONVERSION_FAILED, "ReferenceItemValueType");
        }
        this.t1 = arr.get(0);
        this.t2 = arr.size() == 2 ? arr.get(1) : null;
    }

    /**
     * 序列化时还原成数组
     */
    @JsonValue
    public List<String> toArray() {
        return t2 == null
                ? Collections.singletonList(t1)
                : Arrays.asList(t1, t2);
    }
}