package com.sinitek.mind.core.workflow.dispatch.tools;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.app.mcp.McpToolDataType;
import com.sinitek.mind.core.app.model.SecretValue;
import com.sinitek.mind.core.app.util.SecurityUtils;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.McpSyncClient;
import io.modelcontextprotocol.client.transport.*;
import io.modelcontextprotocol.spec.McpClientTransport;
import io.modelcontextprotocol.spec.McpSchema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.net.URI;
import java.time.Duration;
import java.util.*;

/**
 * packages\service\core\workflow\dispatch\plugin\runTool.ts
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PluginRunToolDispatcher implements NodeDispatcher {

    private final SecurityUtils securityUtils;

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.TOOL.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchRunTool(dispatchData);
    }

    /**
     * 执行工具调用
     * 对应 TypeScript 中的 dispatchRunTool 函数
     */
    private Map<String, Object> dispatchRunTool(ModuleDispatchProps dispatchData) {
        Map<String, Object> params = dispatchData.getParams();
        String avatar = dispatchData.getNode().getAvatar();

        // 获取工具数据，优先使用toolData，如果没有则使用system_toolData
        McpToolDataType toolData = extractToolData(params);
        if (toolData == null) {
            throw new BussinessException(WorkflowErrorCodeConstant.PLUGIN_CONFIG_NOT_FOUND);
        }

        // 获取工具配置
        String toolName = toolData.getName();
        String url = toolData.getUrl();
        Map<String, SecretValue> headerSecret = toolData.getHeaderSecret();

        // 处理密钥值
        Map<String, String> headers = SecurityUtils.getSecretValue(headerSecret);

        // 创建MCP客户端
        McpSyncClient mcpClient = createMcpClient(url, headers);

        try {
            // 准备工具调用参数（排除toolData和system_toolData）
            Map<String, Object> toolParams = prepareToolParams(params);

            // 执行工具调用
            McpSchema.CallToolResult result = mcpClient.callTool(
                    new McpSchema.CallToolRequest(toolName, toolParams));
            Object toolResult = extractToolResult(result);

            return buildSuccessResponse(avatar, toolResult);

        } catch (Exception e) {
            log.error("MCP工具调用执行失败: toolName={}, url={}", toolName, url, e);
            throw new BussinessException(WorkflowErrorCodeConstant.PLUGIN_RUN_FAILED, e.getMessage());
        } finally {
            // 关闭客户端连接
            try {
                mcpClient.close();
            } catch (Exception e) {
                log.warn("关闭MCP客户端连接失败", e);
            }
        }
    }

    /**
     * 提取工具数据
     */
    private McpToolDataType extractToolData(Map<String, Object> params) {
        Object toolData = params.get(NodeInputKeyEnum.TOOL_DATA.getValue());
        if (toolData instanceof McpToolDataType) {
            return (McpToolDataType) toolData;
        }

        // 尝试从system_toolData获取
        Object systemToolData = params.get("system_toolData");
        if (systemToolData instanceof McpToolDataType) {
            return (McpToolDataType) systemToolData;
        }

        return null;
    }

    /**
     * 创建MCP客户端
     */
    private McpSyncClient createMcpClient(String url, Map<String, String> headers) {
        try {
            URI uri = URI.create(url);
            String baseUrl = uri.getScheme() + "://" + uri.getHost();
            if (uri.getPort() != -1) {
                baseUrl += ":" + uri.getPort();
            }
            String endpoint = uri.getPath();
            if (uri.getQuery() != null) {
                endpoint += "?" + uri.getQuery();
            }
            McpClientTransport transport = null;
            if (endpoint.startsWith("/mcp")) {
                if (endpoint.endsWith("mcp")) {
                    endpoint = endpoint + "/";
                }
                // 创建Streamable HTTP传输
                transport = HttpClientStreamableHttpTransport
                        .builder(baseUrl)
                        .endpoint(endpoint)
                        .objectMapper(new ObjectMapper())
                        .build();
            } else {
                transport = HttpClientSseClientTransport.builder(baseUrl)
                        .sseEndpoint(endpoint)
                        .objectMapper(new ObjectMapper()).build();
            }
            if (transport != null) {
                McpClient.SyncSpec capabilities = McpClient.sync(transport)
                        .initializationTimeout(Duration.ofSeconds(10))
                        .requestTimeout(Duration.ofSeconds(10))
                        .roots(new ArrayList<>());

                // Create a sync client with custom configuration
                return capabilities.build();
            } else {
                throw new BussinessException(WorkflowErrorCodeConstant.MCP_CLIENT_CREATE_FAILED);
            }
//            URI uri = URI.create(url);
//            String scheme = uri.getScheme();
//
//            // 根据URL协议选择合适的传输方式
//            if ("http".equals(scheme) || "https".equals(scheme)) {
//                // 使用WebFluxSseClientTransport进行SSE连接（推荐用于生产环境）
//                WebClient.Builder webClientBuilder = WebClient.builder()
//                        .baseUrl(url);
//
//                // 如果有自定义headers，添加到WebClient中
//                if (headers != null && !headers.isEmpty()) {
//                    webClientBuilder.defaultHeaders(httpHeaders -> {
//                        headers.forEach(httpHeaders::add);
//                    });
//                }
//
//                WebFluxSseClientTransport transport = new WebFluxSseClientTransport(webClientBuilder);
//
//                // 使用McpClient.async()创建异步客户端
//                return McpClient.async(transport)
//                        .requestTimeout(Duration.ofSeconds(30))
//                        .build();
//            } else if ("stdio".equals(scheme)) {
//                // Stdio传输 - 需要使用ServerParameters
//                // 从URL中解析命令和参数
//                String[] urlParts = url.replace("stdio://", "").split("\\s+");
//                if (urlParts.length == 0) {
//                    throw new BussinessException(WorkflowErrorCodeConstant.STDIO_URL_FORMAT_ERROR);
//                }
//
//                String command = urlParts[0];
//                String[] args = Arrays.copyOfRange(urlParts, 1, urlParts.length);
//
//                ServerParameters params = ServerParameters.builder(command)
//                        .args(args)
//                        .build();
//
//                StdioClientTransport transport = new StdioClientTransport(params);
//
//                // 使用McpClient.async()创建异步客户端
//                return McpClient.async(transport)
//                        .requestTimeout(Duration.ofSeconds(30))
//                        .build();
//            } else {
//                throw new BussinessException(WorkflowErrorCodeConstant.UNSUPPORTED_PROTOCOL, scheme);
//            }
        } catch (Exception e) {
            log.error("创建MCP客户端失败: url={}", url, e);
            throw new BussinessException(WorkflowErrorCodeConstant.MCP_CLIENT_CREATE_FAILED, e.getMessage());
        }
    }


    /**
     * 请求头密钥类型枚举
     * 对应TypeScript版本的HeaderSecretTypeEnum
     */
    @Getter
    public enum HeaderSecretTypeEnum {
        BEARER("Bearer"),
        BASIC("Basic");

        private final String value;

        HeaderSecretTypeEnum(String value) {
            this.value = value;
        }

    }

    /**
     * 准备工具调用参数
     */
    private Map<String, Object> prepareToolParams(Map<String, Object> params) {
        Map<String, Object> toolParams = new HashMap<>(params);

        // 移除工具配置相关的参数
        toolParams.remove(NodeInputKeyEnum.TOOL_DATA.getValue());
        toolParams.remove("system_toolData");

        return toolParams;
    }

    /**
     * 提取工具调用结果
     */
    private Object extractToolResult(McpSchema.CallToolResult result) {
        if (result == null) {
            return null;
        }

        // 如果有内容，返回内容列表
        if (result.content() != null && !result.content().isEmpty()) {
            return result.content();
        }

        // 如果有错误，抛出异常
        if (result.isError()) {
            throw new BussinessException(WorkflowErrorCodeConstant.TOOL_CALL_RETURN_ERROR);
        }

        return result;
    }

    /**
     * 构建成功响应
     * 对应TypeScript版本的成功返回结构
     */
    private Map<String, Object> buildSuccessResponse(String avatar, Object toolResult) {
        Map<String, Object> result = new HashMap<>();

        // 构建节点响应
        DispatchNodeResponseType nodeResponse = DispatchNodeResponseType.builder()
                .toolRes(toolResult)
                .moduleLogo(avatar)
                .build();

        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), nodeResponse);
        result.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), toolResult);
        result.put(NodeOutputKeyEnum.RAW_RESPONSE.getValue(), toolResult);

        return result;
    }

    /**
     * 构建错误响应
     * 对应TypeScript版本的错误返回结构
     */
    private Map<String, Object> buildErrorResponse(ModuleDispatchProps dispatchData, Exception error) {
        String avatar = dispatchData.getNode().getAvatar();
        String errorMessage = getErrorText(error);

        Map<String, Object> result = new HashMap<>();

        // 构建节点响应
        DispatchNodeResponseType nodeResponse = DispatchNodeResponseType.builder()
                .moduleLogo(avatar)
                .error(errorMessage)
                .build();

        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), nodeResponse);
        result.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), errorMessage);

        return result;
    }

    /**
     * 获取错误文本
     * 对应TypeScript版本的getErrText函数
     */
    private String getErrorText(Exception error) {
        if (error == null) {
            return "未知错误";
        }

        String message = error.getMessage();
        if (StringUtils.hasText(message)) {
            return message;
        }

        return error.getClass().getSimpleName();
    }
}
