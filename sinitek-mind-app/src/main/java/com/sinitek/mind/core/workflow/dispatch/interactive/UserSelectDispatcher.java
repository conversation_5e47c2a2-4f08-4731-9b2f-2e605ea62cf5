package com.sinitek.mind.core.workflow.dispatch.interactive;

import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户选择节点
 */
@Component
public class UserSelectDispatcher implements NodeDispatcher {

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.USER_SELECT.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        Map<String, Object> params = dispatchData.getParams();
        List<ChatItemType> histories = dispatchData.getHistories();
        RuntimeNodeItemType node = dispatchData.getNode();
        List<ChatItemValueItemType> query = dispatchData.getQuery();
        WorkflowInteractiveResponseType lastInteractive = dispatchData.getLastInteractive();

        String description = (String) params.get(NodeInputKeyEnum.DESCRIPTION.getValue());
        List<UserSelectOptionItemType> userSelectOptions = (List<UserSelectOptionItemType>) params.get(NodeInputKeyEnum.USER_SELECT_OPTIONS.getValue());

        Boolean isEntry = node.getIsEntry();
        String nodeId = node.getNodeId();

        // Interactive node is not the entry node, return interactive result
        if (!isEntry || !Objects.equals(lastInteractive.getNodeResponse().getType(), "userSelect")) {
            UserSelectInteractive userSelectInteractive = UserSelectInteractive.builder()
                    .params(UserSelectInteractive.Params.builder()
                            .description(description)
                            .userSelectOptions(userSelectOptions)
                            .build())
                    .build();
            return Map.of(DispatchNodeResponseKeyEnum.INTERACTIVE.getValue(), userSelectInteractive);
        }

        node.setIsEntry(false);

        RuntimePromptType runtimePromptType = ChatAdaptor.chatValue2RuntimePrompt(query);
        String userSelectedVal = runtimePromptType.getText();

        if (StringUtils.isBlank(userSelectedVal)) {
            List<String> skipHandleIdList = userSelectOptions.stream()
                    .map(item -> WorkflowUtil.getHandleId(nodeId, "source", item.getValue()))
                    .toList();
            return Map.of(DispatchNodeResponseKeyEnum.SKIP_HANDLE_ID.getValue(), skipHandleIdList);
        }

        Map<String, Object> result = new HashMap<>();

        // Removes the current session record as the history of subsequent nodes
        result.put(DispatchNodeResponseKeyEnum.REWRITE_HISTORIES.getValue(), histories.subList(0, histories.size() - 2));
        List<String> skipHandleIdList = userSelectOptions.stream()
                .filter(item -> !StringUtils.equals(item.getValue(), userSelectedVal))
                .map(item -> WorkflowUtil.getHandleId(nodeId, "source", item.getValue()))
                .toList();
        result.put(DispatchNodeResponseKeyEnum.SKIP_HANDLE_ID.getValue(), skipHandleIdList);
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                .userSelectResult(userSelectedVal)
                .build());
        result.put(NodeOutputKeyEnum.SELECT_RESULT.getValue(), userSelectedVal);

        return result;
    }
}
