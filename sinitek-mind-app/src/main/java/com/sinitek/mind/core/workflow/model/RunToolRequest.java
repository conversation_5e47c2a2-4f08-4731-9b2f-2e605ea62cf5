package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.model.dto.SystemModelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class RunToolRequest extends ModuleDispatchProps {

    private List<ChatCompletionMessageParam> messages;

    private List<ToolNodeItemType> toolNodes;

    private SystemModelDTO toolModel;

    private WorkflowInteractiveResponseType.ToolParams interactiveEntryToolParams;

    private Integer maxRunToolTimes;
}
