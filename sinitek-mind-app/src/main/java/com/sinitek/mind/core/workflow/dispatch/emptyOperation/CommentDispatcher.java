package com.sinitek.mind.core.workflow.dispatch.emptyOperation;

import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import org.springframework.stereotype.Component;

import java.util.Map;

// FlowNodeTypeEnum.comment
@Component
public class CommentDispatcher implements NodeDispatcher {
    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.COMMENT.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return Map.of();
    }
}
