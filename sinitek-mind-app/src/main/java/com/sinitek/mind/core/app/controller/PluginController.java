package com.sinitek.mind.core.app.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.GetVersionListRequest;
import com.sinitek.mind.core.app.enumerate.PluginSourceEnum;
import com.sinitek.mind.core.app.model.NodeTemplateListItem;
import com.sinitek.mind.core.app.model.SplitCombineToolIdRes;
import com.sinitek.mind.core.app.model.VersionListItemType;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.app.service.IPluginService;
import com.sinitek.mind.core.app.util.PluginUtils;
import com.sinitek.mind.core.workflow.model.FlowNodeTemplateType;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 插件控制器
 * 对应原始的getPreviewNode.ts接口
 */
@Slf4j
@RestController
@RequestMapping("/mind/api/core/app/plugin")
@RequiredArgsConstructor
@Tag(name = "插件管理", description = "插件相关接口")
public class PluginController {

    private final IPluginService pluginService;

    private final IAuthService authService;

    private final IAuthAppService authAppService;

    @GetMapping("/getPluginGroups")
    @Operation(summary = "获取插件分组")
    public ApiResponse<?> getPluginGroups() throws JsonProcessingException {
        Map<String, Object> group = new LinkedHashMap<>();       // 保持顺序
        group.put("groupId", "systemPlugin");
        group.put("groupName", "common:core.module.template.System Plugin");
        group.put("groupAvatar", "core/app/type/pluginLight");

        List<Map<String, String>> groupTypes = new LinkedList<>();
        groupTypes.add(Map.of("typeId", "tools", "typeName", "app:tool_type_tools"));
        groupTypes.add(Map.of("typeId", "search", "typeName", "app:tool_type_search"));
        groupTypes.add(Map.of("typeId", "multimodal", "typeName", "app:tool_type_multimodal"));
        groupTypes.add(Map.of("typeId", "productivity", "typeName", "app:tool_type_productivity"));
        groupTypes.add(Map.of("typeId", "scientific", "typeName", "app:tool_type_scientific"));
        groupTypes.add(Map.of("typeId", "finance", "typeName", "app:tool_type_finance"));
        groupTypes.add(Map.of("typeId", "design", "typeName", "app:tool_type_design"));
        groupTypes.add(Map.of("typeId", "news", "typeName", "app:tool_type_news"));
        groupTypes.add(Map.of("typeId", "entertainment", "typeName", "app:tool_type_entertainment"));
        groupTypes.add(Map.of("typeId", "communication", "typeName", "app:tool_type_communication"));
        groupTypes.add(Map.of("typeId", "social", "typeName", "app:tool_type_social"));
        groupTypes.add(Map.of("typeId", "other", "typeName", "common:Other"));

        group.put("groupTypes", groupTypes);
        group.put("groupOrder", 0);
        return ApiResponse.success(List.of(group));
    }

    /**
     * 获取插件预览节点
     *
     * @return ApiResponse<FlowNodeTemplate> 插件预览节点信息
     */
    @GetMapping("/getPreviewNode")
    @Operation(summary = "获取插件预览节点")
    public ApiResponse<FlowNodeTemplateType> getPreviewNodePost(
            @RequestParam @NotNull(message = "应用ID不能为空") String appId,
            @RequestParam(required = false) String versionId) {
        SplitCombineToolIdRes toolIdRes = PluginUtils.splitCombineToolId(appId);
        String source = toolIdRes.getSource();

        if (PluginSourceEnum.PERSONAL.getValue().equals(source)) {
            // 个人插件，需要权限验证
            authAppService.authApp(appId, PermissionConstant.READ_PER);
        }

        // 3. 获取插件预览节点
        FlowNodeTemplateType previewNode = pluginService.getChildAppPreviewNode(
                appId,
                versionId
        );

        return ApiResponse.success(previewNode);
    }

    /**
     * 获取系统插件模板列表 - GET请求
     *
     * @param searchKey 搜索关键字
     * @param parentId  父级ID
     * @return 插件模板列表
     */
    @PostMapping("/getSystemPluginTemplates")
    @Operation(summary = "获取系统插件模板列表", description = "根据搜索条件获取系统插件模板列表")
    public ApiResponse<List<NodeTemplateListItem>> getSystemPluginTemplatesGet(
            @Parameter(description = "搜索关键字") @RequestParam(required = false) String searchKey,
            @Parameter(description = "父级ID") @RequestParam(required = false) String parentId) throws Exception {

        // 认证检查
        authService.authCert();

        List<NodeTemplateListItem> result = pluginService.getSystemPluginTemplates(searchKey, parentId);
        return ApiResponse.success(result);
    }

    /**
     * 获取插件版本列表
     * 对应原始的getVersionList.ts接口
     *
     * @return 版本列表响应
     */
    @PostMapping("/getVersionList")
    @Operation(summary = "获取插件版本列表", description = "根据工具ID获取插件版本列表")
    public ApiResponse<PageResult<VersionListItemType>> getVersionList(@RequestBody GetVersionListRequest requestBody) throws Exception {

        // 用户认证
        AuthDTO authDTO = authService.authCert();

        // 获取参数
        String toolId = requestBody.getToolId();
        int offset = requestBody.getOffset() != null ? requestBody.getOffset() : 0;
        int pageSize = requestBody.getPageSize() != null ? requestBody.getPageSize() : 10;

        // 获取版本列表
        PageResult<VersionListItemType> response = pluginService.getVersionList(toolId, offset, pageSize, authDTO.getUserId());

        return ApiResponse.success(response);
    }

    /**
     * 健康检查接口
     *
     * @return ApiResponse<String> 健康状态
     */
    @GetMapping("/health")
    public ApiResponse<String> health() {
        return ApiResponse.success("Plugin service is healthy");
    }
}