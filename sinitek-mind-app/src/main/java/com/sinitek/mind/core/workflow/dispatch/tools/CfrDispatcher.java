package com.sinitek.mind.core.workflow.dispatch.tools;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.ai.model.ChatCompletionContentPartText;
import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.chat.model.ChatsToGPTMessagesParam;
import com.sinitek.mind.core.chat.util.ChatUtil;
import com.sinitek.mind.core.dataset.model.QueryExtensionResult;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.dispatch.util.DispatchUtil;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import com.sinitek.mind.core.workflow.model.RuntimeNodeItemType;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.model.core.llm.LLMChatModelFactory;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.mind.model.util.TokenCalculatorUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对应ts的 dispatchQueryExtension
 */
@Slf4j
@Component
public class CfrDispatcher implements NodeDispatcher {

    @Autowired
    private ISystemModelService systemModelService;

    @Autowired
    private LLMChatModelFactory llmChatModelFactory;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.QUERY_EXTENSION.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchQueryExtension(dispatchData);
    }

    private Map<String, Object> dispatchQueryExtension(ModuleDispatchProps props) {
        List<ChatItemType> histories = props.getHistories();
        RuntimeNodeItemType node = props.getNode();
        Map<String, Object> params = props.getParams();
        String model = (String) params.get("model");
        String systemPrompt = (String) params.get("systemPrompt");

        Integer history = (Integer) params.get("history");
        String userChatInput = (String) params.get("userChatInput");

        // 验证必要参数
        if (!StringUtils.hasText(userChatInput)) {
            throw new BussinessException(WorkflowErrorCodeConstant.QUESTION_IS_EMPTY);
        }
        try {

            // 获取模型信息
            SystemModelDTO queryExtensionModel = systemModelService.getModelDetail(model);
            if (queryExtensionModel == null) {
                throw new BussinessException(WorkflowErrorCodeConstant.MODEL_NOT_FOUND, model);
            }

            // 获取历史记录
            List<ChatItemType> chatHistories = DispatchUtil.getHistories(history, histories);

            // 调用查询扩展
            QueryExtensionResult result = queryExtension(
                    systemPrompt,
                    userChatInput,
                    chatHistories,
                    queryExtensionModel.getModel()
            );

            // 将原始查询添加到扩展查询列表的开头
            List<String> extensionQueries = new ArrayList<>(result.getExtensionQueries());
            extensionQueries.add(0, userChatInput);

            // 计算总点数（这里简化处理，实际应该根据模型计费规则计算）
            double totalPoints = formatModelChars2Points(queryExtensionModel, result.getInputTokens(), result.getOutputTokens(), "llm");

            // 去重处理
            Set<String> seen = new HashSet<>();
            List<String> filterSameQueries = extensionQueries.stream()
                    .filter(item -> {
                        // 删除所有的标点符号与空格等，只对文本进行比较
                        String normalized = item.replaceAll("[^\\p{L}\\p{N}]", "");
                        String hash = Integer.toString(normalized.hashCode());
                        if (seen.contains(hash)) {
                            return false;
                        }
                        seen.add(hash);
                        return true;
                    })
                    .collect(Collectors.toList());

            DispatchNodeResponseType nodeResponse = DispatchNodeResponseType.builder()
                    .totalPoints(totalPoints)
                    .model(queryExtensionModel.getName())
                    .inputTokens(result.getInputTokens())
                    .outputTokens(result.getOutputTokens())
                    .query(userChatInput)
                    .textOutput(objectMapper.writeValueAsString(filterSameQueries))
                    .build();

            Map<String, Object> nodeDispatchUsage = new HashMap<>();
            nodeDispatchUsage.put("moduleName", node.getName());
            nodeDispatchUsage.put("totalPoints", totalPoints);
            nodeDispatchUsage.put("model", queryExtensionModel.getName());
            nodeDispatchUsage.put("inputTokens", result.getInputTokens());
            nodeDispatchUsage.put("outputTokens", result.getOutputTokens());

            Map<String, Object> response = new HashMap<>();
            response.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), nodeResponse);
            response.put(DispatchNodeResponseKeyEnum.NODE_DISPATCH_USAGES.getValue(), Collections.singletonList(nodeDispatchUsage));
            response.put(NodeOutputKeyEnum.TEXT.getValue(), objectMapper.writeValueAsString(filterSameQueries));

            return response;
        } catch (Exception e) {
            log.error("Query extension failed", e);
            throw new BussinessException(WorkflowErrorCodeConstant.QUERY_EXTENSION_FAILED, e.getMessage());
        }

    }

    // 默认提示词模板
    private static final String DEFAULT_PROMPT_TEMPLATE = "## 你的任务\n" +
            "你作为一个向量检索助手，你的任务是结合历史记录，从不同角度，为\"原问题\"生成个不同版本的\"检索词\"，从而提高向量检索的语义丰富度，提高向量检索的精度。\n" +
            "生成的问题要求指向对象清晰明确，并与\"原问题语言相同\"。\n\n" +
            "## 参考示例\n\n" +
            "历史记录: \n" +
            "\"\"\"\n" +
            "null\n" +
            "\"\"\"\n" +
            "原问题: 介绍下剧情。\n" +
            "检索词: [\"介绍下故事的背景。\",\"故事的主题是什么？\",\"介绍下故事的主要人物。\"]\n" +
            "----------------\n" +
            "历史记录: \n" +
            "\"\"\"\n" +
            "user: 对话背景。\n" +
            "assistant: 当前对话是关于 Nginx 的介绍和使用等。\n" +
            "\"\"\"\n" +
            "原问题: 怎么下载\n" +
            "检索词: [\"Nginx 如何下载？\",\"下载 Nginx 需要什么条件？\",\"有哪些渠道可以下载 Nginx？\"]\n" +
            "----------------\n" +
            "## 输出要求\n\n" +
            "1. 输出格式为 JSON 数组，数组中每个元素为字符串。无需对输出进行任何解释。\n" +
            "2. 输出语言与原问题相同。原问题为中文则输出中文；原问题为英文则输出英文。\n\n" +
            "## 开始任务\n\n" +
            "历史记录:\n" +
            "\"\"\"\n" +
            "{{histories}}\n" +
            "\"\"\"\n" +
            "原问题: {{query}}\n" +
            "检索词: ";

    /**
     * 查询扩展核心方法
     */
    private QueryExtensionResult queryExtension(
            String chatBg,
            String query,
            List<ChatItemType> histories,
            String model
    ) {
        try {
            // 构建系统背景
            String systemFewShot = StringUtils.hasText(chatBg)
                    ? "user: 对话背景。\nassistant: " + chatBg + "\n"
                    : "";

            SystemModelDTO modelData = systemModelService.findModelByModelId(model);

            ChatsToGPTMessagesParam param = new ChatsToGPTMessagesParam();
            param.setReserveId(false);
            param.setMessages(histories);
            List<ChatCompletionMessageParam> messageParamList = ChatAdaptor.chats2GPTMessages(param);
            List<ChatCompletionMessageParam> filterHistories = ChatUtil.filterGPTMessageByMaxContext(messageParamList, modelData.getMaxContext() - 1000);

            // 构建历史对话
            String historyFewShot = buildHistoryFewShot(filterHistories);
            String concatFewShot = (systemFewShot + historyFewShot).trim();

            // 替换模板变量
            Map<String, Object> var = new HashMap<>();
            var.put("query", query);
            var.put("histories", StringUtils.hasText(concatFewShot) ? concatFewShot : "null");
            String prompt = (String) WorkflowUtil.replaceVariable(DEFAULT_PROMPT_TEMPLATE, var);

            // 创建聊天模型
            ChatModel chatModel = llmChatModelFactory.chatModel(model);

            // 构建消息
            Message userMessage = new UserMessage(prompt);

            // 设置聊天选项
            OpenAiChatOptions chatOptions = OpenAiChatOptions.builder()
                    .model(model)
                    .temperature(0.1)
                    .build();

            // 创建提示
            Prompt chatPrompt = new Prompt(Collections.singletonList(userMessage), chatOptions);

            // 调用AI模型
            ChatResponse response = chatModel.call(chatPrompt);
            String answer = response.getResult().getOutput().getText();

            // 计算token使用量（简化处理）
            int inputTokens = TokenCalculatorUtil.estimateTokens(prompt);
            int outputTokens = TokenCalculatorUtil.estimateTokens(answer);

            if (!StringUtils.hasText(answer)) {
                return new QueryExtensionResult(
                        model,
                        inputTokens,
                        outputTokens,
                        "",
                        query,
                        Collections.emptyList()
                );
            }

            // 解析JSON响应
            List<String> extensionQueries = parseJsonResponse(answer);

            return new QueryExtensionResult(
                    model,
                    inputTokens,
                    outputTokens,
                    "",
                    query,
                    extensionQueries
            );

        } catch (Exception e) {
            log.error("Query extension AI call failed", e);
            throw new BussinessException(WorkflowErrorCodeConstant.QUERY_EXTENSION_AI_CALL_FAILED, e.getMessage());
        }
    }

    /**
     * 构建历史对话字符串
     */
    private String buildHistoryFewShot(List<ChatCompletionMessageParam> histories) {
        if (CollectionUtils.isEmpty(histories)) {
            return "";
        }

        return histories.stream()
                .map(item -> {
                    String role = item.getRole();
                    Object content = item.getContent();

                    if (("user".equals(role) || "assistant".equals(role))  && content != null) {
                        if (content instanceof String) {
                            return role + ": " + content;
                        } else if (content instanceof List) {
                            @SuppressWarnings("unchecked")
                            List<ChatCompletionContentPartText> contentParts = (List<ChatCompletionContentPartText>) content;

                            String textContent = contentParts.stream()
                                    .filter(part -> "text".equals(part.getType()))
                                    .map(part -> part.getText() != null ? part.getText() : "")
                                    .collect(Collectors.joining("\n"));

                            return role + ": " + textContent;
                        }
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.joining("\n"));
    }

    /**
     * 格式化聊天项
     */
    private String formatChatItem(ChatItemType item) {
        if (item == null || item.getObj() == null) {
            return null;
        }

        String role = item.getObj().name().toLowerCase();
        if (!"user".equals(role) && !"assistant".equals(role)) {
            return null;
        }

        String content = extractTextContent(item.getValue());
        if (!StringUtils.hasText(content)) {
            return null;
        }

        return role + ": " + content;
    }

    /**
     * 提取文本内容
     */
    private String extractTextContent(List<ChatItemValueItemType> values) {
        if (CollectionUtils.isEmpty(values)) {
            return "";
        }

        return values.stream()
                .filter(value -> "text".equals(value.getType()))
                .map(value -> {
                    if (value.getText() != null) {
                        return value.getText().getContent();
                    }
                    return "";
                })
                .filter(StringUtils::hasText)
                .collect(Collectors.joining("\n"));
    }


    /**
     * 解析JSON响应
     */
    private List<String> parseJsonResponse(String answer) {
        try {
            int start = answer.indexOf('[');
            int end = answer.lastIndexOf(']');

            if (start == -1 || end == -1) {
                log.warn("Query extension failed, not a valid JSON: {}", answer);
                return Collections.emptyList();
            }

            // 截取JSON部分并清理
            String jsonStr = answer.substring(start, end + 1)
                    .replaceAll("(\\\\n|\\\\)", "")
                    .replaceAll("  ", "");

            // 解析JSON数组
            List<String> queries = objectMapper.readValue(jsonStr, new TypeReference<List<String>>() {
            });

            // 限制返回数量
            return queries != null ? queries.stream().limit(5).collect(Collectors.toList()) : Collections.emptyList();

        } catch (Exception e) {
            log.warn("Query extension failed, JSON parse error: {}", answer, e);
            return Collections.emptyList();
        }
    }

    /**
     * 计算点数（简化实现）
     */
    private double formatModelChars2Points(SystemModelDTO model, int inputTokens, int outputTokens, String modelType) {
        // 这里应该根据实际的模型计费规则计算
        // 简化处理：假设每1000个token消耗1个点数
        Double inputPrice = model.getInputPrice();
        boolean isIOPriceType = inputPrice > 0;

        double v = inputPrice * ((double) inputTokens / 1000) + model.getOutputPrice() * ((double) outputTokens / 1000);
        double v1 = model.getCharsPointsPrice() * ((double) (inputTokens + outputTokens) / 1000);

        return isIOPriceType ? v : v1;
    }
}
