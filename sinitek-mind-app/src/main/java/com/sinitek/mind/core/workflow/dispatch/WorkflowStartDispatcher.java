package com.sinitek.mind.core.workflow.dispatch;

import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.model.ChatItemValueItemFileInfo;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import com.sinitek.mind.core.workflow.model.RuntimePromptType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class WorkflowStartDispatcher implements NodeDispatcher {
    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.WORKFLOW_START.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        // 获取查询数据（对应 TypeScript 中的 query）
        List<ChatItemValueItemType> query = dispatchData.getQuery();

        // 获取变量（对应 TypeScript 中的 variables）
        Map<String, Object> variables = dispatchData.getVariables();

        // 获取用户聊天输入参数（对应 TypeScript 中的 params.userChatInput）
        Object userChatInputParam = dispatchData.getParams().get(NodeInputKeyEnum.USER_CHAT_INPUT.getValue());
        String userChatInputStr = userChatInputParam != null ? userChatInputParam.toString() : "";

        // 将查询值转换为运行时提示（对应 TypeScript 中的 chatValue2RuntimePrompt(query)）
        RuntimePromptType runtimePrompt = ChatAdaptor.chatValue2RuntimePrompt(query);
        String text = runtimePrompt.getText();
        List<ChatItemValueItemFileInfo> files = runtimePrompt.getFiles();

        // 提取查询文件URL列表（对应 TypeScript 中的 queryFiles）
        List<String> queryFiles = files.stream()
                .map(ChatItemValueItemFileInfo::getUrl)
                .filter(StringUtils::hasText)
                .toList();

        // 提取变量文件URL列表（对应 TypeScript 中的 variablesFiles）
        List<String> variablesFiles = new ArrayList<>();
        if (variables != null && variables.get("fileUrlList") instanceof List<?>) {
            try {
                @SuppressWarnings("unchecked")
                List<String> fileUrlList = (List<String>) variables.get("fileUrlList");
                variablesFiles = fileUrlList.stream()
                        .filter(StringUtils::hasText)
                        .toList();
            } catch (ClassCastException e) {
                // 如果类型转换失败，使用空列表
                variablesFiles = new ArrayList<>();
            }
        }

        // 合并所有文件URL
        List<String> allFiles = new ArrayList<>();
        allFiles.addAll(queryFiles);
        allFiles.addAll(variablesFiles);

        // 构建返回结果，对应 TypeScript 中的返回格式
        Map<String, Object> result = new HashMap<>();

        // 设置节点响应（对应 TypeScript 中的 DispatchNodeResponseKeyEnum.nodeResponse）
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder().build());

        // 设置用户聊天输入（优先使用 text，如果为空则使用 userChatInput 参数）
        String finalUserChatInput = StringUtils.hasText(text) ? text : userChatInputStr;
        result.put(NodeInputKeyEnum.USER_CHAT_INPUT.getValue(), finalUserChatInput);

        // 设置用户文件列表（对应 TypeScript 中的 NodeOutputKeyEnum.userFiles）
        result.put(NodeOutputKeyEnum.USER_FILES.getValue(), allFiles);
        result.put(NodeOutputKeyEnum.USER_FILES_STREAM.getValue(), allFiles);

        return result;
    }

}
