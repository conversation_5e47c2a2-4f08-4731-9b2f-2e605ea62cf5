package com.sinitek.mind.core.app.enumerate;

import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Getter;

@Getter
public enum FlowNodeTemplateTypeEnum {

    SYSTEM_INPUT("systemInput"),
    AI("ai"),
    FUNCTION("function"),
    TOOLS("tools"),
    INTERACTIVE("interactive"),

    SEARCH("search"),
    MULTI_MODAL("multiModal"),
    COMMUNICATION("communication"),
    OTHER("other"),
    TEAM_APP("teamApp");

    private final String value;

    FlowNodeTemplateTypeEnum(String value) {
        this.value = value;
    }

    // 根据字符串值查找枚举
    public static FlowNodeTemplateTypeEnum fromValue(String value) {
        for (FlowNodeTemplateTypeEnum type : FlowNodeTemplateTypeEnum.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new BussinessException(AppErrorCodeConstant.UNKNOWN_FLOW_NODE_TYPE, value);
    }
}
