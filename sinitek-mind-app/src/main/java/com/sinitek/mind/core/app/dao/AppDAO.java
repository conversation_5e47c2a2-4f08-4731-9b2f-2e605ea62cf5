package com.sinitek.mind.core.app.dao;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.mind.core.app.dto.AppPageParamDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.mapper.AppMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
public class AppDAO extends ServiceImpl<AppMapper, App> {

    public List<App> pageByParamDTO(AppPageParamDTO param) {
        return baseMapper.pageByParamDTO(param);
    }

    /**
     * 根据父级ID查找所有子应用
     */
    public List<App> findAllChildrenByParentId(Long parentId) {
        if (Objects.isNull(parentId)) {
            return List.of();
        }

        LambdaQueryWrapper<App> queryWrapper = Wrappers.lambdaQuery(App.class);
        queryWrapper.eq(App::getParentId, parentId);
        List<App> subAppList = list(queryWrapper);

        if (CollUtil.isEmpty(subAppList)) {
            return List.of();
        }

        List<App> allApp = new LinkedList<>(subAppList);
        List<App> currentLevelApps = new LinkedList<>(subAppList);
        
        while (!currentLevelApps.isEmpty()) {
            List<Long> parentIds = currentLevelApps.stream()
                    .map(App::getId)
                    .filter(Objects::nonNull)
                    .toList();
            
            if (CollUtil.isEmpty(parentIds)) {
                break;
            }

            LambdaQueryWrapper<App> childQueryWrapper = Wrappers.lambdaQuery(App.class);
            childQueryWrapper.in(App::getParentId, parentIds);
            List<App> childApps = list(childQueryWrapper);

            if (CollUtil.isEmpty(childApps)) {
                break;
            }

            allApp.addAll(childApps);
            currentLevelApps = childApps;
        }
        return allApp;
    }

    /**
     * 根据团队ID和类型查找应用
     */
    public List<App> findByType(String type) {
        if (StringUtils.isBlank(type)) {
            return List.of();
        }

        LambdaQueryWrapper<App> queryWrapper = Wrappers.lambdaQuery(App.class);
        queryWrapper.eq(App::getType, type);
        return list(queryWrapper);
    }

    /**
     * 根据团队ID和多个ID查找应用
     */
    public List<App> findByIdIn(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return List.of();
        }

        LambdaQueryWrapper<App> queryWrapper = Wrappers.lambdaQuery(App.class);
        queryWrapper.in(App::getId, ids);
        return list(queryWrapper);
    }

    public void updateAvatarByParentIdAndTeamId(String avatar, Long parentId) {
        if (parentId == null) {
            return;
        }

        LambdaUpdateWrapper<App> updateWrapper = Wrappers.lambdaUpdate(App.class);
        updateWrapper.eq(App::getParentId, parentId);
        updateWrapper.set(App::getAvatar, avatar);
        update(updateWrapper);
    }

    /**
     * 根据应用ID更新orgId
     */
    public void updateOrgIdById(Long appId, String orgId) {
        if (appId == null || StringUtils.isBlank(orgId)) {
            return;
        }

        LambdaUpdateWrapper<App> updateWrapper = Wrappers.lambdaUpdate(App.class);
        updateWrapper.eq(App::getId, appId);
        updateWrapper.set(App::getOrgId, orgId);
        update(updateWrapper);
    }

    public List<App> findByTypeInAndInheritPermissionTrue(List<String> types) {
        if (CollUtil.isEmpty(types)) {
            return List.of();
        }

        LambdaQueryWrapper<App> queryWrapper = Wrappers.lambdaQuery(App.class);
        queryWrapper.in(App::getType, types);
        return list(queryWrapper);
    }

    public void deleteByNamespaceIn(List<String> namespaceList) {
        if (CollUtil.isEmpty(namespaceList)) {
            return;
        }

        LambdaQueryWrapper<App> queryWrapper = Wrappers.lambdaQuery(App.class);
        queryWrapper.in(App::getNamespace, namespaceList);
        remove(queryWrapper);
    }

    public void updateInheritPermissionTrueById(Long appId) {
        if (Objects.isNull(appId)) {
            return;
        }

        LambdaUpdateWrapper<App> updateWrapper = Wrappers.lambdaUpdate(App.class);
        updateWrapper.eq(App::getId, appId);
        updateWrapper.set(App::getInheritPermission, true);
        update(updateWrapper);
    }

    public void updateTypeById(String type, Long appId) {
        if (Objects.isNull(appId)) {
            return;
        }

        LambdaUpdateWrapper<App> updateWrapper = Wrappers.lambdaUpdate(App.class);
        updateWrapper.eq(App::getId, appId);
        updateWrapper.set(App::getType, type);
        update(updateWrapper);
    }

    public List<App> listByParentIdAndPluginDataNotNull(Long parentId) {
        LambdaQueryWrapper<App> queryWrapper = Wrappers.lambdaQuery(App.class);
        queryWrapper.eq(App::getParentId, parentId);
        queryWrapper.isNotNull(App::getPluginData);
        return list(queryWrapper);
    }
}
