package com.sinitek.mind.core.app.constant;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.app.model.AppAutoExecuteConfigType;
import com.sinitek.mind.core.app.model.AppQGConfigType;
import com.sinitek.mind.core.app.model.AppTTSConfigType;
import com.sinitek.mind.core.app.model.AppWhisperConfigType;
import com.sinitek.mind.core.chat.model.ChatInputGuideConfigType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;

import java.util.List;

public class AppConstant {

    public static final AppTTSConfigType DEFAULT_TTS_CONFIG;

    public static final AppQGConfigType DEFAULT_QG_CONFIG;

    public static final AppWhisperConfigType DEFAULT_WHISPER_CONFIG;

    public static final ChatInputGuideConfigType DEFAULT_CHAT_INPUT_GUIDE_CONFIG;

    public static final AppAutoExecuteConfigType DEFAULT_AUTO_EXECUTE_CONFIG;

    /**
     * 工作流默认配置，基于mind前端创建工作流时发送的数据
     */
    public static final List<StoreNodeItemType> WORKFLOW_DEFAULT_MODULES;

    static {
        String WORKFLOW_DEFAULT_MODULES_JSON = """
            [
              {
                "nodeId": "userGuide",
                "name": "common:core.module.template.system_config",
                "intro": "common:core.module.template.system_config_info",
                "avatar": "core/workflow/template/systemConfig",
                "flowNodeType": "userGuide",
                "position": { "x": 262.27, "y": -476.00 },
                "version": "481",
                "inputs": [
                  { "key": "welcomeText", "renderTypeList": ["hidden"], "valueType": "string", "label": "core.app.Welcome Text", "value": "" },
                  { "key": "variables", "renderTypeList": ["hidden"], "valueType": "any", "label": "core.app.Chat Variable", "value": [] },
                  { "key": "questionGuide", "valueType": "any", "renderTypeList": ["hidden"], "label": "core.app.Question Guide", "value": { "open": false } },
                  { "key": "tts", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": { "type": "web" } },
                  { "key": "whisper", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": { "open": false, "autoSend": false, "autoTTSResponse": false } },
                  { "key": "scheduleTrigger", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": null }
                ],
                "outputs": []
              },
              {
                "nodeId": "448745",
                "name": "common:core.module.template.work_start",
                "intro": "",
                "avatar": "core/workflow/template/workflowStart",
                "flowNodeType": "workflowStart",
                "position": { "x": 632.37, "y": -347.74 },
                "version": "481",
                "inputs": [
                  { "key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "common:core.module.input.label.user question", "required": true, "toolDescription": "common:core.module.input.label.user question" }
                ],
                "outputs": [
                  { "id": "userChatInput", "key": "userChatInput", "label": "common:core.module.input.label.user question", "type": "static", "valueType": "string" }
                ]
              }
            ]
            """;

        ObjectMapper mapper = new ObjectMapper();
        try {
            WORKFLOW_DEFAULT_MODULES = mapper.readValue(WORKFLOW_DEFAULT_MODULES_JSON, new TypeReference<List<StoreNodeItemType>>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        DEFAULT_TTS_CONFIG = new AppTTSConfigType();
        DEFAULT_TTS_CONFIG.setType(AppTTSConfigType.Type.web);

        DEFAULT_QG_CONFIG = new AppQGConfigType();
        // 设置为空，当没有模型时，就报错
        DEFAULT_QG_CONFIG.setModel("");
        DEFAULT_QG_CONFIG.setOpen(false);
        DEFAULT_QG_CONFIG.setCustomPrompt("");

        DEFAULT_WHISPER_CONFIG = new AppWhisperConfigType();
        DEFAULT_WHISPER_CONFIG.setOpen(false);
        DEFAULT_WHISPER_CONFIG.setAutoSend(false);
        DEFAULT_WHISPER_CONFIG.setAutoTTSResponse(false);

        DEFAULT_CHAT_INPUT_GUIDE_CONFIG = new ChatInputGuideConfigType();
        DEFAULT_CHAT_INPUT_GUIDE_CONFIG.setOpen(false);
        DEFAULT_CHAT_INPUT_GUIDE_CONFIG.setCustomUrl("");

        DEFAULT_AUTO_EXECUTE_CONFIG = new AppAutoExecuteConfigType();
        DEFAULT_AUTO_EXECUTE_CONFIG.setOpen(false);
        DEFAULT_AUTO_EXECUTE_CONFIG.setDefaultPrompt("");
    }

}
