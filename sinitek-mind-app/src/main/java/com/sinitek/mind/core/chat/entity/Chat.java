package com.sinitek.mind.core.chat.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.sinitek.data.mybatis.base.BaseEntity;
import com.sinitek.mind.common.typehandler.FlowNodeInputItemTypeListTypeHandler;
import com.sinitek.mind.common.typehandler.VariableItemTypeListTypeHandler;
import com.sinitek.mind.core.app.model.VariableItemType;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "mind_chat", autoResultMap = true)
public class Chat extends BaseEntity {

    @Schema(description = "聊天Id")
    private String chatId;

    @Schema(description = "用户Id")
    private String orgId;

    @Schema(description = "应用id")
    private Long appId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "自定义标题")
    private String customTitle;

    @Schema(description = "是否置顶")
    private Boolean top;

    @Schema(description = "来源-ChatSourceEnum")
    private String source;

    @Schema(description = "来源名称")
    private String sourceName;

    @Schema(description = "分享链接id")
    private String shareId;

    @Schema(description = "分享链接用户id")
    private String outLinkUid;

    @Schema(description = "变量列表")
    @TableField(typeHandler = VariableItemTypeListTypeHandler.class)
    private List<VariableItemType> variableList;

    @Schema(description = "欢迎语")
    private String welcomeText;

    @Schema(description = "变量")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> variables;

    @Schema(description = "插件输入")
    @TableField(typeHandler = FlowNodeInputItemTypeListTypeHandler.class)
    private List<FlowNodeInputItemType> pluginInputs;

    @Schema(description = "元数据")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> metadata;
}
