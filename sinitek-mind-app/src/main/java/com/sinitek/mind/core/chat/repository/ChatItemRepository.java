package com.sinitek.mind.core.chat.repository;

import com.sinitek.mind.core.chat.entity.ChatItem;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface ChatItemRepository extends MongoRepository<ChatItem, String> {
    void deleteByAppId(String id);

    /**
     * 根据聊天ID和应用ID查询聊天项
     * @param chatId 聊天ID
     * @param appId 应用ID
     * @return 聊天项列表
     */
    List<ChatItem> findByChatIdAndAppId(String chatId, String appId);


    /**
     * 分页查询聊天项
     * @param chatId 聊天ID
     * @param appId 应用ID
     * @param pageable 分页参数
     * @return 分页聊天项
     */
    Page<ChatItem> findByChatIdAndAppIdOrderByTimeDesc(String chatId, String appId, Pageable pageable);

    /**
     * 统计聊天项数量
     * @param chatId 聊天ID
     * @param appId 应用ID
     * @return 聊天项数量
     */
    long countByChatIdAndAppId(String chatId, String appId);

    List<ChatItem> Obj(String obj);

    void deleteByAppIdAndChatId(String appId, String chatId);
}
