package com.sinitek.mind.core.workflow.dispatch.dataset;

import com.sinitek.mind.core.dataset.model.SearchDataResponseItemType;
import com.sinitek.mind.core.dataset.model.SearchResultItem;
import com.sinitek.mind.core.dataset.util.DatasetUtil;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class DatasetConcatNodeDispatcher implements NodeDispatcher {

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.DATASET_CONCAT_NODE.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchDatasetConcat(dispatchData);
    }

    private Map<String, Object> dispatchDatasetConcat(ModuleDispatchProps props) {

        Map<String, Object> params = props.getParams();

        // 获取limit参数，默认为5000
        Integer limit = (Integer) params.getOrDefault("limit", 5000);

        // 提取所有搜索结果列表（排除limit参数）
        List<SearchResultItem> searchResults = new ArrayList<>();

        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 跳过limit参数
            if ("limit".equals(key)) {
                continue;
            }

            // 检查是否为SearchDataResponseItemType列表
            if (value instanceof List<?> list) {
                if (!list.isEmpty() && list.get(0) instanceof SearchDataResponseItemType) {
                    @SuppressWarnings("unchecked")
                    List<SearchDataResponseItemType> searchDataList = (List<SearchDataResponseItemType>) list;

                    // 创建SearchResultItem，k值设为60（与TypeScript保持一致）
                    SearchResultItem searchResultItem = SearchResultItem.builder()
                            .k(60)
                            .list(searchDataList)
                            .build();

                    searchResults.add(searchResultItem);
                }
            }
        }

        // 使用RRF算法合并搜索结果
        List<SearchDataResponseItemType> rrfConcatResults = DatasetUtil.datasetSearchResultConcat(searchResults);

        // 根据最大token数过滤结果
        List<SearchDataResponseItemType> filteredResults = WorkflowUtil.filterSearchResultsByMaxChars(
                rrfConcatResults, limit);
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put(NodeOutputKeyEnum.DATASET_QUOTE_QA.getValue(), filteredResults);

        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                .concatLength(rrfConcatResults.size())
                .build());

        return result;
    }
}
