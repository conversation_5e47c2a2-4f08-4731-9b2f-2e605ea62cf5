package com.sinitek.mind.core.chat.adapter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.ai.model.*;
import com.sinitek.mind.core.chat.enumerate.ChatCompletionRequestMessageRoleEnum;
import com.sinitek.mind.core.chat.enumerate.ChatFileType;
import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.model.*;
import com.sinitek.mind.core.workflow.model.RuntimePromptType;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ChatAdaptor {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将 GPT 格式的消息转换为 FastGPT 内部聊天格式
     *
     * @param messages GPT 格式的消息列表
     * @param reserveTool 是否保留工具调用信息
     * @return FastGPT 内部格式的聊天消息列表
     */
    public static List<ChatItemType> gptMessages2Chats(
            List<ChatCompletionMessageParam> messages,
            boolean reserveTool) {

        List<ChatItemType> chatMessages = messages.stream()
                .map(item -> convertMessage(item, messages, reserveTool))
                .filter(item -> item != null && !item.getValue().isEmpty())
                .toList();

        // 合并具有相同 dataId 和角色的连续消息
        return mergeConsecutiveMessages(chatMessages);
    }

    /**
     * 转换单个消息
     */
    private static ChatItemType convertMessage(
            ChatCompletionMessageParam item,
            List<ChatCompletionMessageParam> allMessages,
            boolean reserveTool) {

        ChatRoleEnum role = mapRole(item.getRole());
        if (role == null) {
            return null;
        }

        List<ChatItemValueItemType> values = switch (role) {
            case SYSTEM -> processSystemMessage(item);
            case HUMAN -> processUserMessage(item);
            case AI -> processAssistantMessage(item, allMessages, reserveTool);
        };

        if (values.isEmpty()) {
            return null;
        }

        ChatItemType chatItem = new ChatItemType();
        chatItem.setDataId(item.getDataId());
        chatItem.setObj(role);
        chatItem.setHideInUI(item.getHideInUI());
        chatItem.setValue(values);

        return chatItem;
    }

    /**
     * 获取系统提示词语
     */
    public static List<ChatItemType> getSystemPrompt_ChatItemType(String prompt) {
        List<ChatItemType> result = new ArrayList<>();
        if (StringUtils.hasText(prompt)) {
            ChatItemValueItemType itemType = new ChatItemValueItemType();
            itemType.setType(ChatItemValueType.TEXT.getValue());
            itemType.setText(new ChatItemValueItemTextInfo(prompt));
            ChatItemType systemMessage = new ChatItemType();
            systemMessage.setObj(ChatRoleEnum.SYSTEM);
            systemMessage.setValue(List.of(itemType));
            result.add(systemMessage);
        }
        return result;
    }

    /**
     * 处理系统消息
     */
    public static List<ChatItemValueItemType> processSystemMessage(ChatCompletionMessageParam item) {
        List<ChatItemValueItemType> values = new ArrayList<>();

        Object content = item.getContent();
        if (content instanceof String) {
            ChatItemValueItemType textValue = new ChatItemValueItemType();
            textValue.setType(ChatItemValueType.TEXT.getValue());
            textValue.setText(new ChatItemValueItemTextInfo((String) content));
            values.add(textValue);
        } else if (content instanceof List) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> contentList = (List<Map<String, Object>>) content;
            for (Map<String, Object> contentItem : contentList) {
                String text = (String) contentItem.get("text");
                if (text != null) {
                    ChatItemValueItemType textValue = new ChatItemValueItemType();
                    textValue.setType(ChatItemValueType.TEXT.getValue());
                    textValue.setText(new ChatItemValueItemTextInfo(text));
                    values.add(textValue);
                }
            }
        }

        return values;
    }

    /**
     * 处理用户消息
     */
    private static List<ChatItemValueItemType> processUserMessage(ChatCompletionMessageParam item) {
        List<ChatItemValueItemType> values = new ArrayList<>();

        Object content = item.getContent();
        if (content instanceof String) {
            ChatItemValueItemType textValue = new ChatItemValueItemType();
            textValue.setType(ChatItemValueType.TEXT.getValue());
            textValue.setText(new ChatItemValueItemTextInfo((String) content));
            values.add(textValue);
        } else if (content instanceof List) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> contentList = (List<Map<String, Object>>) content;

            for (Map<String, Object> contentItem : contentList) {
                String type = (String) contentItem.get("type");

                if ("text".equals(type)) {
                    String text = (String) contentItem.get("text");
                    ChatItemValueItemType textValue = new ChatItemValueItemType();
                    textValue.setType(ChatItemValueType.TEXT.getValue());
                    textValue.setText(new ChatItemValueItemTextInfo(text));
                    values.add(textValue);

                } else if ("image_url".equals(type)) {
                    @SuppressWarnings("unchecked")
                    Map<String, String> imageUrl = (Map<String, String>) contentItem.get("image_url");
                    String url = imageUrl.get("url");

                    ChatItemValueItemType fileValue = new ChatItemValueItemType();
                    fileValue.setType(ChatItemValueType.FILE.getValue());
                    fileValue.setFile(new ChatItemValueItemFileInfo(ChatFileType.IMAGE.getValue(), "", url));
                    values.add(fileValue);

                } else if ("file_url".equals(type)) {
                    String name = (String) contentItem.get("name");
                    String url = (String) contentItem.get("url");

                    ChatItemValueItemType fileValue = new ChatItemValueItemType();
                    fileValue.setType(ChatItemValueType.FILE.getValue());
                    fileValue.setFile(new ChatItemValueItemFileInfo(ChatFileType.FILE.getValue(), name, url));
                    values.add(fileValue);
                }
            }
        }

        return values;
    }

    /**
     * 处理助手消息
     */
    private static List<ChatItemValueItemType> processAssistantMessage(
            ChatCompletionMessageParam item,
            List<ChatCompletionMessageParam> allMessages,
            boolean reserveTool) {

        List<ChatItemValueItemType> values = new ArrayList<>();

        // 处理推理文本
        if (item.getReasoningText() != null && !item.getReasoningText().isEmpty()) {
            ChatItemValueItemType reasoningValue = new ChatItemValueItemType();
            reasoningValue.setType(ChatItemValueType.REASONING.getValue());
            reasoningValue.setReasoning(new ChatItemValueItemReasoningInfo(item.getReasoningText()));
            values.add(reasoningValue);
        }

        // 处理工具调用
        if (item.getToolCalls() != null && reserveTool) {
            List<ToolModuleResponseItemType> tools = new ArrayList<>();

            for (ChatCompletionMessageToolCall toolCall : item.getToolCalls()) {
                String toolResponse = findToolResponse(allMessages, toolCall.getId());

                ToolModuleResponseItemType toolInfo = new ToolModuleResponseItemType();
                toolInfo.setId(toolCall.getId());
                toolInfo.setToolName(toolCall.getToolName() != null ? toolCall.getToolName() : "");
                toolInfo.setToolAvatar(toolCall.getToolAvatar() != null ? toolCall.getToolAvatar() : "");
                toolInfo.setFunctionName(toolCall.getFunction().getName());
                toolInfo.setParams(toolCall.getFunction().getArguments());
                toolInfo.setResponse(toolResponse);

                tools.add(toolInfo);
            }

            if (!tools.isEmpty()) {
                ChatItemValueItemType toolValue = new ChatItemValueItemType();
                toolValue.setType(ChatItemValueType.TOOL.getValue());
                toolValue.setTools(tools);
                values.add(toolValue);
            }
        }

        // 处理函数调用（旧版本兼容）
        if (item.getFunctionCall() != null && reserveTool) {
            ChatCompletionMessageFunctionCall functionCall = item.getFunctionCall();
            String functionResponse = findFunctionResponse(allMessages, functionCall.getName());

            if (functionResponse != null) {
                ToolModuleResponseItemType toolInfo = new ToolModuleResponseItemType();
                toolInfo.setId(functionCall.getId() != null ? functionCall.getId() : "");
                toolInfo.setToolName(functionCall.getToolName() != null ? functionCall.getToolName() : "");
                toolInfo.setToolAvatar(functionCall.getToolAvatar() != null ? functionCall.getToolAvatar() : "");
                toolInfo.setFunctionName(functionCall.getName());
                toolInfo.setParams(functionCall.getArguments());
                toolInfo.setResponse(functionResponse);

                ChatItemValueItemType toolValue = new ChatItemValueItemType();
                toolValue.setType(ChatItemValueType.TOOL.getValue());
                toolValue.setTools(List.of(toolInfo));
                values.add(toolValue);
            }
        }

        // 处理交互响应
        if (item.getInteractive() != null) {
            ChatItemValueItemType interactiveValue = new ChatItemValueItemType();
            interactiveValue.setType(ChatItemValueType.INTERACTIVE.getValue());
            interactiveValue.setInteractive(item.getInteractive());
            values.add(interactiveValue);
        }

        // 处理文本内容
        if (item.getContent() instanceof String content && !((String) item.getContent()).isEmpty()) {

            // 如果最后一个值是文本类型，则追加内容
            if (!values.isEmpty()) {
                ChatItemValueItemType lastValue = values.get(values.size() - 1);
                if (Objects.equals(lastValue.getType(), ChatItemValueType.TEXT.getValue()) && lastValue.getText() != null) {
                    lastValue.getText().setContent(lastValue.getText().getContent() + content);
                    return values;
                }
            }

            // 否则创建新的文本值
            ChatItemValueItemType textValue = new ChatItemValueItemType();
            textValue.setType(ChatItemValueType.TEXT.getValue());
            textValue.setText(new ChatItemValueItemTextInfo (content));
            values.add(textValue);
        }

        return values;
    }

    /**
     * 查找工具响应
     */
    private static String findToolResponse(List<ChatCompletionMessageParam> messages, String toolCallId) {
        return messages.stream()
                .filter(msg -> "tool".equals(msg.getRole()) && toolCallId.equals(msg.getToolCallId()))
                .map(msg -> {
                    Object content = msg.getContent();
                    if (content instanceof String) {
                        return (String) content;
                    } else {
                        try {
                            return objectMapper.writeValueAsString(content);
                        } catch (Exception e) {
                            return "";
                        }
                    }
                })
                .findFirst()
                .orElse("");
    }

    /**
     * 查找函数响应
     */
    private static String findFunctionResponse(List<ChatCompletionMessageParam> messages, String functionName) {
        return messages.stream()
                .filter(msg -> "function".equals(msg.getRole()) && functionName.equals(msg.getName()))
                .map(msg -> {
                    Object content = msg.getContent();
                    return content instanceof String ? (String) content : "";
                })
                .findFirst()
                .orElse(null);
    }

    /**
     * 合并具有相同 dataId 和角色的连续消息
     */
    private static List<ChatItemType> mergeConsecutiveMessages(List<ChatItemType> chatMessages) {
        List<ChatItemType> result = new ArrayList<>();

        for (ChatItemType currentItem : chatMessages) {
            if (!result.isEmpty()) {
                ChatItemType lastItem = result.get(result.size() - 1);

                if (Objects.equals(lastItem.getDataId(), currentItem.getDataId()) &&
                        lastItem.getObj() == currentItem.getObj()) {
                    // 合并值
                    lastItem.getValue().addAll(currentItem.getValue());
                    continue;
                }
            }

            result.add(currentItem);
        }

        return result;
    }

    /**
     * 将聊天值转换为运行时提示
     * 对应 TypeScript 中的 chatValue2RuntimePrompt 函数
     *
     * @param chatValue 聊天值列表
     * @return 运行时提示
     */
    public static RuntimePromptType chatValue2RuntimePrompt(List<ChatItemValueItemType> chatValue) {
        RuntimePromptType prompt = RuntimePromptType.builder()
                .files(new ArrayList<>())
                .text("")
                .build();

        if (chatValue == null || chatValue.isEmpty()) {
            return prompt;
        }

        StringBuilder textBuilder = new StringBuilder();

        for (ChatItemValueItemType item : chatValue) {
            if (item == null || item.getType() == null) {
                continue;
            }

            if (ChatItemValueType.FILE.getValue().equals(item.getType()) && item.getFile() != null) {
                prompt.getFiles().add(item.getFile());
            } else if (item.getText() != null && item.getText().getContent() != null) {
                textBuilder.append(item.getText().getContent());
            }
        }

        prompt.setText(textBuilder.toString());
        return prompt;
    }

    /**
     * 从聊天项中提取文本内容
     *
     * @param chatItem 聊天项
     * @return 文本内容，如果没有文本则返回空字符串
     */
    public static String extractTextFromChatItem(ChatItemType chatItem) {
        if (chatItem == null || chatItem.getValue() == null) {
            return "";
        }

        StringBuilder textBuilder = new StringBuilder();
        for (ChatItemValueItemType item : chatItem.getValue()) {
            if (ChatItemValueType.TEXT.getValue().equals(item.getType()) &&
                    item.getText() != null &&
                    item.getText().getContent() != null) {
                textBuilder.append(item.getText().getContent());
            }
        }

        return textBuilder.toString();
    }

    /**
     * 检查聊天项是否包含指定类型的值
     *
     * @param chatItem 聊天项
     * @param valueType 值类型
     * @return 是否包含指定类型
     */
    public static boolean containsValueType(ChatItemType chatItem, String valueType) {
        if (chatItem == null || chatItem.getValue() == null) {
            return false;
        }

        return chatItem.getValue().stream()
                .anyMatch(item -> valueType.equals(item.getType()));
    }

    /**
     * 角色映射
     */
    private static ChatRoleEnum mapRole(String role) {
        return switch (role) {
            case "system" -> ChatRoleEnum.SYSTEM;
            case "user" -> ChatRoleEnum.HUMAN;
            case "assistant" -> ChatRoleEnum.AI;
            case "tool" -> ChatRoleEnum.AI;
            case "function" -> ChatRoleEnum.AI;
            default -> null;
        };
    }

    /**
     * 将运行时提示转换为聊天值
     * 对应TypeScript方法: runtimePrompt2ChatsValue
     *
     * @param prompt 运行时用户提示
     * @return 用户聊天项的值列表
     */
    public static List<ChatItemValueItemType> runtimePrompt2ChatsValue(RuntimePromptType prompt) {
        List<ChatItemValueItemType> value = new ArrayList<>();

        if (prompt == null) {
            return value;
        }

        // 处理文件
        if (prompt.getFiles() != null && !prompt.getFiles().isEmpty()) {
            prompt.getFiles().forEach(file -> {
                ChatItemValueItemType fileItem = new ChatItemValueItemType();
                fileItem.setType(ChatItemValueType.FILE.getValue());
                fileItem.setFile(file);
                value.add(fileItem);
            });
        }

        // 处理文本
        if (StringUtils.hasText(prompt.getText())) {
            ChatItemValueItemType textItem = new ChatItemValueItemType();
            textItem.setType(ChatItemValueType.TEXT.getValue());

            // 创建文本内容对象
            ChatItemValueItemTextInfo textContent = new ChatItemValueItemTextInfo();
            textContent.setContent(prompt.getText());
            textItem.setText(textContent);

            value.add(textItem);
        }

        return value;
    }

    /**
     * chat转gpt消息
     */
    public static List<ChatCompletionMessageParam> chats2GPTMessages(ChatsToGPTMessagesParam param) {
        List<ChatCompletionMessageParam> results = new ArrayList<>();
        // 逻辑转写
        boolean reserveId = param.isReserveId();
        boolean reserveTool = param.isReserveTool();
        for (ChatItemType message : param.getMessages()) {
            String dataId = reserveId ? message.getDataId() : null;

            if (message.getObj().equals(ChatRoleEnum.SYSTEM)) {
                String content = "";
                List<ChatItemValueItemType> value = message.getValue();
                if (value != null && !value.isEmpty()) {
                    ChatItemValueItemType chatItemValueItemType = value.get(0);
                    ChatItemValueItemTextInfo text = chatItemValueItemType.getText();
                    content = text.getContent();
                }
                if (content != null && !content.isEmpty()) {
                    ChatCompletionMessageParam param1 = new ChatCompletionMessageParam();
                    param1.setDataId(dataId);
                    param1.setRole(ChatCompletionRequestMessageRoleEnum.SYSTEM.getValue());
                    param1.setContent(content);
                    results.add(param1);
                }
            } else if (message.getObj().equals(ChatRoleEnum.HUMAN)) {
                List<ChatItemValueItemType> valueItemTypeList = message.getValue();
                List<ChatCompletionContentPart> value = new ArrayList<>();
                if (valueItemTypeList != null && !valueItemTypeList.isEmpty()) {
                    for (ChatItemValueItemType item : valueItemTypeList) {
                        if (item.getType().equals(ChatItemValueType.TEXT.getValue())) {
                            ChatItemValueItemTextInfo text = item.getText();
                            if (text != null && !text.getContent().isEmpty()) {
                                value.add(new ChatCompletionContentPartText(text.getContent()));
                            } else {
                                value.add(new ChatCompletionContentPartText(""));
                            }
                        }
                        if (item.getType().equals(ChatItemValueType.FILE.getValue())) {
                            ChatItemValueItemFileInfo file = item.getFile();
                            if (file != null && !file.getType().equals(ChatFileType.IMAGE.getValue())) {
                                ChatCompletionContentPartImage.ImageUrl imageUrl = new ChatCompletionContentPartImage.ImageUrl();
                                imageUrl.setUrl(file.getUrl());
                                value.add(new ChatCompletionContentPartImage(imageUrl));
                            } else if (file != null && !file.getType().equals(ChatFileType.FILE.getValue())) {
                                value.add(new ChatCompletionContentPartFile(file.getName(), file.getUrl()));
                            }
                        }
                    }
                }

                ChatCompletionMessageParam param2 = new ChatCompletionMessageParam();
                param2.setDataId(dataId);
                param2.setHideInUI(message.getHideInUI());
                param2.setRole(ChatCompletionRequestMessageRoleEnum.USER.getValue());
                param2.setContent(ChatAdaptor.simpleUserContentPart(value));
                results.add(param2);
            } else {
                List<ChatCompletionMessageParam> aiResults = new ArrayList<>();
                List<ChatItemValueItemType> valueList = message.getValue();
                for (int i = 0; i < valueList.size(); i++) {
                    ChatItemValueItemType value = valueList.get(i);
                    if (value.getType().equals(ChatItemValueType.TOOL.getValue()) && reserveTool && !value.getTools().isEmpty()) {
                        List<ChatCompletionMessageToolCall> tool_calls = new ArrayList<>();
                        List<ChatCompletionMessageParam> toolResponse = new ArrayList<>();
                        List<ToolModuleResponseItemType> tools = value.getTools();
                        for (ToolModuleResponseItemType tool : tools) {
                            ChatCompletionMessageToolCall.Function function = new ChatCompletionMessageToolCall.Function();
                            function.setName(tool.getFunctionName());
                            function.setArguments(tool.getParams());
                            ChatCompletionMessageToolCall toolCall = new ChatCompletionMessageToolCall();
                            toolCall.setId(tool.getId());
                            toolCall.setType("function");
                            toolCall.setFunction(function);
                            tool_calls.add(toolCall);

                            ChatCompletionMessageParam result = new ChatCompletionMessageParam();
                            result.setToolCallId(tool.getId());
                            result.setRole(ChatCompletionRequestMessageRoleEnum.TOOL.getValue());
                            result.setName(tool.getFunctionName());
                            result.setContent(tool.getResponse());
                            toolResponse.add(result);
                        }
                        ChatCompletionMessageParam airesult = new ChatCompletionMessageParam();
                        airesult.setDataId(dataId);
                        airesult.setRole(ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue());
                        airesult.setToolCalls(tool_calls);
                        aiResults.add(airesult);

                        aiResults.addAll(toolResponse);
                    } else if (value.getType().equals(ChatItemValueType.TEXT.getValue()) &&
                    value.getText() != null && value.getText().getContent() != null && value.getText().getContent().equals("string")) {
                        if (message.getValue().size() > 1) {
                            break;
                        }

                        ChatItemValueItemType lastValue = message.getValue().get(i - 1);
                        ChatCompletionMessageParam laseResult = aiResults.get(aiResults.size() - 1);

                        if (lastValue != null && lastValue.getType().equals(ChatItemValueType.TEXT.getValue())) {
                            if (laseResult != null && laseResult.getContent() instanceof String) {
                                String content = value.getText().getContent();
                                laseResult.setContent(laseResult.getContent() + content);
                            }
                        } else {
                            ChatCompletionMessageParam airesult = new ChatCompletionMessageParam();
                            airesult.setDataId(dataId);
                            airesult.setRole(ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue());
                            airesult.setContent(value.getText().getContent());
                            aiResults.add(airesult);
                        }
                    } else if (value.getType().equals(ChatItemValueType.INTERACTIVE.getValue())) {
                        ChatCompletionMessageParam airesult = new ChatCompletionMessageParam();
                        airesult.setDataId(dataId);
                        airesult.setRole(ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue());
                        airesult.setInteractive(value.getInteractive());
                        aiResults.add(airesult);
                    }
                }

                if (!aiResults.isEmpty()) {
                    results.addAll(aiResults);
                } else {
                    ChatCompletionMessageParam airesult = new ChatCompletionMessageParam();
                    airesult.setDataId(dataId);
                    airesult.setRole(ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue());
                    airesult.setContent("");
                    results.add(airesult);
                }
            }
        }

        return results;
    }

    private static Object simpleUserContentPart(List<ChatCompletionContentPart> value) {
        if (value != null && !value.isEmpty()) {
            if (value.size() == 1) {
                ChatCompletionContentPartText part = (ChatCompletionContentPartText) value.get(0);
                if (part.getType().equals(ChatItemValueType.TEXT.getValue())) {
                    return part.getText();
                }
            }
        }
        return value;
    }
}
