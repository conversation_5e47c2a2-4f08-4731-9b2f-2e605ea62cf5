package com.sinitek.mind.core.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class ChatPageParamDTO {

    @Schema(description = "外链用户id")
    private String outLinkUid;

    @Schema(description = "外链id")
    private String shareId;

    @Schema(description = "用户id")
    private String orgId;

    @Schema(description = "appId")
    private Long appId;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "创建时间开始范围")
    private Date startCreateTime;

    @Schema(description = "创建时间结束范围")
    private Date endCreateTime;

    @Schema(description = "更新时间开始范围")
    private Date startUpdateTime;

    @Schema(description = "更新时间结束范围")
    private Date endUpdateTime;

}
