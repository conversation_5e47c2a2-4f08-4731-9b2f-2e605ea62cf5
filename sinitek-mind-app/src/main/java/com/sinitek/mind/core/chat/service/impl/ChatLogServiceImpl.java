package com.sinitek.mind.core.chat.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.ExportChatLogsBody;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.util.CustomAggregationOperation;
import com.sinitek.mind.core.chat.constant.ChatErrorCodeConstant;
import com.sinitek.mind.core.chat.dto.ChatLogResponse;
import com.sinitek.mind.core.chat.dto.GetChatLogsRequest;
import com.sinitek.mind.core.chat.repository.ChatItemRepository;
import com.sinitek.mind.core.chat.repository.ChatRepository;
import com.sinitek.mind.core.chat.service.IChatLogService;
import com.sinitek.mind.support.account.dto.SourceMemberDTO;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.framework.exception.BussinessException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.io.IOException;
import java.io.PrintWriter;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatLogServiceImpl implements IChatLogService {

    private final ChatRepository chatRepository;

    private final ChatItemRepository chatItemRepository;

    private final IPermissionService permissionService;

    private final MongoTemplate mongoTemplate;

    private final IOperationLogService operationLogService;

    private final IAccountService accountService;

    /**
     * 获取聊天日志列表
     * @param request 请求参数
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     * @return 分页响应数据
     */
    @Override
    public PageResult<ChatLogResponse> getChatLogs(GetChatLogsRequest request, String teamId, String tmbId, App app) {
        PageResult<ChatLogResponse> result = new PageResult<>();

        // 构建查询条件 - 直接写在主函数中
        Criteria criteria = Criteria.where("appId").is(new org.bson.types.ObjectId(request.getAppId()))
                .and("updateTime").gte(request.getDateStart()).lte(request.getDateEnd());

        // 添加来源条件
        if (request.getSources() != null && !request.getSources().isEmpty()) {
            criteria.and("source").in(request.getSources());
        }

        // 添加标题搜索条件
        if (request.getLogTitle() != null && !request.getLogTitle().trim().isEmpty()) {
            String escapedTitle = escapeRegexChars(request.getLogTitle());
            Pattern pattern = Pattern.compile(escapedTitle, Pattern.CASE_INSENSITIVE);
            criteria.orOperator(
                    Criteria.where("title").regex(pattern),
                    Criteria.where("customTitle").regex(pattern)
            );
        }

        // 构建聚合管道 - 直接写在主函数中
        Aggregation aggregation = Aggregation.newAggregation(
                // 1. 匹配条件
                Aggregation.match(criteria),

                // 2. 排序
                Aggregation.sort(Sort.Direction.DESC, "updateTime"),

                // 3. 分页
                Aggregation.skip(request.getOffset().longValue()),
                Aggregation.limit(request.getPageSize()),

                // 4. Lookup chatitems collection with pipeline
                new CustomAggregationOperation(new Document("$lookup", new Document()
                        .append("from", "chatitems")
                        .append("let", new Document()
                                .append("chatId", "$chatId")
                                .append("appId", "$appId"))
                        .append("pipeline", Arrays.asList(
                                new Document("$match", new Document("$expr", new Document("$and", Arrays.asList(
                                        new Document("$eq", Arrays.asList("$appId", "$$appId")),
                                        new Document("$eq", Arrays.asList("$chatId", "$$chatId"))
                                )))),
                                new Document("$group", new Document()
                                        .append("_id", null)
                                        .append("messageCount", new Document("$sum", 1))
                                        .append("goodFeedback", new Document("$sum",
                                                new Document("$cond", Arrays.asList(
                                                        new Document("$eq", Arrays.asList("$userGoodFeedback", true)), 1, 0))))
                                        .append("badFeedback", new Document("$sum",
                                                new Document("$cond", Arrays.asList(
                                                        new Document("$eq", Arrays.asList("$userBadFeedback", true)), 1, 0))))
                                        .append("customFeedback", new Document("$sum",
                                                new Document("$cond", Arrays.asList(
                                                        new Document("$gt", Arrays.asList(
                                                                new Document("$size", new Document("$ifNull", Arrays.asList("$customFeedbacks", Arrays.asList()))), 0)), 1, 0))))
                                        .append("adminMark", new Document("$sum",
                                                new Document("$cond", Arrays.asList(
                                                        new Document("$eq", Arrays.asList("$adminFeedback", true)), 1, 0)))))
                        ))
                        .append("as", "chatItemsData"))),

                // 5. 添加计算字段
                new CustomAggregationOperation(new Document("$addFields", new Document()
                        .append("messageCount", new Document("$ifNull", Arrays.asList(
                                new Document("$arrayElemAt", Arrays.asList("$chatItemsData.messageCount", 0)), 0)))
                        .append("userGoodFeedbackCount", new Document("$ifNull", Arrays.asList(
                                new Document("$arrayElemAt", Arrays.asList("$chatItemsData.goodFeedback", 0)), 0)))
                        .append("userBadFeedbackCount", new Document("$ifNull", Arrays.asList(
                                new Document("$arrayElemAt", Arrays.asList("$chatItemsData.badFeedback", 0)), 0)))
                        .append("customFeedbacksCount", new Document("$ifNull", Arrays.asList(
                                new Document("$arrayElemAt", Arrays.asList("$chatItemsData.customFeedback", 0)), 0)))
                        .append("markCount", new Document("$ifNull", Arrays.asList(
                                new Document("$arrayElemAt", Arrays.asList("$chatItemsData.adminMark", 0)), 0))))),

                // 6. 投影最终字段
                new CustomAggregationOperation(new Document("$project", new Document()
                        .append("_id", 1)
                        .append("chatId", "$chatId")
                        .append("title", 1)
                        .append("customTitle", 1)
                        .append("source", 1)
                        .append("sourceName", 1)
                        .append("time", "$updateTime")
                        .append("messageCount", 1)
                        .append("userGoodFeedbackCount", 1)
                        .append("userBadFeedbackCount", 1)
                        .append("customFeedbacksCount", 1)
                        .append("markCount", 1)
                        .append("outLinkUid", 1)
                        .append("shareId", 1)
                        .append("tmbId", 1)))
        );

        // 并行执行聚合查询和总数查询
        CompletableFuture<List<ChatLogResponse>> listFuture = CompletableFuture.supplyAsync(() -> {
            // 执行聚合查询
            return mongoTemplate.aggregate(aggregation, "chats", ChatLogResponse.class)
                    .getMappedResults();
        });

        CompletableFuture<Long> totalFuture = CompletableFuture.supplyAsync(() -> {
            // 获取总数 - 直接写在主函数中
            Query query = new Query(criteria);
            return mongoTemplate.count(query, "chats");
        });

        try {
            // 等待两个查询完成
            List<ChatLogResponse> list = listFuture.get();
            long total = totalFuture.get();

            result.setTotal((int) total);

            // 处理来源成员信息
            List<ChatLogResponse> listWithSourceMember = addSourceMember(list);

            // 过滤掉没有tmbId的记录
            List<ChatLogResponse> listWithoutTmbId = list.stream()
                    .filter(item -> StringUtils.isBlank(item.getTmbId()))
                    .collect(Collectors.toList());

            // 记录操作日志（异步）
            CompletableFuture.runAsync(() -> {
                Map<String, String> params = new HashMap<>();
                params.put("appName", app.getName());
                params.put("appType", app.getType());
                operationLogService.addOperationLog(MindConstant.CHAT, String.format("查询应用【%s】聊天记录",
                        app.getName()));
            });

            // 按照TS版本的逻辑：listWithSourceMember.concat(listWithoutTmbId)
            List<ChatLogResponse> finalList = new ArrayList<>(listWithSourceMember);
            finalList.addAll(listWithoutTmbId);
            result.setList(finalList);

        } catch (Exception e) {
            log.error("获取聊天日志失败: teamId={}, appId={}", teamId, request.getAppId(), e);
            result.setTotal(0);
            result.setList(new ArrayList<>());
        }

        return result;
    }

    /**
     * 导出聊天日志为JSON格式
     * @param request 导出请求参数
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    @Override
    public void exportChatLogs(ExportChatLogsBody request, String teamId, HttpServletResponse response) throws IOException {
        // 从认证信息中获取teamId，这里暂时使用默认值
        execExport(request, teamId, response);
    }

    /**
     * 转义正则表达式特殊字符
     */
    private String escapeRegexChars(String input) {
        if (input == null) return "";
        return input.replaceAll("[\\^$.*+?()\\[\\]{}|]", "\\\\$0");
    }

    /**
     * 添加来源成员信息
     */
    private List<ChatLogResponse> addSourceMember(List<ChatLogResponse> list) {
        // 这里可以添加来源成员信息的逻辑
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        List<String> tmbIdList = list.stream()
                .map(ChatLogResponse::getTmbId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        Map<String, SourceMemberDTO> tmbMemberMap = tmbIdList.stream()
                .distinct()
                .map(tmbId -> {
                    SourceMemberDTO member = accountService.getSourceMemberByOrgId(tmbId);
                    return Tuples.of(tmbId, member);
                })
                .collect(Collectors.toMap(Tuple2::getT1, Tuple2::getT2));

        list.forEach(response -> {
            SourceMemberDTO memberDTO = MapUtils.getObject(tmbMemberMap, response.getTmbId(), new SourceMemberDTO());
            response.setSourceMember(memberDTO);
        });

        return list;
    }

    /**
     * 导出聊天日志为CSV文件
     * @param request 导出请求参数
     * @param teamId 团队ID
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    public void execExport(ExportChatLogsBody request, String teamId, HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("text/csv; charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=chatlog_export.csv");

        PrintWriter writer = response.getWriter();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        try {
            // 写入BOM头，确保Excel正确识别UTF-8编码
            writer.write("\uFEFF");

            // 写入标题
            writer.println(request.getTitle());

            // 写入CSV表头
            writer.println("时间,来源,用户名,联系方式,标题,消息数量,好评反馈项,差评反馈项,自定义反馈项,标记项,聊天详情");

            // 构建查询条件
            Criteria criteria = buildExportCriteria(request, teamId);

            // 构建导出聚合查询
            Aggregation aggregation = buildExportAggregation(criteria);

            // 执行聚合查询
            List<Document> results = mongoTemplate.aggregate(aggregation, "chats", Document.class)
                    .getMappedResults();

            // 处理每条记录并写入CSV
            for (Document doc : results) {
                try {
                    // 格式化时间
                    String timeStr = "";
                    if (doc.getDate("time") != null) {
                        timeStr = doc.getDate("time").toInstant()
                                .atZone(ZoneId.systemDefault())
                                .format(formatter);
                    }

                    // 获取来源信息
                    String source = doc.getString("source");
                    String sourceName = source;
                    if (source != null && request.getSourcesMap() != null) {
                        ExportChatLogsBody.SourceInfo sourceInfo = request.getSourcesMap().get(source);
                        if (sourceInfo != null && sourceInfo.getLabel() != null) {
                            sourceName = sourceInfo.getLabel();
                        }
                    }

                    // 获取其他字段
                    String userId = doc.getString("outLinkUid");
                    String title = doc.getString("customTitle");
                    if (title == null || title.trim().isEmpty()) {
                        title = doc.getString("title");
                    }
                    Integer messageCount = doc.getInteger("messageCount", 0);

                    // 处理反馈数据
                    List<?> userGoodFeedbackItems = (List<?>) doc.get("userGoodFeedbackItems");
                    List<?> userBadFeedbackItems = (List<?>) doc.get("userBadFeedbackItems");
                    List<?> customFeedbackItems = (List<?>) doc.get("customFeedbackItems");
                    List<?> markItems = (List<?>) doc.get("markItems");
                    List<?> chatDetails = (List<?>) doc.get("chatDetails");

                    // 格式化JSON字符串用于CSV
                    String userGoodFeedbackItemsStr = formatJsonString(userGoodFeedbackItems != null ? userGoodFeedbackItems : new ArrayList<>());
                    String userBadFeedbackItemsStr = formatJsonString(userBadFeedbackItems != null ? userBadFeedbackItems : new ArrayList<>());
                    String customFeedbackItemsStr = formatJsonString(customFeedbackItems != null ? customFeedbackItems : new ArrayList<>());
                    String markItemsStr = formatJsonString(markItems != null ? markItems : new ArrayList<>());
                    String chatDetailsStr = formatJsonString(chatDetails != null ? chatDetails : new ArrayList<>());

                    // 写入CSV行
                    writer.printf("%s,%s,%s,%s,%s,%d,\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"%n",
                            formatCsvField(timeStr),
                            formatCsvField(sourceName != null ? sourceName : ""),
                            formatCsvField(userId != null ? userId : ""),
                            "", // 联系方式字段暂时为空
                            formatCsvField(title != null ? title : ""),
                            messageCount,
                            userGoodFeedbackItemsStr.replace("\"", "\"\""),
                            userBadFeedbackItemsStr.replace("\"", "\"\""),
                            customFeedbackItemsStr.replace("\"", "\"\""),
                            markItemsStr.replace("\"", "\"\""),
                            chatDetailsStr.replace("\"", "\"\"")
                    );

                } catch (Exception e) {
                    log.error("处理导出记录时出错", e);
                    continue;
                }
            }

            writer.flush();

        } catch (Exception e) {
            log.error("导出聊天日志失败", e);
            throw new BussinessException(ChatErrorCodeConstant.EXPORT_FAILED, e.getMessage());
        } finally {
            if (writer != null) {
                writer.close();
            }
        }
    }

    /**
     * 构建导出查询条件
     */
    private Criteria buildExportCriteria(ExportChatLogsBody request, String teamId) {
        // 转换日期字符串为LocalDateTime
        Date dateStart = request.getDateStart();
        Date dateEnd = request.getDateEnd();

        Criteria criteria = Criteria.where("teamId").is(teamId)
                .and("appId").is(request.getAppId())
                .and("updateTime").gte(dateStart).lte(dateEnd);

        // 添加来源条件
        if (request.getSources() != null && !request.getSources().isEmpty()) {
            criteria.and("source").in(request.getSources());
        }

        // 添加标题搜索条件
        if (request.getLogTitle() != null && !request.getLogTitle().trim().isEmpty()) {
            String escapedTitle = escapeRegexChars(request.getLogTitle());
            Pattern pattern = Pattern.compile(escapedTitle, Pattern.CASE_INSENSITIVE);
            criteria.orOperator(
                    Criteria.where("title").regex(pattern),
                    Criteria.where("customTitle").regex(pattern)
            );
        }

        return criteria;
    }

    /**
     * 构建导出聚合管道
     */
    private Aggregation buildExportAggregation(Criteria criteria) {
        return Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.sort(Sort.by(Sort.Direction.DESC, "userBadFeedbackCount", "userGoodFeedbackCount", "customFeedbacksCount", "updateTime")),
                Aggregation.limit(50000),
                // Lookup chatitems collection
                Aggregation.lookup("chatitems", "chatId", "chatId", "chatitems"),
                // Add computed fields
                new CustomAggregationOperation(new Document("$addFields", new Document()
                        .append("userGoodFeedbackItems", new Document("$filter", new Document()
                                .append("input", "$chatitems")
                                .append("as", "item")
                                .append("cond", new Document("$ifNull", Arrays.asList("$$item.userGoodFeedback", false)))))
                        .append("userBadFeedbackItems", new Document("$filter", new Document()
                                .append("input", "$chatitems")
                                .append("as", "item")
                                .append("cond", new Document("$ifNull", Arrays.asList("$$item.userBadFeedback", false)))))
                        .append("customFeedbackItems", new Document("$filter", new Document()
                                .append("input", "$chatitems")
                                .append("as", "item")
                                .append("cond", new Document("$gt", Arrays.asList(
                                        new Document("$size", new Document("$ifNull", Arrays.asList("$$item.customFeedbacks", Collections.emptyList()))), 0)))))
                        .append("markItems", new Document("$filter", new Document()
                                .append("input", "$chatitems")
                                .append("as", "item")
                                .append("cond", new Document("$ifNull", Arrays.asList("$$item.adminFeedback", false)))))
                        .append("chatDetails", new Document("$map", new Document()
                                .append("input", new Document("$slice", Arrays.asList("$chatitems", -1000)))
                                .append("as", "item")
                                .append("in", new Document()
                                        .append("id", "$$item._id")
                                        .append("value", "$$item.value")))))),
                // Project final fields
                new CustomAggregationOperation(new Document("$project", new Document()
                        .append("_id", "$_id")
                        .append("id", "$chatId")
                        .append("title", "$title")
                        .append("customTitle", "$customTitle")
                        .append("source", "$source")
                        .append("time", "$updateTime")
                        .append("outLinkUid", "$outLinkUid")
                        .append("tmbId", "$tmbId")
                        .append("messageCount", new Document("$size", new Document("$ifNull", Arrays.asList("$chatitems", Arrays.asList()))))
                        .append("userGoodFeedbackItems", "$userGoodFeedbackItems")
                        .append("userBadFeedbackItems", "$userBadFeedbackItems")
                        .append("customFeedbackItems", "$customFeedbackItems")
                        .append("markItems", "$markItems")
                        .append("chatDetails", "$chatDetails")))
        );
    }

    /**
     * 格式化JSON字符串用于CSV
     */
    private String formatJsonString(Object data) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(data);
            return jsonString.replace("\n", "\\n");
        } catch (Exception e) {
            log.error("格式化JSON字符串失败", e);
            return "";
        }
    }

    /**
     * 格式化CSV字段
     */
    private String formatCsvField(String field) {
        if (field == null) {
            return "";
        }
        // 转义双引号并用双引号包围包含逗号、换行符或双引号的字段
        if (field.contains(",") || field.contains("\n") || field.contains("\"")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        return field;
    }
}
