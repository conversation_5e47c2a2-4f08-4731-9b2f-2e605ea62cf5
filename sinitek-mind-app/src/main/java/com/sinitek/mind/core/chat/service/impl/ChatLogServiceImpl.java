package com.sinitek.mind.core.chat.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.ExportChatLogsBody;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.chat.constant.ChatErrorCodeConstant;
import com.sinitek.mind.core.chat.dao.ChatDAO;
import com.sinitek.mind.core.chat.dto.*;
import com.sinitek.mind.core.chat.service.IChatLogService;
import com.sinitek.mind.support.account.dto.SourceMemberDTO;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.sirm.framework.exception.BussinessException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.io.IOException;
import java.io.PrintWriter;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatLogServiceImpl implements IChatLogService {

    private final ChatDAO chatDAO;

    private final IOperationLogService operationLogService;

    private final IAccountService accountService;

    /**
     * 获取聊天日志列表
     * @param request 请求参数
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     * @return 分页响应数据
     */
    @Override
    public PageResult<ChatLogResponse> getChatLogs(GetChatLogsRequest request, String teamId, String tmbId, App app) {
        PageResult<ChatLogResponse> result = new PageResult<>();

        try {
            // 创建参数DTO
            ChatLogParamDTO params = new ChatLogParamDTO();
            params.setAppId(request.getAppId());
            params.setDateStart(request.getDateStart());
            params.setDateEnd(request.getDateEnd());
            params.setSources(request.getSources());
            params.setLogTitle(request.getLogTitle());
            
            // 创建分页对象，使用offset和pageSize
            Page<ChatLogResponse> page = new Page<>(request.getOffset() / request.getPageSize() + 1, request.getPageSize());
            
            // 执行分页查询，MyBatis-Plus会自动查询总数
            Page<ChatLogResponse> pageResult = chatDAO.getChatLogs(params, page);

            result.setTotal((int) pageResult.getTotal());
            result.setList(pageResult.getRecords());

            // 处理来源成员信息
            List<ChatLogResponse> listWithSourceMember = addSourceMember(pageResult.getRecords());

            // 过滤掉没有tmbId的记录
            List<ChatLogResponse> listWithoutTmbId = pageResult.getRecords().stream()
                    .filter(item -> StringUtils.isBlank(item.getTmbId()))
                    .toList();

            // 记录操作日志（异步）
            operationLogService.addOperationLog(MindConstant.CHAT, String.format("查询应用【%s】聊天记录",
                        app.getName()));

            // 按照TS版本的逻辑：listWithSourceMember.concat(listWithoutTmbId)
            List<ChatLogResponse> finalList = new ArrayList<>(listWithSourceMember);
            finalList.addAll(listWithoutTmbId);
            result.setList(finalList);

        } catch (Exception e) {
            log.error("获取聊天日志失败: teamId={}, appId={}", teamId, request.getAppId(), e);
            result.setTotal(0);
            result.setList(new ArrayList<>());
        }

        return result;
    }

    /**
     * 导出聊天日志为JSON格式
     * @param request 导出请求参数
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    @Override
    public void exportChatLogs(ExportChatLogsBody request, String teamId, HttpServletResponse response) throws IOException {
        // 从认证信息中获取teamId，这里暂时使用默认值
        execExport(request, teamId, response);
    }

    
    /**
     * 添加来源成员信息
     */
    private List<ChatLogResponse> addSourceMember(List<ChatLogResponse> list) {
        // 这里可以添加来源成员信息的逻辑
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        List<String> tmbIdList = list.stream()
                .map(ChatLogResponse::getTmbId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        Map<String, SourceMemberDTO> tmbMemberMap = tmbIdList.stream()
                .distinct()
                .map(tmbId -> {
                    SourceMemberDTO member = accountService.getSourceMemberByOrgId(tmbId);
                    return Tuples.of(tmbId, member);
                })
                .collect(Collectors.toMap(Tuple2::getT1, Tuple2::getT2));

        list.forEach(response -> {
            SourceMemberDTO memberDTO = MapUtils.getObject(tmbMemberMap, response.getTmbId(), new SourceMemberDTO());
            response.setSourceMember(memberDTO);
        });

        return list;
    }

    /**
     * 导出聊天日志为CSV文件
     * @param request 导出请求参数
     * @param teamId 团队ID
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    public void execExport(ExportChatLogsBody request, String teamId, HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("text/csv; charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=chatlog_export.csv");

        PrintWriter writer = response.getWriter();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        try {
            // 写入BOM头，确保Excel正确识别UTF-8编码
            writer.write("\uFEFF");

            // 写入标题
            writer.println(request.getTitle());

            // 写入CSV表头
            writer.println("时间,来源,用户名,联系方式,标题,消息数量,好评反馈项,差评反馈项,自定义反馈项,标记项,聊天详情");

            // 创建导出参数DTO
            ChatLogExportParamDTO exportParams = new ChatLogExportParamDTO();
            exportParams.setAppId(request.getAppId());
            exportParams.setDateStart(request.getDateStart());
            exportParams.setDateEnd(request.getDateEnd());
            exportParams.setSources(request.getSources());
            exportParams.setLogTitle(request.getLogTitle());
            
            // 执行导出查询
            List<ChatLogExportDTO> results = chatDAO.getChatLogsForExport(exportParams);

            // 处理每条记录并写入CSV
            for (ChatLogExportDTO dto : results) {
                try {
                    // 格式化时间
                    String timeStr = "";
                    if (dto.getTime() != null) {
                        timeStr = dto.getTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .format(formatter);
                    }

                    // 获取来源信息
                    String source = dto.getSource();
                    String sourceName = source;
                    if (source != null && request.getSourcesMap() != null) {
                        ExportChatLogsBody.SourceInfo sourceInfo = request.getSourcesMap().get(source);
                        if (sourceInfo != null && sourceInfo.getLabel() != null) {
                            sourceName = sourceInfo.getLabel();
                        }
                    }

                    // 获取其他字段
                    String userId = dto.getOutLinkUid();
                    String title = dto.getCustomTitle();
                    if (title == null || title.trim().isEmpty()) {
                        title = dto.getTitle();
                    }
                    Integer messageCount = dto.getMessageCount() != null ? dto.getMessageCount() : 0;

                    // 处理反馈数据
                    List<?> userGoodFeedbackItems = dto.getUserGoodFeedbackItems();
                    List<?> userBadFeedbackItems = dto.getUserBadFeedbackItems();
                    List<?> customFeedbackItems = dto.getCustomFeedbackItems();
                    List<?> markItems = dto.getMarkItems();
                    List<?> chatDetails = dto.getChatDetails();

                    // 格式化JSON字符串用于CSV
                    String userGoodFeedbackItemsStr = formatJsonString(userGoodFeedbackItems != null ? userGoodFeedbackItems : new ArrayList<>());
                    String userBadFeedbackItemsStr = formatJsonString(userBadFeedbackItems != null ? userBadFeedbackItems : new ArrayList<>());
                    String customFeedbackItemsStr = formatJsonString(customFeedbackItems != null ? customFeedbackItems : new ArrayList<>());
                    String markItemsStr = formatJsonString(markItems != null ? markItems : new ArrayList<>());
                    String chatDetailsStr = formatJsonString(chatDetails != null ? chatDetails : new ArrayList<>());

                    // 写入CSV行
                    writer.printf("%s,%s,%s,%s,%s,%d,\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"%n",
                            formatCsvField(timeStr),
                            formatCsvField(sourceName != null ? sourceName : ""),
                            formatCsvField(userId != null ? userId : ""),
                            "", // 联系方式字段暂时为空
                            formatCsvField(title != null ? title : ""),
                            messageCount,
                            userGoodFeedbackItemsStr.replace("\"", "\"\""),
                            userBadFeedbackItemsStr.replace("\"", "\"\""),
                            customFeedbackItemsStr.replace("\"", "\"\""),
                            markItemsStr.replace("\"", "\"\""),
                            chatDetailsStr.replace("\"", "\"\"")
                    );

                } catch (Exception e) {
                    log.error("处理导出记录时出错", e);
                    continue;
                }
            }

            writer.flush();

        } catch (Exception e) {
            log.error("导出聊天日志失败", e);
            throw new BussinessException(ChatErrorCodeConstant.EXPORT_FAILED, e.getMessage());
        } finally {
            if (writer != null) {
                writer.close();
            }
        }
    }

    
    /**
     * 格式化JSON字符串用于CSV
     */
    private String formatJsonString(Object data) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(data);
            return jsonString.replace("\n", "\\n");
        } catch (Exception e) {
            log.error("格式化JSON字符串失败", e);
            return "";
        }
    }

    /**
     * 格式化CSV字段
     */
    private String formatCsvField(String field) {
        if (field == null) {
            return "";
        }
        // 转义双引号并用双引号包围包含逗号、换行符或双引号的字段
        if (field.contains(",") || field.contains("\n") || field.contains("\"")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        return field;
    }
}
