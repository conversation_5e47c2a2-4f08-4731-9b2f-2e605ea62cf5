package com.sinitek.mind.core.app.controller;

import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.model.VersionListItemType;
import com.sinitek.mind.core.app.service.IAppVersionService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.service.IPermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 应用版本控制器
 * 对应原始的 /api/core/app/version/list 接口
 */
@Slf4j
@RestController
@RequestMapping("/mind/api/core/app/version")
@RequiredArgsConstructor
@Tag(name = "应用版本管理", description = "应用版本相关接口")
public class AppVersionController {
    
    private final IAppVersionService appVersionService;
    
    private final IPermissionService permissionService;

    private final IOperationLogService operationLogService;

    private final IAuthAppService authAppService;

    /**
     * 获取应用版本列表
     *
     * @param request 版本列表请求参数
     * @return 版本列表分页数据
     */
    @PostMapping("/list")
    @Operation(summary = "获取应用版本列表")
    public ApiResponse<PageResult<VersionListItemType>> getVersionList(
            @Valid @RequestBody VersionListRequest request) {
        
        // 应用权限验证 - 需要写权限和token认证
        authAppService.authApp(request.getAppId(), PermissionConstant.READ_PER);
        
        // 获取版本列表
        PageResult<VersionListItemType> response = appVersionService.getVersionList(request);
        
        return ApiResponse.success(response);
    }

    /**
     * 获取应用版本详情
     *
     * @param versionDetailRequest 版本详情请求参数
     * @return 版本详情数据
     */
    @PostMapping("/detail")
    public ApiResponse<VersionDetailResponse> getVersionDetail(@Valid @RequestBody VersionDetailRequest versionDetailRequest) {
        // 应用权限验证 - 需要写权限和token认证
        AuthAppDTO authAppDTO = authAppService.authApp(versionDetailRequest.getAppId(), PermissionConstant.READ_PER);

        // 获取版本详情
        VersionDetailResponse response = appVersionService.getVersionDetail(
                versionDetailRequest, authAppDTO);

        return ApiResponse.success(response);
    }

    /**
     * 获取应用最新版本
     * 对应原始的 /api/core/app/version/latest 接口
     *
     * @param appId 应用ID
     * @return 最新版本数据
     */
    @GetMapping("/latest")
    @Operation(summary = "获取应用最新版本")
    public ApiResponse<LatestVersionResDTO> getLatestVersion(@RequestParam("appId") @NotNull(message = "应用ID不能为空") Long appId) {
        // 应用权限验证 - 需要写权限和token认证
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.WRITE_PER);

        // 获取最新版本
        LatestVersionResDTO response = appVersionService.getAppLatestVersion(appId, authAppDTO);

        return ApiResponse.success(response);
    }

    /**
     * 更新应用版本
     *
     * @param request 更新版本请求参数
     * @return 更新结果
     */
    @PostMapping("/update")
    @Operation(summary = "更新应用版本", description = "更新应用版本名称")
    public ApiResponse<Void> updateAppVersion(@Valid @RequestBody UpdateAppVersionDTO request) {
        // 应用权限验证 - 需要写权限和token认证
        authAppService.authApp(request.getAppId(), PermissionConstant.WRITE_PER);

        // 更新版本名称
        appVersionService.updateAppVersion(request.getVersionId(), request.getVersionName());

        return ApiResponse.success();
    }

    /**
     * 发布应用版本
     * 对应原始的 /api/core/app/version/publish 接口
     *
     * @param appId 应用ID
     * @param request 发布请求参数
     * @return 发布结果
     */
    @PostMapping("/publish")
    @Operation(summary = "发布应用版本", description = "发布或保存应用版本")
    public ApiResponse<PublishAppResponseDTO> publishApp(
            @RequestParam("appId") @NotNull(message = "应用ID不能为空") Long appId,
            @Valid @RequestBody PublishAppDTO request) {
        // 应用权限验证 - 需要写权限和token认证
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.WRITE_PER);

        // 调用服务层发布应用
        PublishAppResponseDTO response = appVersionService.publishApp(appId, request, authAppDTO);

        // 记录操作日志
        if (request.getIsPublish() != null && request.getIsPublish()) {
            operationLogService.addOperationLog(MindConstant.APP,
                    String.format("发布应用【%s】",
                            authAppDTO.getApp().getName()));
        } else {
            operationLogService.addOperationLog(MindConstant.APP,
                    String.format("更新应用版本【%s】",
                            authAppDTO.getApp().getName()));
        }

        return ApiResponse.success(response);
    }

}