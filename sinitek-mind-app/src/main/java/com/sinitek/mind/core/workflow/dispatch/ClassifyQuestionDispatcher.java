package com.sinitek.mind.core.workflow.dispatch;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.common.util.TikTokenUtil;
import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.core.ai.util.PromptUtil;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemTextInfo;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.chat.model.ChatsToGPTMessagesParam;
import com.sinitek.mind.core.chat.util.ChatUtil;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.mind.core.workflow.dispatch.util.DispatchUtil;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.model.core.llm.LLMChatModelFactory;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ClassifyQuestionDispatcher implements NodeDispatcher{

    @Autowired
    private LLMChatModelFactory llmChatModelFactory;

    @Autowired
    private ISystemModelService systemModelService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.CLASSIFY_QUESTION.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchClassifyQuestion(dispatchData);
    }

    private Map<String, Object> dispatchClassifyQuestion(ModuleDispatchProps props) {
        // 提取参数
        ExternalProviderType externalProvider = props.getExternalProvider();
        RunningAppInfo runningAppInfo = props.getRunningAppInfo();
        RuntimeNodeItemType node = props.getNode();
        String nodeId = node.getNodeId();
        String name = node.getName();
        List<ChatItemType> histories = props.getHistories();

        Map<String, Object> params = props.getParams();
        String model = (String) params.get("model");

        Integer history = (Integer) params.getOrDefault("history", 6);
        List<DispatchNodeResponseType.ClassifyQuestionAgentItemType> agents = getListParam(params, "agents");
        String userChatInput = (String) params.get("userChatInput");

        String systemPrompt = (String) params.getOrDefault("systemPrompt", "");

        // 验证输入
        if (!StringUtils.hasText(userChatInput)) {
            throw new BussinessException(WorkflowErrorCodeConstant.INPUT_IS_EMPTY);
        }

        // 获取模型配置
        SystemModelDTO cqModel = systemModelService.findModelByModelId(model);
        if (cqModel == null) {
            throw new BussinessException(WorkflowErrorCodeConstant.MODEL_NOT_FOUND);
        }

        String memoryKey = runningAppInfo.getId() + "-" + nodeId;

        List<ChatItemType> chatHistories = DispatchUtil.getHistories(history, histories);

        // 获取上次记忆
        DispatchNodeResponseType.ClassifyQuestionAgentItemType lastMemory = getLastMemory(chatHistories, memoryKey);

        // 执行AI完成
        ActionProps actionProps = new ActionProps();
        BeanUtils.copyProperties(props, actionProps);
        actionProps.setLastMemory(lastMemory);
        actionProps.setHistories(chatHistories);
        actionProps.setCqModel(cqModel);
        CompletionResult completionResult = completions(actionProps);

        // 查找匹配的代理
        DispatchNodeResponseType.ClassifyQuestionAgentItemType result = agents.stream()
                .filter(item -> item.getKey().equals(completionResult.getArg().getType()))
                .findFirst()
                .orElse(agents.get(agents.size() - 1));

        // 计算使用量和费用
        Map<String, Object> usageInfo = calculateUsage(cqModel, completionResult.getInputTokens(),
                completionResult.getOutputTokens(), externalProvider);

        // 构建响应
        Map<String, Object> response = new HashMap<>();
        response.put(NodeOutputKeyEnum.CQ_RESULT.getValue(), result.getValue());

        // 构建跳过的处理ID列表
        List<String> skipHandleIds = agents.stream()
                .filter(item -> !item.getKey().equals(result.getKey()))
                .map(item -> WorkflowUtil.getHandleId(nodeId, "source", item.getKey()))
                .collect(Collectors.toList());
        response.put(DispatchNodeResponseKeyEnum.SKIP_HANDLE_ID.getValue(), skipHandleIds);

        // 构建内存
        Map<String, Object> memories = new HashMap<>();
        memories.put(memoryKey, result);
        response.put(DispatchNodeResponseKeyEnum.MEMORIES.getValue(), memories);

        // 构建节点响应
        response.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                .totalPoints(externalProvider.getOpenaiAccount() != null ? 0.0 : (double) usageInfo.get("totalPoints"))
                .model((String) usageInfo.get("modelName"))
                .query(userChatInput)
                .inputTokens(completionResult.getInputTokens())
                .outputTokens(completionResult.getOutputTokens())
                .cqList(agents)
                .cqResult(result.getValue())
                .contextTotalLen(chatHistories.size() + 2)
                .build());

        // 构建节点使用量
        Map<String, Object> nodeUsage = new HashMap<>();
        nodeUsage.put("moduleName", name);
        nodeUsage.put("totalPoints", externalProvider.getOpenaiAccount() != null ? 0 : usageInfo.get("totalPoints"));
        nodeUsage.put("model", usageInfo.get("modelName"));
        nodeUsage.put("inputTokens", completionResult.getInputTokens());
        nodeUsage.put("outputTokens", completionResult.getOutputTokens());
        response.put(DispatchNodeResponseKeyEnum.NODE_DISPATCH_USAGES.getValue(), Arrays.asList(nodeUsage));

        return response;
    }

    /**
     * 获取List参数
     */
    @SuppressWarnings("unchecked")
    private <T> List<T> getListParam(Map<String, Object> params, String key) {
        Object value = params.get(key);
        if (value instanceof List) {
            return (List<T>) value;
        }
        return new ArrayList<>();
    }

    private DispatchNodeResponseType.ClassifyQuestionAgentItemType getLastMemory(
            List<ChatItemType> chatHistories, String memoryKey) {
        ChatItemType chatItemType = chatHistories.get(chatHistories.size() - 1);
        if (chatItemType != null) {
            Map<String, Object> memories = chatItemType.getMemories();
            if (chatItemType.getMemories() != null) {
            return (DispatchNodeResponseType.ClassifyQuestionAgentItemType) chatItemType.getMemories().get(memoryKey);
            }
        }
        return null;
    }

    private CompletionResult completions(ActionProps actionProps) {

        Map<String, Object> params = actionProps.getParams();
        String userChatInput = (String) params.get("userChatInput");

        // 构建消息列表
        List<ChatItemType> messages = new ArrayList<>();

        // 添加系统消息
        ChatItemType systemMessage = buildSystemPrompt(actionProps);
        messages.add(systemMessage);

        // 添加历史消息
        messages.addAll(actionProps.getHistories());

        // 添加用户消息
        ChatItemType userMessage = new ChatItemType();

        ChatItemValueItemTextInfo textInfo = new ChatItemValueItemTextInfo();
        textInfo.setContent(userChatInput);

        ChatItemValueItemType itemType = new ChatItemValueItemType();
        itemType.setType(ChatItemValueType.TEXT.getValue());
        itemType.setText(textInfo);

        userMessage.setObj(ChatRoleEnum.HUMAN);
        userMessage.setValue(List.of(itemType));
        messages.add(userMessage);

        ChatsToGPTMessagesParam param = new ChatsToGPTMessagesParam();
        param.setMessages(messages);
        param.setReserveId(false);
        List<ChatCompletionMessageParam> messageParamList = ChatAdaptor.chats2GPTMessages(param);
        CompletableFuture<List<ChatCompletionMessageParam>> requestMessagesCom = ChatUtil.loadRequestMessages(messageParamList, false, "");
        List<ChatCompletionMessageParam> requestMessages = new ArrayList<>();
        try {
            requestMessages = requestMessagesCom.get();
        } catch (Exception e) {
            requestMessages = new ArrayList<>();
            log.error("问题分类调度失败", e);
            throw new BussinessException(WorkflowErrorCodeConstant.CLASSIFY_QUESTION_DISPATCH_FAILED, e.getMessage());
        }
        // 计算输入token
        String combinedPrompt = requestMessages.stream()
                .map(msg -> msg.getContent() != null ? msg.getContent().toString() : "")
                .collect(Collectors.joining("\n"));
        int inputTokens = TikTokenUtil.countPromptTokens(combinedPrompt, null);

        // 创建聊天模型
        ChatModel chatModel = llmChatModelFactory.createChatModel(actionProps.getCqModel());

        // 执行聊天完成
        ChatResponse response = chatModel.call(new Prompt(convertToSpringAIMessages(requestMessages)));

        String responseContent = response.getResult().getOutput().getText() ;
        int outputTokens = TikTokenUtil.countPromptTokens(responseContent, null);

        // 解析响应
        ClassifyQuestionResponse cqResponse = parseClassifyResponse(responseContent);

        return new CompletionResult(inputTokens, outputTokens, cqResponse);
    }

    private ChatItemType buildSystemPrompt(ActionProps actionProps) {

        Map<String, Object> params = actionProps.getParams();
        List<DispatchNodeResponseType.ClassifyQuestionAgentItemType> agents = getListParam(params, "agents");


        String systemPrompt = (String) params.getOrDefault("systemPrompt", "");

        String memory = "";
        try {
            memory = actionProps.getLastMemory() != null ? objectMapper.writeValueAsString(actionProps.getLastMemory()) : "";
        } catch (Exception e) {
            throw new BussinessException(WorkflowErrorCodeConstant.CONVERSION_FAILED, e);
        }

        // 构建类型列表JSON
        List<Map<String, String>> typeList = new ArrayList<>();
        for (DispatchNodeResponseType.ClassifyQuestionAgentItemType agent : agents) {
            Map<String, String> type = new HashMap<>();
            type.put("id", agent.getKey());
            type.put("description", agent.getValue());
            typeList.add(type);
        }

        String typeListJson;
        try {
            typeListJson = objectMapper.writeValueAsString(typeList);
        } catch (Exception e) {
            typeListJson = "[]";
        }

        ChatItemValueItemTextInfo textInfo = new ChatItemValueItemTextInfo();
        String content = PromptUtil.getCQSystemPrompt(systemPrompt, memory, typeListJson);
        textInfo.setContent(content);

        ChatItemType itemType = new ChatItemType();
        itemType.setObj(ChatRoleEnum.SYSTEM);
        ChatItemValueItemType valueItemType = new ChatItemValueItemType();
        valueItemType.setType(ChatItemValueType.TEXT.getValue());
        valueItemType.setText(textInfo);
        itemType.setValue(List.of(valueItemType));
        return itemType;
    }


    private List<Message> convertToSpringAIMessages(List<ChatCompletionMessageParam> messages) {
        return messages.stream()
                .map(msg -> {
                    String content = msg.getContent() != null ? msg.getContent().toString() : "";
                    if ("system".equals(msg.getRole())) {
                        return new SystemMessage(content);
                    } else if ("user".equals(msg.getRole())) {
                        return new UserMessage(content);
                    } else if ("assistant".equals(msg.getRole())) {
                        return new AssistantMessage(content);
                    }
                    return new UserMessage(content);
                })
                .collect(Collectors.toList());
    }

    private ClassifyQuestionResponse parseClassifyResponse(String responseContent) {
        try {
            // 首先尝试解析为JSON
            JsonNode jsonNode = objectMapper.readTree(responseContent);
            if (jsonNode.has("type")) {
                String type = jsonNode.get("type").asText();
                return new ClassifyQuestionResponse(type);
            }
        } catch (Exception e) {
            // JSON解析失败，尝试直接使用响应内容作为分类ID
            log.debug("Failed to parse as JSON, trying direct content: {}", responseContent);
        }

        // 如果不是JSON格式，直接使用响应内容（去除空白字符）
        String trimmedContent = responseContent.trim();
        if (StringUtils.hasText(trimmedContent)) {
            return new ClassifyQuestionResponse(trimmedContent);
        }

        log.warn("Failed to parse classify response: {}, using default", responseContent);
        return new ClassifyQuestionResponse("default");
    }

    private Map<String, Object> calculateUsage(SystemModelDTO model, int inputTokens,
                                               int outputTokens, ExternalProviderType externalProvider) {
        Map<String, Object> usage = new HashMap<>();

        usage.put("modelName", model.getName());

        if (externalProvider.getOpenaiAccount() != null) {
            // 使用外部提供商，不计算费用
            usage.put("totalPoints", 0);
        } else {
            // 计算内部模型费用
            double inputCost = (inputTokens / 1000.0) * (model.getInputPrice() != null ? model.getInputPrice() : 0.0);
            double outputCost = (outputTokens / 1000.0) * (model.getOutputPrice() != null ? model.getOutputPrice() : 0.0);
            double totalPoints = inputCost + outputCost;
            usage.put("totalPoints", totalPoints);
        }

        return usage;
    }

    // 内部类定义
    private static class CompletionResult {
        private final int inputTokens;
        private final int outputTokens;
        private final ClassifyQuestionResponse arg;

        public CompletionResult(int inputTokens, int outputTokens, ClassifyQuestionResponse arg) {
            this.inputTokens = inputTokens;
            this.outputTokens = outputTokens;
            this.arg = arg;
        }

        public int getInputTokens() { return inputTokens; }
        public int getOutputTokens() { return outputTokens; }
        public ClassifyQuestionResponse getArg() { return arg; }
    }

    private static class ClassifyQuestionResponse {
        private final String type;

        public ClassifyQuestionResponse(String type) {
            this.type = type;
        }

        public String getType() { return type; }
    }

}
