package com.sinitek.mind.core.chat.dto;

import com.sinitek.mind.core.chat.entity.Chat;
import com.sinitek.mind.core.chat.enumerate.AuthUserTypeEnum;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AuthChatCrudDTO {

    private String teamId;

    private String tmbId;

    private String uid;

    private Chat chat;

    private Boolean responseDetail;

    private Boolean showNodeStatus;

    private Boolean showRawSource;

    private AuthUserTypeEnum authType;

}
