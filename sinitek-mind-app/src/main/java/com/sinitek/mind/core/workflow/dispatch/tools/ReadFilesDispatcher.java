package com.sinitek.mind.core.workflow.dispatch.tools;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.AppFileSelectConfigType;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.dispatch.util.DispatchUtil;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import com.sinitek.mind.core.workflow.model.RunningUserInfo;
import com.sinitek.mind.core.workflow.model.RuntimeNodeItemType;
import com.sinitek.mind.core.workflow.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 变量更新调度器
 * 对应 TypeScript 中的 dispatchUpdateVariable 函数
 * 用于处理工作流中的变量更新操作
 */
@Slf4j
@Component
public class ReadFilesDispatcher implements NodeDispatcher {


    public static final int MAX_FILES = 20;
    public static final boolean CUSTOM_PDF_PARSE = false;

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.READ_FILES.getValue();
    }

    /**
     * 调度方法 - 主要入口点
     * 
     * @param dispatchData 调度数据，包含执行所需的所有参数
     * @return 执行结果映射，包含更新后的变量状态和响应数据
     */
    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        // 1. 参数提取和验证逻辑 - 从 ModuleDispatchProps 中获取 updateList
        Map<String, Object> params = dispatchData.getParams();
        RunningUserInfo runningUserInfo = dispatchData.getRunningUserInfo();
        List<ChatItemType> histories = dispatchData.getHistories();
        AppChatConfigType chatConfig = dispatchData.getChatConfig();
        RuntimeNodeItemType node = dispatchData.getNode();
        String requestOrigin = dispatchData.getRequestOrigin();

        int maxFiles = MAX_FILES;
        boolean customPdfParse = CUSTOM_PDF_PARSE;
        if (!Objects.isNull(chatConfig.getFileSelectConfig())) {
            AppFileSelectConfigType fileSelectConfig = chatConfig.getFileSelectConfig();
            maxFiles = fileSelectConfig.getMaxFiles();
            customPdfParse = fileSelectConfig.getCustomPdfParse();
        }

        List<String> fileUrlList = (List<String>) params.get(NodeInputKeyEnum.FILE_URL_LIST.getValue());
        Long version = node.getVersion();
        String teamId = runningUserInfo.getTeamId();
        String tmbId = runningUserInfo.getTmbId();

        List<String> filesFromHistories = new LinkedList<>();
        // TODO 这里的489含义未知
        if (version == 489L) {
            filesFromHistories = DispatchUtil.getHistoryFileLinks(histories);
        }

        LinkedList<String> urls = new LinkedList<>(fileUrlList);
        urls.addAll(filesFromHistories);

        CompletableFuture<FileUtil.FileContentResult> fileContentFromLinks = FileUtil.getFileContentFromLinks(urls,
                requestOrigin, maxFiles, teamId, tmbId, customPdfParse);

        FileUtil.FileContentResult fileContentResult = fileContentFromLinks.join();
        String text = fileContentResult.getText();
        List<FileUtil.FileResponseObject> readFilesResult = fileContentResult.getReadFilesResult();


        // 构建响应结构
        Map<String, Object> nodeResult = new HashMap<>();
        nodeResult.put(NodeOutputKeyEnum.TEXT.getValue(), text);

        nodeResult.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), Map.of("fileContext", text));
        nodeResult.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                        .readFiles(readFilesResult.stream()
                                .map(item -> DispatchNodeResponseType.ReadFileNodeResponse.builder()
                                        .name(item.getFilename())
                                        .url(item.getUrl())
                                        .build())
                                .toList())
                        .readFilesResult(readFilesResult.stream()
                                .map(FileUtil.FileResponseObject::getNodeResponsePreviewText)
                                .collect(Collectors.joining("\n******\n")))
                .build());
        return nodeResult;
    }

}