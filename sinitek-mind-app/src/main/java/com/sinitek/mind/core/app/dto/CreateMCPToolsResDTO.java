package com.sinitek.mind.core.app.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建MCP工具集响应
 * 对应原始的 createMCPToolsResponse
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateMCPToolsResDTO {
    
    /**
     * 创建的MCP工具集ID
     */
    private Long mcpToolsId;
    
    /**
     * 创建的子工具ID列表
     */
    private java.util.List<Long> childToolIds;
    
    /**
     * 创建时间
     */
    private java.time.LocalDateTime createTime;
    
    /**
     * 状态信息
     */
    private String status;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 构造成功响应
     */
    public static CreateMCPToolsResDTO success(Long mcpToolsId, java.util.List<Long> childToolIds) {
        CreateMCPToolsResDTO response = new CreateMCPToolsResDTO();
        response.setMcpToolsId(mcpToolsId);
        response.setChildToolIds(childToolIds);
        response.setCreateTime(java.time.LocalDateTime.now());
        response.setStatus("success");
        response.setMessage("MCP工具集创建成功");
        return response;
    }
}