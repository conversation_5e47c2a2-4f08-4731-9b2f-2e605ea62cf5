package com.sinitek.mind.core.workflow.dispatch.plugin;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.AppDetailType;
import com.sinitek.mind.core.app.model.PluginRuntimeType;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.app.service.IPluginService;
import com.sinitek.mind.core.app.util.PluginUtils;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.model.ChatHistoryItemResType;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemFileInfo;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.dispatch.util.DispatchUtil;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.service.IWorkflowService;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.dto.OpenaiAccountType;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

@Slf4j
@Component
public class PluginModuleDispatcher implements NodeDispatcher {

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private IPluginService pluginService;

    @Autowired
    private IWorkflowService workflowService;

    @Autowired
    private IAuthAppService authAppService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.PLUGIN_MODULE.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchRunPlugin(dispatchData);
    }

    private Map<String, Object> dispatchRunPlugin(ModuleDispatchProps props) {
        // 提取参数
        RuntimeNodeItemType node = props.getNode();
        String pluginId = node.getPluginId();
        String version = node.getVersion();
        RunningAppInfo runningAppInfo = props.getRunningAppInfo();
        List<ChatItemValueItemType> query = props.getQuery();
        Map<String, Object> params = props.getParams();
        Map<String, Object> variables = props.getVariables();
        Consumer<WorkflowStreamResponse> workflowStreamResponse = props.getWorkflowStreamResponse();

        // 提取params中的参数
        boolean system_forbid_stream = (boolean) params.getOrDefault("system_forbid_stream", false);
        Map<String, Object> data = new HashMap<>(params);
        data.remove("system_forbid_stream");

        if (pluginId == null || pluginId.trim().isEmpty()) {
            throw new BussinessException(WorkflowErrorCodeConstant.PLUGIN_ID_IS_EMPTY);
        }

        // 获取文件信息
        RuntimePromptType runtimePromptType = ChatAdaptor.chatValue2RuntimePrompt(query);
        List<ChatItemValueItemFileInfo> files = runtimePromptType.getFiles();

        // 权限验证
        AppDetailType pluginData = authAppService.authAppByTmbId(
                runningAppInfo.getTmbId(),
                pluginId,
                PermissionConstant.READ_PER,
                false
        );

        // 获取插件运行时信息 TODO getChildAppRuntimeById
        PluginRuntimeType plugin = pluginService.getChildAppRuntimeById(pluginId, version);

        // 构建输出过滤映射
        Map<String, Boolean> outputFilterMap = buildOutputFilterMap(plugin.getNodes());

        // 构建运行时节点
        List<RuntimeNodeItemType> runtimeNodes = buildRuntimeNodes(plugin.getNodes(), data);

        // TODO 获取用户聊天信息和团队权限
        ExternalProviderType externalProvider = new ExternalProviderType();
        externalProvider.setExternalWorkflowVariables(new HashMap<>());
        externalProvider.setOpenaiAccount(new OpenaiAccountType());

        // 构建运行时变量
        SystemVariablesType systemVariables = DispatchUtil.filterSystemVariables(variables);

        // 构建子应用运行变量
        Map systemVariablesMap = objectMapper.convertValue(systemVariables, Map.class);
        Map<String, Object> runtimeVariables = new HashMap<>(systemVariablesMap);
        runtimeVariables.put("appId", String.valueOf(pluginData.getId()));
        if (externalProvider.getExternalWorkflowVariables() != null) {
            runtimeVariables.putAll(externalProvider.getExternalWorkflowVariables());
        }

        // 构建插件运行用户查询
        List<FlowNodeInputItemType> pluginInputs = PluginUtils.getPluginInputsFromStoreNodes(plugin.getNodes());

        ChatItemType queryNew = WorkflowUtil.getPluginRunUserQuery(pluginInputs, runtimeVariables, files);


        // 构建工作流调度参数
        ChatDispatchProps dispatchProps = ChatDispatchProps.builder()
                .res(props.getRes())
                .requestOrigin(props.getRequestOrigin())
                .runningUserInfo(props.getRunningUserInfo())
                .uid(props.getUid())
                .chatId(props.getChatId())
                .responseChatItemId(props.getResponseChatItemId())
                .lastInteractive(props.getLastInteractive())
                .maxRunTimes(props.getMaxRunTimes())
                .isToolCall(props.getIsToolCall())
                .workflowDispatchDeep(props.getWorkflowDispatchDeep())
                .version(version)
                .responseAllData(props.getResponseAllData())
                .responseDetail(props.getResponseDetail())
                .histories(props.getHistories())
                .timezone(props.getTimezone())
                .retainDatasetCite(props.getRetainDatasetCite())
                .runningAppInfo(RunningAppInfo.builder()
                        .id(String.valueOf(plugin.getId()))
                        .teamId(plugin.getTeamId() != null ? String.valueOf(plugin.getTeamId()) : runningAppInfo.getTeamId())
                        .tmbId(plugin.getTmbId() != null ? String.valueOf(plugin.getTmbId()) : runningAppInfo.getTmbId())
                        .isChildApp(true)
                        .build())
                .variables(runtimeVariables)
                .query(queryNew.getValue())
                .chatConfig(new AppChatConfigType())
                .runtimeNodes(runtimeNodes)
                .runtimeEdges(WorkflowUtil.storeEdges2RuntimeEdges(plugin.getEdges(), null))
                .stream(!system_forbid_stream && props.getStream())
                .workflowStreamResponse(system_forbid_stream ? null : workflowStreamResponse)
                .externalProvider(externalProvider)
                .build();

        // 如果禁用流式输出，重写流模式
        if (system_forbid_stream) {
            dispatchProps.setStream(false);
            dispatchProps.setWorkflowStreamResponse(null);
        }

        // 调用工作流服务执行
        CompletableFuture<DispatchFlowResponse> flowResultFuture = workflowService.dispatchWorkFlow(dispatchProps);
        DispatchFlowResponse flowResult = flowResultFuture.join();

        // 查找输出节点
        ChatHistoryItemResType output = findOutputNode(flowResult.getFlowResponses());
        if (output != null) {
            output.setModuleLogo(plugin.getAvatar());
        }

        // TODO 计算插件使用点数
        double usagePoints = 0.0;
        if (output != null) {
            Map<String, Object> pluginOutput1 = output.getPluginOutput();
            Boolean error = (Boolean) pluginOutput1.get("error");
            usagePoints = PluginUtils.computedPluginUsage(plugin, flowResult.getFlowUsage(), error);
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();

        // 添加助手响应
        result.put("assistantResponses", system_forbid_stream ? new ArrayList<>() : flowResult.getAssistantResponses());

        // 添加系统内存
        result.put("system_memories", flowResult.getSystem_memories());

        // 添加运行时间
        result.put(DispatchNodeResponseKeyEnum.RUN_TIMES.getValue(), flowResult.getRunTimes());

        // 添加节点响应
//        Map<String, Object> nodeResponse = new HashMap<>();
//        nodeResponse.put("moduleLogo", plugin.getAvatar());
//        nodeResponse.put("totalPoints", usagePoints);
//        nodeResponse.put("pluginOutput", output.getPluginOutput());
        // TODO 添加插件详情（如果有写权限）
        // nodeResponse.put("pluginDetail", pluginDetail);
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                .moduleLogo(plugin.getAvatar())
                .totalPoints(usagePoints)
                .pluginOutput(output.getPluginOutput())
                .build());

        // 添加节点调度使用情况
        List<Map<String, Object>> nodeDispatchUsages = new ArrayList<>();
        Map<String, Object> usage = new HashMap<>();
        usage.put("moduleName", plugin.getName());
        usage.put("totalPoints", usagePoints);
        nodeDispatchUsages.add(usage);
        result.put(DispatchNodeResponseKeyEnum.NODE_DISPATCH_USAGES.getValue(), nodeDispatchUsages);

        // 添加工具响应
        if (output.getPluginOutput() != null) {
            Map<String, Object> pluginOutput = output.getPluginOutput();
            Map<String, Object> toolResponses = new HashMap<>();
            for (Map.Entry<String, Object> entry : pluginOutput.entrySet()) {
                if (outputFilterMap.getOrDefault(entry.getKey(), true)) {
                    toolResponses.put(entry.getKey(), entry.getValue());
                }
            }
            result.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), toolResponses);

            // 添加插件输出到结果中
            result.putAll(pluginOutput);
        } else {
            result.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), null);
        }

        return result;
    }

    /**
     * 构建输出过滤映射
     */
    private Map<String, Boolean> buildOutputFilterMap(List<StoreNodeItemType> nodes) {
        return nodes.stream()
                .filter(node -> FlowNodeTypeEnum.PLUGIN_OUTPUT.getValue().equals(node.getFlowNodeType()))
                .findFirst()
                .map(node -> {
                    Map<String, Boolean> filterMap = new HashMap<>();
                    if (node.getInputs() != null) {
                        for (FlowNodeInputItemType input : node.getInputs()) {
                            filterMap.put(input.getKey(), input.getToolOutput() != Boolean.FALSE);
                        }
                    }
                    return filterMap;
                })
                .orElse(new HashMap<>());
    }

    /**
     * 构建运行时节点
     */
    private List<RuntimeNodeItemType> buildRuntimeNodes(List<StoreNodeItemType> nodes, Map<String, Object> data) {
        List<String> entryNodeIds = WorkflowUtil.getWorkflowEntryNodeIds(nodes, null);
        return WorkflowUtil.storeNodes2RuntimeNodes(nodes, entryNodeIds)
                .stream()
                .map(node -> {
                    // 更新插件输入值
                    if (FlowNodeTypeEnum.PLUGIN_INPUT.getValue().equals(node.getFlowNodeType())) {
                        List<FlowNodeInputItemType> updatedInputs = node.getInputs().stream()
                                .map(input -> {
                                    FlowNodeInputItemType newInput = new FlowNodeInputItemType();
                                    // 复制原有属性
                                    BeanUtils.copyProperties(input, newInput);
                                    // 更新值
                                    newInput.setValue(data.getOrDefault(input.getKey(), input.getValue()));
                                    return newInput;
                                })
                                .toList();

                        RuntimeNodeItemType newNode = new RuntimeNodeItemType();
                        // 复制原有属性
                        BeanUtils.copyProperties(node, newNode);
                        // 设置更新后的输入和状态
                        newNode.setShowStatus(false);
                        newNode.setInputs(updatedInputs);
                        return newNode;
                    }

                    // 其他节点只设置showStatus
                    RuntimeNodeItemType newNode = new RuntimeNodeItemType();
                    // 复制所有属性
                    BeanUtils.copyProperties(node, newNode);
                    return newNode;
                })
                .toList();
    }

    /**
     * 查找输出节点
     */
    private ChatHistoryItemResType findOutputNode(List<ChatHistoryItemResType> flowResponses) {
        if (flowResponses == null) {
            return null;
        }
        return flowResponses.stream()
                .filter(item -> FlowNodeTypeEnum.PLUGIN_OUTPUT.getValue().equals(item.getModuleType()))
                .findFirst()
                .orElse(null);
    }
}
