package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.core.chat.model.AIChatItemValueItemType;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class RunToolResponse {

    private List<DispatchFlowResponse> dispatchFlowResponse;

    private Integer toolNodeInputTokens;

    private Integer toolNodeOutputTokens;

    private List<ChatCompletionMessageParam> completeMessages;

    private List<AIChatItemValueItemType> assistantResponses;

    private WorkflowInteractiveResponseType toolWorkflowInteractiveResponse;

    private Integer runTimes;

    private String finishReason;
}
