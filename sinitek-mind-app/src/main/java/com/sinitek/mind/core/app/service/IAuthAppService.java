package com.sinitek.mind.core.app.service;

import com.sinitek.mind.core.app.dto.AuthAppDTO;
import com.sinitek.mind.core.app.model.AppDetailType;

/**
 * 应用相关的权限校验
 */
public interface IAuthAppService {


    /**
     * 判断用户是否有对应应用的权限
     * @param appId appId
     * @param per 应有的权限值
     * @return 校验后的权限结果
     */
    AuthAppDTO authApp(Long appId, long per);

    /**
     * 判断对应用户是否有对应权限值，如果isRoot为true，则直接返回全部权限
     * @param tmbId 成员Id
     * @param appId appId
     * @param per 应有的权限值
     * @param isRoot 是否为root
     * @return 含有权限信息的app
     */
    AppDetailType authAppByTmbId(String tmbId, Long appId, long per, boolean isRoot);

}
