package com.sinitek.mind.core.chat.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.mind.core.chat.entity.ChatItem;
import com.sinitek.mind.core.chat.mapper.ChatItemMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ChatItemDAO extends ServiceImpl<ChatItemMapper, ChatItem> {

    public void deleteByAppId(Long appId) {
        if (appId == null) {
            return;
        }
        LambdaQueryWrapper<ChatItem> queryWrapper = Wrappers.lambdaQuery(ChatItem.class);
        queryWrapper.eq(ChatItem::getAppId, appId);
        remove(queryWrapper);
    }

    /**
     * 分页查询聊天项
     * @param chatId 聊天ID
     * @param appId 应用ID
     * @param page 分页参数
     * @return 分页聊天项
     */
    public Page<ChatItem> pageByChatIdAndAppId(String chatId, Long appId, Page<ChatItem> page) {
        LambdaQueryWrapper<ChatItem> queryWrapper = Wrappers.lambdaQuery(ChatItem.class);
        queryWrapper.eq(ChatItem::getChatId, chatId);
        queryWrapper.eq(ChatItem::getAppId, appId);

        if (CollUtil.isEmpty(page.orders())) {
            page.addOrder(OrderItem.desc("id"));
        }

        return baseMapper.selectPage(page, queryWrapper);
    }

    public void deleteByAppIdAndChatId(Long appId, String chatId) {
        if (appId == null || chatId == null) {
            return;
        }
        LambdaQueryWrapper<ChatItem> queryWrapper = Wrappers.lambdaQuery(ChatItem.class);
        queryWrapper.eq(ChatItem::getAppId, appId);
        queryWrapper.eq(ChatItem::getChatId, chatId);
        remove(queryWrapper);
    }

    public void deleteByAppIdAndChatIdIn(Long appId, List<String> idList) {
        if (appId == null || CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaQueryWrapper<ChatItem> queryWrapper = Wrappers.lambdaQuery(ChatItem.class);
        queryWrapper.eq(ChatItem::getAppId, appId);
        queryWrapper.in(ChatItem::getChatId, idList);
        remove(queryWrapper);
    }

    public ChatItem getByAppIdAndChatIdAndDataId(Long appId, String chatId, String dataId) {
        LambdaQueryWrapper<ChatItem> queryWrapper = Wrappers.lambdaQuery(ChatItem.class);
        queryWrapper.eq(ChatItem::getChatId, chatId);
        queryWrapper.eq(ChatItem::getAppId, appId);
        queryWrapper.eq(ChatItem::getDataId, dataId);

        return getOne(queryWrapper);
    }
}
