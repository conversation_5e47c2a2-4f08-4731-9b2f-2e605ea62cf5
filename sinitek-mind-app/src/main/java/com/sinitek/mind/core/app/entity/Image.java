package com.sinitek.mind.core.app.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mind_image")
public class Image extends BaseEntity {

    private Date expiredTime;

    private byte[] binary;

    private Object metadata;
}
