package com.sinitek.mind.core.workflow.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sinitek.mind.core.app.model.JsonSchemaPropertiesItemType;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PropertiesValue {

    private String type;

    private String description;

    private List<String> enums;

    private Boolean required;

    private JsonSchemaPropertiesItemType.ItemInfo items;
}
