package com.sinitek.mind.core.chat.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 聊天日志导出数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatLogExportDTO {
    
    /**
     * 文档ID
     */
    private String id;
    
    /**
     * 聊天ID
     */
    private String chatId;
    
    /**
     * 聊天标题
     */
    private String title;
    
    /**
     * 自定义标题
     */
    private String customTitle;
    
    /**
     * 来源
     */
    private String source;
    
    /**
     * 时间
     */
    private Date time;
    
    /**
     * 外部链接UID
     */
    private String outLinkUid;
    
    /**
     * 团队成员ID
     */
    private String tmbId;
    
    /**
     * 消息数量
     */
    private Integer messageCount;
    
    /**
     * 好评反馈项
     */
    private List<Object> userGoodFeedbackItems;
    
    /**
     * 差评反馈项
     */
    private List<Object> userBadFeedbackItems;
    
    /**
     * 自定义反馈项
     */
    private List<Object> customFeedbackItems;
    
    /**
     * 标记项
     */
    private List<Object> markItems;
    
    /**
     * 聊天详情
     */
    private List<Object> chatDetails;
}