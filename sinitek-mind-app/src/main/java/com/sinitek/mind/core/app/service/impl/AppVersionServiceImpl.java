package com.sinitek.mind.core.app.service.impl;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.dao.AppDAO;
import com.sinitek.mind.core.app.dao.AppVersionDAO;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.entity.AppVersion;
import com.sinitek.mind.core.app.model.*;
import com.sinitek.mind.core.app.service.IAppVersionService;
import com.sinitek.mind.core.app.util.AppUtil;
import com.sinitek.mind.core.app.util.TimeUtils;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.mind.support.account.dto.SourceMemberDTO;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppVersionServiceImpl implements IAppVersionService {

    private final AppVersionDAO appVersionDAO;
    private final AppDAO appDAO;
    private final IOperationLogService operationLogService;
    private final IAccountService accountService;

    @Override
    public AppVersionDTO getAppVersionById(Long appId, Long versionId, App app) {
        if (versionId != null) {
            AppVersion version = appVersionDAO.getById(versionId);
            if (version != null) {
                return new AppVersionDTO(
                        version.getId(),
                        version.getName(),
                        version.getNodes(),
                        version.getEdges(),
                        version.getChatConfig() != null ? version.getChatConfig() : (app != null ? app.getChatConfig() : new AppChatConfigType())
                );
            }
        }
        return getLatestAppVersion(appId, app);
    }

    @Override
    public AppVersionDTO getLatestAppVersion(Long appId, App app) {
        AppVersion appVersion = appVersionDAO.getByAppIdAndIsPublish(appId, true);
        if (appVersion != null) {
            return new AppVersionDTO(
                    appVersion.getId(),
                    appVersion.getName(),
                    appVersion.getNodes(),
                    appVersion.getEdges(),
                    appVersion.getChatConfig() != null ?
                            appVersion.getChatConfig() : (app != null ? app.getChatConfig() : new AppChatConfigType())
            );
        }
        return new AppVersionDTO(
                app != null ? Long.valueOf(app.getPluginData().getNodeVersion()) : null,
                app != null ? app.getName() : null,
                app != null ? app.getModules() : Collections.emptyList(),
                app != null ? app.getEdges() : Collections.emptyList(),
                app != null ? app.getChatConfig() : new AppChatConfigType()
        );
    }

    @Override
    public boolean checkIsLatestVersion(Long appId, Long versionId) {
        if (versionId == null || versionId <= 0) {
            return false;
        }
        AppVersion version = appVersionDAO
                .getByAppIdAndIsPublishAndIdGreaterThan(appId, true, versionId);
        return version == null;
    }

    @Override
    public PageResult<VersionListItemType> getVersionList(VersionListRequest request) {
        // 创建分页对象

        // 执行分页查询和总数统计
        Page<AppVersion> versionsPage = new Page<>(
                request.getOffset() / request.getPageSize(),
                request.getPageSize());
        versionsPage.setOrders(List.of(OrderItem.desc("time")));

        // 根据发布状态查询
        versionsPage = appVersionDAO.pageByAppIdAndIsPublish(versionsPage, Long.valueOf(request.getAppId()), request.getIsPublish());

        
        // 转换结果
        List<VersionListItemType> items = versionsPage.getRecords().stream()
                .map(this::converToVersionListItem)
                .toList();
        PageResult<VersionListItemType> result = new PageResult<>();
        result.setTotal((int) versionsPage.getTotal());
        result.setList(items);
        // TODO getSourceMemberInfo
        return result;
    }

    @Override
    public VersionDetailResponse getVersionDetail(VersionDetailRequest request, AuthAppDTO authAppDTO) {
        // 根据版本ID查询版本信息
        AppVersion version = appVersionDAO.getById(request.getVersionId());

        if (version == null) {
            throw new BussinessException(AppErrorCodeConstant.APP_VERSION_NOT_FOUND);
        }


        // 验证版本是否属于指定的应用
        if (!request.getAppId().equals(version.getAppId())) {
            throw new BussinessException(AppErrorCodeConstant.APP_VERSION_UPDATE_FAILED);
        }

        // 转换为响应DTO
        VersionDetailResponse response = convertToVersionDetailResponse(version);

        // 重写工作流详情（对应原始的rewriteAppWorkflowToDetail函数）
        if (response.getNodes() != null && !response.getNodes().isEmpty()) {
            RewriteParam rewriteParam = new RewriteParam();
            rewriteParam.setNodes(response.getNodes());
            AppUtil.rewriteAppWorkflowToDetail(
                    response.getNodes(),
                    authAppDTO.getTeamId(),
                    authAppDTO.getIsRoot(),
                    authAppDTO.getTmbId()
            );
        }

        return response;
    }

    @Override
    public LatestVersionResDTO getAppLatestVersion(Long appId, AuthAppDTO authAppDTO) {
        try {
            AppVersion latestVersion = appVersionDAO.getLatestByAppId(appId);
            LatestVersionResDTO resDTO = new LatestVersionResDTO();
            if (latestVersion != null) {
                List<StoreNodeItemType> nodes = AppUtil.rewriteAppWorkflowToDetail(latestVersion.getNodes(), authAppDTO.getTeamId(), authAppDTO.getIsRoot(), authAppDTO.getTmbId());
                resDTO.setNodes(nodes);
                resDTO.setEdges(latestVersion.getEdges());
                resDTO.setChatConfig(latestVersion.getChatConfig());
            } else {
                App app = appDAO.getById(appId);
                if (app == null) {
                    throw new BussinessException(AppErrorCodeConstant.APP_NOT_FOUND);
                }
                resDTO.setNodes(app.getModules());
                resDTO.setEdges(app.getEdges());
                resDTO.setChatConfig(app.getChatConfig());
            }
            return  resDTO;
        } catch (Exception e) {
            log.error("获取应用最新版本失败: {}", e.getMessage(), e);
            throw new BussinessException(AppErrorCodeConstant.APP_VERSION_UPDATE_FAILED);
        }
    }

    @Override
    public void updateAppVersion(Long versionId, String versionName) {
        // 查找版本是否存在
        AppVersion version = appVersionDAO.getById(versionId);
        if (version == null) {
            throw new BussinessException(AppErrorCodeConstant.APP_VERSION_NOT_FOUND);
        }

        version.setName(versionName);

        // 保存更新
        appVersionDAO.save(version);
    }



    @Override
    public PublishAppResponseDTO publishApp(Long appId, PublishAppDTO request, AuthAppDTO authAppDTO) {
        try {
            // 1. 更新应用的模块配置（对应原始的MongoApp.findByIdAndUpdate）
            App app = authAppDTO.getApp();

            // 处理自动保存配置（对应原始的autoSave逻辑）
            if (request.getAutoSave() != null &&  request.getAutoSave()) {
                // 这里可以根据需要设置自动保存相关的配置

                // 更新应用的基本配置
                app.setModules(request.getNodes());
                app.setEdges(request.getEdges());
                app.setChatConfig(request.getChatConfig());
                app.setUpdateTimeStamp(new Date());
                // 保存应用更新
                appDAO.updateById(app);

                operationLogService.addOperationLog(MindConstant.APP, String.format("更新应用节点配置【%s】",
                        app.getName()));

                return new PublishAppResponseDTO();
            }

            // 2. 如果需要发布，创建应用版本历史（对应原始的MongoAppVersion.create）
            AppVersion appVersion = new AppVersion();
            appVersion.setAppId(Long.valueOf(appId));
            appVersion.setName(request.getVersionName());
            appVersion.setNodes(request.getNodes());
            appVersion.setEdges(request.getEdges());
            appVersion.setChatConfig(request.getChatConfig());
            appVersion.setIsPublish(request.getIsPublish());
            appVersion.setOrgId(authAppDTO.getTmbId());
            Date now = new Date();
            appVersion.setCreateTimeStamp(now);
            appVersion.setUpdateTimeStamp(now);

            // 保存版本
            appVersionDAO.save(appVersion);
            Long versionId = appVersion.getId();

            // update app
            app.setModules(request.getNodes());
            app.setEdges(request.getEdges());
            app.setChatConfig(request.getChatConfig());

            PluginData pluginData = app.getPluginData();
            if (pluginData == null) {
                pluginData = new PluginData();
            }
            pluginData.setNodeVersion(String.valueOf(versionId));

            // 只有发布才会更新定时器
            if (request.getIsPublish() != null && request.getIsPublish()) {
                if (request.getChatConfig() != null) {
                    AppScheduledTriggerConfigType scheduledTriggerConfig = request.getChatConfig().getScheduledTriggerConfig();
                    if (scheduledTriggerConfig != null && scheduledTriggerConfig.getCronString() != null) {
                        app.setScheduledTriggerConfig(scheduledTriggerConfig);
                        app.setScheduledTriggerNextTime(TimeUtils.getNextTimeByCronStringAndTimezone(scheduledTriggerConfig.getCronString(), scheduledTriggerConfig.getTimezone()));
                    } else {
                        app.setScheduledTriggerConfig(null);
                        app.setScheduledTriggerNextTime(null);
                    }
                }
            }

            appDAO.updateById(app);

            // 3. 构造返回结果
            PublishAppResponseDTO response = new PublishAppResponseDTO();
            response.setSuccess(true);
            response.setVersionId(versionId);

            return response;

        } catch (Exception e) {
            log.error("发布应用失败: {}", e.getMessage(), e);
            throw new BussinessException(AppErrorCodeConstant.APP_VERSION_UPDATE_FAILED);
        }
    }

    /**
     * 将AppVersion实体转换为VersionDetailResponse
     * 对应原始代码中的返回值构造逻辑
     */
    private VersionDetailResponse convertToVersionDetailResponse(AppVersion version) {
        VersionDetailResponse response = new VersionDetailResponse();

        response.set_id(String.valueOf(version.getId()));
        response.setAppId(version.getAppId());
        response.setTime(version.getCreateTimeStamp());
        response.setIsPublish(version.getIsPublish());
        response.setTmbId(version.getOrgId());
        response.setTime(version.getUpdateTimeStamp());

        // 设置版本名称，如果为空则使用格式化的时间
        if (StringUtils.hasText(version.getName())) {
            response.setVersionName(version.getName());
        } else {
            response.setVersionName(TimeUtils.formatTime2YMDHM(version.getCreateTimeStamp()));
        }

        // 从版本配置中提取nodes、edges和chatConfig
        if (version.getNodes() != null) {
            response.setNodes(version.getNodes());
        }
        if (version.getEdges() != null) {

            response.setEdges(version.getEdges());
        }
        if (version.getChatConfig() != null) {
            response.setChatConfig(version.getChatConfig());
        }

        return response;
    }

    private VersionListItemType converToVersionListItem(AppVersion version) {
        VersionListItemType item = new VersionListItemType();
        item.setId(version.getId());
        item.setAppId(version.getAppId());
        item.setVersionName(version.getName());
        item.setTime(version.getUpdateTimeStamp());
        item.setIsPublish(version.getIsPublish());
        item.setTmbId(version.getOrgId());

        SourceMemberDTO member = accountService.getSourceMemberByOrgId(version.getOrgId());
        item.setSourceMember(member);
        return item;
    }

}
