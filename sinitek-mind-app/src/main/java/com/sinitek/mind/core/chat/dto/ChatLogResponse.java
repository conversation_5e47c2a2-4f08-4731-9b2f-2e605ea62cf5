package com.sinitek.mind.core.chat.dto;

import com.sinitek.mind.support.account.dto.SourceMemberDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 聊天日志响应数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatLogResponse {

    /**
     * 文档ID
     */
    private String _id;

    // _id和id不能同时出现，否则mongodb查询时会报错，不知道哪一个字段为主键
    /**
     * 聊天ID
     */
    private String chatId;
    
    /**
     * 聊天标题
     */
    private String title;
    
    /**
     * 自定义标题
     */
    private String customTitle;
    
    /**
     * 来源
     */
    private String source;
    
    /**
     * 来源名称
     */
    private String sourceName;
    
    /**
     * 时间
     */
    private Date time;
    
    /**
     * 消息数量
     */
    private Integer messageCount;
    
    /**
     * 用户好评反馈数量
     */
    private Integer userGoodFeedbackCount;
    
    /**
     * 用户差评反馈数量
     */
    private Integer userBadFeedbackCount;
    
    /**
     * 自定义反馈数量
     */
    private Integer customFeedbacksCount;
    
    /**
     * 标记数量
     */
    private Integer markCount;
    
    /**
     * 外部链接UID
     */
    private String outLinkUid;

    /**
     * 外链Id
     */
    private String shareId;

    /**
     * 团队成员ID
     */
    private String tmbId;

    /**
     * sourceMember
     */
    private SourceMemberDTO sourceMember;
}