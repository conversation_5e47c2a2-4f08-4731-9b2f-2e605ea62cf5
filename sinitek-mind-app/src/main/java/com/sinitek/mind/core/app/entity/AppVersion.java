package com.sinitek.mind.core.app.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.sinitek.data.mybatis.base.BaseEntity;
import com.sinitek.mind.common.typehandler.StoreEdgeItemListTypeHandler;
import com.sinitek.mind.common.typehandler.StoreNodeItemListTypeHandler;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "应用版本实体")
@TableName(value = "mind_app_version", autoResultMap = true)
public class AppVersion extends BaseEntity {

    @Schema(description = "组织ID")
    private String orgId;

    @Schema(description = "应用ID")
    private Long appId;

    @TableField(typeHandler = StoreNodeItemListTypeHandler.class)
    @Schema(description = "节点列表")
    private List<StoreNodeItemType> nodes;

    @TableField(typeHandler = StoreEdgeItemListTypeHandler.class)
    @Schema(description = "边列表")
    private List<StoreEdgeItemType> edges;

    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "聊天配置")
    private AppChatConfigType chatConfig;

    @Schema(description = "版本名称")
    private String name;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "是否发布")
    private Boolean isPublish;
}
