package com.sinitek.mind.core.workflow.enumerate;

import lombok.Getter;

/**
 * 节点输出键枚举
 * 对应 TypeScript 中的 NodeOutputKeyEnum
 */
@Getter
public enum NodeOutputKeyEnum {
    // common - 通用
    USER_CHAT_INPUT("userChatInput"),
    HISTORY("history"),
    ANSWER_TEXT("answerText"), // node answer. the value will be show and save to history
    REASONING_TEXT("reasoningText"), // node reasoning. the value will be show but not save to history
    SUCCESS("success"),
    FAILED("failed"),
    ERROR("error"),
    TEXT("system_text"),
    ADD_OUTPUT_PARAM("system_addOutputParam"),
    RAW_RESPONSE("system_rawResponse"),

    // start - 开始节点
    USER_FILES("userFiles"),
    USER_FILES_STREAM("userFilesStream"),

    // dataset - 数据集
    DATASET_QUOTE_QA("quoteQA"),

    // classify - 分类
    CQ_RESULT("cqResult"),
    // context extract - 上下文提取
    CONTEXT_EXTRACT_FIELDS("fields"),

    // tf switch - 条件分支
    RESULT_TRUE("system_resultTrue"),
    RESULT_FALSE("system_resultFalse"),

    // tools - 工具
    SELECTED_TOOLS("selectedTools"),

    // http - HTTP请求
    HTTP_RAW_RESPONSE("httpRawResponse"),

    // plugin - 插件
    PLUGIN_START("pluginStart"),

    // if else - 条件判断
    IF_ELSE_RESULT("ifElseResult"),

    // user select - 用户选择
    SELECT_RESULT("selectResult"),

    // loop - 循环
    LOOP_ARRAY("loopArray"),
    // loop start - 循环开始
    LOOP_START_INPUT("loopStartInput"),
    LOOP_START_INDEX("loopStartIndex"),

    // form input - 表单输入
    FORM_INPUT_RESULT("formInputResult");

    private final String value;

    NodeOutputKeyEnum(String value) {
        this.value = value;
    }

    /**
     * 根据字符串值获取对应的枚举
     * @param value 字符串值
     * @return 对应的枚举，如果未找到则返回 null
     */
    public static NodeOutputKeyEnum fromValue(String value) {
        for (NodeOutputKeyEnum keyEnum : values()) {
            if (keyEnum.value.equals(value)) {
                return keyEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return value;
    }
}