package com.sinitek.mind.core.app.util;

import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.sirm.framework.exception.BussinessException;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.concurrent.Callable;

/**
 * MongoDB会话工具类
 */
public class MongoSessionUtils {

    /**
     * 在MongoDB会话中运行操作
     *
     * @param callable 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     * @throws Exception 如果操作失败
     */
    public static <T> T mongoSessionRun(Callable<T> callable, MongoTransactionManager transactionManager) throws Exception {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        return transactionTemplate.execute(status -> {
            try {
                return callable.call();
            } catch (Exception e) {
                throw new BussinessException(AppErrorCodeConstant.APP_COPY_FAILED, e);
            }
        });
    }

    /**
     * 在MongoDB会话中运行无返回值的操作
     *
     * @param runnable 要执行的操作
     * @param transactionManager 事务管理器
     */
    public static void mongoSessionRunWithoutResult(Runnable runnable, MongoTransactionManager transactionManager) {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                try {
                    runnable.run();
                } catch (Exception e) {
                    throw new BussinessException(AppErrorCodeConstant.APP_COPY_FAILED, e);
                }
            }
        });
    }
}