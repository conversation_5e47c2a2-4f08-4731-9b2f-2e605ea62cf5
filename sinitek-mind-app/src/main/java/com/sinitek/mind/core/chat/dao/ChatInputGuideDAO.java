package com.sinitek.mind.core.chat.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.mind.core.chat.entity.ChatInputGuide;
import com.sinitek.mind.core.chat.mapper.ChatInputGuideMapper;
import org.springframework.stereotype.Component;

@Component
public class ChatInputGuideDAO extends ServiceImpl<ChatInputGuideMapper, ChatInputGuide> {
    public void deleteByAppId(Long appId) {
        if (appId == null) {
            return;
        }
        LambdaQueryWrapper<ChatInputGuide> queryWrapper = Wrappers.lambdaQuery(ChatInputGuide.class);
        queryWrapper.eq(ChatInputGuide::getAppId, appId);
        remove(queryWrapper);
    }


    /**
     * 根据应用ID统计聊天输入引导数量
     * @param appId 应用ID
     * @return 数量
     */
    public long countByAppId(String appId) {
        LambdaQueryWrapper<ChatInputGuide> queryWrapper = Wrappers.lambdaQuery(ChatInputGuide.class);
        queryWrapper.eq(ChatInputGuide::getAppId, appId);
        return count(queryWrapper);
    }
}
