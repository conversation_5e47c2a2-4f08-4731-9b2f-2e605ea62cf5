package com.sinitek.mind.core.workflow.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 变量更新列表项数据模型
 * 用于封装变量更新操作的相关信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateListItem {
    
    /**
     * 变量引用，格式为 [nodeId, variableKey]
     * - nodeId: 节点ID，"VARIABLE_NODE_ID" 表示全局变量
     * - variableKey: 变量键名
     */
    private ReferenceItemValueType variable;
    
    /**
     * 更新值，格式为 [referenceNodeId, referenceKey] 或 [null, directValue]
     * - 如果第一项为空，表示直接值，使用第二项
     * - 如果第一项不为空，表示引用值，需要解析引用
     */
    private ReferenceItemValueType value;
    
    /**
     * 值类型，支持的类型：
     * - string: 字符串类型
     * - number: 数字类型
     * - boolean: 布尔类型
     * - object: 对象类型
     * - array: 数组类型
     * - any: 任意类型
     */
    private String valueType;

    private String renderType;
}