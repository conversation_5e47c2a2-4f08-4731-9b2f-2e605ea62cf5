package com.sinitek.mind.core.app.service;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.model.VersionListItemType;

public interface IAppVersionService {

    AppVersionDTO getAppVersionById(Long appId, Long versionId, App app);

    AppVersionDTO getLatestAppVersion(Long appId, App app);

    boolean checkIsLatestVersion(Long appId, Long versionId);

    PageResult<VersionListItemType> getVersionList(VersionListRequest request);

    VersionDetailResponse getVersionDetail(VersionDetailRequest versionDetailRequest, AuthAppDTO authAppDTO);

    LatestVersionResDTO getAppLatestVersion(Long appId, AuthAppDTO authAppDTO);

    void updateAppVersion(Long versionId, String versionName);

    PublishAppResponseDTO publishApp(Long appId, PublishAppDTO request, AuthAppDTO authAppDTO);

}
