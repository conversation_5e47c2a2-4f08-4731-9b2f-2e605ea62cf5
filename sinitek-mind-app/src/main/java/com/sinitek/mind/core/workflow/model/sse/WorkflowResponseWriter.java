package com.sinitek.mind.core.workflow.model.sse;

import com.sinitek.mind.core.workflow.enumerate.SseResponseEventEnum;
import com.sinitek.mind.core.workflow.model.TextAdaptGptResponseParams;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

// 替代 workflowResponseWrite
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WorkflowResponseWriter {

    /**
     * 发送消息
     *
     * @param emitter sse
     * @param event 事件
     * @param data 数据
     */
    public static void write(SseEmitter emitter, String event, SseResponseType data) {
        try {
            SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                    .name(event)
                    .data(data);
            emitter.send(eventBuilder);
        } catch (Exception e) {
            log.error("SSE消息发送失败,SSE事件连接已断开或已complete或已超时", e);
        }
    }

    /**
     * 发送内容，只有内容，没有字段等信息
     *
     * @param event 事件
     * @param content 数据，不能包含\n，否则\n会被修改为\ndata:
     */
    public static void writeContent(SseEmitter emitter, String event, String content) {
        try {
            SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                    .name(event)
                    .data(content);
            emitter.send(eventBuilder);
        } catch (Exception e) {
            log.error("SSE消息发送失败,SSE事件连接已断开或已complete或已超时", e);
        }
    }


    /**
     * 发送结束消息
     */
    public static void writeDone(SseEmitter emitter) {
        writeContent(emitter, SseResponseEventEnum.ANSWER.getValue(), "[DONE]");
    }

    /**
     * 发送结束消息
     */
    public static void writeStop(SseEmitter emitter) {
        GptResponse gptResponse = WorkflowUtil.textAdaptGptResponse(TextAdaptGptResponseParams.builder()
                .finishReason("stop")
                .build());
        write(emitter, SseResponseEventEnum.ANSWER.getValue(), gptResponse);
    }

    /**
     * 发送工作流结束消息
     * <p>writeStop + writeDone 两个个方法的集合体</p>
     */
    public static void sseComplete(SseEmitter emitter) {
        writeStop(emitter);
        writeDone(emitter);
        // 结束工作流
        emitter.complete();
    }

    /**
     * 发送sse异常消息,并关闭sse
     * @param emitter
     * @param e
     */
    public static void sseErrRes(SseEmitter emitter, Throwable e) {
        if (e instanceof BussinessException bussinessException) {
            // 业务上的异常
            // TODO 需要拦截特殊的异常，例如：api积分不足，需要返回特殊的数据结构，暂时直接全部返回
            write(emitter, SseResponseEventEnum.ERROR.getValue(), SseErrorResponse.builder()
                    .message(e.getMessage())
                    .build());
        } else {
            // 系统异常
            write(emitter, SseResponseEventEnum.ERROR.getValue(), SseErrorResponse.builder()
                    .message(e.getMessage())
                    .build());
        }
        writeDone(emitter);
        emitter.complete();
    }
}