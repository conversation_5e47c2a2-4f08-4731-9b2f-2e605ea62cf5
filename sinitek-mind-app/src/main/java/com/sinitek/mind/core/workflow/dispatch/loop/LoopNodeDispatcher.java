package com.sinitek.mind.core.workflow.dispatch.loop;

import cn.hutool.core.util.ObjectUtil;
import com.sinitek.mind.core.chat.model.AIChatItemValueItemType;
import com.sinitek.mind.core.chat.model.ChatHistoryItemResType;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.model.LoopInteractive.Params;
import com.sinitek.mind.core.workflow.service.IWorkflowService;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.system.support.SystemGlobals;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025/7/17
 */
@Component
@RequiredArgsConstructor
public class LoopNodeDispatcher implements NodeDispatcher {

    private final IWorkflowService workflowService;

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.LOOP.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        Map<String, Object> params = dispatchData.getParams();
        List<RuntimeEdgeItemType> runtimeEdges = dispatchData.getRuntimeEdges();
        List<RuntimeNodeItemType> runtimeNodes = dispatchData.getRuntimeNodes();
        WorkflowInteractiveResponseType lastInteractive = dispatchData.getLastInteractive();
        String name = dispatchData.getNode().getName();

        List<Object> loopInputArray = (List<Object>) params.get(NodeInputKeyEnum.LOOP_INPUT_ARRAY.getValue());
        List<String> childrenNodeIdList = (List<String>) params.get(
            NodeInputKeyEnum.CHILDREN_NODE_ID_LIST.getValue());

        if (!(loopInputArray instanceof List)) {
            throw new BussinessException(WorkflowErrorCodeConstant.LOOP_INPUT_NOT_LIST);
        }

        Integer workflowMaxLoopTimes = SystemGlobals.getSecurityEnv().getWorkflowMaxLoopTimes();
        if (ObjectUtil.isNotNull(workflowMaxLoopTimes)
            && workflowMaxLoopTimes < loopInputArray.size()) {
            throw new BussinessException(WorkflowErrorCodeConstant.LOOP_INPUT_EMPTY);
        }

        Params interactiveData = Optional.ofNullable(lastInteractive)
            .map(WorkflowInteractiveResponseType::getNodeResponse)
            .map(item -> {
                if ("loopInteractive".equals(item.getType())) {
                    return (Params) item.getParam();
                }
                return null;
            })
            // 使用构建器模式创建实例
            .orElse(null);

        int lastIndex = -1;
        List<Object> outputValueArr = List.of();
        if (interactiveData != null) {
            lastIndex = interactiveData.getCurrentIndex();
            outputValueArr = interactiveData.getLoopResult();
        }

        List<ChatHistoryItemResType> loopResponseDetail = new LinkedList<>();
        List<AIChatItemValueItemType> assistantResponses = new LinkedList<>();
        double totalPoint = 0;
        Map<String, Object> newVariables = dispatchData.getVariables();
        WorkflowInteractiveResponseType interactiveResponse = null;
        int index = 0;

        // 过滤掉 null 元素
        List<Object> filteredLoopInputArray = loopInputArray.stream()
            .filter(Objects::nonNull)
            .toList();

        for (Object item : filteredLoopInputArray) {
            // Skip already looped
            if (lastIndex != -1 && index < lastIndex) {
                index++;
                continue;
            }

            // It takes effect only once in current loop
            boolean isInteractiveResponseIndex =
                interactiveData != null && index == interactiveData.getCurrentIndex();

            // Init entry
            if (isInteractiveResponseIndex) {
                for (RuntimeNodeItemType node : runtimeNodes) {
                    if (interactiveData.getChildrenResponse() != null &&
                        interactiveData.getChildrenResponse().getEntryNodeIds() != null &&
                        interactiveData.getChildrenResponse().getEntryNodeIds()
                            .contains(node.getNodeId())) {
                        node.setIsEntry(true);
                    }
                }
            } else {
                for (RuntimeNodeItemType node : runtimeNodes) {
                    if (!childrenNodeIdList.contains(node.getNodeId())) {
                        continue;
                    }

                    // Init interactive response
                    if (Objects.equals(node.getFlowNodeType(),
                        FlowNodeTypeEnum.LOOP_START.getValue())) {
                        node.setIsEntry(true);
                        int finalIndex = index;
                        node.getInputs().forEach(input -> {
                            if (Objects.equals(input.getKey(),
                                NodeInputKeyEnum.LOOP_START_INPUT.getValue())) {
                                input.setValue(item);
                            } else if (Objects.equals(input.getKey(),
                                NodeInputKeyEnum.LOOP_START_INDEX.getValue())) {
                                input.setValue(finalIndex + 1);
                            }
                        });
                    }
                }
            }

            index++;

            CompletableFuture<DispatchFlowResponse> responseFuture = workflowService.dispatchWorkFlow(
                buildChatDispatchProps(
                    dispatchData, interactiveData == null ? null : interactiveData.getChildrenResponse(),
                    newVariables, runtimeNodes, runtimeEdges
                ));

            DispatchFlowResponse response = responseFuture.join();

            List<ChatHistoryItemResType> flowResponses = response.getFlowResponses();

            ChatHistoryItemResType loopOutputValue = flowResponses.stream()
                .filter(res -> ObjectUtil.equals(res.getModuleType(),
                    FlowNodeTypeEnum.LOOP_END.getValue()))
                .findFirst().orElse(null);

            // Concat runtime response
            if (response.getWorkflowInteractiveResponse() != null) {
                outputValueArr.add(loopOutputValue);
            }
            loopResponseDetail.addAll(response.getFlowResponses());
            assistantResponses.addAll(response.getAssistantResponses());
            totalPoint += response.getFlowUsage().stream()
                .mapToDouble(ChatNodeUsageType::getTotalPoints)
                .sum();

            // Concat new variables
            newVariables = buildVariables(newVariables, response.getNewVariables());

            // handle interactive response
            if (response.getWorkflowInteractiveResponse() != null) {
                interactiveResponse = response.getWorkflowInteractiveResponse();
                break;
            }
        }

        Map<String, Object> result = new HashMap<>();
        // 处理 [DispatchNodeResponseKeyEnum.interactive]
        if (interactiveResponse != null) {
            LoopInteractive loopInteractive = new LoopInteractive();
            loopInteractive.setParams(LoopInteractive.Params.builder()
                .loopResult(outputValueArr)
                .childrenResponse(interactiveResponse)
                .currentIndex(index - 1)
                .build());

            result.put(DispatchNodeResponseKeyEnum.INTERACTIVE.getValue(), loopInteractive);
        } else {
            result.put(DispatchNodeResponseKeyEnum.INTERACTIVE.getValue(), null);
        }

        // 处理 [DispatchNodeResponseKeyEnum.assistantResponses]
        result.put(DispatchNodeResponseKeyEnum.ASSISTANT_RESPONSES.getValue(), assistantResponses);

        // 处理 [DispatchNodeResponseKeyEnum.nodeResponse]
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
            .totalPoints(totalPoint)
            .loopInput(loopInputArray)
            .loopResult(outputValueArr)
            .loopDetail(loopResponseDetail)
            .mergeSignId(dispatchData.getNode().getNodeId())
            .build());

        // 处理 [DispatchNodeResponseKeyEnum.nodeDispatchUsages]
        result.put(DispatchNodeResponseKeyEnum.NODE_DISPATCH_USAGES.getValue(), List.of(ChatNodeUsageType.builder()
            .totalPoints((int) totalPoint)
            .moduleName(name)
            .build()));

        // 处理 [NodeOutputKeyEnum.loopArray]
        result.put(NodeOutputKeyEnum.LOOP_ARRAY.getValue(), outputValueArr);

        // 处理 [DispatchNodeResponseKeyEnum.newVariables]
        result.put(DispatchNodeResponseKeyEnum.NEW_VARIABLES.getValue(), newVariables);

        return result;
    }

    private ChatDispatchProps buildChatDispatchProps(ModuleDispatchProps dispatchData, WorkflowInteractiveResponseType lastInteractive,
        Map<String, Object> variables, List<RuntimeNodeItemType> runtimeNodes, List<RuntimeEdgeItemType> runtimeEdges) {

        List<RuntimeEdgeItemType> newRuntimeEdges = WorkflowUtil.storeEdges2RuntimeEdges(
            runtimeEdges, lastInteractive);

        ChatDispatchProps chatDispatchProps = new ChatDispatchProps();
        BeanUtils.copyProperties(dispatchData, chatDispatchProps);

        // 覆盖
        chatDispatchProps.setLastInteractive(lastInteractive);
        chatDispatchProps.setVariables(variables);
        chatDispatchProps.setRuntimeNodes(runtimeNodes);
        chatDispatchProps.setRuntimeEdges(newRuntimeEdges);

        return chatDispatchProps;
    }

    private Map<String, Object> buildVariables(Map<String, Object> oldVariables, Map<String, Object> newVariables) {
        Map<String, Object> result = new HashMap<>();
        result.putAll(oldVariables);
        result.putAll(newVariables);
        return result;
    }
}

