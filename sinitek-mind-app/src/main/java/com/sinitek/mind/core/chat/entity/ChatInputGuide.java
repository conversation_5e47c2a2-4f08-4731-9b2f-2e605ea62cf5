package com.sinitek.mind.core.chat.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mind_chat_input_guide")
public class ChatInputGuide extends BaseEntity {

    @Schema(description = "应用id")
    private Long appId;

    @Schema(description = "内容")
    private String text;
}
