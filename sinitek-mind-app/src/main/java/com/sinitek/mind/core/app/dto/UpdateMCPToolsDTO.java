package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.core.app.model.McpToolConfig;
import com.sinitek.mind.core.app.model.SecretValue;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 更新MCP工具集请求
 * 对应原始的 updateMCPToolsBody
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateMCPToolsDTO {
    
    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    private Long appId;
    
    /**
     * MCP服务器URL
     */
    @NotBlank(message = "MCP服务器URL不能为空")
    private String url;
    
    /**
     * 请求头密钥配置
     */
    @NotNull(message = "请求头密钥配置不能为空")
    private Map<String, SecretValue> headerSecret;
    
    /**
     * 工具列表
     */
    @NotEmpty(message = "工具列表不能为空")
    @Valid
    private List<McpToolConfig> toolList;
}