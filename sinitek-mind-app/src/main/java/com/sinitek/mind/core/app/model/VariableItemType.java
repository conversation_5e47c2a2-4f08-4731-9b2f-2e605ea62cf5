package com.sinitek.mind.core.app.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class VariableItemType {

    @Schema(description = "主键-6位字符（前端提供），全局变量中唯一即可", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    @Schema(description = "变量名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String key;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String label;

    @Schema(description = "变量类型-对应前端的组件", requiredMode = Schema.RequiredMode.REQUIRED)
    private String type;// VariableInputEnum

    @Schema(description = "是否必填", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean required;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String valueType; // WorkflowIOValueTypeEnum

    @Schema(description = "默认值")
    private Object defaultValue;

    // input
    @Schema(description = "最大长度-input变量类型独有")
    private Integer maxLen;

    // numberInput
    @Schema(description = "最大值-numberInput变量类型独有")
    private Integer max;

    @Schema(description = "最小值-numberInput变量类型独有")
    private Integer min;

    // select
    @Schema(description = "选项列表-select变量类型独有")
    private List<ListInfo> enums;
}
