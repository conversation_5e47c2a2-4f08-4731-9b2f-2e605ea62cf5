package com.sinitek.mind.core.app.enumerate;

import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Getter;

@Getter
public enum FlowNodeInputTypeEnum {
    REFERENCE("reference"), // reference to other node output
    INPUT("input"), // one line input
    TEXTAREA("textarea"),
    NUMBER_INPUT("numberInput"),
    SWITCH("switch"), // true/false
    SELECT("select"),
    MULTIPLE_SELECT("multipleSelect"),

    // editor
    JSO<PERSON>_EDITOR("JSONEditor"),

    ADD_INPUT_PARAM("addInputParam"), // params input

    // special input
    SELECT_APP("selectApp"),
    CUSTOM_VARIABLE("customVariable"),

    // ai model select
    SELECT_LLM_MODEL("selectLLMModel"),
    SETTING_LLM_MODEL("settingLLMModel"),

    // dataset special input
    SELECT_DATASET("selectDataset"),
    SELECT_DATASET_PARAMS_MODAL("selectDatasetParamsModal"),
    SETTING_DATASET_QUOTE_PROMPT("settingDatasetQuotePrompt"),

    HIDDEN("hidden"),
    CUSTOM("custom"),

    FILE_SELECT("fileSelect");

    private final String value;

    FlowNodeInputTypeEnum(String value) {
        this.value = value;
    }

    // 根据字符串值查找枚举
    public static FlowNodeInputTypeEnum fromValue(String value) {
        for (FlowNodeInputTypeEnum type : FlowNodeInputTypeEnum.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new BussinessException(AppErrorCodeConstant.UNKNOWN_FLOW_NODE_INPUT_TYPE, value);
    }
}
