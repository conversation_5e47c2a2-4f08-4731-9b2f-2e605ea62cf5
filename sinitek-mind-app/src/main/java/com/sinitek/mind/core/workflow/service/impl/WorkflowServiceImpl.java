package com.sinitek.mind.core.workflow.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import com.sinitek.mind.core.chat.model.AIChatItemValueItemType;
import com.sinitek.mind.core.chat.model.ChatHistoryItemResType;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.NodeOutputItemType;
import com.sinitek.mind.core.dataset.model.DebugResponse;
import com.sinitek.mind.core.workflow.dispatch.NodeService;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.SseResponseEventEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.model.sse.FlowNodeStatusResponse;
import com.sinitek.mind.core.workflow.model.sse.WorkflowDurationResponse;
import com.sinitek.mind.core.workflow.service.IWorkflowService;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.support.outlink.entity.OutLink;
import com.sinitek.sirm.common.limit.annotation.SiniCubeLimiter;
import com.sinitek.sirm.common.limit.enumerate.SiniCubeLimiterMode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowServiceImpl implements IWorkflowService {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final NodeService nodeService;

    @Autowired
    @Qualifier("sinicubeAsyncThreadPool")
    private Executor threadExecutor;


    @Override
    public CompletableFuture<DispatchFlowResponse> dispatchWorkFlow(ChatDispatchProps props) {
        return CompletableFuture.supplyAsync(() -> executeWorkFlow(props), threadExecutor);
    }

    @Override
    @SiniCubeLimiter(mode = SiniCubeLimiterMode.SERVER, rate = "#outLink?.Limit?.QPM ?: 100", interval = "60", blocking = false)
    public CompletableFuture<DispatchFlowResponse> dispatchWorkFlowWithQpmLimit(ChatDispatchProps dispatchProps, OutLink outLink) {
        return dispatchWorkFlow(dispatchProps);
    }

    @Override
    public Map<String, Object> getSystemVariables(ChatDispatchProps props) {
        log.debug("开始获取系统变量");
        Map<String, Object> variables = new HashMap<>();

        // 处理聊天配置变量
        if (props.getChatConfig() != null && props.getChatConfig().getVariables() != null) {
            log.debug("处理聊天配置变量 - 变量数量: {}", props.getChatConfig().getVariables().size());
            props.getChatConfig().getVariables().forEach(variable -> {
                Object value = WorkflowUtil.valueTypeFormat(variable.getDefaultValue(), variable.getValueType());
                variables.put(variable.getKey(), value);
                log.debug("添加聊天配置变量 - 键: {}, 值类型: {}", variable.getKey(),
                    value != null ? value.getClass().getSimpleName() : "null");
            });
        } else {
            log.debug("无聊天配置变量需要处理");
        }

        // 添加系统变量
        log.debug("添加系统变量");
        variables.put("userId", props.getUid());
        variables.put("appId", props.getRunningAppInfo().getId());
        variables.put("chatId", props.getChatId());
        variables.put("responseChatItemId", props.getResponseChatItemId());
        variables.put("histories", props.getHistories());
        variables.put("cTime", getSystemTime(props.getTimezone()));

        log.debug("系统变量获取完成 - 总变量数量: {}", variables.size());
        return variables;
    }

    private DispatchFlowResponse executeWorkFlow(ChatDispatchProps data) {
        long startTime = System.currentTimeMillis();
        log.debug("开始执行工作流 - 应用ID: {}, 聊天ID: {}, 用户ID: {}",
                data.getRunningAppInfo().getId(), data.getChatId(), data.getUid());

        // 解构参数
        SseEmitter res = data.getRes();
        List<RuntimeNodeItemType> runtimeNodes = Optional.ofNullable(data.getRuntimeNodes()).orElse(new ArrayList<>());
        List<RuntimeEdgeItemType> runtimeEdges = Optional.ofNullable(data.getRuntimeEdges()).orElse(new ArrayList<>());
        List<ChatItemType> histories = Optional.ofNullable(data.getHistories()).orElse(new ArrayList<>());
        Map<String, Object> variables = new HashMap<>(Optional.ofNullable(data.getVariables()).orElse(new HashMap<>()));
        String timezone = data.getTimezone();
        ExternalProviderType externalProvider = data.getExternalProvider();
        boolean stream = data.getStream();
        Boolean retainDatasetCite = data.getRetainDatasetCite();
        String version = Optional.ofNullable(data.getVersion()).orElse("v1");
        Boolean responseDetail = data.getResponseDetail();
        Boolean responseAllData = data.getResponseAllData();

        log.debug("工作流参数解析完成 - 节点数量: {}, 边数量: {}, 历史记录数量: {}, 变量数量: {}",
                runtimeNodes.size(), runtimeEdges.size(), histories.size(), variables.size());

        WorkflowUtil.rewriteRuntimeWorkFlow(data.getRuntimeNodes(), data.getRuntimeEdges());
        log.debug("工作流重写完成");

        // 初始化深度和自动增加深度，避免无限嵌套
        Integer workflowDispatchDeep = data.getWorkflowDispatchDeep();
        if (workflowDispatchDeep == null) {
            data.setWorkflowDispatchDeep(1);
            workflowDispatchDeep = 1;
        } else {
            data.setWorkflowDispatchDeep(workflowDispatchDeep + 1);
            workflowDispatchDeep += 1;
        }

        boolean isRootRuntime = workflowDispatchDeep == 1;
        log.debug("工作流调度深度: {}, 是否根运行时: {}", workflowDispatchDeep, isRootRuntime);

        // 防止无限嵌套
        if (data.getWorkflowDispatchDeep() > 20) {
            log.warn("工作流调度深度超过限制(20)，终止执行 - 当前深度: {}", data.getWorkflowDispatchDeep());
            return DispatchFlowResponse.builder()
                    .flowResponses(new ArrayList<>())
                    .flowUsage(new ArrayList<>())
                    .debugResponse(DebugResponse.builder()
                            .finishedNodes(new ArrayList<>())
                            .finishedEdges(new ArrayList<>())
                            .nextStepRunNodes(new ArrayList<>())
                            .build())
                    .runTimes(1)
                    .assistantResponses(null)
                    .toolResponses(null)
                    .newVariables(WorkflowUtil.removeSystemVariable(data.getVariables(), data.getExternalProvider().getExternalWorkflowVariables()))
                    .durationSeconds(0.0)
                    .build();
        }
        int workflowRunTimes = 0;

        // 初始化
        if (isRootRuntime) {
            log.debug("根运行时初始化开始");

            // 变量优先级：请求>外部工作流>系统变量

            // 添加系统变量
            Map<String, Object> systemVariables = getSystemVariables(data);
            log.debug("系统变量获取完成 - 数量: {}", systemVariables.size());
            if (externalProvider.getExternalWorkflowVariables() != null) {
                log.debug("外部工作流变量添加 - 数量: {}", externalProvider.getExternalWorkflowVariables().size());
                systemVariables.putAll(externalProvider.getExternalWorkflowVariables());
            }
            systemVariables.putAll(variables);
            log.debug("根运行时初始化完成 - 总变量数量: {}", systemVariables.size());

            // 进行替换
            variables = systemVariables;
        }

        // 初始化响应数据
        WorkflowExecutionContext context = new WorkflowExecutionContext();
        context.setChatResponses(new ArrayList<>());
        context.setChatAssistantResponse(new ArrayList<>());
        context.setChatNodeUsages(new ArrayList<>());
        context.setDebugNextStepRunNodes(new ArrayList<>());
        context.setSystemMemories(new HashMap<>());
        context.setVariables(variables);
        context.setHistories(histories);
        context.setWorkflowRunTimes(workflowRunTimes);
        context.setProps(data);
        context.setQuery(data.getQuery());

        try {
            // 开始处理入口节点
            List<RuntimeNodeItemType> entryNodes = runtimeNodes.stream()
                    .filter(item -> item.getIsEntry() != null && item.getIsEntry())
                    .toList();

            log.debug("找到入口节点 - 数量: {}", entryNodes.size());
            if (log.isDebugEnabled()) {
                entryNodes.forEach(node ->
                    log.debug("入口节点详情 - ID: {}, 名称: {}, 类型: {}",
                        node.getNodeId(), node.getName(), node.getFlowNodeType()));
            }

            // 重置入口状态
            runtimeNodes.forEach(item -> {
                // 交互节点将使用"isEntry"，不需要更新
                if (!FlowNodeTypeEnum.USER_SELECT.getValue().equals(item.getFlowNodeType()) &&
                        !FlowNodeTypeEnum.FORM_INPUT.getValue().equals(item.getFlowNodeType()) &&
                        !FlowNodeTypeEnum.TOOLS.getValue().equals(item.getFlowNodeType())) {
                    item.setIsEntry(false);
                }
            });
            log.debug("入口状态重置完成");

            // 并行执行入口节点
            log.debug("开始并行执行入口节点");

            // 异步执行入口节点
            List<CompletableFuture<List<RuntimeNodeItemType>>> entryFutures = entryNodes.stream()
                    .map(node -> CompletableFuture.supplyAsync(() -> {
                        log.debug("开始异步执行入口节点 - ID: {}, 名称: {}", node.getNodeId(), node.getName());
                        return checkNodeCanRun(node, new HashSet<>(), runtimeNodes, runtimeEdges, context);
                    }, threadExecutor))
                    .toList();

            CompletableFuture.allOf(entryFutures.toArray(new CompletableFuture[0])).join();

            // 尝试运行插件输出模块
            Optional<RuntimeNodeItemType> pluginOutputModule = runtimeNodes.stream()
                    .filter(item -> FlowNodeTypeEnum.PLUGIN_OUTPUT.getValue().equals(item.getFlowNodeType()))
                    .findFirst();

            if (pluginOutputModule.isPresent()) {
                log.debug("找到插件输出模块 - ID: {}, 名称: {}",
                    pluginOutputModule.get().getNodeId(), pluginOutputModule.get().getName());
                if (!"debug".equals(data.getMode())) {
                    log.debug("执行插件输出模块");
                    nodeRunWithActive(pluginOutputModule.get(), runtimeNodes, runtimeEdges, context);
                } else {
                    log.debug("调试模式，跳过插件输出模块执行");
                }
            } else {
                log.debug("未找到插件输出模块");
            }

            // 处理交互节点
            WorkflowInteractiveResponseType interactiveResult = null;
            if (context.getNodeInteractiveResponse() != null) {
                log.debug("处理交互节点响应 - 入口节点数量: {}",
                    context.getNodeInteractiveResponse().getEntryNodeIds().size());
                AIChatItemValueItemType interactiveAssistant = handleInteractiveResult(
                        context.getNodeInteractiveResponse().getEntryNodeIds(),
                        context.getNodeInteractiveResponse().getInteractiveResponse(),
                        runtimeNodes, runtimeEdges, data, isRootRuntime
                );
                if (isRootRuntime) {
                    context.getChatAssistantResponse().add(interactiveAssistant);
                    log.debug("交互响应已添加到助手响应列表");
                }
                interactiveResult = (WorkflowInteractiveResponseType)interactiveAssistant.getInteractive();
            } else {
                log.debug("无交互节点响应需要处理");
            }

            double durationSeconds = (System.currentTimeMillis() - startTime) / 1000.0;
            log.debug("工作流执行完成 - 总耗时: {}秒", durationSeconds);

            // 输出工作流执行时长
            if (isRootRuntime && stream) {
                log.debug("发送工作流执行时长信息");
                SseEmitter res1 = data.getRes();
                sendWorkflowDuration(data, durationSeconds);
            }

            return DispatchFlowResponse.builder()
                    .flowResponses(context.getChatResponses())
                    .flowUsage(context.getChatNodeUsages())
                    .debugResponse(DebugResponse.builder()
                            .finishedNodes(runtimeNodes)
                            .finishedEdges(runtimeEdges)
                            .nextStepRunNodes(context.getDebugNextStepRunNodes())
                            .build())
                    .workflowInteractiveResponse(interactiveResult)
                    .runTimes(context.getWorkflowRunTimes())
                    .assistantResponses(mergeAssistantResponseAnswerText(context.getChatAssistantResponse()))
                    .toolResponses(context.getToolRunResponse())
                    .newVariables(WorkflowUtil.removeSystemVariable(context.getVariables(), externalProvider.getExternalWorkflowVariables()))
                    .system_memories(context.getSystemMemories().isEmpty() ? null : context.getSystemMemories())
                    .durationSeconds(durationSeconds)
                    .build();
        } catch (Exception error) {
            log.error("工作流执行异常 - 应用ID: {}, 聊天ID: {}, 执行时长: {}ms",
                data.getRunningAppInfo().getId(), data.getChatId(),
                System.currentTimeMillis() - startTime, error);
            return null;
        }

    }

    /**
     * 检查节点是否可以运行
     */
    private List<RuntimeNodeItemType> checkNodeCanRun(
            RuntimeNodeItemType node,
            Set<String> skippedNodeIdList,
            List<RuntimeNodeItemType> runtimeNodes,
            List<RuntimeEdgeItemType> runtimeEdges,
            WorkflowExecutionContext context) {

        log.debug("开始检查节点是否可以运行 - 节点ID: {}, 节点名称: {}, 节点类型: {}",
            node.getNodeId(), node.getName(), node.getFlowNodeType());

        if (context.getProps().getRes() == null ||
                context.getProps().getMaxRunTimes() <= 0) {
            log.debug("节点检查终止 - 响应为空或最大运行次数已用完 - 节点: {}, 最大运行次数: {}",
                node.getName(), context.getProps().getMaxRunTimes());
            return new ArrayList<>();
        }

        // 线程让步
        surrenderProcess();

        log.info("运行节点: {}, 最大运行次数: {}, 应用ID: {}",
                node.getName(), context.getProps().getMaxRunTimes(), context.getProps().getRunningAppInfo().getId());

        // 通过边获取节点运行状态
        String status = checkNodeRunStatus(node, runtimeEdges);
        log.debug("节点运行状态检查结果 - 节点: {}, 状态: {}", node.getName(), status);

        NodeRunResult nodeRunResult = null;
        if ("run".equals(status)) {
            log.debug("节点状态为运行 - 执行nodeRunWithActive - 节点: {}", node.getName());
            nodeRunBeforeHook(node, runtimeEdges);
            log.info("[dispatchWorkFlow] nodeRunWithActive: {}", node.getName());
            nodeRunResult = nodeRunWithActive(node, runtimeNodes, runtimeEdges, context);
                        log.debug("节点运行完成 - 节点: {}, 运行状态: {}", node.getName(),
                nodeRunResult != null ? nodeRunResult.getRunStatus() : "null");
        } else if ("skip".equals(status) && !skippedNodeIdList.contains(node.getNodeId())) {
            log.debug("节点状态为跳过且未在跳过列表中 - 执行nodeRunWithSkip - 节点: {}", node.getName());
            nodeRunBeforeHook(node, runtimeEdges);
            context.getProps().setMaxRunTimes(context.getProps().getMaxRunTimes() - 0.1);
            skippedNodeIdList.add(node.getNodeId());
            log.info("[dispatchWorkFlow] nodeRunWithSkip: {}", node.getName());
            nodeRunResult = nodeRunWithSkip(node, runtimeEdges);
            log.debug("节点跳过完成 - 节点: {}", node.getName());
        } else {
            log.debug("节点不满足运行或跳过条件 - 节点: {}, 状态: {}, 已在跳过列表: {}",
                node.getName(), status, skippedNodeIdList.contains(node.getNodeId()));
        }

        if (nodeRunResult == null) {
            log.debug("节点运行结果为空，返回空列表 - 节点: {}", node.getName());
            return new ArrayList<>();
        }

        // 特殊情况：通过skipEdges可以判断是运行了分支节点
        List<String> skipEdges = (List<String>) nodeRunResult.getResult().get("skipHandleId");
        if (skipEdges != null && !skipEdges.isEmpty()) {
            log.debug("检测到分支节点跳过边 - 节点: {}, 跳过边数量: {}", node.getName(), skipEdges.size());
            skippedNodeIdList.add(node.getNodeId());
        }

        // 当前版本只允许同时有一个交互节点
        Object interactiveResponse = nodeRunResult.getResult().get("interactive");
        if (interactiveResponse != null) {
            log.debug("检测到交互响应 - 节点: {}, 设置交互响应并终止后续节点执行", node.getName());
            pushStore(nodeRunResult.getNode(), nodeRunResult.getResult(), context);

            if ("debug".equals(context.getProps().getMode())) {
                log.debug("调试模式 - 添加节点到下一步运行节点列表: {}", node.getName());
                context.getDebugNextStepRunNodes().add(nodeRunResult.getNode());
            }

            context.setNodeInteractiveResponse(NodeInteractiveResponse.builder()
                    .entryNodeIds(List.of(nodeRunResult.getNode().getNodeId()))
                    .interactiveResponse((InteractiveNodeType) interactiveResponse)
                    .build());
            log.debug("交互响应设置完成，返回空列表终止执行");
            return new ArrayList<>();
        }

        // 在运行结束时更新节点输出并获取下一个节点
        log.debug("开始处理节点输出并获取下一个节点 - 当前节点: {}", node.getName());
        NodeOutputResult outputResult = nodeOutput(nodeRunResult.getNode(), nodeRunResult.getResult(),
                runtimeNodes, runtimeEdges, context);

        List<RuntimeNodeItemType> nextStepActiveNodes = outputResult.getNextStepActiveNodes();
        List<RuntimeNodeItemType> nextStepSkipNodes = outputResult.getNextStepSkipNodes();

        log.debug("节点输出处理完成 - 当前节点: {}, 下一步活跃节点数量: {}, 下一步跳过节点数量: {}",
            node.getName(), nextStepActiveNodes.size(), nextStepSkipNodes.size());

        // 去除重复节点（确保节点只执行一次）
        nextStepActiveNodes = removeDuplicateNodes(nextStepActiveNodes);
        nextStepSkipNodes = removeDuplicateNodes(nextStepSkipNodes);

        log.debug("去重后 - 下一步活跃节点数量: {}, 下一步跳过节点数量: {}",
            nextStepActiveNodes.size(), nextStepSkipNodes.size());

        // 运行下一个节点（先运行run的，再运行skip的）
        log.debug("开始并行执行下一步活跃节点 - 数量: {}", nextStepActiveNodes.size());
        if (log.isDebugEnabled()) {
            nextStepActiveNodes.forEach(nextNode ->
                log.debug("准备执行活跃节点 - ID: {}, 名称: {}", nextNode.getNodeId(), nextNode.getName()));
        }

        // 异步执行下一步活跃节点

        List<CompletableFuture<List<RuntimeNodeItemType>>> activeFutures = nextStepActiveNodes.stream()
                .map(nextNode -> CompletableFuture.supplyAsync(() -> {
                    log.debug("开始异步执行活跃节点 - ID: {}, 名称: {}", nextNode.getNodeId(), nextNode.getName());
                    return checkNodeCanRun(nextNode, skippedNodeIdList, runtimeNodes, runtimeEdges, context);
                }, threadExecutor))
                .toList();

        List<RuntimeNodeItemType> nextStepActiveNodesResults = activeFutures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .toList();

        log.debug("活跃节点执行完成 - 结果节点数量: {}", nextStepActiveNodesResults.size());

        // 如果已经active运行过，不再执行skip（active中有闭环）
        nextStepSkipNodes = nextStepSkipNodes.stream()
                .filter(node1 -> nextStepActiveNodesResults.stream()
                        .noneMatch(item -> item.getNodeId().equals(node1.getNodeId())))
                .collect(Collectors.toList());

        log.debug("过滤已在活跃结果中的跳过节点 - 原数量: {}, 过滤后数量: {}",
                nextStepSkipNodes.size(), nextStepSkipNodes.size());

        log.debug("开始并行执行下一步跳过节点 - 数量: {}", nextStepSkipNodes.size());
        if (log.isDebugEnabled()) {
            nextStepSkipNodes.forEach(nextNode ->
                log.debug("准备执行跳过节点 - ID: {}, 名称: {}", nextNode.getNodeId(), nextNode.getName()));
        }

        // 异步执行下一步跳过节点
        List<CompletableFuture<List<RuntimeNodeItemType>>> skipFutures = nextStepSkipNodes.stream()
                .map(nextNode -> CompletableFuture.supplyAsync(() -> {
                    log.debug("开始异步执行跳过节点 - ID: {}, 名称: {}", nextNode.getNodeId(), nextNode.getName());
                    return checkNodeCanRun(nextNode, skippedNodeIdList, runtimeNodes, runtimeEdges, context);
                }, threadExecutor))
                .toList();

        List<RuntimeNodeItemType> nextStepSkipNodesResults = skipFutures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .toList();

        log.debug("跳过节点执行完成 - 结果节点数量: {}", nextStepSkipNodesResults.size());

        if (context.getProps().getRes() == null) {
            log.warn("请求已关闭, 应用ID: {}, 节点ID: {}, 节点名称: {}",
                    context.getProps().getRunningAppInfo().getId(), node.getNodeId(), node.getName());
            return new ArrayList<>();
        }

        List<RuntimeNodeItemType> result = new ArrayList<>();
        result.addAll(nextStepActiveNodes);
        result.addAll(nextStepSkipNodes);
        result.addAll(nextStepActiveNodesResults);
        result.addAll(nextStepSkipNodesResults);

        log.debug("节点检查完成 - 当前节点: {}, 返回结果节点数量: {}", node.getName(), result.size());
        return result;
    }

    private NodeRunResult nodeRunWithSkip(RuntimeNodeItemType node, List<RuntimeEdgeItemType> runtimeEdges) {
        // 跳过节点运行逻辑
        List<String> skipHandleIds = runtimeEdges.stream()
                .filter(item -> node.getNodeId().equals(item.getSource()))
                .map(RuntimeEdgeItemType::getSourceHandle)
                .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("skipHandleId", skipHandleIds);

        return NodeRunResult.builder()
                .node(node)
                .runStatus("skip")
                .result(result)
                .build();
    }

    private NodeRunResult nodeRunWithActive(RuntimeNodeItemType node, List<RuntimeNodeItemType> runtimeNodes,
                                            List<RuntimeEdgeItemType> runtimeEdges, WorkflowExecutionContext context) {
        log.debug("开始活跃运行节点 - ID: {}, 名称: {}, 类型: {}",
            node.getNodeId(), node.getName(), node.getFlowNodeType());

        // 推送运行状态消息
        if (Boolean.TRUE.equals(node.getShowStatus()) && !Boolean.TRUE.equals(context.getProps().getIsToolCall())) {
            log.debug("推送节点运行状态消息 - 节点: {}", node.getName());
            if (context.getProps().getWorkflowStreamResponse() != null) {
                FlowNodeStatusResponse flowNodeStatusResponse = FlowNodeStatusResponse.builder()
                        .status("running")
                        .name(node.getName())
                        .build();

                WorkflowStreamResponse streamResponse =
                        WorkflowStreamResponse.builder()
                                .event(SseResponseEventEnum.FLOW_NODE_STATUS.getValue())
                                .data(flowNodeStatusResponse)
                                .build();
                context.getProps().getWorkflowStreamResponse().accept(streamResponse);
                log.debug("节点运行状态消息推送完成");
            } else {
                log.debug("工作流流响应为空，跳过状态消息推送");
            }
        } else {
            log.debug("节点不显示状态或为工具调用，跳过状态消息推送 - 显示状态: {}, 工具调用: {}",
                node.getShowStatus(), context.getProps().getIsToolCall());
        }

        long startTime = System.currentTimeMillis();
        log.debug("节点开始执行时间记录 - 节点: {}", node.getName());

        // 获取节点运行参数
        log.debug("开始获取节点运行参数 - 节点: {}", node.getName());
        Map<String, Object> params = getNodeRunParams(node, runtimeNodes, context.getVariables());
        log.debug("节点运行参数获取完成 - 节点: {}, 参数数量: {}", node.getName(), params.size());

        // 构建调度数据
        log.debug("开始构建模块调度数据 - 节点: {}", node.getName());
        ModuleDispatchProps dispatchData = ModuleDispatchProps.builder()
                .res(context.getProps().getRes())
                .requestOrigin(context.getProps().getRequestOrigin())
                .runningAppInfo(context.getProps().getRunningAppInfo())
                .runningUserInfo(context.getProps().getRunningUserInfo())
                .uid(context.getProps().getUid())
                .chatId(context.getProps().getChatId())
                .responseChatItemId(context.getProps().getResponseChatItemId())
                .chatConfig(context.getProps().getChatConfig())
                .lastInteractive(context.getProps().getLastInteractive())
                .maxRunTimes(context.getProps().getMaxRunTimes())
                .isToolCall(context.getProps().getIsToolCall())
                .workflowDispatchDeep(context.getProps().getWorkflowDispatchDeep())
                .version(context.getProps().getVersion())
                .responseAllData(context.getProps().getResponseAllData())
                .responseDetail(context.getProps().getResponseDetail())
                .variables(context.getVariables())
                .histories(context.getHistories())
                .timezone(context.getProps().getTimezone())
                .externalProvider(context.getProps().getExternalProvider())
                .stream(context.getProps().getStream())
                .workflowStreamResponse(context.getProps().getWorkflowStreamResponse())
                .retainDatasetCite(context.getProps().getRetainDatasetCite())
                .node(node)
                .runtimeNodes(runtimeNodes)
                .runtimeEdges(runtimeEdges)
                .params(params)
                .query(context.getQuery())
                .mode("debug".equals(context.getProps().getMode()) ? "test" : context.getProps().getMode())
                .build();
        log.debug("模块调度数据构建完成 - 节点: {}", node.getName());

        // 运行模块
        Map<String, Object> dispatchRes = new HashMap<>();
        try {
            log.debug("开始执行节点逻辑 - 节点: {}, 类型: {}", node.getName(), node.getFlowNodeType());
            // 这里应该调用具体的节点处理器
            dispatchRes = executeNodeLogic(node, dispatchData);
            log.debug("节点逻辑执行完成 - 节点: {}, 响应数据键数量: {}", node.getName(), dispatchRes.size());
        } catch (Exception error) {
            log.error("节点执行异常 - 节点: {}, 类型: {}", node.getName(), node.getFlowNodeType(), error);
            // 获取输出边的源句柄
            List<String> skipHandleIds = runtimeEdges.stream()
                    .filter(item -> node.getNodeId().equals(item.getSource()))
                    .map(RuntimeEdgeItemType::getSourceHandle)
                    .collect(Collectors.toList());

            log.debug("节点执行异常处理 - 跳过边数量: {}", skipHandleIds.size());
            // TODO getErrText(error)方法
            context.setToolRunResponse(error.getMessage());

            // 跳过所有边并返回错误
            // TODO formatHttpError
            dispatchRes.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                    .error(node.getName() + ":" + error.getMessage())
                    .build());
            dispatchRes.put("skipHandleId", skipHandleIds);
        }

        // TODO 剩余参数 ；格式化响应数据，添加模块名称和模块类型
        ChatHistoryItemResType formatResponseData = null;
        if (!Objects.isNull(dispatchRes.get(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue()))) {
            DispatchNodeResponseType responseData = (DispatchNodeResponseType) dispatchRes.get(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue());
            formatResponseData = new ChatHistoryItemResType();
            formatResponseData.setId(WorkflowUtil.getNanoid(21));
            formatResponseData.setNodeId(node.getNodeId());
            formatResponseData.setModuleName(node.getName());
            formatResponseData.setModuleType(node.getFlowNodeType());
            // 设置剩余的数据
            BeanUtils.copyProperties(responseData, formatResponseData);

            // 防止被覆盖
            formatResponseData.setRunningTime((System.currentTimeMillis() - startTime) / 1000.0);
        }


        // 响应节点响应
        if ("v2".equals(context.getProps().getVersion()) &&
                !Boolean.TRUE.equals(context.getProps().getIsToolCall()) &&
                context.getProps().getWorkflowDispatchDeep() == 1 &&
                formatResponseData != null) {

            if (context.getProps().getWorkflowStreamResponse() != null) {
                ChatHistoryItemResType responseData = Boolean.TRUE.equals(context.getProps().getResponseAllData()) ?
                        formatResponseData :
                        WorkflowUtil.filterPublicNodeResponseData(List.of(formatResponseData),
                                Boolean.TRUE.equals(context.getProps().getResponseDetail())).get(0);

                WorkflowStreamResponse streamResponse =
                        WorkflowStreamResponse.builder()
                                .event(SseResponseEventEnum.FLOW_NODE_RESPONSE.getValue())
                                .data(responseData)
                                .build();
                context.getProps().getWorkflowStreamResponse().accept(streamResponse);
            }
        }

        // 添加输出默认值
        if (node.getOutputs() != null) {
            for (FlowNodeOutputItemType output : node.getOutputs()) {
                if (Boolean.TRUE.equals(output.getRequired()) &&
                        dispatchRes.get(output.getKey()) == null &&
                        output.getDefaultValue() != null) {
                    dispatchRes.put(output.getKey(), WorkflowUtil.valueTypeFormat(output.getDefaultValue(), output.getValueType()));
                }
            }
        }

        // 更新新变量
        Map<String, Object> newVariables = (Map<String, Object>) dispatchRes.get("newVariables");
        if (newVariables != null) {
            context.getVariables().putAll(newVariables);
        }

        // 错误处理
        if (formatResponseData != null && formatResponseData.getError() != null) {
            log.warn("工作流错误: {}", formatResponseData.getError());
        }

        Map<String, Object> result = new HashMap<>(dispatchRes);
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), formatResponseData);

        return NodeRunResult.builder()
                .node(node)
                .runStatus("run")
                .result(result)
                .build();
    }

    private AIChatItemValueItemType handleInteractiveResult(List<String> entryNodeIds, Object interactiveResponse,
                                                    List<RuntimeNodeItemType> runtimeNodes, List<RuntimeEdgeItemType> runtimeEdges,
                                                    ChatDispatchProps props, boolean isRootRuntime) {

        List<NodeOutputItemType> nodeOutputs = new ArrayList<>();
        runtimeNodes.forEach(item -> {
            item.getOutputs().forEach(itemOutput -> {
                if (Boolean.TRUE.equals(itemOutput.getValue())) {
                    NodeOutputItemType nodeOutputItemType = new NodeOutputItemType();
                    nodeOutputItemType.setKey(itemOutput.getKey());
                    nodeOutputItemType.setValue(itemOutput.getValue());
                    nodeOutputItemType.setNodeId(item.getNodeId());
                    nodeOutputs.add(nodeOutputItemType);
                }
            });
        });
        List<RuntimeEdgeItemType> me = runtimeEdges.stream().map(item -> {
            RuntimeEdgeItemType runtimeEdgeItemType = new RuntimeEdgeItemType();
            BeanUtils.copyProperties(item, runtimeEdgeItemType);
            if (entryNodeIds.contains(item.getTarget())) {
                runtimeEdgeItemType.setStatus("active");
            }
            return runtimeEdgeItemType;
        }).toList();
        WorkflowInteractiveResponseType interactiveResult = WorkflowInteractiveResponseType.builder()
                .nodeResponse((InteractiveNodeType) interactiveResponse)
                .entryNodeIds(entryNodeIds)
                .nodeOutputs(nodeOutputs)
                .memoryEdges(me)
                .build();

        if (props.getIsToolCall() && isRootRuntime) {
            WorkflowStreamResponse streamResponse =
                    WorkflowStreamResponse.builder()
                            .event(SseResponseEventEnum.INTERACTIVE.getValue())
                            .data(interactiveResult)
                            .build();
            props.getWorkflowStreamResponse().accept(streamResponse);
        }
        AIChatItemValueItemType type = new AIChatItemValueItemType();
        type.setType(ChatItemValueType.INTERACTIVE.getValue());
        type.setInteractive(interactiveResult);
        // 处理交互结果
        return type;
    }

    private List<RuntimeNodeItemType> removeDuplicateNodes(List<RuntimeNodeItemType> nodes) {
        return new ArrayList<>(nodes.stream()
                .collect(Collectors.toMap(
                        RuntimeNodeItemType::getNodeId,
                        node -> node,
                        (existing, replacement) -> existing))
                .values());
    }

    private void sendWorkflowDuration(ChatDispatchProps props, double durationSeconds) {
        // 发送工作流持续时间
        if (props.getWorkflowStreamResponse() != null) {
            WorkflowDurationResponse workflowDurationResponse = WorkflowDurationResponse.builder()
                    .durationSeconds(durationSeconds)
                    .build();

            WorkflowStreamResponse streamResponse =
                    WorkflowStreamResponse.builder()
                            .event(SseResponseEventEnum.WORKFLOW_DURATION.getValue())
                            .data(workflowDurationResponse)
                            .build();
            props.getWorkflowStreamResponse().accept(streamResponse);
        }
    }

    private List<AIChatItemValueItemType> mergeAssistantResponseAnswerText(List<AIChatItemValueItemType> responses) {
        // 合并助手响应文本
        return WorkflowUtil.mergeAssistantResponseAnswerText(responses);
    }

    /**
     * 获取节点运行参数
     */
    private Map<String, Object> getNodeRunParams(RuntimeNodeItemType node, List<RuntimeNodeItemType> runtimeNodes, Map<String, Object> variables) {
        if (FlowNodeTypeEnum.PLUGIN_INPUT.getValue().equals(node.getFlowNodeType())) {
            // 格式化插件输入为对象
            Map<String, Object> params = new HashMap<>();
            if (node.getInputs() != null) {
                node.getInputs().forEach(item -> {
                    params.put(item.getKey(), WorkflowUtil.valueTypeFormat(item.getValue(), item.getValueType()));
                });
            }
            return params;
        }

        // 动态输入需要存储一个键
        FlowNodeInputItemType dynamicInput = null;
        if (node.getInputs() != null) {
            dynamicInput = node.getInputs().stream()
                    .filter(item -> item.getRenderTypeList() != null &&
                            item.getRenderTypeList().contains("addInputParam"))
                    .findFirst().orElse(null);
        }

        Map<String, Object> params = new HashMap<>();
        if (dynamicInput != null) {
            params.put(dynamicInput.getKey(), new HashMap<>());
        }

        if (node.getInputs() != null) {
            FlowNodeInputItemType finalDynamicInput = dynamicInput;
            node.getInputs().forEach(input -> {
                // 特殊输入，不格式化
                if (finalDynamicInput != null && input.getKey().equals(finalDynamicInput.getKey())) {
                    return;
                }

                // 跳过一些特殊键
                if ("childrenNodeIdList".equals(input.getKey()) || "system_httpJsonBody".equals(input.getKey())) {
                    params.put(input.getKey(), input.getValue());
                    return;
                }

                // 替换{{$xx.xx$}}和{{xx}}变量
                Object value = WorkflowUtil.replaceEditorVariable(input.getValue(), runtimeNodes, variables);

                // 替换引用变量
                value = WorkflowUtil.getReferenceVariableValue(value, runtimeNodes, variables);

                // 动态输入存储在动态键中
                if (Boolean.TRUE.equals(input.getCanEdit()) && finalDynamicInput != null &&
                        params.get(finalDynamicInput.getKey()) instanceof Map) {
                    ((Map<String, Object>) params.get(finalDynamicInput.getKey()))
                            .put(input.getKey(), WorkflowUtil.valueTypeFormat(value, input.getValueType()));
                }

                params.put(input.getKey(), WorkflowUtil.valueTypeFormat(value, input.getValueType()));
            });
        }

        return params;
    }

    /**
     * 执行节点逻辑（模拟实现）
     */
    private Map<String, Object> executeNodeLogic(RuntimeNodeItemType node, ModuleDispatchProps dispatchData) {
        // 这里应该根据节点类型调用相应的处理器
        // 目前返回模拟数据
        return nodeService.processNode(node.getFlowNodeType(), dispatchData);
    }

    private void surrenderProcess() {
        // 线程让步
        try {
            Thread.sleep(1);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private String checkNodeRunStatus(RuntimeNodeItemType node, List<RuntimeEdgeItemType> runtimeEdges) {
        // 检查节点运行状态
        String status = WorkflowUtil.checkNodeRunStatus(node, runtimeEdges);
        log.debug("节点运行状态检查 - 节点: {}, 状态: {}", node.getName(), status);
        return status;
    }

    private void nodeRunBeforeHook(RuntimeNodeItemType node, List<RuntimeEdgeItemType> runtimeEdges) {
        // 节点运行前钩子
        log.debug("执行节点运行前钩子 - 节点: {}", node.getName());
        int updatedEdges = 0;
        for (RuntimeEdgeItemType item : runtimeEdges) {
            if (node.getNodeId().equals(item.getTarget())) {
                String oldStatus = item.getStatus();
                item.setStatus("waiting");
                log.debug("边状态更新为等待 - 边: {} -> {}, 原状态: {}", item.getSource(), item.getTarget(), oldStatus);
                updatedEdges++;
            }
        }
        log.debug("节点运行前钩子完成 - 节点: {}, 更新边数量: {}", node.getName(), updatedEdges);
    }

    private NodeOutputResult nodeOutput(RuntimeNodeItemType node, Map<String, Object> result,
                                        List<RuntimeNodeItemType> runtimeNodes, List<RuntimeEdgeItemType> runtimeEdges,
                                        WorkflowExecutionContext context) {
        log.debug("开始处理节点输出 - 节点: {}, 结果键数量: {}", node.getName(), result.size());

        // 节点输出处理逻辑
        pushStore(node, result, context);
        log.debug("节点存储推送完成 - 节点: {}", node.getName());

        // 分配输出值到下一个节点
        if (node.getOutputs() != null) {
            log.debug("开始分配输出值 - 节点: {}, 输出项数量: {}", node.getName(), node.getOutputs().size());
            node.getOutputs().forEach(outputItem -> {
                if (result.containsKey(outputItem.getKey())) {
                    log.debug("分配输出值 - 节点: {}, 输出键: {}, 值类型: {}",
                        node.getName(), outputItem.getKey(),
                        result.get(outputItem.getKey()) != null ? result.get(outputItem.getKey()).getClass().getSimpleName() : "null");
                    outputItem.setValue(result.get(outputItem.getKey()));
                } else {
                    log.debug("输出键不存在于结果中 - 节点: {}, 输出键: {}", node.getName(), outputItem.getKey());
                }
            });
        } else {
            log.debug("节点无输出项 - 节点: {}", node.getName());
        }

        // 获取下一个源边并更新状态
        List<String> skipHandleId = (List<String>) result.getOrDefault("skipHandleId", new ArrayList<>());
        log.debug("获取跳过句柄ID - 节点: {}, 跳过句柄数量: {}", node.getName(), skipHandleId.size());

        List<RuntimeEdgeItemType> filterEdges = runtimeEdges.stream()
                .filter(edge -> !"selectedTools".equals(edge.getSourceHandle()) && !"selectedTools".equals(edge.getTargetHandle()))
                .toList();
        log.debug("过滤边完成 - 原边数量: {}, 过滤后数量: {}", runtimeEdges.size(), filterEdges.size());

        List<RuntimeEdgeItemType> targetEdges = filterEdges.stream()
                .filter(item -> node.getNodeId().equals(item.getSource()))
                .toList();
        log.debug("获取目标边 - 节点: {}, 目标边数量: {}", node.getName(), targetEdges.size());

        // 更新边状态
        targetEdges.forEach(edge -> {
            String oldStatus = edge.getStatus();
            if (skipHandleId.contains(edge.getSourceHandle())) {
                edge.setStatus("skipped");
                log.debug("边状态更新为跳过 - 边: {} -> {}, 句柄: {}, 原状态: {}",
                    edge.getSource(), edge.getTarget(), edge.getSourceHandle(), oldStatus);
            } else {
                edge.setStatus("active");
                log.debug("边状态更新为活跃 - 边: {} -> {}, 句柄: {}, 原状态: {}",
                    edge.getSource(), edge.getTarget(), edge.getSourceHandle(), oldStatus);
            }
        });

        List<RuntimeNodeItemType> nextStepActiveNodes = new ArrayList<>();
        List<RuntimeNodeItemType> nextStepSkipNodes = new ArrayList<>();

        log.debug("开始确定下一步节点 - 当前节点: {}, 运行时节点总数: {}", node.getName(), runtimeNodes.size());

        runtimeNodes.forEach(runtimeNode -> {
            boolean hasActiveEdge = targetEdges.stream()
                    .anyMatch(item -> runtimeNode.getNodeId().equals(item.getTarget()) && "active".equals(item.getStatus()));
            boolean hasSkippedEdge = targetEdges.stream()
                    .anyMatch(item -> runtimeNode.getNodeId().equals(item.getTarget()) && "skipped".equals(item.getStatus()));

            if (hasActiveEdge) {
                nextStepActiveNodes.add(runtimeNode);
                log.debug("添加到下一步活跃节点 - 节点: {}, 来源: {}", runtimeNode.getName(), node.getName());
            }
            if (hasSkippedEdge) {
                nextStepSkipNodes.add(runtimeNode);
                log.debug("添加到下一步跳过节点 - 节点: {}, 来源: {}", runtimeNode.getName(), node.getName());
            }
        });

        log.debug("下一步节点确定完成 - 当前节点: {}, 活跃节点数量: {}, 跳过节点数量: {}",
            node.getName(), nextStepActiveNodes.size(), nextStepSkipNodes.size());

        if ("debug".equals(context.getProps().getMode())) {
            if (context.getProps().getLastInteractive() != null) {
                context.getDebugNextStepRunNodes().addAll(nextStepActiveNodes);
            } else {
                context.getDebugNextStepRunNodes().addAll(nextStepActiveNodes);
                context.getDebugNextStepRunNodes().addAll(nextStepSkipNodes);
            }
            return NodeOutputResult.builder()
                    .nextStepActiveNodes(new ArrayList<>())
                    .nextStepSkipNodes(new ArrayList<>())
                    .build();
        }

        return NodeOutputResult.builder()
                .nextStepActiveNodes(nextStepActiveNodes)
                .nextStepSkipNodes(nextStepSkipNodes)
                .build();
    }

    /**
     * 存储特殊响应字段
     */
    private void pushStore(RuntimeNodeItemType node, Map<String, Object> result, WorkflowExecutionContext context) {
        log.debug("开始存储节点响应字段 - 节点: {}", node.getName());

        // 添加运行次数
        Integer runTimes = (Integer) result.getOrDefault("runTimes", 1);
        int oldWorkflowRunTimes = context.getWorkflowRunTimes();
        double oldMaxRunTimes = context.getProps().getMaxRunTimes();
        context.setWorkflowRunTimes(context.getWorkflowRunTimes() + runTimes);
        context.getProps().setMaxRunTimes(context.getProps().getMaxRunTimes() - runTimes);
        log.debug("运行次数更新 - 节点: {}, 本次运行次数: {}, 工作流总运行次数: {} -> {}, 最大运行次数: {} -> {}",
            node.getName(), runTimes, oldWorkflowRunTimes, context.getWorkflowRunTimes(),
            oldMaxRunTimes, context.getProps().getMaxRunTimes());

        // 更新系统内存
        Map<String, Object> newMemories = (Map<String, Object>) result.get("systemMemories");
        if (newMemories != null) {
            log.debug("更新系统内存 - 节点: {}, 新内存项数量: {}", node.getName(), newMemories.size());
            context.getSystemMemories().putAll(newMemories);
        } else {
            log.debug("无系统内存更新 - 节点: {}", node.getName());
        }

        // 添加响应数据
        DispatchNodeResponseType responseData = (DispatchNodeResponseType) result.get(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue());
        if (responseData != null) {
            log.debug("添加响应数据 - 节点: {}, 响应数据: {}", node.getName(), responseData);
            try {
                ChatHistoryItemResType chatHistoryItemResType = new ChatHistoryItemResType();
                BeanUtils.copyProperties(responseData, chatHistoryItemResType);
                List<ChatHistoryItemResType> chatResponses = context.getChatResponses();
                chatResponses.add(chatHistoryItemResType);
                log.debug("响应数据添加成功 - 节点: {}, 当前响应总数: {}", node.getName(), chatResponses.size());
            } catch (Exception e) {
                log.warn("响应数据类型转换异常 - 节点: {}", node.getName(), e);
            }
        } else {
            log.debug("无响应数据需要添加 - 节点: {}", node.getName());
        }

        // 添加节点使用情况
        List<ChatNodeUsageType> nodeDispatchUsages = (List<ChatNodeUsageType>) result.get("nodeDispatchUsages");
        if (nodeDispatchUsages != null) {
            log.debug("添加节点使用情况 - 节点: {}, 使用情况数量: {}", node.getName(), nodeDispatchUsages.size());
            context.getChatNodeUsages().addAll(nodeDispatchUsages);
        } else {
            log.debug("无节点使用情况需要添加 - 节点: {}", node.getName());
        }

        // 处理工具响应
        Object toolResponses = result.get("toolResponses");
        if (toolResponses != null) {
            if (toolResponses instanceof List && ((List<?>) toolResponses).isEmpty()) {
                log.debug("工具响应为空列表，跳过处理 - 节点: {}", node.getName());
                return;
            }
            if (toolResponses instanceof Map && ((Map<?, ?>) toolResponses).isEmpty()) {
                log.debug("工具响应为空映射，跳过处理 - 节点: {}", node.getName());
                return;
            }
            log.debug("设置工具响应 - 节点: {}, 响应类型: {}", node.getName(), toolResponses.getClass().getSimpleName());
            context.setToolRunResponse(toolResponses);
        } else {
            log.debug("无工具响应需要处理 - 节点: {}", node.getName());
        }

        // 历史记录存储
        List<AIChatItemValueItemType> assistantResponses = (List<AIChatItemValueItemType>) result.get("assistantResponses");
        if (assistantResponses != null) {
            log.debug("添加助手响应 - 节点: {}, 响应数量: {}", node.getName(), assistantResponses.size());
            context.getChatAssistantResponse().addAll(assistantResponses);
        } else {
            log.debug("无助手响应，处理文本响应 - 节点: {}", node.getName());
            handleTextResponses(node, result, context);
        }

        // 重写历史记录
        List<ChatItemType> rewriteHistories = (List<ChatItemType>) result.get("rewriteHistories");
        if (rewriteHistories != null) {
            log.debug("重写历史记录 - 节点: {}, 新历史记录数量: {}", node.getName(), rewriteHistories.size());
            context.setHistories(rewriteHistories);
        } else {
            log.debug("无历史记录重写 - 节点: {}", node.getName());
        }

        log.debug("节点响应字段存储完成 - 节点: {}", node.getName());
    }

    /**
     * 处理文本响应
     */
    private void handleTextResponses(RuntimeNodeItemType node, Map<String, Object> result, WorkflowExecutionContext context) {
        String reasoningText = (String) result.get("reasoningText");
        String answerText = (String) result.get("answerText");

        if (reasoningText != null) {
            boolean isResponseReasoningText = node.getInputs().stream()
                    .anyMatch(item -> "aiChatReasoning".equals(item.getKey()) && Boolean.TRUE.equals(item.getValue()));
            if (isResponseReasoningText) {
                AIChatItemValueItemType reasoningItem = new AIChatItemValueItemType();
                reasoningItem.setType(ChatItemValueType.TEXT.getValue());
                reasoningItem.setReasoning(ReasoningContent.builder().content(reasoningText).build());
                context.getChatAssistantResponse().add(reasoningItem);
            }
        }

        if (answerText != null) {
            boolean isResponseAnswerText = node.getInputs().stream()
                    .filter(item -> "isResponseAnswerText".equals(item.getKey()))
                    .findFirst()
                    .map(item -> (Boolean) Optional.ofNullable(item.getValue()).orElse(true))
                    .orElse(true);
            if (isResponseAnswerText) {
                AIChatItemValueItemType textItem = new AIChatItemValueItemType();
                textItem.setType(ChatItemValueType.TEXT.getValue());
                textItem.setText(TextContent.builder().content(answerText).build());
                context.getChatAssistantResponse().add(textItem);
            }
        }
    }

    private String getSystemTime(String timezone) {
        // 实现系统时间获取逻辑
        return Instant.now().toString();
    }

    /**
     * 设置SSE响应头
     * @param res SSE发射器
     */
    private void setupSseHeaders(SseEmitter res) {
        // 设置SSE响应头
        try {
            res.send(SseEmitter.event().name("connected").data("Connection established"));
        } catch (Exception e) {
            log.error("设置SSE响应头失败", e);
        }
    }

    private void setupStreamResponse(SseEmitter res, ChatDispatchProps data) {
        // 设置流式响应
        // 10秒发送一次心跳消息，防止浏览器认为连接断开
        // 不能使用守护线程的方法进行运行，否则无法停下
        Timer timer = new Timer();
        timer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                try {
                    if (data.getWorkflowStreamResponse() != null) {
                        WorkflowStreamResponse heartbeat =
                                WorkflowStreamResponse.builder()
                                        .event(SseResponseEventEnum.ANSWER.getValue())
                                        .data(WorkflowUtil.textAdaptGptResponse(TextAdaptGptResponseParams.builder().text("").build()))
                                        .build();
                        data.getWorkflowStreamResponse().accept(heartbeat);
                    }
                } catch (Exception e) {
                    log.error("发送心跳消息失败", e);
                    timer.cancel();
                }
            }
        }, 10000, 10000);

        // 当连接关闭时取消定时器
        res.onCompletion(timer::cancel);
        res.onTimeout(timer::cancel);
        res.onError(throwable -> timer.cancel());
    }
}