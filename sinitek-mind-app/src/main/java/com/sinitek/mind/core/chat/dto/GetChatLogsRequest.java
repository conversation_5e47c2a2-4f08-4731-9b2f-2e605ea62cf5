package com.sinitek.mind.core.chat.dto;

import com.sinitek.mind.common.support.PageParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 获取聊天日志请求参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class GetChatLogsRequest extends PageParam {
    
    /**
     * 应用ID
     */
    private Long appId;
    
    /**
     * 开始日期
     */
    private Date dateStart;
    
    /**
     * 结束日期
     */
    private Date dateEnd;
    
    /**
     * 来源列表
     */
    private List<String> sources;
    
    /**
     * 日志标题（用于搜索）
     */
    private String logTitle;
}