package com.sinitek.mind.core.app.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.enumerate.FlowNodeInputTypeEnum;
import com.sinitek.mind.core.app.enumerate.FlowNodeOutputTypeEnum;
import com.sinitek.mind.core.app.enumerate.WorkflowIOValueTypeEnum;
import com.sinitek.mind.core.app.model.PluginData;
import com.sinitek.mind.core.app.model.PositionInfo;
import com.sinitek.mind.core.workflow.model.*;
import io.swagger.parser.OpenAPIParser;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.PathItem;
import io.swagger.v3.oas.models.Paths;
import io.swagger.v3.oas.models.media.Content;
import io.swagger.v3.oas.models.media.MediaType;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.parser.core.models.SwaggerParseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.yaml.snakeyaml.Yaml;

import java.util.*;

/**
 * HTTP插件工具类
 */
@Slf4j
public class HttpPluginUtils {

    /**
     * 将HTTP API Schema转换为插件
     *
     * @param parentId 父ID
     * @param apiSchemaStr API Schema字符串
     * @param customHeader 自定义请求头
     * @return 子插件列表
     */
    public static List<CreateAppDTO> httpApiSchema2Plugins(
            Long parentId,
            String apiSchemaStr,
            String customHeader) {
        
        List<CreateAppDTO> result = new ArrayList<>();
        
        try {
            // 解析API Schema
            OpenAPI openAPI = parseOpenAPISchema(apiSchemaStr);
            if (openAPI == null) {
                return result;
            }
            
            // 获取服务器路径
            String baseUrl = "";
            if (openAPI.getServers() != null && !openAPI.getServers().isEmpty()) {
                Server server = openAPI.getServers().get(0);
                baseUrl = server.getUrl();
            }
            
            // 处理每个路径
            Paths paths = openAPI.getPaths();
            if (paths == null) {
                return result;
            }
            
            for (Map.Entry<String, PathItem> pathEntry : paths.entrySet()) {
                String path = pathEntry.getKey();
                PathItem pathItem = pathEntry.getValue();
                
                // 处理每个HTTP方法
                processHttpMethod(result, parentId, baseUrl, path, "GET", pathItem.getGet(), customHeader);
                processHttpMethod(result, parentId, baseUrl, path, "POST", pathItem.getPost(), customHeader);
                processHttpMethod(result, parentId, baseUrl, path, "PUT", pathItem.getPut(), customHeader);
                processHttpMethod(result, parentId, baseUrl, path, "DELETE", pathItem.getDelete(), customHeader);
                processHttpMethod(result, parentId, baseUrl, path, "PATCH", pathItem.getPatch(), customHeader);
            }
            
        } catch (Exception e) {
            log.error("openapi schema解析异常， data = {}", apiSchemaStr, e);
        }
        
        return result;
    }
    
    /**
     * 处理HTTP方法
     */
    private static void processHttpMethod(
            List<CreateAppDTO> result,
            Long parentId,
            String baseUrl,
            String path,
            String method,
            Operation operation,
            String customHeader) {
        
        if (operation == null) {
            return;
        }
        
        String operationId = operation.getOperationId();
        if (!StringUtils.hasText(operationId)) {
            operationId = method.toLowerCase() + path.replaceAll("[^a-zA-Z0-9]", "");
        }
        String summary = operation.getSummary();
        String description = operation.getDescription();
        if (!StringUtils.hasText(description)) {
            description = summary;
        }
        
        // 生成节点ID
        String pluginInputId = generateId();
        String httpId = generateId();
        String pluginOutputId = generateId();
        
        // 创建输入输出映射
        Map<String, String> inputIdMap = new HashMap<>();
        
        // 创建插件输入节点的输入输出
        List<FlowNodeInputItemType> pluginInputs = createPluginInputs(operation, inputIdMap);
        List<FlowNodeOutputItemType> pluginOutputs = createPluginOutputs(operation, inputIdMap);
        
        // 创建HTTP节点的输入
        List<FlowNodeInputItemType> httpInputs = createHttpInputs(operation, pluginInputId, inputIdMap, baseUrl, path, method, customHeader);
        
        // 创建节点
        List<StoreNodeItemType> modules = new ArrayList<>();
        
        // 插件输入节点
        StoreNodeItemType pluginInputNode = createPluginInputNode(pluginInputId, pluginInputs, pluginOutputs);
        modules.add(pluginInputNode);
        
        // HTTP节点
        StoreNodeItemType httpNode = createHttpNode(httpId, httpInputs);
        modules.add(httpNode);
        
        // 插件输出节点
        StoreNodeItemType pluginOutputNode = createPluginOutputNode(pluginOutputId, httpId);
        modules.add(pluginOutputNode);
        
        // 创建边连接
        List<StoreEdgeItemType> edges = createEdges(pluginInputId, httpId, pluginOutputId);
        
        // 创建插件数据
        PluginData pluginData = new PluginData();
        pluginData.setPluginUniId(operationId);
        
        // 创建子插件请求
        CreateAppDTO request = new CreateAppDTO();
        request.setParentId(parentId);
        request.setName(operationId);
        request.setIntro(description);
        request.setAvatar("/imgs/workflow/http.png");
        request.setType(AppTypeEnum.PLUGIN.getValue());
        request.setModules(modules);
        request.setEdges(edges);
        request.setPluginData(pluginData);
        
        result.add(request);
    }
    
    /**
     * 解析OpenAPI Schema
     */
    private static OpenAPI parseOpenAPISchema(String schemaStr) {
        if (!StringUtils.hasText(schemaStr)) {
            return null;
        }
        
        try {
            // 尝试解析为JSON
            ObjectMapper objectMapper = new ObjectMapper();
            Object jsonObj = objectMapper.readValue(schemaStr, Object.class);
            SwaggerParseResult result = new OpenAPIParser().readContents(
                    objectMapper.writeValueAsString(jsonObj), null, null);
            return result.getOpenAPI();
        } catch (Exception jsonError) {
            try {
                // 尝试解析为YAML
                Yaml yaml = new Yaml();
                Object yamlObj = yaml.load(schemaStr);
                ObjectMapper objectMapper = new ObjectMapper();
                SwaggerParseResult result = new OpenAPIParser().readContents(
                        objectMapper.writeValueAsString(yamlObj), null, null);
                return result.getOpenAPI();
            } catch (Exception yamlError) {
                yamlError.printStackTrace();
                return null;
            }
        }
    }
    
    /**
     * 类型映射工具方法
     */
    private static WorkflowIOValueTypeEnum getWorkflowIOValueType(Schema<?> schema) {
        if (schema == null) {
            return WorkflowIOValueTypeEnum.STRING;
        }
        
        String type = schema.getType();
        if ("integer".equals(type)) {
            return WorkflowIOValueTypeEnum.NUMBER;
        } else if ("array".equals(type)) {
            Schema<?> items = schema.getItems();
            if (items != null) {
                String itemType = items.getType();
                if ("string".equals(itemType)) {
                    return WorkflowIOValueTypeEnum.ARRAY_STRING;
                } else if ("number".equals(itemType) || "integer".equals(itemType)) {
                    return WorkflowIOValueTypeEnum.ARRAY_NUMBER;
                } else if ("boolean".equals(itemType)) {
                    return WorkflowIOValueTypeEnum.ARRAY_BOOLEAN;
                } else if ("object".equals(itemType)) {
                    return WorkflowIOValueTypeEnum.ARRAY_OBJECT;
                }
            }
            return WorkflowIOValueTypeEnum.ARRAY_STRING;
        } else if ("object".equals(type)) {
            return WorkflowIOValueTypeEnum.OBJECT;
        } else if ("boolean".equals(type)) {
            return WorkflowIOValueTypeEnum.BOOLEAN;
        } else if ("number".equals(type)) {
            return WorkflowIOValueTypeEnum.NUMBER;
        }
        
        return WorkflowIOValueTypeEnum.STRING;
    }
    
    /**
     * 创建插件输入节点的输入项
     */
    private static List<FlowNodeInputItemType> createPluginInputs(Operation operation, Map<String, String> inputIdMap) {
        List<FlowNodeInputItemType> inputs = new ArrayList<>();
        
        // 处理parameters
        if (operation.getParameters() != null) {
            for (Parameter param : operation.getParameters()) {
                FlowNodeInputItemType input = new FlowNodeInputItemType();
                input.setKey(param.getName());
                input.setValueType(getWorkflowIOValueType(param.getSchema()).getValue());
                input.setLabel(param.getName());
                input.setRenderTypeList(Arrays.asList(FlowNodeInputTypeEnum.REFERENCE.getValue()));
                input.setRequired(param.getRequired() != null ? param.getRequired() : false);
                input.setDescription(param.getDescription());
                input.setToolDescription(param.getDescription());
                input.setCanEdit(true);
                inputs.add(input);
            }
        }
        
        // 处理requestBody
        if (operation.getRequestBody() != null) {
            Content content = operation.getRequestBody().getContent();
            if (content != null) {
                // 处理JSON类型
                MediaType jsonMediaType = content.get("application/json");
                if (jsonMediaType != null && jsonMediaType.getSchema() != null && jsonMediaType.getSchema().getProperties() != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Schema> properties = (Map<String, Schema>) jsonMediaType.getSchema().getProperties();
                    for (Map.Entry<String, Schema> entry : properties.entrySet()) {
                        FlowNodeInputItemType input = new FlowNodeInputItemType();
                        input.setKey(entry.getKey());
                        input.setValueType(getWorkflowIOValueType(entry.getValue()).getValue());
                        input.setLabel(entry.getKey());
                        input.setRenderTypeList(Arrays.asList(FlowNodeInputTypeEnum.REFERENCE.getValue()));
                        input.setRequired(false);
                        input.setDescription(entry.getValue().getDescription());
                        input.setToolDescription(entry.getValue().getDescription());
                        input.setCanEdit(true);
                        inputs.add(input);
                    }
                }
                
                // 处理form-urlencoded类型
                MediaType formMediaType = content.get("application/x-www-form-urlencoded");
                if (formMediaType != null && formMediaType.getSchema() != null && formMediaType.getSchema().getProperties() != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Schema> properties = (Map<String, Schema>) formMediaType.getSchema().getProperties();
                    for (Map.Entry<String, Schema> entry : properties.entrySet()) {
                        FlowNodeInputItemType input = new FlowNodeInputItemType();
                        input.setKey(entry.getKey());
                        input.setValueType(getWorkflowIOValueType(entry.getValue()).getValue());
                        input.setLabel(entry.getKey());
                        input.setRenderTypeList(Arrays.asList(FlowNodeInputTypeEnum.REFERENCE.getValue()));
                        input.setRequired(operation.getRequestBody().getRequired() != null ? operation.getRequestBody().getRequired() : false);
                        input.setDescription(entry.getValue().getDescription());
                        input.setToolDescription(entry.getValue().getDescription());
                        input.setCanEdit(true);
                        inputs.add(input);
                    }
                }
            }
        }
        
        return inputs;
    }
    
    /**
     * 创建插件输入节点的输出项
     */
    private static List<FlowNodeOutputItemType> createPluginOutputs(Operation operation, Map<String, String> inputIdMap) {
        List<FlowNodeOutputItemType> outputs = new ArrayList<>();
        
        // 处理parameters
        if (operation.getParameters() != null) {
            for (Parameter param : operation.getParameters()) {
                String outputId = generateId();
                inputIdMap.put(param.getName(), outputId);
                
                FlowNodeOutputItemType output = new FlowNodeOutputItemType();
                output.setId(outputId);
                output.setKey(param.getName());
                output.setValueType(getWorkflowIOValueType(param.getSchema()).getValue());
                output.setLabel(param.getName());
                output.setType(FlowNodeOutputTypeEnum.SOURCE.getValue());
                outputs.add(output);
            }
        }
        
        // 处理requestBody
        if (operation.getRequestBody() != null) {
            Content content = operation.getRequestBody().getContent();
            if (content != null) {
                // 处理JSON类型
                MediaType jsonMediaType = content.get("application/json");
                if (jsonMediaType != null && jsonMediaType.getSchema() != null && jsonMediaType.getSchema().getProperties() != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Schema> properties = (Map<String, Schema>) jsonMediaType.getSchema().getProperties();
                    for (Map.Entry<String, Schema> entry : properties.entrySet()) {
                        String outputId = generateId();
                        inputIdMap.put(entry.getKey(), outputId);
                        
                        FlowNodeOutputItemType output = new FlowNodeOutputItemType();
                        output.setId(outputId);
                        output.setKey(entry.getKey());
                        output.setValueType(getWorkflowIOValueType(entry.getValue()).getValue());
                        output.setLabel(entry.getKey());
                        output.setType(FlowNodeOutputTypeEnum.SOURCE.getValue());
                        outputs.add(output);
                    }
                }
                
                // 处理form-urlencoded类型
                MediaType formMediaType = content.get("application/x-www-form-urlencoded");
                if (formMediaType != null && formMediaType.getSchema() != null && formMediaType.getSchema().getProperties() != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Schema> properties = (Map<String, Schema>) formMediaType.getSchema().getProperties();
                    for (Map.Entry<String, Schema> entry : properties.entrySet()) {
                        String outputId = generateId();
                        inputIdMap.put(entry.getKey(), outputId);
                        
                        FlowNodeOutputItemType output = new FlowNodeOutputItemType();
                        output.setId(outputId);
                        output.setKey(entry.getKey());
                        output.setValueType(getWorkflowIOValueType(entry.getValue()).getValue());
                        output.setLabel(entry.getKey());
                        output.setType(FlowNodeOutputTypeEnum.SOURCE.getValue());
                        outputs.add(output);
                    }
                }
            }
        }
        
        return outputs;
    }
    
    /**
     * 创建HTTP节点的输入项
     */
    private static List<FlowNodeInputItemType> createHttpInputs(Operation operation, String pluginInputId,
                                                              Map<String, String> inputIdMap, String baseUrl,
                                                              String path, String method, String customHeader) {

        List<Map<String, Object>> httpParams = new ArrayList<>();
        List<Map<String, Object>> httpHeaders = new ArrayList<>();
        String httpBody = "{}";

        String requestUrl = baseUrl + path;

        // 处理parameters
        if (operation.getParameters() != null) {
            for (Parameter param : operation.getParameters()) {
                if ("header".equals(param.getIn())) {
                    Map<String, Object> header = new HashMap<>();
                    header.put("key", param.getName());
                    header.put("type", getWorkflowIOValueType(param.getSchema()).getValue());
                    header.put("value", "{{$" + pluginInputId + "." + inputIdMap.get(param.getName()) + "$}}");
                    httpHeaders.add(header);
                } else if ("query".equals(param.getIn())) {
                    Map<String, Object> queryParam = new HashMap<>();
                    queryParam.put("key", param.getName());
                    queryParam.put("type", getWorkflowIOValueType(param.getSchema()).getValue());
                    queryParam.put("value", "{{$" + pluginInputId + "." + inputIdMap.get(param.getName()) + "$}}");
                    httpParams.add(queryParam);
                }


            }
        }
        
        // 处理requestBody
        List<Map<String, Object>> httpFormParams = new ArrayList<>();
        String contentType = "json"; // 默认为json

        if (operation.getRequestBody() != null) {
            Map<String, Object> bodyMap = new HashMap<>();
            Content content = operation.getRequestBody().getContent();
            if (content != null) {
                // 处理JSON类型
                MediaType jsonMediaType = content.get("application/json");
                if (jsonMediaType != null && jsonMediaType.getSchema() != null && jsonMediaType.getSchema().getProperties() != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Schema> properties = (Map<String, Schema>) jsonMediaType.getSchema().getProperties();
                    for (String key : properties.keySet()) {
                        bodyMap.put(key, "{{$" + pluginInputId + "." + inputIdMap.get(key) + "$}}");


                    }
                    contentType = "json";
                }

                // 处理form-urlencoded类型
                MediaType formMediaType = content.get("application/x-www-form-urlencoded");
                if (formMediaType != null && formMediaType.getSchema() != null && formMediaType.getSchema().getProperties() != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Schema> properties = (Map<String, Schema>) formMediaType.getSchema().getProperties();
                    for (String key : properties.keySet()) {
                        // form参数添加到form参数列表，而不是query参数
                        Map<String, Object> formParam = new HashMap<>();
                        formParam.put("key", key);
                        formParam.put("type", getWorkflowIOValueType(properties.get(key)).getValue());
                        formParam.put("value", "{{$" + pluginInputId + "." + inputIdMap.get(key) + "$}}");
                        httpFormParams.add(formParam);


                    }
                    contentType = "form-data";
                }
            }

            if (!bodyMap.isEmpty()) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    httpBody = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(bodyMap);
                } catch (Exception e) {
                    httpBody = "{}";
                }
            }
        }
        
        // 处理自定义头部
        if (StringUtils.hasText(customHeader)) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                @SuppressWarnings("unchecked")
                Map<String, String> customHeaders = mapper.readValue(customHeader, Map.class);
                for (Map.Entry<String, String> entry : customHeaders.entrySet()) {
                    Map<String, Object> header = new HashMap<>();
                    header.put("key", entry.getKey());
                    header.put("type", WorkflowIOValueTypeEnum.STRING.getValue());
                    header.put("value", entry.getValue());
                    httpHeaders.add(header);
                }
            } catch (Exception e) {
                // 忽略解析错误
            }
        }
        
        // 只返回系统HTTP输入项，不包含自定义输入
        return createSystemHttpInputs(method, requestUrl, httpParams, httpHeaders, httpBody, httpFormParams, contentType);
    }
    
    /**
     * 创建系统HTTP输入项
     */
    private static List<FlowNodeInputItemType> createSystemHttpInputs(String method, String url, 
                                                                    List<Map<String, Object>> params,
                                                                    List<Map<String, Object>> headers,
                                                                    String body,
                                                                    List<Map<String, Object>> formParams,
                                                                    String contentType) {
        List<FlowNodeInputItemType> inputs = new ArrayList<>();
        
        // 添加动态输入参数配置
        FlowNodeInputItemType addInputParam = new FlowNodeInputItemType();
        addInputParam.setKey("system_addInputParam");
        addInputParam.setRenderTypeList(Arrays.asList("addInputParam"));
        addInputParam.setValueType("dynamic");
        addInputParam.setLabel("");
        addInputParam.setDebugLabel("");
        addInputParam.setDescription("common:core.module.input.description.HTTP Dynamic Input");
        addInputParam.setRequired(false);
        addInputParam.setToolDescription("");
        // 添加customInputConfig配置
        CustomFieldConfigType customInputConfig = new CustomFieldConfigType();
        customInputConfig.setShowDescription(false);
        customInputConfig.setShowDefaultValue(true);
        customInputConfig.setSelectValueTypeList(Arrays.asList(
            "string", "number", "boolean", "object", "arrayString", "arrayNumber",
            "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory",
            "datasetQuote", "dynamic", "selectDataset", "selectApp"
        ));
        addInputParam.setCustomInputConfig(customInputConfig);
        inputs.add(addInputParam);
        
        // HTTP方法
        FlowNodeInputItemType methodInput = new FlowNodeInputItemType();
        methodInput.setKey("system_httpMethod");
        methodInput.setRenderTypeList(Arrays.asList(FlowNodeInputTypeEnum.CUSTOM.getValue()));
        methodInput.setValueType(WorkflowIOValueTypeEnum.STRING.getValue());
        methodInput.setValue(method.toUpperCase());
        methodInput.setLabel("");
        methodInput.setDebugLabel("");
        methodInput.setRequired(true);
        methodInput.setToolDescription("");
        inputs.add(methodInput);
        
        // HTTP超时配置
        FlowNodeInputItemType timeoutInput = new FlowNodeInputItemType();
        timeoutInput.setKey("system_httpTimeout");
        timeoutInput.setRenderTypeList(Arrays.asList("custom"));
        timeoutInput.setValueType("number");
        timeoutInput.setValue(30);
        timeoutInput.setLabel("");
        timeoutInput.setDebugLabel("");
        timeoutInput.setRequired(true);
        timeoutInput.setToolDescription("");
        // 设置min和max属性
        timeoutInput.setMin(5);
        timeoutInput.setMax(600);
        inputs.add(timeoutInput);
        
        // 请求URL
        FlowNodeInputItemType urlInput = new FlowNodeInputItemType();
        urlInput.setKey("system_httpReqUrl");
        urlInput.setRenderTypeList(Arrays.asList(FlowNodeInputTypeEnum.HIDDEN.getValue()));
        urlInput.setValueType(WorkflowIOValueTypeEnum.STRING.getValue());
        urlInput.setValue(url);
        urlInput.setLabel("");
        urlInput.setDebugLabel("");
        urlInput.setDescription("common:core.module.input.description.Http Request Url");
        urlInput.setRequired(false);
        urlInput.setToolDescription("");
        urlInput.setPlaceholder("https://api.ai.com/getInventory");
        inputs.add(urlInput);
        
        // 添加header secret配置
        FlowNodeInputItemType headerSecretInput = new FlowNodeInputItemType();
        headerSecretInput.setKey("system_header_secret");
        headerSecretInput.setRenderTypeList(Arrays.asList("hidden"));
        headerSecretInput.setValueType("object");
        headerSecretInput.setLabel("");
        headerSecretInput.setDebugLabel("");
        headerSecretInput.setRequired(false);
        headerSecretInput.setToolDescription("");
        inputs.add(headerSecretInput);
        
        // 请求头
        FlowNodeInputItemType headerInput = new FlowNodeInputItemType();
        headerInput.setKey("system_httpHeader");
        headerInput.setRenderTypeList(Arrays.asList(FlowNodeInputTypeEnum.CUSTOM.getValue()));
        headerInput.setValueType(WorkflowIOValueTypeEnum.ANY.getValue());
        headerInput.setValue(headers);
        headerInput.setLabel("");
        headerInput.setDebugLabel("");
        headerInput.setDescription("common:core.module.input.description.Http Request Header");
        headerInput.setRequired(false);
        headerInput.setToolDescription("");
        headerInput.setPlaceholder("common:core.module.input.description.Http Request Header");
        inputs.add(headerInput);
        
        // 查询参数
        FlowNodeInputItemType paramsInput = new FlowNodeInputItemType();
        paramsInput.setKey("system_httpParams");
        paramsInput.setRenderTypeList(Arrays.asList(FlowNodeInputTypeEnum.HIDDEN.getValue()));
        paramsInput.setValueType(WorkflowIOValueTypeEnum.ANY.getValue());
        paramsInput.setValue(params);
        paramsInput.setLabel("");
        paramsInput.setDebugLabel("");
        paramsInput.setRequired(false);
        paramsInput.setToolDescription("");
        inputs.add(paramsInput);
        
        // JSON请求体
        FlowNodeInputItemType bodyInput = new FlowNodeInputItemType();
        bodyInput.setKey("system_httpJsonBody");
        bodyInput.setRenderTypeList(Arrays.asList(FlowNodeInputTypeEnum.HIDDEN.getValue()));
        bodyInput.setValueType(WorkflowIOValueTypeEnum.ANY.getValue());
        bodyInput.setValue("form-data".equals(contentType) ? "{}" : body);
        bodyInput.setLabel("");
        bodyInput.setDebugLabel("");
        bodyInput.setRequired(false);
        bodyInput.setToolDescription("");
        inputs.add(bodyInput);
        
        // Form请求体
        FlowNodeInputItemType formBodyInput = new FlowNodeInputItemType();
        formBodyInput.setKey("system_httpFormBody");
        formBodyInput.setRenderTypeList(Arrays.asList(FlowNodeInputTypeEnum.HIDDEN.getValue()));
        formBodyInput.setValueType(WorkflowIOValueTypeEnum.ANY.getValue());
        formBodyInput.setValue(formParams); // 传入实际的form参数
        formBodyInput.setLabel("");
        formBodyInput.setDebugLabel("");
        formBodyInput.setRequired(false);
        formBodyInput.setToolDescription("");
        inputs.add(formBodyInput);

        // Content-Type
        FlowNodeInputItemType contentTypeInput = new FlowNodeInputItemType();
        contentTypeInput.setKey("system_httpContentType");
        contentTypeInput.setRenderTypeList(Arrays.asList(FlowNodeInputTypeEnum.HIDDEN.getValue()));
        contentTypeInput.setValueType(WorkflowIOValueTypeEnum.STRING.getValue());
        contentTypeInput.setValue(contentType); // 根据实际内容类型设置
        contentTypeInput.setLabel("");
        contentTypeInput.setDebugLabel("");
        contentTypeInput.setRequired(false);
        contentTypeInput.setToolDescription("");
        inputs.add(contentTypeInput);
        
        return inputs;
    }
    
    /**
     * 创建插件输入节点
     */
    private static StoreNodeItemType createPluginInputNode(String nodeId, List<FlowNodeInputItemType> inputs, 
                                                         List<FlowNodeOutputItemType> outputs) {
        StoreNodeItemType node = new StoreNodeItemType();
        node.setNodeId(nodeId);
        node.setName("插件输入");
        node.setIntro("插件输入模块");
        node.setAvatar("core/workflow/template/workflowStart");
        node.setFlowNodeType("pluginInput");
        node.setShowStatus(true);
        
        PositionInfo position = new PositionInfo();
        position.setX(473.55206291900333);
        position.setY(-145.65080850146154);
        node.setPosition(position);
        
        // 更新输入项的配置
        for (FlowNodeInputItemType input : inputs) {
            input.setRenderTypeList(Arrays.asList("input", "reference"));
            input.setSelectedTypeIndex(0);
            input.setCanEdit(true);
            input.setMaxFiles(5);
            input.setCanSelectFile(true);
            input.setCanSelectImg(true);
            input.setRequired(true);
            input.setDefaultValue("");
            // 添加list配置
            List<Map<String, String>> list = new ArrayList<>();
            Map<String, String> listItem = new HashMap<>();
            listItem.put("label", "");
            listItem.put("value", "");
            list.add(listItem);
            // 注意：这里需要根据实际的FlowNodeInputItemType类来设置list属性
        }
        
        // 更新输出项的配置
        for (FlowNodeOutputItemType output : outputs) {
            output.setType("hidden");
        }
        
        node.setInputs(inputs);
        node.setOutputs(outputs);
        
        return node;
    }
    
    /**
     * 创建HTTP节点
     */
    private static StoreNodeItemType createHttpNode(String nodeId, List<FlowNodeInputItemType> inputs) {
        StoreNodeItemType node = new StoreNodeItemType();
        node.setNodeId(nodeId);
        node.setName("HTTP 请求");
        node.setIntro("发送HTTP请求");
        node.setAvatar("core/workflow/template/httpRequest");
        node.setFlowNodeType("httpRequest468");
        node.setShowStatus(true);
        
        PositionInfo position = new PositionInfo();
        position.setX(1041.1505186414104);
        position.setY(-480.9168163795506);
        node.setPosition(position);
        
        // 直接使用传入的inputs，不再重复创建
        node.setInputs(inputs);
        
        // HTTP节点的输出
        List<FlowNodeOutputItemType> outputs = new ArrayList<>();
        
        FlowNodeOutputItemType errorOutput = new FlowNodeOutputItemType();
        errorOutput.setId("error");
        errorOutput.setType("static");
        errorOutput.setKey("error");
        errorOutput.setValueType("object");
        errorOutput.setLabel("workflow:request_error");
        errorOutput.setDescription("HTTP请求错误信息，成功时返回空");
        outputs.add(errorOutput);
        
        FlowNodeOutputItemType httpRawResponse = new FlowNodeOutputItemType();
        httpRawResponse.setId("httpRawResponse");
        httpRawResponse.setType("static");
        httpRawResponse.setKey("httpRawResponse");
        httpRawResponse.setValueType("string");
        httpRawResponse.setLabel("HTTP响应");
        httpRawResponse.setDescription("HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。");
        httpRawResponse.setRequired(true);
        outputs.add(httpRawResponse);
        
        node.setOutputs(outputs);
        
        // 设置catchError
        // 注意：这里需要根据实际的StoreNodeItemType类来设置catchError属性
        
        return node;
    }
    
    /**
     * 创建插件输出节点
     */
    private static StoreNodeItemType createPluginOutputNode(String nodeId, String httpId) {
        StoreNodeItemType node = new StoreNodeItemType();
        node.setNodeId(nodeId);
        node.setName("插件输出");
        node.setIntro("插件输出模块");
        node.setAvatar("core/workflow/template/pluginOutput");
        node.setFlowNodeType("pluginOutput");
        node.setShowStatus(true);
        
        PositionInfo position = new PositionInfo();
        position.setX(1847.6);
        position.setY(5.11);
        node.setPosition(position);
        
        // 输入
        List<FlowNodeInputItemType> inputs = new ArrayList<>();
        FlowNodeInputItemType input = new FlowNodeInputItemType();
        input.setKey("result");
        input.setValueType(WorkflowIOValueTypeEnum.STRING.getValue());
        input.setLabel("result");
        input.setRenderTypeList(Arrays.asList(FlowNodeInputTypeEnum.REFERENCE.getValue()));
        input.setRequired(false);
        input.setDescription("");
        input.setCanEdit(true);
        input.setValue(Arrays.asList(httpId, "httpRawResponse"));
        inputs.add(input);
        node.setInputs(inputs);
        
        // 输出
        List<FlowNodeOutputItemType> outputs = new ArrayList<>();
        FlowNodeOutputItemType output = new FlowNodeOutputItemType();
        output.setId(nodeId);
        output.setKey("result");
        output.setValueType(WorkflowIOValueTypeEnum.STRING.getValue());
        output.setLabel("result");
        output.setType(FlowNodeOutputTypeEnum.STATIC.getValue());
        outputs.add(output);
        node.setOutputs(outputs);
        
        return node;
    }
    
    /**
     * 创建边连接
     */
    private static List<StoreEdgeItemType> createEdges(String pluginInputId, String httpId, String pluginOutputId) {
        List<StoreEdgeItemType> edges = new ArrayList<>();
        
        // 插件输入 -> HTTP
        StoreEdgeItemType edge1 = new StoreEdgeItemType();
        edge1.setSource(pluginInputId);
        edge1.setTarget(httpId);
        edge1.setSourceHandle(getHandleId(pluginInputId, "source", "right"));
        edge1.setTargetHandle(getHandleId(httpId, "target", "left"));
        edges.add(edge1);
        
        // HTTP -> 插件输出
        StoreEdgeItemType edge2 = new StoreEdgeItemType();
        edge2.setSource(httpId);
        edge2.setTarget(pluginOutputId);
        edge2.setSourceHandle(getHandleId(httpId, "source", "right"));
        edge2.setTargetHandle(getHandleId(pluginOutputId, "target", "left"));
        edges.add(edge2);
        
        return edges;
    }
    
    /**
     * 生成Handle ID
     */
    private static String getHandleId(String nodeId, String type, String position) {
        return nodeId + "-" + type + "-" + position;
    }
    
    /**
     * 生成唯一ID
     */
    private static String generateId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
}