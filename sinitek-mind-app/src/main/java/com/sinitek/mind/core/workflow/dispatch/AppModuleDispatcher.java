package com.sinitek.mind.core.workflow.dispatch;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.app.dto.AppVersionDTO;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.AppDetailType;
import com.sinitek.mind.core.app.service.IAppVersionService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.model.AIChatItemValueItemType;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemFileInfo;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.mind.core.workflow.dispatch.util.DispatchUtil;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.SseResponseEventEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.service.IWorkflowService;
import com.sinitek.mind.core.workflow.util.FileUtil;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.dto.OpenaiAccountType;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Consumer;

@Slf4j
@Component
public class AppModuleDispatcher implements NodeDispatcher {

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private IAppVersionService appVersionService;

    @Autowired
    private IWorkflowService workflowService;

    @Autowired
    private IAuthAppService authAppService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.APP_MODULE.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchAppModule(dispatchData);
    }


    private Map<String, Object> dispatchAppModule(ModuleDispatchProps props) {
        // 提取参数
        RunningAppInfo runningAppInfo = props.getRunningAppInfo();
        List<ChatItemType> histories = props.getHistories();
        List<ChatItemValueItemType> query = props.getQuery();
        WorkflowInteractiveResponseType lastInteractive = props.getLastInteractive();
        RuntimeNodeItemType node = props.getNode();
        Long appId = node.getPluginId();
        Long version = node.getVersion();

        Consumer<WorkflowStreamResponse> workflowStreamResponse = props.getWorkflowStreamResponse();

        Map<String, Object> params = props.getParams();
        Map<String, Object> variables = props.getVariables();

        // params参数
        boolean system_forbid_stream = (boolean) params.get("system_forbid_stream");
        String userChatInput = (String) params.get("userChatInput");
        Object history = params.get("history");
        List<String> fileUrlList = getListParam(params, "fileUrlList");
        Object childrenAppVariables = params.get("childrenAppVariables");

        RuntimePromptType runtimePromptType = ChatAdaptor.chatValue2RuntimePrompt(query);
        List<ChatItemValueItemFileInfo> files = runtimePromptType.getFiles();

        List<ChatItemValueItemFileInfo> userInputFiles = fileUrlList.isEmpty() ? files
                : fileUrlList.stream().map(FileUtil::parseUrlToFileType)
                .filter(Objects::nonNull).toList();

        if ((userChatInput == null || userChatInput.trim().isEmpty()) &&
                (userInputFiles == null || userInputFiles.isEmpty())) {
            throw new BussinessException(WorkflowErrorCodeConstant.INPUT_IS_EMPTY);
        }
        if (appId == null) {
            throw new BussinessException(WorkflowErrorCodeConstant.PLUGIN_ID_IS_EMPTY);
        }

        AppDetailType appDetailType = authAppService.authAppByTmbId(runningAppInfo.getTmbId(), appId, PermissionConstant.READ_PER, false);
        AppVersionDTO appVersionById = appVersionService.getAppVersionById(appId, version, appDetailType);
        List<StoreNodeItemType> nodes = appVersionById.getNodes();
        List<StoreEdgeItemType> edges = appVersionById.getEdges();
        AppChatConfigType chatConfig = appVersionById.getChatConfig();

        boolean childStreamResponse = system_forbid_stream ? false : props.getStream();

        if (childStreamResponse) {
            // 构建textAdaptGptResponse参数
            TextAdaptGptResponseParams gptParams = TextAdaptGptResponseParams.builder()
                    .text("\n")
                    .build();
            // 创建流式响应
            WorkflowStreamResponse streamResponse = WorkflowStreamResponse.builder()
                    .event(SseResponseEventEnum.ANSWER.getValue())
                    .data(WorkflowUtil.textAdaptGptResponse(gptParams))
                    .build();

            workflowStreamResponse.accept(streamResponse);
        }

        List<ChatItemType> chatHistories = DispatchUtil.getHistories((Integer) history, histories);

        // Rewrite children app variables
        SystemVariablesType systemVariables = DispatchUtil.filterSystemVariables(variables);

        // 获取用户聊天信息和团队权限
        // TODO 实现getUserChatInfoAndAuthTeamPoints方法
        ExternalProviderType externalProviderType = new ExternalProviderType();
        externalProviderType.setExternalWorkflowVariables(new HashMap<>());
        externalProviderType.setOpenaiAccount(new OpenaiAccountType());

        // 构建子应用运行变量
        Map systemVariablesMap = objectMapper.convertValue(systemVariables, Map.class);
        Map<String, Object> childrenRunVariables = new HashMap<>(systemVariablesMap);
        if (childrenAppVariables instanceof Map) {
            childrenRunVariables.putAll((Map<String, Object>) childrenAppVariables);
        }
        childrenRunVariables.put("histories", chatHistories);
        childrenRunVariables.put("appId", String.valueOf(appDetailType.getId()));
        if (externalProviderType.getExternalWorkflowVariables() != null) {
            childrenRunVariables.putAll(externalProviderType.getExternalWorkflowVariables());
        }

        boolean equals = "childrenInteractive".equals(lastInteractive.getNodeResponse().getType());
        Object param = lastInteractive.getNodeResponse().getParam();
        ChildrenInteractive.Params childParam = (ChildrenInteractive.Params) param; // 强制转换
        WorkflowInteractiveResponseType childrenInteractive = equals ? childParam.getChildrenResponse() : null;

        List<String> nodeIds = WorkflowUtil.getWorkflowEntryNodeIds(nodes, childrenInteractive);

        List<RuntimeNodeItemType> runtimeNodesParam = WorkflowUtil.storeNodes2RuntimeNodes(nodes, nodeIds);

        List<RuntimeNodeItemType> runtimeNodes = WorkflowUtil.rewriteNodeOutputByHistories(runtimeNodesParam, childrenInteractive);

        List<RuntimeEdgeItemType> runtimeEdges = WorkflowUtil.storeEdges2RuntimeEdges(edges, childrenInteractive);

        RuntimePromptType type = new RuntimePromptType();
        type.setText(userChatInput);
        type.setFiles(userInputFiles);
        List<ChatItemValueItemType> theQuery = childrenInteractive == null ? query : ChatAdaptor.runtimePrompt2ChatsValue(type);

        // 构建工作流调度参数
        ChatDispatchProps dispatchProps = ChatDispatchProps.builder()
                .runningAppInfo(RunningAppInfo.builder()
                        .id(appDetailType.getId())
                        .tmbId(appDetailType.getOrgId())
                        .isChildApp(true)
                        .build())
                .runtimeNodes(runtimeNodes)
                .lastInteractive(childrenInteractive)
                .runtimeEdges(runtimeEdges)
                .histories(chatHistories)
                .variables(childrenRunVariables)
                .query(theQuery)
                .chatConfig(chatConfig)
                .stream(childStreamResponse)
                .workflowStreamResponse(system_forbid_stream ? null : workflowStreamResponse)
                .externalProvider(externalProviderType)
                .build();

        DispatchFlowResponse dispatchFlowResponse = workflowService.dispatchWorkFlow(dispatchProps).join();

        List<AIChatItemValueItemType> assistantResponsesOld = dispatchFlowResponse.getAssistantResponses();
        List<ChatItemValueItemType> assistantResponses = new ArrayList<>();
        for (AIChatItemValueItemType item : assistantResponsesOld) {
            ChatItemValueItemType itemType = new ChatItemValueItemType();
            BeanUtils.copyProperties(item, itemType);
            assistantResponses.add(itemType);
        }

        // 构建完整消息
        List<ChatItemType> completeMessages = new ArrayList<>(chatHistories);
        ChatItemType itemType = new ChatItemType();
        itemType.setObj(ChatRoleEnum.HUMAN);
        itemType.setValue(query);
        completeMessages.add(itemType);
        // 添加AI响应
        ChatItemType aiType = new ChatItemType();
        aiType.setObj(ChatRoleEnum.AI);
        aiType.setValue(assistantResponses);
        completeMessages.add(itemType);

        RuntimePromptType runtimePromptType1 = ChatAdaptor.chatValue2RuntimePrompt(assistantResponses);
        String text = runtimePromptType1.getText();

        // 计算使用点数
         int usagePoints = dispatchFlowResponse.getFlowUsage().stream()
                 .mapToInt(item -> item.getTotalPoints() != 0 ? (int) item.getTotalPoints() : 0)
                 .sum();
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();

        // 添加系统内存
         result.put("system_memories", dispatchFlowResponse.getSystem_memories());

        // 添加交互响应
         if (dispatchFlowResponse.getWorkflowInteractiveResponse() != null) {
             Map<String, Object> interactive = new HashMap<>();
             interactive.put("type", "childrenInteractive");
             Map<String, Object> params1 = new HashMap<>();
             params1.put("childrenResponse", dispatchFlowResponse.getWorkflowInteractiveResponse());
             interactive.put("params", params1);
             result.put(DispatchNodeResponseKeyEnum.INTERACTIVE.getValue(), interactive);
         }

        // 添加助手响应
         result.put("assistantResponses", system_forbid_stream ? new ArrayList<>() : dispatchFlowResponse.getAssistantResponses());

        // 添加运行时间
         result.put(DispatchNodeResponseKeyEnum.RUN_TIMES.getValue(), dispatchFlowResponse.getRunTimes());

        // 添加节点响应
         result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                 .moduleLogo(appDetailType.getAvatar())
                 .totalPoints((double) usagePoints)
                 .query(userChatInput)
                 .textOutput(text)
                 .mergeSignId(node.getNodeId())
                 .build());

        // 添加节点调度使用情况
         List<Map<String, Object>> nodeDispatchUsages = new ArrayList<>();
         Map<String, Object> usage = new HashMap<>();
         usage.put("moduleName", appDetailType.getName());
         usage.put("totalPoints", usagePoints);
         nodeDispatchUsages.add(usage);
         result.put(DispatchNodeResponseKeyEnum.NODE_DISPATCH_USAGES.getValue(), nodeDispatchUsages);

        // 添加工具响应和答案文本
         result.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), text);
         result.put("answerText", text);
         result.put("history", completeMessages);

        return result;

    }

    /**
     * 获取List参数
     */
    @SuppressWarnings("unchecked")
    private <T> List<T> getListParam(Map<String, Object> params, String key) {
        Object value = params.get(key);
        if (value instanceof List) {
            return (List<T>) value;
        }
        return new ArrayList<>();
    }

}
