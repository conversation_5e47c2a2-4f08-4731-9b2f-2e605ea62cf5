package com.sinitek.mind.core.app.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.sinitek.data.mybatis.base.BaseEntity;
import com.sinitek.mind.core.app.model.UserGuide;
import com.sinitek.mind.core.workflow.model.WorkflowTemplateBasicType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 应用模板实体类
 * 对应MySQL中的app_template表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "应用模板实体")
@TableName(value = "mind_app_template", autoResultMap = true)
public class AppTemplate extends BaseEntity {

    @Schema(description = "模板ID")
    private String templateId;
    
    @Schema(description = "名称")
    private String name;
    
    @Schema(description = "介绍")
    private String intro;
    
    @Schema(description = "头像")
    private String avatar;
    
    @Schema(description = "作者")
    private String author;
    
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;
    
    @Schema(description = "类型")
    private String type;
    
    @Schema(description = "是否激活")
    private Boolean isActive;
    
    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "用户指南")
    private UserGuide userGuide;
    
    @Schema(description = "是否快速模板")
    private Boolean isQuickTemplate;
    
    @Schema(description = "排序")
    private Integer order;
    
    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "工作流")
    private WorkflowTemplateBasicType workflow;

    @Schema(description = "权重")
    private Integer weight;
}