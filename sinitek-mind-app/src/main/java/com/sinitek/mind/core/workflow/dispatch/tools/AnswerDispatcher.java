package com.sinitek.mind.core.workflow.dispatch.tools;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.SseResponseEventEnum;
import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import com.sinitek.mind.core.workflow.model.TextAdaptGptResponseParams;
import com.sinitek.mind.core.workflow.model.WorkflowStreamResponse;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Component
public class AnswerDispatcher implements NodeDispatcher {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.ANSWER_NODE.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchAnswer(dispatchData);
    }

    /**
     * 执行answer节点
     * 对应 TypeScript 中的 dispatchAnswer 函数
     *
     * @param dispatchData 调度数据
     * @return 节点执行结果
     */
    private Map<String, Object> dispatchAnswer(ModuleDispatchProps dispatchData) {
        // 提取参数
        Map<String, Object> params = dispatchData.getParams();
        Consumer<WorkflowStreamResponse> workflowStreamResponse = dispatchData.getWorkflowStreamResponse();

        // 获取text参数，默认为空字符串
        Object text = params.getOrDefault("text", "");

        // 格式化文本：如果是字符串直接使用，否则转为JSON字符串
        String formatText;
        if (text instanceof String) {
            formatText = (String) text;
        } else {
            try {
                formatText = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(text);
            } catch (Exception e) {
                log.warn("Failed to serialize text to JSON, using toString: {}", e.getMessage());
                formatText = text != null ? text.toString() : "";
            }
        }
        // 构建响应文本，添加换行符前缀
        String responseText = "\n" + formatText;

        // 发送流式响应
        if (workflowStreamResponse != null) {
            try {
                // 构建textAdaptGptResponse参数
                TextAdaptGptResponseParams gptParams = TextAdaptGptResponseParams.builder()
                        .text(responseText)
                        .build();

                // 创建流式响应
                WorkflowStreamResponse streamResponse = WorkflowStreamResponse.builder()
                        .event(SseResponseEventEnum.FAST_ANSWER.getValue())
                        .data(WorkflowUtil.textAdaptGptResponse(gptParams))
                        .build();

                workflowStreamResponse.accept(streamResponse);
            } catch (Exception e) {
                log.warn("Failed to send workflow stream response: {}", e.getMessage());
            }
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();

        // 设置answerText输出
        result.put(NodeOutputKeyEnum.ANSWER_TEXT.getValue(), responseText);

        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                .textOutput(formatText)
                .build());

        return result;
    }
}
