package com.sinitek.mind.core.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.core.app.constant.AppConstant;
import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.dao.AppDAO;
import com.sinitek.mind.core.app.dao.AppVersionDAO;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.entity.AppVersion;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.AppDetailType;
import com.sinitek.mind.core.app.model.VariableItemType;
import com.sinitek.mind.core.app.service.IAppService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.app.service.IImageService;
import com.sinitek.mind.core.app.util.AppUtil;
import com.sinitek.mind.core.chat.dao.ChatDAO;
import com.sinitek.mind.core.chat.dao.ChatInputGuideDAO;
import com.sinitek.mind.core.chat.dao.ChatItemDAO;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.mind.support.account.dto.SourceMemberDTO;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.openapi.service.IOpenApiService;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.outlink.dao.OutLinkDAO;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.*;
import com.sinitek.mind.support.permission.enumerate.AuthTypeEnum;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.ICollaboratorService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.dto.TeamMemberDTO;
import com.sinitek.mind.support.team.service.ITeamMemberService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppServiceImpl implements IAppService {

    private final AppDAO appDAO;

    private final AppVersionDAO appVersionDAO;

    private final IOperationLogService operationLogService;

    private final ChatItemDAO chatItemDAO;

    private final ChatDAO chatDAO;

    private final OutLinkDAO outLinkDAO;

    private final IOpenApiService openApiService;

    private final ChatInputGuideDAO chatInputGuideDAO;

    private final IImageService imageService;

    private final ICollaboratorService collaboratorService;

    private final IAuthService authService;

    private final IPermissionService permissionService;

    private final IAccountService accountService;

    private final IAuthAppService authAppService;

    private final ITeamMemberService teamMemberService;

    private static final List<AppTypeEnum> APP_FOLDER_TYPE_LIST = List.of(AppTypeEnum.FOLDER, AppTypeEnum.HTTP_PLUGIN);

    private static final List<String> FOLDER_TYPE_LIST = List.of(AppTypeEnum.FOLDER.getValue(), AppTypeEnum.HTTP_PLUGIN.getValue());

    @Override
    public List<AppListItemDTO> getAppList(ListAppDTO request) {
        StopWatch sw = new StopWatch("app列表查询耗时：");
        sw.start("权限校验");
        // 校验权限
        AuthMemberDTO authMemberDTO = authService.authUserPer(PermissionConstant.READ_PER);
        if (request.getParentId() != null) {
            authAppService.authApp(request.getParentId(), PermissionConstant.READ_PER);
        }
        sw.stop();

        sw.start("获取有权限的appId");
        // 获取所有有权限的appId
        List<Permission> authedAppList = permissionService.findAllAuthedResource(authMemberDTO.getTmbId(),
                ResourceTypeEnum.APP, List.of(AuthTypeEnum.READ, AuthTypeEnum.WRITE, AuthTypeEnum.MANAGE));

        List<Long> appIdList = authedAppList.stream()
                .map(Permission::getResourceId)
                .map(Long::valueOf)
                .toList();
        sw.stop();

        sw.start("构建查询条件");
        AppPageParamDTO pageParamDTO = new AppPageParamDTO();
        pageParamDTO.setIds(appIdList);
        pageParamDTO.setOrgId(authMemberDTO.getTmbId());
        pageParamDTO.setNamespace(request.getNamespace());

        // 处理最近聊天应用查询
        if (Boolean.TRUE.equals(request.getGetRecentlyChat())) {
            pageParamDTO.setTypes(Arrays.asList(
                    AppTypeEnum.WORKFLOW.getValue(),
                    AppTypeEnum.SIMPLE.getValue(),
                    AppTypeEnum.PLUGIN.getValue()));
        } else {
            // 处理搜索条件
            pageParamDTO.setSearchKey(escapeRegexChars(request.getSearchKey()));

            // 处理类型过滤
            pageParamDTO.setTypes(request.getType());

            // 处理父级ID过滤
            pageParamDTO.setParentId(request.getParentId());
        }

        // 设置限制
        if (Boolean.TRUE.equals(request.getGetRecentlyChat())) {
            pageParamDTO.setLimit(15);
        } else if (StringUtils.isNotBlank(request.getSearchKey())) {
            pageParamDTO.setLimit(50);
        }
        sw.stop();

        sw.start("查询db中的应用");
        List<App> apps = appDAO.pageByParamDTO(pageParamDTO);
        sw.stop();

        Set<String> orgIds = new HashSet<>();
        List<String> appIds = new LinkedList<>();
        for (App app : apps) {
            orgIds.add(app.getOrgId());
            appIds.add(String.valueOf(app.getId()));
        }

        sw.start("查询或构建相关map");
        Map<String, Long> appValueMap = authedAppList.stream()
                .collect(Collectors.toMap(Permission::getResourceId, Permission::getPermission));
        Map<String, SourceMemberDTO> sourceMemberMap = accountService.getSourceMemberMapByOrgIds(new ArrayList<>(orgIds));
        Map<String, Integer> clbNumberMap = collaboratorService.findClbNumberMap(appIds, ResourceTypeEnum.APP);
        sw.stop();

        List<AppListItemDTO> appListItemDTOS = new ArrayList<>();
        sw.start("类型转换");
        for (App app : apps) {
            AppListItemDTO appListItemDTO = new AppListItemDTO();
            appListItemDTO.setName(app.getName());
            appListItemDTO.setAvatar(app.getAvatar());
            appListItemDTO.setType(app.getType());
            appListItemDTO.set_id(String.valueOf(app.getId()));
            appListItemDTO.setTmbId(app.getOrgId());
            appListItemDTO.setIntro(app.getIntro());
            appListItemDTO.setUpdateTime(app.getUpdateTimeStamp());
            appListItemDTO.setPluginData(app.getPluginData());
            appListItemDTO.setInheritPermission(app.getInheritPermission());

            PermissionDTO permissionDTO = new PermissionDTO();
            if (authMemberDTO.getTmbId().equals(app.getOrgId())) {
                permissionDTO.setValue(PermissionConstant.OWNER_PERMISSION_VAL);
            } else {
                long perVal = MapUtils.getLongValue(appValueMap, String.valueOf(app.getId()), PermissionConstant.READ_PER);
                permissionDTO.setValue(perVal);
            }
            appListItemDTO.setPermission(permissionDTO);

            int clbNumber = MapUtils.getIntValue(clbNumberMap, String.valueOf(app.getId()), 0);
            appListItemDTO.setIsPrivate(clbNumber == 0);

            SourceMemberDTO sourceMember = sourceMemberMap.get(app.getOrgId());
            appListItemDTO.setSourceMember(sourceMember);

            appListItemDTOS.add(appListItemDTO);
        }
        sw.stop();

        log.info(sw.prettyPrint(TimeUnit.MILLISECONDS));
        return appListItemDTOS;
    }

    @Override
    public Long createApp(CreateAppDTO body) {
        // 校验参数
        Long parentId = body.getParentId();
        String name = body.getName();
        String type = body.getType();
        List<StoreNodeItemType> modules = body.getModules();

        if (!StringUtils.isNotBlank(name) || !StringUtils.isNotBlank(type) || CollUtil.isEmpty(modules)) {
            throw new BussinessException(AppErrorCodeConstant.MISSING_REQUIRED_PARAMS);
        }

        AuthDTO authDTO;
        if (parentId != null) {
            authDTO = authAppService.authApp(parentId, PermissionConstant.WRITE_PER);
        } else {
            authDTO = authService.authUserPer(PermissionConstant.APP_CREATE_PER);
        }

        String teamId = authDTO.getTeamId();
        String tmbId = authDTO.getTmbId();
        String userId = authDTO.getUserId();

        // app创建上限校验 TODO 需要确认是否需要该校验

        TeamMemberDTO teamMemberDTO = teamMemberService.getById(tmbId);

        body.setTeamId(teamId);
        body.setTmbId(tmbId);
        body.setUserAvatar(teamMemberDTO.getAvatar());
        body.setUsername(teamMemberDTO.getMemberName());


        // 创建应用
        return onCreateApp(body);
    }

    @Override
    public AppDetailType getAppDetail(Long appId) {
        if (appId == null) {
            throw new BussinessException(AppErrorCodeConstant.APP_ID_NOT_EXISTS);
        }

        // 权限校验
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);

        App app = authAppDTO.getApp();
        if (!APP_FOLDER_TYPE_LIST.contains(AppTypeEnum.fromValue(app.getType()))) {
            // 文件夹没有这些字段
            AppUtil.rewriteAppWorkflowToDetail(app.getModules(), authAppDTO.getTeamId(), authAppDTO.getIsRoot(), app.getOrgId());
        }

        AppDetailType appDetailType = new AppDetailType();
        BeanUtils.copyProperties(app, appDetailType);

        if (!authAppDTO.getPermission().getHasWritePer()) {
            appDetailType.setModules(List.of());
            appDetailType.setEdges(List.of());
        }

        // 设置_id
        appDetailType.set_id(String.valueOf(appDetailType.getId()));
        return appDetailType;
    }

    @Override
    public List<App> findAppAndAllChildren(String teamId, Long appId) {
        App app = appDAO.getById(appId);
        if (app == null) {
            throw new BussinessException(AppErrorCodeConstant.APP_NOT_FOUND);
        }
        List<App> childApps = appDAO.findAllChildrenByParentId(app.getId());

        List<App> allApps = new ArrayList<>();
        allApps.add(app);
        allApps.addAll(childApps);
        return allApps;
    }

    @Override
    public List<AppBasicInfoDTO> getAppBasicInfoByIds(String teamId, List<Long> ids) {
        List<App> apps = appDAO.findByIdIn(ids);

        return apps.stream()
                .map(app -> new AppBasicInfoDTO(String.valueOf(app.getId()), app.getName(), app.getAvatar()))
                .collect(Collectors.toList());
    }


    @Override
    public List<App> findByTeamIdAndType(String teamId, String type) {
        return appDAO.findByType(type);
    }

    @Override
    public List<App> findAppAndAllChildren(String teamId, Long appId, String fields) {
        App app = appDAO.getById(appId);
        if (app == null) {
            throw new BussinessException(AppErrorCodeConstant.DATASET_NOT_FOUND);
        }

        // 查找所有子应用
        List<App> childApps = appDAO.findAllChildrenByParentId(app.getId());

        // 组合结果：主应用 + 所有子应用
        List<App> result = new ArrayList<>();
        result.add(app);
        result.addAll(childApps);

        return result;
    }

    @Override
    public void deleteApp(Long appId) {

        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.OWNER_PERMISSION_VAL);
        String teamId = authAppDTO.getTeamId();
        String tmbId = authAppDTO.getTmbId();
        String userId = authAppDTO.getUserId();
        List<App> apps = this.findAppAndAllChildren(teamId, appId);
        App app = authAppDTO.getApp();
//        List<App> children = appRepository.findChildrenByTeamIdAndParentId(teamId, appId);
//        List<App> allApps = children;
//        for (App appItem : children) {
//            List<App> recursively = findChildrenRecursively(appItem.getTeamId(), appItem.getId());
//            allApps.addAll(recursively);
//        }
//        allApps.add(app);

        for (App appItem : apps) {
            String id = String.valueOf(appItem.getId());
            // 删除聊天相关
            // TODO deleteChatFiles
            chatItemDAO.deleteByAppId(Long.valueOf(id));

            chatDAO.deleteByAppId(Long.valueOf(id));

            // 删除分享链接
            outLinkDAO.deleteByAppId(id);

            // 删除 OpenAPI
            openApiService.deleteByAppId(id);

            // 删除版本
            appVersionDAO.deleteByAppId(Long.valueOf(id));

            // 删除输入引导
            chatInputGuideDAO.deleteByAppId(Long.valueOf(id));

            // 删除权限
            permissionService.deletePermission(id, ResourceTypeEnum.APP);

            // 删除 app
            appDAO.removeById(id);

            // 删除头像图片
            imageService.removeImageByPath(app.getAvatar());
        }

        // 记录操作日志
        operationLogService.addOperationLog(MindConstant.APP,
                String.format("删除应用【%s】",
                        app == null ? "" : app.getName()));
    }

    @Override
    public Long copyApp(Long appId) {
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.WRITE_PER);
        App app = authAppDTO.getApp();

        String tmbId;
        if (!Objects.isNull(app.getParentId())) {
            AuthAppDTO parentAuthAppDTO = authAppService.authApp(app.getParentId(), PermissionConstant.WRITE_PER);
            tmbId = parentAuthAppDTO.getTmbId();
        } else {
            AuthMemberDTO authMemberDTO = authService.authUserPer(PermissionConstant.APP_CREATE_PER);
            tmbId = authMemberDTO.getTmbId();
        }

        CreateAppDTO copyApp = new CreateAppDTO();
        copyApp.setParentId(app.getParentId());
        copyApp.setName(app.getName() + " Copy");
        copyApp.setIntro(app.getIntro());
        copyApp.setAvatar(app.getAvatar());
        copyApp.setType(app.getType());
        copyApp.setModules(app.getModules());
        copyApp.setEdges(app.getEdges());
        copyApp.setChatConfig(app.getChatConfig());
        copyApp.setTmbId(tmbId);
        copyApp.setPluginData(app.getPluginData());
        copyApp.setNamespace(app.getNamespace());
        Long newAppId = this.onCreateApp(copyApp);

        // 记录操作日志
        operationLogService.addOperationLog(MindConstant.APP,
                String.format("复制应用【%s】",
                        copyApp.getName()));
        return newAppId;
    }

    @Override
    public App updateApp(AppUpdateDTO params) {
        Long appId = params.getAppId();
        if (appId == null) {
            throw new BussinessException(AppErrorCodeConstant.MISSING_PARAMS);
        }
        boolean isMove = params.getParentId() != null;
        // 2. 查询 app 及权限
        App app = appDAO.getById(appId);
        if (app == null) {
            throw new BussinessException(AppErrorCodeConstant.APP_NOT_EXIST);
        }

        // 3 权限校验
        String targetName;
        String teamId = "";
        String tmbId = "";
        if (isMove) {
            App targetApp = appDAO.getById(params.getParentId());
            if (targetApp == null) {
                targetName = "";
                throw new BussinessException(AppErrorCodeConstant.TARGET_FOLDER_NOT_EXISTS);
            } else {
                targetName = "root";
            }
            if (app.getParentId() != null) {
                // 原文件夹权限校验（略）
            }
            if (params.getParentId() == null || app.getParentId() == null) {
                // 根目录权限校验（略）
            }
        } else {
            targetName = "";
            // 非移动，校验写权限（略）
        }

        // 4. 事务处理
        App result = new App();
        if (isMove) {
            // 如果是文件夹，继承权限
            if (AppTypeEnum.FOLDER.getValue().equals(app.getType())) {
                // 获取父文件夹协作者和分组
                List<CollaboratorPermissionDTO> parentClbsAndGroups = collaboratorService.getResourceClbsAndGroups(
                        String.valueOf(params.getParentId()), ResourceTypeEnum.APP
                );
                // 同步自身协作者
                collaboratorService.syncCollaborators(String.valueOf(app.getId()), ResourceTypeEnum.APP, parentClbsAndGroups);
                // 同步子节点协作者
                collaboratorService.syncChildrenPermission(app, FOLDER_TYPE_LIST, parentClbsAndGroups);
            } else {
                operationLogService.addOperationLog(MindConstant.APP,
                        String.format("移动应用【%s】到目录【%s】",
                                app.getName(), targetName));
                // 非文件夹，删除所有协作者
                permissionService.deletePermission(String.valueOf(app.getId()), ResourceTypeEnum.APP);
            }
            // 更新
            result = doUpdate(app, params, appId, true);
        } else {
            logAppUpdate(tmbId, teamId, app, params.getName(), params.getIntro());
            result = doUpdate(app, params, appId, false);
        }
        return result;
    }

    @Override
    public Long transitionWorkFlow(TransitionWorkflowDTO body) {
        Long appId = body.getAppId();
        boolean createNew = body.isCreateNew();
        // TODO 需要确认什么样的权限
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);
        App app = authAppDTO.getApp();
        if (createNew) {
            CreateAppDTO appNew = new CreateAppDTO();
            appNew.setParentId(app.getParentId());
            appNew.setName(app.getName() + " Copy");
            appNew.setAvatar(app.getAvatar());
            appNew.setType(app.getType());
            appNew.setModules(app.getModules());
            appNew.setEdges(app.getEdges());
            appNew.setChatConfig(app.getChatConfig());
            appNew.setTmbId(app.getOrgId());
            return createApp(appNew);
        }
        appDAO.updateTypeById(AppTypeEnum.WORKFLOW.getValue(), app.getId());
        return appId;
    }

    @Override
    public Long resumeInheritPermission(Long appId) {
        // TODO 需要确认什么样的权限
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);
        App app = authAppDTO.getApp();
        assert app != null;
        if (app.getParentId() != null) {
            // TODO
        } else {
            appDAO.updateInheritPermissionTrueById(app.getId());
        }
        return appId;
    }

    @Override
    public List<BasicInfoResDTO> getBasicInfo(String teamId, List<Long> ids) {
        List<App> apps = appDAO.findByIdIn(ids);
        List<BasicInfoResDTO> res = new ArrayList<>();
        for (App app : apps) {
            BasicInfoResDTO basicInfoResDTO = new BasicInfoResDTO();
            basicInfoResDTO.setId(String.valueOf(app.getId()));
            basicInfoResDTO.setName(app.getName());
            basicInfoResDTO.setAvatar(app.getAvatar());
            res.add(basicInfoResDTO);
        }
        return res;
    }

    @Override
    public Long createSimpleApp(SimpleCreateAppRequestDTO requestDTO) {
        // 校验参数
        String name = requestDTO.getName();
        String type = requestDTO.getType();

        if (!StringUtils.isNotBlank(name) || !StringUtils.isNotBlank(type)) {
            throw new BussinessException(AppErrorCodeConstant.MISSING_REQUIRED_PARAMS);
        }

        if (!type.equals(AppTypeEnum.WORKFLOW.getValue())) {
            throw new BussinessException(AppErrorCodeConstant.ONLY_WORKFLOW_SIMPLE_CREATE);
        }

        AuthDTO authDTO = authService.authUserPer(PermissionConstant.APP_CREATE_PER);
        String teamId = authDTO.getTeamId();
        String tmbId = authDTO.getTmbId();

        // app创建上限校验 TODO 需要确认是否需要该校验

        TeamMemberDTO teamMemberDTO = teamMemberService.getById(tmbId);

        CreateAppDTO body = new CreateAppDTO();

        String namespace = requestDTO.getNamespace();
        String intro = requestDTO.getIntro();
        List<VariableItemType> globalVariables = requestDTO.getGlobalVariables();

        // 设置基本信息
        body.setName(name);
        body.setType(type);
        body.setIntro(intro);
        body.setNamespace(namespace);

        // 设置全局变量
        AppChatConfigType appChatConfigType = new AppChatConfigType();
        appChatConfigType.setVariables(globalVariables);
        body.setChatConfig(appChatConfigType);

        // 设置工作流默认的modules
        body.setModules(AppConstant.WORKFLOW_DEFAULT_MODULES);

        body.setTeamId(teamId);
        body.setTmbId(tmbId);
        body.setUserAvatar(teamMemberDTO.getAvatar());
        body.setUsername(teamMemberDTO.getMemberName());

        // 创建应用
        return onCreateApp(body);
    }

    @Override
    public void deleteAppByNamespace(List<String> namespaceList) {
        if (CollUtil.isEmpty(namespaceList)) {
            return;
        }
        appDAO.deleteByNamespaceIn(namespaceList);
    }

    private App doUpdate(App app, AppUpdateDTO req, Long appId, boolean isMove) {
        // TODO 格式化 nodes 数据（略，假设 beforeUpdateAppFormat 已实现）
        // beforeUpdateAppFormat(req.getNodes());

        // TODO 刷新头像（略，假设 refreshSourceAvatar 已实现）
        // refreshSourceAvatar(req.getAvatar(), app.getAvatar());

        // 如果是工具集类型且有新头像，批量更新子应用头像
        if (AppTypeEnum.TOOL_SET.getValue().equals(app.getType()) && req.getAvatar() != null && !req.getAvatar().isEmpty()) {
            appDAO.updateAvatarByParentIdAndTeamId(req.getAvatar(), appId);
        }

        // 构建更新内容
        if (req.getParentId() != null) app.setParentId(Long.valueOf(req.getParentId()));
        if (req.getName() != null) app.setName(req.getName());
        if (req.getType() != null) app.setType(req.getType());
        if (req.getAvatar() != null) app.setAvatar(req.getAvatar());
        if (req.getIntro() != null) app.setIntro(req.getIntro());
        if (req.getNodes() != null) app.setModules(req.getNodes());
        if (req.getEdges() != null) app.setEdges(req.getEdges());
        if (req.getChatConfig() != null) app.setChatConfig(req.getChatConfig());
        if (req.getNamespace() != null) app.setNamespace(req.getNamespace());
        if (isMove) app.setInheritPermission(true);

        appDAO.updateById(app);
        return app;
    }

    private void logAppUpdate(String tmbId, String teamId, App app, String name, String intro) {
        StringBuilder names = new StringBuilder();
        StringBuilder values = new StringBuilder();

        if (name != null) {
            names.append(AppUtil.i18n("common:name")).append(",");
            values.append(name).append(",");
        }
        if (intro != null) {
            names.append(AppUtil.i18n("common:Intro")).append(",");
            values.append(intro).append(",");
        }

        operationLogService.addOperationLog(MindConstant.APP,
                String.format("更新应用信息【%s】",
                        app.getName())
        );
    }

    private Long onCreateApp(CreateAppDTO body) {
        App app = new App();
        app.setAvatar(body.getAvatar());
        app.setName(body.getName());
        app.setIntro(body.getIntro());
        app.setOrgId(body.getTmbId());
        app.setModules(body.getModules());
        app.setEdges(body.getEdges());
        app.setChatConfig(body.getChatConfig());
        app.setType(body.getType());
        app.setPluginData(body.getPluginData());
        app.setUpdateTimeStamp(new Date());
        app.setNamespace(body.getNamespace());

        // 如果有父级ID，设置父级关系
        if (body.getParentId() != null) {
            app.setParentId(Long.valueOf(body.getParentId()));
        }
        // 保存应用
        appDAO.save(app);
        Long appId = app.getId();

        if (!APP_FOLDER_TYPE_LIST.contains(AppTypeEnum.fromValue(app.getType()))) {
            AppVersion appVersion = new AppVersion();
            appVersion.setOrgId(body.getTmbId());
            appVersion.setAppId(appId);
            appVersion.setNodes(body.getModules());
            appVersion.setEdges(body.getEdges());
            appVersion.setChatConfig(body.getChatConfig());
            appVersion.setName(body.getName());
            appVersion.setUsername(body.getUsername());
            appVersion.setAvatar(body.getUserAvatar());
            appVersion.setIsPublish(true);
            appVersionDAO.save(appVersion);
        }

        // 记录操作日志
        operationLogService.addOperationLog(MindConstant.APP, String.format("创建应用【%s】", body.getName()));

        return appId;
    }


    private String escapeRegexChars(String input) {
        if (input == null) {
            return null;
        }
        return input.replaceAll("[.*+?^${}()|\\[\\]\\\\]", "\\\\$0");
    }

    private int concatPermissions(List<Integer> permissions) {
        return permissions.stream()
                .mapToInt(Integer::intValue)
                .reduce(0, (a, b) -> a | b);
    }
}
