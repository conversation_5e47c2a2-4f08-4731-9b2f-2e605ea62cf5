package com.sinitek.mind.core.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.core.app.constant.AppConstant;
import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.entity.AppVersion;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.AppDetailType;
import com.sinitek.mind.core.app.model.VariableItemType;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.core.app.repository.AppVersionRepository;
import com.sinitek.mind.core.app.service.IAppService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.app.service.IImageService;
import com.sinitek.mind.core.app.util.AppUtil;
import com.sinitek.mind.core.chat.repository.ChatInputGuideRepository;
import com.sinitek.mind.core.chat.repository.ChatItemRepository;
import com.sinitek.mind.core.chat.repository.ChatRepository;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.mind.support.account.dto.SourceMemberDTO;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.openapi.repository.OpenApiRepository;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.outlink.repository.OutLinkRepository;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.*;
import com.sinitek.mind.support.permission.enumerate.AuthTypeEnum;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.ICollaboratorService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.dto.TeamMemberDTO;
import com.sinitek.mind.support.team.service.ITeamMemberService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppServiceImpl implements IAppService {

    private final MongoTemplate mongoTemplate;

    private final AppRepository appRepository;

    private final AppVersionRepository appVersionRepository;

    private final IOperationLogService operationLogService;

    private final ChatItemRepository chatItemRepository;

    private final ChatRepository chatRepository;

    private final OutLinkRepository outLinkRepository;

    private final OpenApiRepository openApiRepository;

    private final ChatInputGuideRepository chatInputGuideRepository;

    private final IImageService imageService;

    private final MongoTransactionManager mongoTransactionManager;

    private final ICollaboratorService collaboratorService;

    private final IAuthService authService;

    private final IPermissionService permissionService;

    private final IAccountService accountService;

    private final IAuthAppService authAppService;

    private final ITeamMemberService teamMemberService;

    private static final List<AppTypeEnum> APP_FOLDER_TYPE_LIST = List.of(AppTypeEnum.FOLDER, AppTypeEnum.HTTP_PLUGIN);

    private static final List<String> FOLDER_TYPE_LIST = List.of(AppTypeEnum.FOLDER.getValue(), AppTypeEnum.HTTP_PLUGIN.getValue());

    @Override
    public List<AppListItemDTO> getAppList(ListAppDTO request) {
        StopWatch sw = new StopWatch("app列表查询耗时：");
        sw.start("权限校验");
        // 校验权限
        AuthMemberDTO authMemberDTO = authService.authUserPer(PermissionConstant.READ_PER);
        if (StringUtils.isNotBlank(request.getParentId())) {
            authAppService.authApp(request.getParentId(), PermissionConstant.READ_PER);
        }
        sw.stop();

        sw.start("获取有权限的appId");
        // 获取所有有权限的appId
        List<Permission> authedAppList = permissionService.findAllAuthedResource(authMemberDTO.getTmbId(),
                ResourceTypeEnum.APP, List.of(AuthTypeEnum.READ, AuthTypeEnum.WRITE, AuthTypeEnum.MANAGE));

        List<String> appIdList = authedAppList.stream()
                .map(Permission::getResourceId)
                .toList();
        sw.stop();

        sw.start("构建查询条件");
        Query query = new Query();

        // 构建所有查询条件的列表
        List<Criteria> criteriaList = new ArrayList<>();

        Criteria idCriteria = new Criteria().orOperator(
                Criteria.where("_id").in(appIdList),
                Criteria.where("tmbId").is(authMemberDTO.getTmbId())
        );
        criteriaList.add(idCriteria);

        if (StringUtils.isNotBlank(request.getNamespace())) {
            criteriaList.add(Criteria.where("namespace").is(request.getNamespace()));
        }

        // 处理最近聊天应用查询
        if (Boolean.TRUE.equals(request.getGetRecentlyChat())) {
            criteriaList.add(Criteria.where("type").in(Arrays.asList(
                    AppTypeEnum.WORKFLOW.getValue(),
                    AppTypeEnum.SIMPLE.getValue(),
                    AppTypeEnum.PLUGIN.getValue())));
            query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        } else {
            // 处理搜索条件
            if (StringUtils.isNotBlank(request.getSearchKey())) {
                String searchKey = escapeRegexChars(request.getSearchKey());
                Criteria searchCriteria = new Criteria().orOperator(
                        Criteria.where("name").regex(searchKey, "i"),
                        Criteria.where("intro").regex(searchKey, "i")
                );
                criteriaList.add(searchCriteria);
            }

            // 处理类型过滤
            if (request.getType() != null) {
                criteriaList.add(Criteria.where("type").in(request.getType()));
            }

            // 处理父级ID过滤
            if (StringUtils.isNotBlank(request.getParentId())) {
                // 查询指定父级ID下的子应用
                criteriaList.add(Criteria.where("parentId").is(request.getParentId()));
            } else {
                // 查询根级应用（parentId为null或空字符串）
                Criteria parentIdCriteria = new Criteria().orOperator(
                        Criteria.where("parentId").isNull(),
                        Criteria.where("parentId").is("")
                );
                criteriaList.add(parentIdCriteria);
            }

            // 设置查询条件
            query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        }
        // 设置排序
        query.with(Sort.by(Sort.Direction.DESC, "updateTime"));

        // 设置限制
        if (Boolean.TRUE.equals(request.getGetRecentlyChat())) {
            query.limit(15);
        } else if (StringUtils.isNotBlank(request.getSearchKey())) {
            query.limit(50);
        }
        sw.stop();

        sw.start("查询db中的应用");
        List<App> apps = mongoTemplate.find(query, App.class);
        sw.stop();

        Set<String> orgIds = new HashSet<>();
        List<String> appIds = new LinkedList<>();
        for (App app : apps) {
            orgIds.add(app.getTmbId());
            appIds.add(app.getId());
        }

        sw.start("查询或构建相关map");
        Map<String, Long> appValueMap = authedAppList.stream()
                .collect(Collectors.toMap(Permission::getResourceId, Permission::getPermission));
        Map<String, SourceMemberDTO> sourceMemberMap = accountService.getSourceMemberMapByOrgIds(new ArrayList<>(orgIds));
        Map<String, Integer> clbNumberMap = collaboratorService.findClbNumberMap(appIds, ResourceTypeEnum.APP);
        sw.stop();

        List<AppListItemDTO> appListItemDTOS = new ArrayList<>();
        sw.start("类型转换");
        for (App app : apps) {
            AppListItemDTO appListItemDTO = new AppListItemDTO();
            appListItemDTO.setName(app.getName());
            appListItemDTO.setAvatar(app.getAvatar());
            appListItemDTO.setType(app.getType());
            appListItemDTO.set_id(app.getId());
            appListItemDTO.setTmbId(app.getTmbId());
            appListItemDTO.setIntro(app.getIntro());
            appListItemDTO.setUpdateTime(app.getUpdateTime());
            appListItemDTO.setPluginData(app.getPluginData());
            appListItemDTO.setInheritPermission(app.getInheritPermission());

            PermissionDTO permissionDTO = new PermissionDTO();
            if (authMemberDTO.getTmbId().equals(app.getTmbId())) {
                permissionDTO.setValue(PermissionConstant.OWNER_PERMISSION_VAL);
            } else {
                long perVal = MapUtils.getLongValue(appValueMap, app.getId(), PermissionConstant.READ_PER);
                permissionDTO.setValue(perVal);
            }
            appListItemDTO.setPermission(permissionDTO);

            int clbNumber = MapUtils.getIntValue(clbNumberMap, app.getId(), 0);
            appListItemDTO.setIsPrivate(clbNumber == 0);

            SourceMemberDTO sourceMember = sourceMemberMap.get(app.getTmbId());
            appListItemDTO.setSourceMember(sourceMember);

            appListItemDTOS.add(appListItemDTO);
        }
        sw.stop();

        log.info(sw.prettyPrint(TimeUnit.MILLISECONDS));
        return appListItemDTOS;
    }

    @Override
    public String createApp(CreateAppDTO body) {
        // 校验参数
        String parentId = body.getParentId();
        String name = body.getName();
        String type = body.getType();
        List<StoreNodeItemType> modules = body.getModules();

        if (!StringUtils.isNotBlank(name) || !StringUtils.isNotBlank(type) || CollUtil.isEmpty(modules)) {
            throw new BussinessException(AppErrorCodeConstant.MISSING_REQUIRED_PARAMS);
        }

        AuthDTO authDTO;
        if (StringUtils.isNotBlank(parentId)) {
            authDTO = authAppService.authApp(parentId, PermissionConstant.WRITE_PER);
        } else {
            authDTO = authService.authUserPer(PermissionConstant.APP_CREATE_PER);
        }

        String teamId = authDTO.getTeamId();
        String tmbId = authDTO.getTmbId();
        String userId = authDTO.getUserId();

        // app创建上限校验 TODO 需要确认是否需要该校验

        TeamMemberDTO teamMemberDTO = teamMemberService.getById(tmbId);

        body.setTeamId(teamId);
        body.setTmbId(tmbId);
        body.setUserAvatar(teamMemberDTO.getAvatar());
        body.setUsername(teamMemberDTO.getMemberName());


        // 创建应用
        String appId = onCreateApp(body);

        return appId;
    }

    @Override
    public AppDetailType getAppDetail(String appId) {
        if (appId == null || appId.isEmpty()) {
            throw new BussinessException(AppErrorCodeConstant.APP_ID_NOT_EXISTS);
        }

        // 权限校验
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);

        App app = authAppDTO.getApp();
        if (!APP_FOLDER_TYPE_LIST.contains(AppTypeEnum.fromValue(app.getType()))) {
            // 文件夹没有这些字段
            AppUtil.rewriteAppWorkflowToDetail(app.getModules(), authAppDTO.getTeamId(), authAppDTO.getIsRoot(), app.getTmbId());
        }

        AppDetailType appDetailType = new AppDetailType();
        BeanUtils.copyProperties(app, appDetailType);

        if (!authAppDTO.getPermission().getHasWritePer()) {
            appDetailType.setModules(List.of());
            appDetailType.setEdges(List.of());
        }

        // 设置_id
        appDetailType.set_id(appDetailType.getId());
        return appDetailType;
    }

    @Override
    public List<App> findAppAndAllChildren(String teamId, String appId) {
        App app = appRepository.findById(appId).orElse(null);
        if (app == null) {
            throw new BussinessException(AppErrorCodeConstant.APP_NOT_FOUND);
        }
        List<App> childApps = findChildrenRecursively(teamId, appId);

        List<App> allApps = new ArrayList<>();
        allApps.add(app);
        allApps.addAll(childApps);
        return allApps;
    }

    @Override
    public List<AppBasicInfoDTO> getAppBasicInfoByIds(String teamId, List<String> ids) {
        List<App> apps = appRepository.findByTeamIdAndIdIn(teamId, ids);

        return apps.stream()
                .map(app -> new AppBasicInfoDTO(app.getId(), app.getName(), app.getAvatar()))
                .collect(Collectors.toList());
    }

    @Override
    public List<App> findByTeamId(String teamId) {
        return appRepository.findByTeamId(teamId);
    }

    @Override
    public List<App> findByTeamIdAndType(String teamId, String type) {
        return appRepository.findByTeamIdAndType(teamId, type);
    }

    @Override
    public List<App> findByTeamIdOrderByUpdateTimeDesc(String teamId) {
        return appRepository.findByTeamIdOrderByUpdateTimeDesc(teamId);
    }

    @Override
    public List<App> findAppAndAllChildren(String teamId, String appId, String fields) {
        App app = appRepository.findById(appId).orElse(null);
        if (app == null) {
            throw new BussinessException(AppErrorCodeConstant.DATASET_NOT_FOUND);
        }

        // 查找所有子应用
        List<App> childApps = findChildren(teamId, app.getId());

        // 组合结果：主应用 + 所有子应用
        List<App> result = new ArrayList<>();
        result.add(app);
        result.addAll(childApps);

        return result;
    }

    @Override
    public void deleteApp(String appId) {

        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.OWNER_PERMISSION_VAL);
        String teamId = authAppDTO.getTeamId();
        String tmbId = authAppDTO.getTmbId();
        String userId = authAppDTO.getUserId();
        List<App> apps = this.findAppAndAllChildren(teamId, appId);
        App app = authAppDTO.getApp();
//        List<App> children = appRepository.findChildrenByTeamIdAndParentId(teamId, appId);
//        List<App> allApps = children;
//        for (App appItem : children) {
//            List<App> recursively = findChildrenRecursively(appItem.getTeamId(), appItem.getId());
//            allApps.addAll(recursively);
//        }
//        allApps.add(app);

        for (App appItem : apps) {
            String id = appItem.getId();
            // 删除聊天相关
            // TODO deleteChatFiles
            chatItemRepository.deleteByAppId(id);

            chatRepository.deleteByAppId(id);

            // 删除分享链接
            outLinkRepository.deleteByAppId(id);

            // 删除 OpenAPI
            openApiRepository.deleteByAppId(id);

            // 删除版本
            appVersionRepository.deleteByAppId(id);

            // 删除输入引导
            chatInputGuideRepository.deleteByAppId(id);

            // 删除权限
            permissionService.deletePermission(id, ResourceTypeEnum.APP);

            // 删除 app
            appRepository.deleteById(id);

            // 删除头像图片
            imageService.removeImageByPath(app.getAvatar());
        }

        // 记录操作日志
        operationLogService.addOperationLog(MindConstant.APP,
                String.format("删除应用【%s】",
                        app == null ? "" : app.getName()));
    }

    @Override
    public String copyApp(String appId) {
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.WRITE_PER);
        App app = authAppDTO.getApp();

        String tmbId;
        if (StringUtils.isNotBlank(app.getParentId())) {
            AuthAppDTO parentAuthAppDTO = authAppService.authApp(app.getParentId(), PermissionConstant.WRITE_PER);
            tmbId = parentAuthAppDTO.getTmbId();
        } else {
            AuthMemberDTO authMemberDTO = authService.authUserPer(PermissionConstant.APP_CREATE_PER);
            tmbId = authMemberDTO.getTmbId();
        }

        CreateAppDTO copyApp = new CreateAppDTO();
        copyApp.setParentId(app.getParentId());
        copyApp.setName(app.getName() + " Copy");
        copyApp.setIntro(app.getIntro());
        copyApp.setAvatar(app.getAvatar());
        copyApp.setType(app.getType());
        copyApp.setModules(app.getModules());
        copyApp.setEdges(app.getEdges());
        copyApp.setChatConfig(app.getChatConfig());
        copyApp.setTeamId(app.getTeamId());
        copyApp.setTmbId(tmbId);
        copyApp.setPluginData(app.getPluginData());
        copyApp.setNamespace(app.getNamespace());
        String newAppId = this.onCreateApp(copyApp);

        // 记录操作日志
        operationLogService.addOperationLog(MindConstant.APP,
                String.format("复制应用【%s】",
                        copyApp.getName()));
        return newAppId;
    }

    @Override
    public App updateApp(AppUpdateDTO params) {
        AtomicReference<App> result = new AtomicReference<>(new App());
        String appId = params.getAppId();
        if (appId == null || appId.isEmpty()) {
            throw new BussinessException(AppErrorCodeConstant.MISSING_PARAMS);
        }
        boolean isMove = params.getParentId() != null;
        // 2. 查询 app 及权限
        Optional<App> appOpt = appRepository.findById(appId);
        if (appOpt.isEmpty()) {
            throw new BussinessException(AppErrorCodeConstant.APP_NOT_EXIST);
        }
        App app = appOpt.get();

        // 3 权限校验
        String targetName;
        String teamId = "";
        String tmbId = "";
        if (isMove) {
            Optional<App> targetAppOpt = appRepository.findById(params.getParentId());
            if (targetAppOpt.isEmpty()) {
                targetName = "";
                throw new BussinessException(AppErrorCodeConstant.TARGET_FOLDER_NOT_EXISTS);
            } else {
                targetName = "root";
            }
            if (app.getParentId() != null && !app.getParentId().isEmpty()) {
                // 原文件夹权限校验（略）
            }
            if (params.getParentId() == null || app.getParentId() == null || app.getParentId().isEmpty()) {
                // 根目录权限校验（略）
            }
        } else {
            targetName = "";
            // 非移动，校验写权限（略）
        }

        // 4. 事务处理
        TransactionTemplate template = new TransactionTemplate(mongoTransactionManager);
        template.execute(status -> {
            if (isMove) {
                // 如果是文件夹，继承权限
                if (AppTypeEnum.FOLDER.getValue().equals(app.getType())) {
                    // 获取父文件夹协作者和分组
                    List<CollaboratorPermissionDTO> parentClbsAndGroups = collaboratorService.getResourceClbsAndGroups(
                            params.getParentId(), ResourceTypeEnum.APP
                    );
                    // 同步自身协作者
                    collaboratorService.syncCollaborators(app.getId(), ResourceTypeEnum.APP, parentClbsAndGroups);
                    // 同步子节点协作者
                    collaboratorService.syncChildrenPermission(app, FOLDER_TYPE_LIST, parentClbsAndGroups);
                } else {
                    operationLogService.addOperationLog(MindConstant.APP,
                            String.format("移动应用【%s】到目录【%s】",
                                    app.getName(), targetName));
                    // 非文件夹，删除所有协作者
                    permissionService.deletePermission(app.getId(), ResourceTypeEnum.APP);
                }
                // 更新
                result.set(doUpdate(app, params, appId, true));
            } else {
                logAppUpdate(tmbId, teamId, app, params.getName(), params.getIntro());
                result.set(doUpdate(app, params, appId, false));
            }
            return result;
        });
        return result.get();
    }

    @Override
    public String transitionWorkFlow(TransitionWorkflowDTO body) {
        String appId = body.getAppId();
        boolean createNew = body.isCreateNew();
        // TODO 需要确认什么样的权限
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);
        App app = authAppDTO.getApp();
        if (createNew) {
            CreateAppDTO appNew = new CreateAppDTO();
            appNew.setParentId(app.getParentId());
            appNew.setName(app.getName() + " Copy");
            appNew.setAvatar(app.getAvatar());
            appNew.setType(app.getType());
            appNew.setModules(app.getModules());
            appNew.setEdges(app.getEdges());
            appNew.setChatConfig(app.getChatConfig());
            appNew.setTeamId(app.getTeamId());
            appNew.setTmbId(app.getTmbId());
            return createApp(appNew);
        }
        updateAppType(appId, AppTypeEnum.WORKFLOW.getValue());
        return "";
    }

    @Override
    public String resumeInheritPermission(String appId) {
        // TODO 需要确认什么样的权限
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);
        App app = authAppDTO.getApp();
        assert app != null;
        if (app.getParentId() != null) {
            // TODO
        } else {
            Query query = new Query(Criteria.where("_id").is(appId));
            Update update = new Update().set("inheritPermission", true);
            mongoTemplate.updateFirst(query, update, App.class);
        }
        return appId;
    }

    @Override
    public List<BasicInfoResDTO> getBasicInfo(String teamId, List<String> ids) {
        List<App> apps = appRepository.findByTeamIdAndIdIn(teamId, ids);
        List<BasicInfoResDTO> res = new ArrayList<>();
        for (App app : apps) {
            BasicInfoResDTO basicInfoResDTO = new BasicInfoResDTO();
            basicInfoResDTO.setId(app.getId());
            basicInfoResDTO.setName(app.getName());
            basicInfoResDTO.setAvatar(app.getAvatar());
            res.add(basicInfoResDTO);
        }
        return res;
    }

    @Override
    public String createSimpleApp(SimpleCreateAppRequestDTO requestDTO) {
        // 校验参数
        String name = requestDTO.getName();
        String type = requestDTO.getType();

        if (!StringUtils.isNotBlank(name) || !StringUtils.isNotBlank(type)) {
            throw new BussinessException(AppErrorCodeConstant.MISSING_REQUIRED_PARAMS);
        }

        if (!type.equals(AppTypeEnum.WORKFLOW.getValue())) {
            throw new BussinessException(AppErrorCodeConstant.ONLY_WORKFLOW_SIMPLE_CREATE);
        }

        AuthDTO authDTO = authService.authUserPer(PermissionConstant.APP_CREATE_PER);
        String teamId = authDTO.getTeamId();
        String tmbId = authDTO.getTmbId();

        // app创建上限校验 TODO 需要确认是否需要该校验

        TeamMemberDTO teamMemberDTO = teamMemberService.getById(tmbId);

        CreateAppDTO body = new CreateAppDTO();

        String namespace = requestDTO.getNamespace();
        String intro = requestDTO.getIntro();
        List<VariableItemType> globalVariables = requestDTO.getGlobalVariables();

        // 设置基本信息
        body.setName(name);
        body.setType(type);
        body.setIntro(intro);
        body.setNamespace(namespace);

        // 设置全局变量
        AppChatConfigType appChatConfigType = new AppChatConfigType();
        appChatConfigType.setVariables(globalVariables);
        body.setChatConfig(appChatConfigType);

        // 设置工作流默认的modules
        body.setModules(AppConstant.WORKFLOW_DEFAULT_MODULES);

        body.setTeamId(teamId);
        body.setTmbId(tmbId);
        body.setUserAvatar(teamMemberDTO.getAvatar());
        body.setUsername(teamMemberDTO.getMemberName());

        // 创建应用
        return onCreateApp(body);
    }

    @Override
    public void deleteAppByNamespace(List<String> namespaceList) {
        if (CollUtil.isEmpty(namespaceList)) {
            return;
        }
        appRepository.deleteByNamespaceIn(namespaceList);
    }

    private void updateAppType(String appId, String type) {
        Query query = new Query(Criteria.where("_id").is(appId));
        Update update = new Update().set("type", type);
        mongoTemplate.updateFirst(query, update, App.class);
    }

    private List<App> findChildren(String appId, String teamId) {
        List<App> children = appRepository.findChildrenByTeamIdAndParentId(teamId, appId);
        List<App> apps = new ArrayList<>(children);

        // 递归查找每个子应用的子应用
        for (App child : children) {
            List<App> grandChildren = findChildren(child.getId(), child.getTeamId());
            apps.addAll(grandChildren);
        }

        return apps;
    }

    private App doUpdate(App app, AppUpdateDTO req, String appId, boolean isMove) {
        // TODO 格式化 nodes 数据（略，假设 beforeUpdateAppFormat 已实现）
        // beforeUpdateAppFormat(req.getNodes());

        // TODO 刷新头像（略，假设 refreshSourceAvatar 已实现）
        // refreshSourceAvatar(req.getAvatar(), app.getAvatar());

        // 如果是工具集类型且有新头像，批量更新子应用头像
        if (AppTypeEnum.TOOL_SET.getValue().equals(app.getType()) && req.getAvatar() != null && !req.getAvatar().isEmpty()) {
            appRepository.updateAvatarByParentIdAndTeamId(req.getAvatar(), appId, app.getTeamId());
        }

        // 构建更新内容
        if (req.getParentId() != null) app.setParentId(req.getParentId());
        if (req.getName() != null) app.setName(req.getName());
        if (req.getType() != null) app.setType(req.getType());
        if (req.getAvatar() != null) app.setAvatar(req.getAvatar());
        if (req.getIntro() != null) app.setIntro(req.getIntro());
        if (req.getTeamTags() != null) app.setTeamTags(req.getTeamTags());
        if (req.getNodes() != null) app.setModules(req.getNodes());
        if (req.getEdges() != null) app.setEdges(req.getEdges());
        if (req.getChatConfig() != null) app.setChatConfig(req.getChatConfig());
        if (req.getNamespace() != null) app.setNamespace(req.getNamespace());
        if (isMove) app.setInheritPermission(true);

        appRepository.save(app);
        return app;
    }

    private void logAppUpdate(String tmbId, String teamId, App app, String name, String intro) {
        StringBuilder names = new StringBuilder();
        StringBuilder values = new StringBuilder();

        if (name != null) {
            names.append(AppUtil.i18n("common:name")).append(",");
            values.append(name).append(",");
        }
        if (intro != null) {
            names.append(AppUtil.i18n("common:Intro")).append(",");
            values.append(intro).append(",");
        }

        operationLogService.addOperationLog(MindConstant.APP,
                String.format("更新应用信息【%s】",
                        app.getName())
        );
    }

    /**
     * 递归查找所有子应用
     */
    private List<App> findChildrenRecursively(String teamId, String parentId) {
        List<App> children = appRepository.findByTeamIdAndParentId(teamId, parentId);
        List<App> allChildren = new ArrayList<>(children);

        // 递归查找每个子应用的子应用
        for (App child : children) {
            List<App> grandChildren = findChildrenRecursively(teamId, child.getId());
            allChildren.addAll(grandChildren);
        }

        return allChildren;
    }

    private String onCreateApp(CreateAppDTO body) {
        App app = new App();
        app.setAvatar(body.getAvatar());
        app.setName(body.getName());
        app.setIntro(body.getIntro());
        app.setTeamId(body.getTeamId());
        app.setTmbId(body.getTmbId());
        app.setModules(body.getModules());
        app.setEdges(body.getEdges());
        app.setChatConfig(body.getChatConfig());
        app.setType(body.getType());
        app.setVersion("v2");
        app.setPluginData(body.getPluginData());
        app.setUpdateTime(new Date());
        app.setNamespace(body.getNamespace());

        // 如果有父级ID，设置父级关系
        if (body.getParentId() != null) {
            app.setParentId(body.getParentId());
        }
        // 保存应用
        App savedApp = appRepository.save(app);
        String appId = savedApp.getId();

        if (!APP_FOLDER_TYPE_LIST.contains(AppTypeEnum.fromValue(savedApp.getType()))) {
            AppVersion appVersion = new AppVersion();
            appVersion.setTmbId(body.getTmbId());
            appVersion.setAppId(appId);
            appVersion.setNodes(body.getModules());
            appVersion.setEdges(body.getEdges());
            appVersion.setChatConfig(body.getChatConfig());
            appVersion.setVersionName(body.getName());
            appVersion.setUsername(body.getUsername());
            appVersion.setAvatar(body.getUserAvatar());
            appVersion.setIsPublish(true);
            appVersionRepository.save(appVersion);
        }

        // 记录操作日志
        Map<String, String> map = new HashMap<>();
        map.put("appName", body.getName());
        map.put("appType", body.getType());
        operationLogService.addOperationLog(MindConstant.APP, String.format("创建应用【%s】", body.getName()));

        return appId;
    }


    private String escapeRegexChars(String input) {
        if (input == null) {
            return null;
        }
        return input.replaceAll("[.*+?^${}()|\\[\\]\\\\]", "\\\\$0");
    }

    private int concatPermissions(List<Integer> permissions) {
        return permissions.stream()
                .mapToInt(Integer::intValue)
                .reduce(0, (a, b) -> a | b);
    }
}
