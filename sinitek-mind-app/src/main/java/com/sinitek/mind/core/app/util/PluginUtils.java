package com.sinitek.mind.core.app.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.common.util.I18nUtil;
import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.mind.core.app.enumerate.*;
import com.sinitek.mind.core.app.model.*;
import com.sinitek.mind.core.workflow.constant.WorkflowConstant;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.template.InputTemplate;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 插件工具类
 * 对应原始的splitCombineToolId函数
 */
@Slf4j
public class PluginUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 拆分组合工具ID
     * 对应原始的splitCombineToolId函数
     *
     * @param combineId 组合ID，格式为 "source-pluginId"
     * @return PluginInfo 包含插件来源和插件ID的对象
     */
    public static SplitCombineToolIdRes splitCombineToolId(String combineId) {
        if (!StringUtils.hasText(combineId)) {
            throw new BussinessException(AppErrorCodeConstant.COMBINE_ID_CANNOT_BE_EMPTY);
        }

        String[] parts = combineId.split("-", 2);
        if (parts.length == 1) {
            // app id
            Long appId = Long.valueOf(parts[0]);
            return new SplitCombineToolIdRes(PluginSourceEnum.PERSONAL.getValue(), appId);
        }

        String sourceStr = parts[0];
        Long pluginId = Long.valueOf(parts[1]);

        PluginSourceEnum source;
        try {
            source = PluginSourceEnum.fromValue(sourceStr);
        } catch (IllegalArgumentException e) {
            throw new BussinessException(AppErrorCodeConstant.INVALID_PLUGIN_SOURCE, sourceStr);
        }

        return new SplitCombineToolIdRes(source.getValue(), pluginId);
    }

    /**
     * 组合工具ID
     *
     * @param source   插件来源
     * @param pluginId 插件ID
     * @return 组合ID
     */
    public static String combineTool(PluginSourceEnum source, String pluginId) {
        if (source == null || !StringUtils.hasText(pluginId)) {
            throw new BussinessException(AppErrorCodeConstant.PLUGIN_SOURCE_AND_ID_CANNOT_BE_EMPTY);
        }
        return source.getValue() + "-" + pluginId;
    }

    /**
     * 从存储节点中获取插件输入
     * 对应TypeScript方法: getPluginInputsFromStoreNodes
     *
     * @param nodes 存储节点列表
     * @return 插件输入列表，如果未找到则返回空列表
     */
    public static List<FlowNodeInputItemType> getPluginInputsFromStoreNodes(List<StoreNodeItemType> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return List.of();
        }

        return nodes.stream()
                .filter(node -> FlowNodeTypeEnum.PLUGIN_INPUT.getValue().equals(node.getFlowNodeType()))
                .findFirst()
                .map(StoreNodeItemType::getInputs)
                .orElse(List.of());
    }

    /**
     * 获取插件运行内容
     * 对应TypeScript方法: getPluginRunContent
     *
     * @param pluginInputs 插件输入列表
     * @param variables    变量映射
     * @return JSON字符串格式的插件输入内容
     */
    public static String getPluginRunContent(List<FlowNodeInputItemType> pluginInputs, Map<String, Object> variables) {
        if (pluginInputs == null) {
            pluginInputs = new ArrayList<>();
        }
        if (variables == null) {
            variables = new HashMap<>();
        }

        Map<String, Object> finalVariables = variables;
        List<Map<String, Object>> pluginInputsWithValue = pluginInputs.stream()
                .map(input -> {
                    // 获取输入项的key
                    String key = input.getKey();

                    // 如果variables中包含该key，使用variables中的值，否则使用默认值
                    Object value = finalVariables.containsKey(key) ? finalVariables.get(key) : input.getDefaultValue();

                    // 创建新的Map，包含原input的所有属性
                    Map<String, Object> inputWithValue = convertInputToMap(input);

                    // 设置value属性
                    inputWithValue.put("value", value);

                    return inputWithValue;
                })
                .toList();

        try {
            return objectMapper.writeValueAsString(pluginInputsWithValue);
        } catch (JsonProcessingException e) {
            log.error("序列化插件输入内容失败", e);
            return "[]";
        }
    }

    /**
     * 将FlowNodeInputItemType转换为Map
     * 对应TypeScript中的 {...input} 展开操作
     *
     * @param input 输入项
     * @return Map表示的输入项
     */
    private static Map<String, Object> convertInputToMap(FlowNodeInputItemType input) {
        Map<String, Object> map = new HashMap<>();

        // 复制所有基本属性（对应TypeScript的展开操作）
        map.put("key", input.getKey());
        map.put("valueType", input.getValueType());
        map.put("valueDesc", input.getValueDesc());
        map.put("label", input.getLabel());
        map.put("debugLabel", input.getDebugLabel());
        map.put("description", input.getDescription());
        map.put("defaultValue", input.getDefaultValue());
        map.put("required", input.getRequired());
        map.put("deprecated", input.getDeprecated());
        map.put("selectedTypeIndex", input.getSelectedTypeIndex());
        map.put("renderTypeList", input.getRenderTypeList());

        // 从父类继承的属性
        map.put("canEdit", input.getCanEdit());
        map.put("placeholder", input.getPlaceholder());
        map.put("max", input.getMax());
        map.put("min", input.getMin());
        map.put("step", input.getStep());
        map.put("markList", input.getMarkList());
        map.put("list", input.getList());
        map.put("llmModelType", input.getLlmModelType());
        map.put("customInputConfig", input.getCustomInputConfig());

        return map;
    }

    public static Double computedPluginUsage(PluginRuntimeType plugin,
                                             List<ChatNodeUsageType> childrenUsage,
                                             Boolean error) {
        if (plugin == null || plugin.getId() == null) {
            return 0.0;
        }

        // 拆分插件ID获取来源
        SplitCombineToolIdRes splitResult = splitCombineToolId(String.valueOf(plugin.getId()));
        String source = splitResult.getSource();

        // 计算子流程总使用点数
        double childrenUsages = 0.0;
        if (childrenUsage != null) {
            childrenUsages = childrenUsage.stream()
                    .mapToDouble(ChatNodeUsageType::getTotalPoints)
                    .sum();
        }

        // 如果不是个人插件（系统插件或商业插件）
        if (!PluginSourceEnum.PERSONAL.getValue().equals(source)) {
            // 如果有错误，返回0
            if (Boolean.TRUE.equals(error)) {
                return 0.0;
            }

            // 获取插件当前费用，默认为0
            double pluginCurrentCost = plugin.getCurrentCost() != null ? plugin.getCurrentCost() : 0.0;

            // 如果有token费用，返回当前费用+子流程费用，否则只返回当前费用
            return Boolean.TRUE.equals(plugin.getHasTokenFee()) ?
                    pluginCurrentCost + childrenUsages :
                    pluginCurrentCost;
        }

        // 个人插件无论成功失败都收取子流程费用
        return childrenUsages;
    }

    public static NodeIOConfig toolSetData2FlowNodeIO(List<StoreNodeItemType> nodes) {
        Optional<StoreNodeItemType> toolSetNodeOpt = nodes.stream()
                .filter(node -> FlowNodeTypeEnum.TOOL_SET.getValue().equals(node.getFlowNodeType()))
                .findFirst();

        NodeIOConfig nodeIOConfig = new NodeIOConfig();

        if (toolSetNodeOpt.isPresent()) {
            StoreNodeItemType storeNodeItemType = toolSetNodeOpt.get();
            nodeIOConfig.setInputs(storeNodeItemType.getInputs());
            nodeIOConfig.setOutputs(storeNodeItemType.getOutputs());
        } else {
            nodeIOConfig.setInputs(List.of());
            nodeIOConfig.setOutputs(List.of());
        }

        return nodeIOConfig;
    }

    public static NodeIOConfig toolData2FlowNodeIO(List<StoreNodeItemType> nodes) {
        Optional<StoreNodeItemType> toolNodeOpt = nodes.stream()
                .filter(node -> FlowNodeTypeEnum.TOOL.getValue().equals(node.getFlowNodeType()))
                .findFirst();

        NodeIOConfig nodeIOConfig = new NodeIOConfig();

        if (toolNodeOpt.isPresent()) {
            StoreNodeItemType storeNodeItemType = toolNodeOpt.get();
            nodeIOConfig.setInputs(storeNodeItemType.getInputs());
            nodeIOConfig.setOutputs(storeNodeItemType.getOutputs());
        } else {
            nodeIOConfig.setInputs(List.of());
            nodeIOConfig.setOutputs(List.of());
        }

        return nodeIOConfig;
    }

    public static NodeIOConfig pluginData2FlowNodeIO(List<StoreNodeItemType> nodes) {
        Optional<StoreNodeItemType> pluginInputOpt = nodes.stream()
                .filter(node -> FlowNodeTypeEnum.PLUGIN_INPUT.getValue().equals(node.getFlowNodeType()))
                .findFirst();
        Optional<StoreNodeItemType> pluginOutputOpt = nodes.stream()
                .filter(node -> FlowNodeTypeEnum.PLUGIN_OUTPUT.getValue().equals(node.getFlowNodeType()))
                .findFirst();

        NodeIOConfig nodeIOConfig = new NodeIOConfig();

        if (pluginInputOpt.isPresent()) {
            StoreNodeItemType storeNodeItemType = pluginInputOpt.get();
            List<FlowNodeInputItemType> inputs = storeNodeItemType.getInputs();
            List<FlowNodeInputItemType> list = new ArrayList<>(inputs.stream()
                    .map(item -> {
                        FlowNodeInputItemType inputItem = new FlowNodeInputItemType();
                        BeanUtils.copyProperties(item, inputItem);

                        Object value = getOrInitModuleInputValue(item);
                        inputItem.setValue(value);
                        inputItem.setCanEdit(false);

                        List<String> renderTypeList = item.getRenderTypeList();
                        if (renderTypeList != null && !renderTypeList.isEmpty()) {
                            String renderType = renderTypeList.get(0);
                            if (FlowNodeInputTypeEnum.CUSTOM_VARIABLE.getValue().equals(renderType)) {
                                inputItem.setRenderTypeList(List.of(FlowNodeInputTypeEnum.REFERENCE.getValue(),
                                        FlowNodeInputTypeEnum.INPUT.getValue()));
                            } else {
                                inputItem.setRenderTypeList(item.getRenderTypeList());
                            }
                        }

                        return inputItem;
                    }).toList());
            list.add(InputTemplate.INPUT_TEMPLATE_STREAM_MODE);
            nodeIOConfig.setInputs(list);
        } else {
            nodeIOConfig.setInputs(List.of());
        }

        if (pluginOutputOpt.isPresent()) {
            StoreNodeItemType storeNodeItemType = pluginOutputOpt.get();
            List<FlowNodeInputItemType> inputs = storeNodeItemType.getInputs();
            List<FlowNodeOutputItemType> list = inputs.stream()
                    .map(item -> {
                        FlowNodeOutputItemType outputItem = new FlowNodeOutputItemType();
                        outputItem.setId(item.getKey());
                        outputItem.setType(FlowNodeOutputTypeEnum.STATIC.getValue());
                        outputItem.setKey(item.getKey());
                        outputItem.setValueType(item.getValueType());
                        outputItem.setLabel(StringUtils.hasText(item.getLabel()) ? item.getLabel() : item.getKey());
                        outputItem.setDescription(item.getDescription());
                        return outputItem;
                    })
                    .toList();
            nodeIOConfig.setOutputs(list);
        } else {
            nodeIOConfig.setOutputs(List.of());
        }

        return nodeIOConfig;
    }
    public static NodeIOConfig appData2FlowNodeIO(AppChatConfigType chatConfig) {
        List<FlowNodeInputItemType> variableInput;
        List<VariableItemType> variables = chatConfig.getVariables();
        if (variables != null && !variables.isEmpty()) {
            Map<String, List<String>> renderTypeMap = Map.of(VariableInputEnum.INPUT.getValue(), List.of(FlowNodeInputTypeEnum.INPUT.getValue(), FlowNodeInputTypeEnum.REFERENCE.getValue()),
                    VariableInputEnum.TEXTAREA.getValue(), List.of(FlowNodeInputTypeEnum.TEXTAREA.getValue(), FlowNodeInputTypeEnum.REFERENCE.getValue()),
                    VariableInputEnum.NUMBER_INPUT.getValue(), List.of(FlowNodeInputTypeEnum.NUMBER_INPUT.getValue()),
                    VariableInputEnum.SELECT.getValue(), List.of(FlowNodeInputTypeEnum.SELECT.getValue()),
                    VariableInputEnum.CUSTOM.getValue(), List.of(FlowNodeInputTypeEnum.INPUT.getValue(), FlowNodeInputTypeEnum.REFERENCE.getValue()),
                    "default", List.of(FlowNodeInputTypeEnum.REFERENCE.getValue()));
            variableInput = variables.stream()
                    .map(item -> {
                        FlowNodeInputItemType nodeInput = new FlowNodeInputItemType();
                        nodeInput.setKey(item.getKey());
                        nodeInput.setRenderTypeList(renderTypeMap.getOrDefault(item.getType(), renderTypeMap.get("default")));
                        nodeInput.setLabel(item.getLabel());
                        nodeInput.setDebugLabel(item.getLabel());
                        nodeInput.setDescription("");
                        nodeInput.setValueType(WorkflowIOValueTypeEnum.ANY.getValue());
                        nodeInput.setRequired(item.getRequired());
                        List<ListInfo> enums = item.getEnums();
                        if (enums != null && !enums.isEmpty()) {
                            nodeInput.setList(enums.stream()
                                    .map(enumItem -> {
                                        ListInfo listInfo = new ListInfo();
                                        listInfo.setLabel(enumItem.getValue());
                                        listInfo.setValue(enumItem.getValue());
                                        return listInfo;
                                    }).toList());
                        }
                        return nodeInput;
                    }).toList();
        } else {
            variableInput = List.of();
        }

        NodeIOConfig nodeIOConfig = new NodeIOConfig();

        List<FlowNodeInputItemType> inputs = new LinkedList<>();
        inputs.add(InputTemplate.INPUT_TEMPLATE_STREAM_MODE);
        inputs.add(InputTemplate.INPUT_TEMPLATE_HISTORY);
        if (chatConfig.getFileSelectConfig() != null) {
            AppFileSelectConfigType fileSelectConfig = chatConfig.getFileSelectConfig();
            if (fileSelectConfig.getCanSelectFile() || fileSelectConfig.getCanSelectImg()) {
                inputs.add(InputTemplate.INPUT_TEMPLATE_FILE_LINK);
            }
        }
        inputs.add(InputTemplate.INPUT_TEMPLATE_USER_CHAT_INPUT);
        inputs.addAll(variableInput);
        nodeIOConfig.setInputs(inputs);

        FlowNodeOutputItemType outputItemType1 = new FlowNodeOutputItemType();
        outputItemType1.setId(NodeOutputKeyEnum.HISTORY.getValue());
        outputItemType1.setKey(NodeOutputKeyEnum.HISTORY.getValue());
        outputItemType1.setRequired(true);
        outputItemType1.setLabel(I18nUtil.t("core.module.output.label.New context"));
        outputItemType1.setDescription(I18nUtil.t("core.module.output.description.New context"));
        outputItemType1.setValueType(WorkflowIOValueTypeEnum.CHAT_HISTORY.getValue());
        outputItemType1.setValueDesc(WorkflowConstant.chatHistoryValueDesc);
        outputItemType1.setType(FlowNodeOutputTypeEnum.STATIC.getValue());
        FlowNodeOutputItemType outputItemType2 = new FlowNodeOutputItemType();
        outputItemType2.setId(NodeOutputKeyEnum.ANSWER_TEXT.getValue());
        outputItemType2.setKey(NodeOutputKeyEnum.ANSWER_TEXT.getValue());
        outputItemType2.setRequired(false);
        outputItemType2.setLabel(I18nUtil.t("core.module.output.label.Ai response content"));
        outputItemType2.setDescription(I18nUtil.t("core.module.output.description.Ai response content"));
        outputItemType2.setValueType(WorkflowIOValueTypeEnum.STRING.getValue());
        outputItemType2.setType(FlowNodeOutputTypeEnum.STATIC.getValue());

        nodeIOConfig.setOutputs(List.of(outputItemType1, outputItemType2));
        return nodeIOConfig;
    }

    /**
     * 获取处理句柄配置
     * @param top
     * @param right
     * @param bottom
     * @param left
     * @return
     */
    public static HandleType getHandleConfig(boolean top, boolean right, boolean bottom, boolean left) {
        HandleType handleType = new HandleType();
        handleType.setLeft(left);
        handleType.setRight(right);
        handleType.setTop(top);
        handleType.setBottom(bottom);
        return handleType;
    }

    /**
     * 获取或初始化模块输入值
     * @param input
     * @return false/0/""
     */
    public static Object getOrInitModuleInputValue(FlowNodeInputItemType input) {
        if (!Objects.isNull(input.getValue()) || !StringUtils.hasText(input.getValueType())) {
            return input.getValue();
        }
        if (!Objects.isNull(input.getDefaultValue())) {
            return input.getDefaultValue();
        }
        String valueType = input.getValueType();

        if (WorkflowIOValueTypeEnum.BOOLEAN.getValue().equals(valueType)) {
            return false;
        } else if (WorkflowIOValueTypeEnum.NUMBER.getValue().equals(valueType)) {
            return 0;
        } else if (WorkflowIOValueTypeEnum.STRING.getValue().equals(valueType)) {
            return "";
        }

        return "";
    }
}