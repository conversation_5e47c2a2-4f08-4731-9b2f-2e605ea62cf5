package com.sinitek.mind.core.app.model;

import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import lombok.Data;

import java.util.List;

@Data
public class PluginRuntimeType {

    private Long id;

    private String teamId;

    private String tmbId;

    private String name;

    private String avatar;

    private Boolean showStatus;

    private Boolean isTool;

    private List<StoreNodeItemType> nodes;

    private List<StoreEdgeItemType> edges;

    private Double currentCost;

    private Boolean hasTokenFee;
}
