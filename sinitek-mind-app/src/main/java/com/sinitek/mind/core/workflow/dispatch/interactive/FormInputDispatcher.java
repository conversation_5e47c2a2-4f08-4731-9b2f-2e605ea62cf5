package com.sinitek.mind.core.workflow.dispatch.interactive;

import cn.hutool.json.JSONUtil;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.*;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户输入都内容，将会以 JSON 字符串格式进入工作流，可以从 query 的 text 中获取。
 */
@Component
public class FormInputDispatcher implements NodeDispatcher {

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.FORM_INPUT.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        Map<String, Object> params = dispatchData.getParams();
        List<ChatItemType> histories = dispatchData.getHistories();
        RuntimeNodeItemType node = dispatchData.getNode();
        List<ChatItemValueItemType> query = dispatchData.getQuery();
        WorkflowInteractiveResponseType lastInteractive = dispatchData.getLastInteractive();

        String description = (String) params.get(NodeInputKeyEnum.DESCRIPTION.getValue());
        List<UserInputFormItemType> userInputForms = (List<UserInputFormItemType>) params.get(NodeInputKeyEnum.USER_INPUT_FORMS.getValue());

        Boolean isEntry = node.getIsEntry();

        // Interactive node is not the entry node, return interactive result
        if (!isEntry || !Objects.equals(lastInteractive.getNodeResponse().getType(), "userInput")) {
            UserInputInteractive userInputInteractive = UserInputInteractive.builder()
                    .params(UserInputInteractive.Params.builder()
                            .description(description)
                            .inputForm(userInputForms)
                            .build())
                    .build();
            return Map.of(DispatchNodeResponseKeyEnum.INTERACTIVE.getValue(), userInputInteractive);
        }

        node.setIsEntry(false);

        RuntimePromptType runtimePromptType = ChatAdaptor.chatValue2RuntimePrompt(query);
        String text = runtimePromptType.getText();

        Map<String, Object> userInputVal = JSONUtil.toBean(text, Map.class);

        Map<String, Object> result = new HashMap<>(userInputVal);

        // Removes the current session record as the history of subsequent nodes
        result.put(DispatchNodeResponseKeyEnum.REWRITE_HISTORIES.getValue(), histories.subList(0, histories.size() - 2));
        result.put(NodeOutputKeyEnum.FORM_INPUT_RESULT.getValue(), userInputVal);
        result.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), userInputVal);
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                .formInputResult(userInputVal.toString())
                .build());

        return result;
    }
}
