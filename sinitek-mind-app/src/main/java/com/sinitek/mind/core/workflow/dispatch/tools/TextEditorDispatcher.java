package com.sinitek.mind.core.workflow.dispatch.tools;

import cn.hutool.json.JSONUtil;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class TextEditorDispatcher implements NodeDispatcher {

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.TEXT_EDITOR.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {

        Map<String, Object> params = dispatchData.getParams();
        Map<String, Object> variables = dispatchData.getVariables();

        String text = (String) params.get(NodeInputKeyEnum.TEXTAREA_INPUT.getValue());
        Map<String, Object> customerVariables = (Map<String, Object>) params.get(NodeInputKeyEnum.ADD_INPUT_PARAM.getValue());

        if (customerVariables == null) {
            customerVariables = new HashMap<>();
        }

        Map<String, Object> newVariables = new HashMap<>();
        customerVariables.forEach((key, value) -> {
            String convertedValue;

            if (value instanceof Map || value instanceof Iterable) {
                convertedValue = JSONUtil.toJsonStr(value);
            } else if (value instanceof Number) {
                convertedValue = value.toString();
            } else if (value instanceof Boolean) {
                convertedValue = ((Boolean) value) ? "true" : "false";
            } else {
                convertedValue = value != null ? value.toString() : "";
            }

            newVariables.put(key, convertedValue);
        });

        newVariables.putAll(variables);
        Object textResult = WorkflowUtil.replaceVariable(text, newVariables);

        Map<String, Object> result = new HashMap<>();
        result.put(NodeOutputKeyEnum.TEXT.getValue(), textResult);
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                .textOutput((String) textResult)
                .build());
        return result;
    }

}
