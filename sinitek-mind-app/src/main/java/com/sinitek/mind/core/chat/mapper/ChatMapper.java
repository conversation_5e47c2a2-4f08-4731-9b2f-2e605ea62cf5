package com.sinitek.mind.core.chat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.mind.core.chat.dto.*;
import com.sinitek.mind.core.chat.entity.Chat;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChatMapper extends BaseMapper<Chat> {
    Page<Chat> pageByParamDTO(@Param("params") ChatPageParamDTO param, @Param("page") Page<Chat> page);
    
    Page<ChatLogResponse> getChatLogs(@Param("params") ChatLogParamDTO params, @Param("page") Page<ChatLogResponse> page);
    
    List<ChatLogExportDTO> getChatLogsForExport(@Param("params") ChatLogExportParamDTO params);
}
