package com.sinitek.mind.core.workflow.dispatch.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.app.model.JsonSchemaPropertiesItemType;
import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.model.*;
import com.sinitek.mind.core.dataset.model.SearchDataResponseItemType;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.util.FileUtil;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.tools.Tool;
import java.util.*;
import java.util.stream.Collectors;

public class DispatchUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static String Prompt_Tool_Call = "<Instruction>\n" +
            "你是一个智能机器人，除了可以回答用户问题外，你还掌握工具的使用能力。有时候，你可以依赖工具的运行结果，来更准确的回答用户。\n" +
            "\n" +
            "工具使用了 JSON Schema 的格式声明，其中 toolId 是工具的唯一标识， description 是工具的描述，parameters 是工具的参数及参数表述，required 是必填参数的列表。\n" +
            "\n" +
            "请你根据工具描述，决定回答问题或是使用工具。在完成任务过程中，USER代表用户的输入，TOOL_RESPONSE代表工具运行结果，ANSWER 代表你的输出。\n" +
            "你的每次输出都必须以0,1开头，代表是否需要调用工具：\n" +
            "0: 不使用工具，直接回答内容。\n" +
            "1: 使用工具，返回工具调用的参数。\n" +
            "\n" +
            "例如：\n" +
            "\n" +
            "USER: 你好呀\n" +
            "ANSWER: 0: 你好，有什么可以帮助你的么？\n" +
            "USER: 现在几点了？\n" +
            "ANSWER:  1: {\"toolId\":\"searchToolId1\"}\n" +
            "TOOL_RESPONSE: \"\"\"\n" +
            "2022/5/5 12:00 Thursday\n" +
            "\"\"\"\n" +
            "ANSWER: 0: 现在是2022年5月5日，星期四，中午12点。\n" +
            "USER: 今天杭州的天气如何？\n" +
            "ANSWER: 1: {\"toolId\":\"searchToolId2\",\"arguments\":{\"city\": \"杭州\"}}\n" +
            "TOOL_RESPONSE: \"\"\"\n" +
            "晴天......\n" +
            "\"\"\"\n" +
            "ANSWER: 0: 今天杭州是晴天。\n" +
            "USER: 今天杭州的天气适合去哪里玩？\n" +
            "ANSWER: 1: {\"toolId\":\"searchToolId3\",\"arguments\":{\"query\": \"杭州 天气 去哪里玩\"}}\n" +
            "TOOL_RESPONSE: \"\"\"\n" +
            "晴天. 西湖、灵隐寺、千岛湖……\n" +
            "\"\"\"\n" +
            "ANSWER: 0: 今天杭州是晴天，适合去西湖、灵隐寺、千岛湖等地玩。\n" +
            "</Instruction>\n" +
            "\n" +
            "------\n" +
            "\n" +
            "现在，我们开始吧！下面是你本次可以使用的工具：\n" +
            "\n" +
            "\"\"\"\n" +
            "{{toolsPrompt}}\n" +
            "\"\"\"\n" +
            "\n" +
            "下面是正式的对话内容：\n" +
            "\n" +
            "USER: {{question}}\n" +
            "ANSWER: ";
    /**
     * 获取历史记录
     */
    public static List<ChatItemType> getHistories(Integer historyLimit, List<ChatItemType> histories) {
        if (CollectionUtils.isEmpty(histories)) {
            return new ArrayList<>();
        }

        // 找到系统消息的分界点
        int systemHistoryIndex = 0;
        for (int i = 0; i < histories.size(); i++) {
            if (!ChatRoleEnum.SYSTEM.equals(histories.get(i).getObj())) {
                systemHistoryIndex = i;
                break;
            }
        }

        List<ChatItemType> systemHistories = histories.subList(0, systemHistoryIndex);
        List<ChatItemType> chatHistories = histories.subList(systemHistoryIndex, histories.size());

        // 根据历史限制过滤
        List<ChatItemType> filterHistories;
        if (historyLimit != null && historyLimit > 0) {
            int maxSize = historyLimit * 2; // 用户和AI的对话对
            if (chatHistories.size() > maxSize) {
                filterHistories = chatHistories.subList(chatHistories.size() - maxSize, chatHistories.size());
            } else {
                filterHistories = new ArrayList<>(chatHistories);
            }
        } else {
            filterHistories = new ArrayList<>();
        }

        List<ChatItemType> result = new ArrayList<>(systemHistories);
        result.addAll(filterHistories);
        return result;
    }


    /**
     * 过滤工具节点ID
     */
    public static List<String> filterToolNodeIdByEdges(String nodeId, List<RuntimeEdgeItemType> edges) {
        return edges.stream()
                .filter(edge -> nodeId.equals(edge.getSource()))
                .map(RuntimeEdgeItemType::getTarget)
                .collect(Collectors.toList());
    }

    /**
     * 从聊天历史中获取文件链接
     * 对应 TypeScript 中的 getHistoryFileLinks 函数
     *
     * @param histories 聊天历史列表
     * @return 文件链接列表
     */
    public static List<String> getHistoryFileLinks(List<ChatItemType> histories) {
        if (CollectionUtils.isEmpty(histories)) {
            return new ArrayList<>();
        }

        return histories.stream()
                // 过滤出人类角色的消息
                .filter(item -> {
                    if (ChatRoleEnum.HUMAN.equals(item.getObj())) {
                        // 检查是否包含文件类型的值
                        return item.getValue() != null &&
                                item.getValue().stream()
                                        .anyMatch(value -> "file".equals(value.getType()));
                    }
                    return false;
                })
                // 提取文件URL
                .flatMap(item -> {
                    List<ChatItemValueItemType> values = item.getValue();

                    // 转换为用户聊天项值类型并提取文件URL
                    return values.stream()
                            .filter(valueItem -> "file".equals(valueItem.getType()))
                            .map(valueItem -> {
                                ChatItemValueItemFileInfo file = valueItem.getFile();
                                return file != null ? file.getUrl() : null;
                            })
                            .filter(StringUtils::hasText);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 适配工具调用消息
     * 对应 TypeScript 中的 toolCallMessagesAdapt 函数
     *
     * @param userInput 包含用户输入和跳过标志的参数对象
     * @param skip 是否跳过
     * @return 适配后的用户聊天项列表
     */
    public static List<ChatItemValueItemType> toolCallMessagesAdapt(List<ChatItemValueItemType> userInput, Boolean skip) {
        // 如果跳过，直接返回原输入
        if (Boolean.TRUE.equals(skip)) {
            return userInput;
        }

        // 过滤出文件类型的项
        List<ChatItemValueItemType> files = userInput.stream()
                .filter(item -> "file".equals(item.getType()))
                        .toList();

        if (!files.isEmpty()) {
            // 统计文件数量
            long filesCount = files.stream()
                    .filter(file -> file.getFile() != null && "file".equals(file.getFile().getType()))
                            .count();

            long imgCount = files.stream()
                    .filter(file -> file.getFile() != null && "image".equals(file.getFile().getType()))
                            .count();

            // 检查是否有文本类型的项
            boolean hasTextItem = userInput.stream()
                    .anyMatch(item -> "text".equals(item.getType()));

            if (hasTextItem) {
                // 映射处理每个项
                return userInput.stream()
                        .map(item -> {
                            if ("text".equals(item.getType())) {
                                String text = item.getText() != null && item.getText().getContent() != null
                                        ? item.getText().getContent() : "";

                                // 创建新的文本内容
                                ChatItemValueItemType newItem = new ChatItemValueItemType();
                                // 复制原有属性
                                BeanUtils.copyProperties(item, newItem);

                                // 设置新的文本内容
                                MultiplePromptParams params = new MultiplePromptParams();
                                params.setFileCount((int) filesCount);
                                params.setImgCount((int) imgCount);
                                params.setQuestion(text);
                                ChatItemValueItemTextInfo newTextContent = new ChatItemValueItemTextInfo();
                                newTextContent.setContent(getMultiplePrompt(params));
                                newItem.setText(newTextContent);

                                return newItem;
                            }
                            return item;
                        })
                        .collect(Collectors.toList());
        }

            // 所有输入都是文件的情况
            ChatItemValueItemType textItem = new ChatItemValueItemType();
            textItem.setType(ChatItemValueType.TEXT.getValue());

            MultiplePromptParams params = new MultiplePromptParams();
            params.setFileCount((int) filesCount);
            params.setImgCount((int) imgCount);
            params.setQuestion("");

            ChatItemValueItemTextInfo textContent = new ChatItemValueItemTextInfo();
            textContent.setContent(getMultiplePrompt(params));
            textItem.setText(textContent);

            return Collections.singletonList(textItem);
    }

    return userInput;
    }



    /**
     * 生成多媒体会话提示词
     * 对应 TypeScript 中的 getMultiplePrompt 函数
     *
     * @param params 包含文件数量、图片数量和问题的参数对象
     * @return 格式化后的提示词
     */
    public static String getMultiplePrompt(MultiplePromptParams params) {
        String prompt = "Number of session file inputs：\n" +
                "Document：{{fileCount}}\n" +
                "Image：{{imgCount}}\n" +
                "------\n" +
                "{{question}}";

        Map<String, Object> variables = new HashMap<>();
        variables.put("fileCount", params.getFileCount());
        variables.put("imgCount", params.getImgCount());
        variables.put("question", params.getQuestion());

        return (String) WorkflowUtil.replaceVariable(prompt, variables);
    }

    /**
     * 检查引用QA值的有效性
     * 对应 TypeScript 中的 checkQuoteQAValue 函数
     *
     * @param quoteQA 搜索数据响应项列表
     * @return 验证后的列表，如果无效则返回null，如果为空则返回空列表
     */
    public static List<SearchDataResponseItemType> checkQuoteQAValue(List<SearchDataResponseItemType> quoteQA) {
        // 如果输入为null，返回null
        if (quoteQA == null) {
            return null;
        }

        // 如果列表为空，返回空列表
        if (quoteQA.isEmpty()) {
            return new ArrayList<>();
        }

        // 检查列表中是否有无效项（null对象或缺少q字段）
        boolean hasInvalidItem = quoteQA.stream()
                .anyMatch(item -> item == null ||
                        StringUtils.isEmpty(item.getQ()));

        if (hasInvalidItem) {
            return null;
        }

        return quoteQA;
    }

    /**
     * 过滤系统变量
     * @param variables 包含所有变量的Map
     * @return 系统变量对象
     */
    public static SystemVariablesType filterSystemVariables(Map<String, Object> variables) {
        SystemVariablesType systemVars = new SystemVariablesType();

        systemVars.setUserId((String) variables.get("userId"));
        systemVars.setAppId((String) variables.get("appId"));
        systemVars.setChatId((String) variables.get("chatId"));
        systemVars.setResponseChatItemId((String) variables.get("responseChatItemId"));

        // 处理histories字段，需要类型转换
        Object historiesObj = variables.get("histories");
        if (historiesObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<ChatItemType> histories = (List<ChatItemType>) historiesObj;
            systemVars.setHistories(histories);
        }

        // 处理cTime字段
        String cTimeObj = (String) variables.get("cTime");
        systemVars.setCTime(cTimeObj);

        return systemVars;
    }

    /**
     * 根据提取键生成JSON Schema属性
     * @param actionProps 包含extractKeys参数的动作属性
     * @return JSON Schema属性映射
     */
    public static Map<String, PropertiesValue> getJsonSchema(ActionPropsCE actionProps) {
        Map<String, Object> params = actionProps.getParams();
        List<ContextExtractAgentItemType> extractKeys = getListParam(params, "extractKeys");

        Map<String, PropertiesValue> properties = new HashMap<>();
        Map<String, JsonSchemaPropertiesItemType> valueTypeJsonSchemaMap = WorkflowUtil.createValueTypeJsonSchemaMap();

        for (ContextExtractAgentItemType item : extractKeys) {
            // 获取JSON Schema，优先使用valueType对应的schema，否则使用默认schema
            JsonSchemaPropertiesItemType jsonSchema;
            if (item.getValueType() != null && !item.getValueType().isEmpty()) {
                jsonSchema = valueTypeJsonSchemaMap.get(item.getValueType());
                if (jsonSchema == null) {
                    jsonSchema = WorkflowUtil.TOOL_VALUE_TYPE_LIST.get(0).getJsonSchema();
                }
            } else {
                jsonSchema = WorkflowUtil.TOOL_VALUE_TYPE_LIST.get(0).getJsonSchema();
            }

            // 创建属性对象
//            Map<String, Object> property = new HashMap<>(objectMapper.convertValue(jsonSchema, Map.class));
            jsonSchema.setDescription(item.getDesc());
//            property.put("description", item.getDesc());

            // 如果有枚举值，添加enum属性
            if (item.getEnums() != null && !item.getEnums().trim().isEmpty()) {
                List<String> enumList = Arrays.stream(item.getEnums().split("\n"))
                        .filter(s -> !s.trim().isEmpty())
                        .collect(Collectors.toList());
                if (!enumList.isEmpty()) {
                    jsonSchema.setEnums(enumList);
//                    property.put("enum", enumList);
                }
            }
            PropertiesValue vl = new PropertiesValue();
            BeanUtils.copyProperties(jsonSchema, vl);
            properties.put(item.getKey(), vl);
        }

        return properties;
    }

    /**
     * 从link中获取文件内容
     */
    public static FileContentFromLinksResult getFileContentFromLinks(List<String> urls, String requestOrigin, Integer maxFiles, String teamId, String tmbId, Boolean customPdfParse) {
        List<String> parseUrlList = urls.stream()
                // 过滤无效url
                .filter(Objects::nonNull)
                .filter(url -> url.startsWith("/") || url.startsWith("http") || url.startsWith("ws"))
                // 只获取文档类型文件
                .filter(url -> {
                    ChatItemValueItemFileInfo fileInfo = FileUtil.parseUrlToFileType(url);
                    return fileInfo != null && "file".equals(fileInfo.getType());
                })
                .map(url -> {
                    try {
                        String processedUrl = url;
                        // 检查是否系统上传文件
                        if (processedUrl.startsWith("/") || (requestOrigin != null && processedUrl.startsWith(requestOrigin))) {
                            // 移除 origin(直接进行内网请求)
                            if (requestOrigin != null && processedUrl.startsWith(requestOrigin)) {
                                processedUrl = processedUrl.replace(requestOrigin, "");
                            }
                        }
                        return processedUrl;
                    } catch (Exception error) {
                        return "";
                    }
                })
                .filter(processedUrl -> !processedUrl.isEmpty())
                .limit(maxFiles)
                .toList();

        // TODO 待完成
        List<ReadFilesResult> readFilesResult = new ArrayList<>();
        List<String> textList = new ArrayList<>();
        for (ReadFilesResult result : readFilesResult) {
            if (result != null && result.getText() != null) {
                textList.add(result.getText());
            } else {
                textList.add("");
            }
        }
        String text = String.join("\\n******\\n", textList);
        FileContentFromLinksResult fileContentFromLinksResult = new FileContentFromLinksResult();
        fileContentFromLinksResult.setReadFilesResult(readFilesResult);
        fileContentFromLinksResult.setText(text);
        return fileContentFromLinksResult;
    }

    public static List<AIChatItemValueItemType> filterToolResponseToPreview(List<AIChatItemValueItemType> response) {
        List<AIChatItemValueItemType> res = new ArrayList<>();
        for (AIChatItemValueItemType type : response) {
            if (ChatItemValueType.TOOL.getValue().equals(type.getType())) {
                List<ToolModuleResponseItemType> tools = type.getTools();
                List<ToolModuleResponseItemType> formatTools = new ArrayList<>();

                for (ToolModuleResponseItemType toolType : tools) {
                    ToolModuleResponseItemType newTool = new ToolModuleResponseItemType();
                    BeanUtils.copyProperties(toolType, newTool);
                    newTool.setResponse(sliceStrStartEnd(toolType.getResponse(), 500, 500));
                    formatTools.add(newTool);
                }
                AIChatItemValueItemType newType = new AIChatItemValueItemType();
                BeanUtils.copyProperties(type, newType);
                newType.setTools(formatTools);
                res.add(newType);
            }
            res.add(type);
        }
        return res;
    }

    public static String sliceStrStartEnd(String str, int start, int end) {
        if (str == null) return "";

        int totalLength = str.length();
        // 检查是否超过截取范围
        boolean overSize = totalLength > (start + end);

        if (!overSize) {
            return str;
        }

        // 截取开头部分
        String startContent = str.substring(0, start);
        // 截取结尾部分
        String endContent = str.substring(totalLength - end);

        // 计算隐藏字符数
        int hiddenChars = totalLength - start - end;

        // 构建结果字符串
        return startContent +
                "\n\n...[hide " + hiddenChars + " chars]...\n\n" +
                endContent;
    }

    /**
     * 获取List参数
     */
    @SuppressWarnings("unchecked")
    private static <T> List<T> getListParam(Map<String, Object> params, String key) {
        Object value = params.get(key);
        if (value instanceof List) {
            return (List<T>) value;
        }
        return new ArrayList<>();
    }

}
