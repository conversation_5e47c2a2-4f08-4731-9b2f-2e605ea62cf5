package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.model.dto.SystemModelDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class DispatchToolModuleProps extends ModuleDispatchProps {

    /**
     * 此类型的param包含
     *   [NodeInputKeyEnum.history]?: ChatItemType[]; ---> List<ChatItemType> history
     *   [NodeInputKeyEnum.userChatInput]: string; ---> String userChatInput
     *
     *   [NodeInputKeyEnum.fileUrlList]?: string[]; ---> List<String> fileUrlList
     *   [NodeInputKeyEnum.aiModel]: string; ---> String model
     *   [NodeInputKeyEnum.aiSystemPrompt]: string; ---> String systemPrompt
     *   [NodeInputKeyEnum.aiChatTemperature]: number; ---> double temperature
     *   [NodeInputKeyEnum.aiChatMaxToken]: number; ---> double maxToken
     *   [NodeInputKeyEnum.aiChatVision]?: boolean; ---> Boolean aiChatVision
     *   [NodeInputKeyEnum.aiChatReasoning]?: boolean; ---> Boolean aiChatReasoning
     *   [NodeInputKeyEnum.aiChatTopP]?: number; ---> double aiChatTopP
     *   [NodeInputKeyEnum.aiChatStopSign]?: string; ---> String aiChatStopSign
     *   [NodeInputKeyEnum.aiChatResponseFormat]?: string; ---> String aiChatResponseFormat
     *   [NodeInputKeyEnum.aiChatJsonSchema]?: string; ---> String aiChatJsonSchema
     */

    private List<ChatCompletionMessageParam> messages;

    private List<ToolNodeItemType> toolNodes;

    private SystemModelDTO toolModel;

    private WorkflowInteractiveResponseType.ToolParams interactiveEntryToolParams;

    private Integer maxRunToolTimes;
}
