package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * app版本信息；对应getAppVersionById返回值
 */
@Data
@AllArgsConstructor
public class AppVersionDTO {

    private Long versionId;

    private String versionName;

    private List<StoreNodeItemType> nodes;

    private List<StoreEdgeItemType> edges;

    private AppChatConfigType chatConfig;
}
