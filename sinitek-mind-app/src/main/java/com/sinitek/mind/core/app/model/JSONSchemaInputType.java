package com.sinitek.mind.core.app.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sinitek.mind.core.workflow.model.PropertiesValue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class JSONSchemaInputType {

    private Object type;
    private Map<String, PropertiesValue> properties;

    private List<String> required;
}
