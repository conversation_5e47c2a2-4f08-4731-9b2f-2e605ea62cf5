package com.sinitek.mind.core.workflow.model;

import lombok.Data;

import java.util.List;

@Data
public class FlowNodeCommonType {

    private String parentNodeId;
    private String flowNodeType;
    private boolean abandon;

    private String avatar;
    private String name;
    private String intro;
    private Boolean showStatus;

    // 应该是应用的版本
    private Long appVersion;
    private String versionLabel;
    private Boolean isLatestVersion;
    // data
    private List<FlowNodeInputItemType> inputs;
    private List<FlowNodeOutputItemType> outputs;
    // plugin data
    private Long pluginId;
    private boolean isFolder;
    private PluginDataType pluginData;
    // tool data
    private NodeToolConfigType toolData;
}
