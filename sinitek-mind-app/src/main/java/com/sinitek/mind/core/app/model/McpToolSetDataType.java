package com.sinitek.mind.core.app.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class McpToolSetDataType {

    private String url;

    private Map<String, SecretValue> headerSecret;

    private List<McpToolConfig> toolList;

    private String name;

    private String avatar;
}
