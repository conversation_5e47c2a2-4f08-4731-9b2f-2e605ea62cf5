package com.sinitek.mind.core.chat.service.impl;

import com.sinitek.mind.core.app.dto.AuthAppDTO;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.chat.constant.ChatErrorCodeConstant;
import com.sinitek.mind.core.chat.dto.AuthChatCrudDTO;
import com.sinitek.mind.core.chat.dto.AuthChatCrudParams;
import com.sinitek.mind.core.chat.entity.Chat;
import com.sinitek.mind.core.chat.enumerate.AuthUserTypeEnum;
import com.sinitek.mind.core.chat.repository.ChatRepository;
import com.sinitek.mind.core.chat.service.IAuthChatService;
import com.sinitek.mind.support.outlink.dto.AuthOutLinkDTO;
import com.sinitek.mind.support.outlink.entity.OutLink;
import com.sinitek.mind.support.outlink.service.IAuthOutLinkService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.PermissionDTO;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AuthChatServiceImpl implements IAuthChatService {

    private final IAuthOutLinkService authOutLinkService;
    private final ChatRepository chatRepository;
    private final IAuthAppService authAppService;

    @Override
    public AuthChatCrudDTO authChatCrud(AuthChatCrudParams params) {
        String appId = params.getAppId();
        String chatId = params.getChatId();
        String shareId = params.getShareId();
        String outLinkUid = params.getOutLinkUid();
        String spaceTeamId = params.getTeamId();
        String teamToken = params.getTeamToken();

        if (StringUtils.isBlank(appId)) {
            throw new BussinessException(ChatErrorCodeConstant.AUTH_APP_ID_EMPTY);
        }

        if (StringUtils.isNotBlank(spaceTeamId) && StringUtils.isNotBlank(teamToken)) {
            // 暂不实现，team相关后续在确定是否需要
        }


        if (StringUtils.isNotBlank(shareId) && StringUtils.isNotBlank(outLinkUid)) {
            AuthOutLinkDTO authOutLinkDTO = authOutLinkService.authOutLink(shareId, outLinkUid);
            String uid = authOutLinkDTO.getUid();
            String shareChatAppId = authOutLinkDTO.getAppId();
            OutLink outLinkConfig = authOutLinkDTO.getOutLinkConfig();

            if (!Objects.equals(shareChatAppId, appId)) {
                throw new BussinessException(ChatErrorCodeConstant.APP_ID_EMPTY);
            }

            if (StringUtils.isBlank(chatId)) {
                return AuthChatCrudDTO.builder()
                        .teamId(outLinkConfig.getTeamId())
                        .tmbId(outLinkConfig.getTmbId())
                        .uid(uid)
                        .responseDetail(outLinkConfig.getResponseDetail())
                        .showRawSource(outLinkConfig.getShowRawSource())
                        .showNodeStatus(outLinkConfig.getShowNodeStatus())
                        .authType(AuthUserTypeEnum.OUT_LINK)
                        .build();
            }

            Optional<Chat> chatOpt = chatRepository.findByAppIdAndChatId(appId, chatId);

            if (chatOpt.isEmpty()) {
                return AuthChatCrudDTO.builder()
                        .teamId(outLinkConfig.getTeamId())
                        .tmbId(outLinkConfig.getTmbId())
                        .uid(uid)
                        .responseDetail(outLinkConfig.getResponseDetail())
                        .showRawSource(outLinkConfig.getShowRawSource())
                        .showNodeStatus(outLinkConfig.getShowNodeStatus())
                        .authType(AuthUserTypeEnum.OUT_LINK)
                        .build();
            }

            Chat chat = chatOpt.get();

            if (!Objects.equals(chat.getOutLinkUid(), uid)) {
                throw new BussinessException(ChatErrorCodeConstant.UID_MISMATCH);
            }

            return AuthChatCrudDTO.builder()
                    .teamId(outLinkConfig.getTeamId())
                    .tmbId(outLinkConfig.getTmbId())
                    .chat(chat)
                    .uid(uid)
                    .responseDetail(outLinkConfig.getResponseDetail())
                    .showRawSource(outLinkConfig.getShowRawSource())
                    .showNodeStatus(outLinkConfig.getShowNodeStatus())
                    .authType(AuthUserTypeEnum.OUT_LINK)
                    .build();
        }

        // Cookie
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);
        String teamId = authAppDTO.getTeamId();
        String tmbId = authAppDTO.getTmbId();
        PermissionDTO permission = authAppDTO.getPermission();

        Optional<Chat> chatOpt = chatRepository.findByAppIdAndChatId(appId, chatId);
        if (StringUtils.isBlank(chatId) || chatOpt.isEmpty()) {
            return AuthChatCrudDTO.builder()
                    .teamId(teamId)
                    .tmbId(tmbId)
                    .uid(tmbId)
                    .responseDetail(true)
                    .showRawSource(true)
                    .showNodeStatus(true)
                    .authType(authAppDTO.getAuthType())
                    .build();
        }
        Chat chat = chatOpt.get();

        if (permission.getHasManagePer() || Objects.equals(tmbId, chat.getTmbId())) {
            return AuthChatCrudDTO.builder()
                    .teamId(teamId)
                    .tmbId(tmbId)
                    .chat(chat)
                    .uid(tmbId)
                    .responseDetail(true)
                    .showRawSource(true)
                    .showNodeStatus(true)
                    .authType(authAppDTO.getAuthType())
                    .build();
        }

        throw new BussinessException(ChatErrorCodeConstant.AUTH_FAILED);
    }
}
