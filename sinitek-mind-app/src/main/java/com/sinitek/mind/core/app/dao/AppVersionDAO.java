package com.sinitek.mind.core.app.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.mind.core.app.entity.AppVersion;
import com.sinitek.mind.core.app.mapper.AppVersionMapper;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;

@Component
public class AppVersionDAO extends ServiceImpl<AppVersionMapper, AppVersion> {

    public AppVersion getByAppIdAndIsPublish(Long appId, boolean isPublish) {
        LambdaQueryWrapper<AppVersion> queryWrapper = Wrappers.lambdaQuery(AppVersion.class)
                .eq(AppVersion::getAppId, appId)
                .eq(AppVersion::getIsPublish, isPublish);
        return getOne(queryWrapper);
    }

    public AppVersion getByAppIdAndIsPublishAndIdGreaterThan(Long appId, boolean isPublish, Long versionId) {
        LambdaQueryWrapper<AppVersion> queryWrapper = Wrappers.lambdaQuery(AppVersion.class)
                .eq(AppVersion::getAppId, appId)
                .eq(AppVersion::getIsPublish, isPublish)
                .gt(AppVersion::getId, versionId);
        return getOne(queryWrapper);
    }

    public void deleteByAppId(Long appId) {
        if (appId == null) {
            return;
        }
        LambdaQueryWrapper<AppVersion> queryWrapper = Wrappers.lambdaQuery(AppVersion.class);
        queryWrapper.eq(AppVersion::getAppId, appId);
        remove(queryWrapper);
    }


    /**
     * 根据应用ID和版本ID查找版本
     * 用于插件预览节点功能
     */
    public AppVersion getByAppIdAndId(Long appId, Long id) {
        LambdaQueryWrapper<AppVersion> queryWrapper = Wrappers.lambdaQuery(AppVersion.class);
        queryWrapper.eq(AppVersion::getAppId, appId);
        queryWrapper.eq(AppVersion::getId, id);
        return getOne(queryWrapper);
    }

    /**
     * 根据应用ID查找最新版本
     * 用于插件预览节点功能
     */
    public AppVersion getLatestByAppId(Long appId) {
        LambdaQueryWrapper<AppVersion> queryWrapper = Wrappers.lambdaQuery(AppVersion.class);
        queryWrapper.eq(AppVersion::getAppId, appId);
        queryWrapper.eq(AppVersion::getIsPublish, true);

        List<AppVersion> versionList = list(queryWrapper);
        if (CollUtil.isEmpty(versionList)) {
            return null;
        }

        // id最大的为最新版本
        return versionList.stream()
                .max(Comparator.comparing(AppVersion::getId))
                .orElse(null);
    }

    /**
     * 分页查询指定app的版本
     * @param page 分页对象
     * @param appId appId
     * @param isPublish 为null时，不设置改条件
     * @return
     */
    public Page<AppVersion> pageByAppIdAndIsPublish(Page<AppVersion> page, Long appId, Boolean isPublish) {
        LambdaQueryWrapper<AppVersion> queryWrapper = Wrappers.lambdaQuery(AppVersion.class);
        queryWrapper.eq(AppVersion::getAppId, appId);
        queryWrapper.eq(isPublish != null, AppVersion::getIsPublish, isPublish);
        return page(page, queryWrapper);
    }

}
