package com.sinitek.mind.core.workflow.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowTemplateType {

    public Long id;

    public Long parentId;

    public Boolean isFolder;

    public String name;

    public String avatar;

    public String intro;

    public String author;

    public String courseUrl;

    public Long appVersion;

    public String versionLabel;

    public Boolean isLatestVersion;

    public Boolean showStatus;

    public Integer weight;

    public WorkflowTemplateBasicType workflow;
}
