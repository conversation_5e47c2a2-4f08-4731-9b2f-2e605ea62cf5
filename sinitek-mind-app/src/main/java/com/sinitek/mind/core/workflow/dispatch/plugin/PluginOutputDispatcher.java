package com.sinitek.mind.core.workflow.dispatch.plugin;

import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class PluginOutputDispatcher implements NodeDispatcher {


    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.PLUGIN_OUTPUT.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchPluginOutput(dispatchData);
    }

    private Map<String, Object> dispatchPluginOutput(ModuleDispatchProps props) {
        Map<String, Object> params = props.getParams();

        // 构建返回结果
        Map<String, Object> result = new HashMap<>(params);
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                        .totalPoints(0.0)
                        .pluginOutput(params)
                .build());
        return result;
    }
}
