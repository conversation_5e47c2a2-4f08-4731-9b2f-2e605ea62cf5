package com.sinitek.mind.core.workflow.dispatch;

import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class NodeDispatcherFactory implements ApplicationContextAware {

    private volatile Map<String, NodeDispatcher> dispatcherMap;
    private final Object lock = new Object();
    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    private Map<String, NodeDispatcher> initializeDispatcherMap() {
        log.info("Initializing NodeDispatcher map...");
        Map<String, NodeDispatcher> beans = applicationContext.getBeansOfType(NodeDispatcher.class);
        
        Map<String, NodeDispatcher> result = new ConcurrentHashMap<>();
        for (NodeDispatcher dispatcher : beans.values()) {
            String nodeType = dispatcher.getNodeType();
            if (nodeType == null) {
                log.warn("NodeDispatcher {} returned null nodeType, skipping", dispatcher.getClass().getSimpleName());
                continue;
            }
            
            NodeDispatcher existing = result.put(nodeType, dispatcher);
            if (existing != null) {
                log.warn("Duplicate node type '{}' found: {} and {}. Using {}.", 
                        nodeType, existing.getClass().getSimpleName(), 
                        dispatcher.getClass().getSimpleName(), dispatcher.getClass().getSimpleName());
            }
        }
        
        log.info("NodeDispatcher map initialized with {} dispatchers", result.size());
        return result;
    }

    public NodeDispatcher getDispatcher(String nodeType) {
        Map<String, NodeDispatcher> localMap = dispatcherMap;
        if (localMap == null) {
            synchronized (lock) {
                localMap = dispatcherMap;
                if (localMap == null) {
                    dispatcherMap = localMap = initializeDispatcherMap();
                }
            }
        }
        NodeDispatcher dispatcher = localMap.get(nodeType);
        if (dispatcher == null) {
            throw new BussinessException(WorkflowErrorCodeConstant.UNSUPPORTED_NODE_TYPE, nodeType);
        }
        return dispatcher;
    }
}