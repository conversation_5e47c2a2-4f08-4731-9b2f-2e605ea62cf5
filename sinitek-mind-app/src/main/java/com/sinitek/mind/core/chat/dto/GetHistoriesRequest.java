package com.sinitek.mind.core.chat.dto;

import com.sinitek.mind.common.support.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "获取聊天历史记录请求参数")
public class GetHistoriesRequest extends PageParam {

    @Schema(description = "分享链接ID，外链认证时必填")
    private String shareId;

    @Schema(description = "外链用户唯一标识，外链认证时必填")
    private String outLinkUid;

    @Schema(description = "免登录窗口自定义验证token")
    private String authToken;

    @Schema(description = "团队ID，团队空间认证时必填")
    private String teamId;

    @Schema(description = "团队令牌，团队空间认证时必填")
    private String teamToken;

    @Schema(description = "应用ID，普通用户认证时可选")
    private Long appId;

    @Schema(description = "来源类型", allowableValues = {"share", "online", "api", "team"})
    private String source;

    @Schema(description = "创建时间开始范围")
    private Date startCreateTime;

    @Schema(description = "创建时间结束范围")
    private Date endCreateTime;

    @Schema(description = "更新时间开始范围")
    private Date startUpdateTime;

    @Schema(description = "更新时间结束范围")
    private Date endUpdateTime;
}