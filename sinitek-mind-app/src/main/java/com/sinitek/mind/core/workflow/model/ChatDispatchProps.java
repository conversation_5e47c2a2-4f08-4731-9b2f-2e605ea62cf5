package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Data
@SuperBuilder
@NoArgsConstructor
public class ChatDispatchProps {

    /**
     * SSE响应对象
     */
    private SseEmitter res;

    private String requestOrigin;

    private String mode; // test chat debug

    private String timezone;

    private ExternalProviderType externalProvider;

    private RunningAppInfo runningAppInfo;

    private RunningUserInfo runningUserInfo;

    private String uid;

    private String chatId;

    private String responseChatItemId;

    /**
     * 聊天历史记录
     */
    private List<ChatItemType> histories;

    private Map<String, Object> variables;

    private List<ChatItemValueItemType> query;

    private AppChatConfigType chatConfig;

    private WorkflowInteractiveResponseType lastInteractive;

    private Boolean stream;

    private Boolean retainDatasetCite;

    private double maxRunTimes;

    private Boolean isToolCall;

    private Consumer<WorkflowStreamResponse> workflowStreamResponse;

    private Integer workflowDispatchDeep;

    private Boolean responseAllData;

    private Boolean responseDetail;

    /**
     * 运行时节点列表
     */
    private List<RuntimeNodeItemType> runtimeNodes;

    /**
     * 运行时边列表
     */
    private List<RuntimeEdgeItemType> runtimeEdges;

}
