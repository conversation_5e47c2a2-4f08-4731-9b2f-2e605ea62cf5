package com.sinitek.mind.core.app.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetToolsRequest {
    /**
     * MCP base URL, e.g. <a href="https://mcp.example.com">...</a>
     */
    private String url;

    /**
     * Optional headers to include with MCP transport requests (e.g. Authorization).
     */
    private Map<String, String> headers;
}