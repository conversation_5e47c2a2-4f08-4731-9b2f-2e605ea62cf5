package com.sinitek.mind.core.workflow.dispatch;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.app.enumerate.WorkflowIOValueTypeEnum;
import com.sinitek.mind.core.app.model.SecretValue;
import com.sinitek.mind.core.app.util.SecurityUtils;
import com.sinitek.mind.core.workflow.constant.WorkflowConstant;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.mind.core.workflow.enumerate.*;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.support.common.service.IFileService;
import com.sinitek.sirm.common.utils.SpringMvcUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.net.URI;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class HttpRequest468Dispatcher implements NodeDispatcher{

    private static final String UNDEFINED_SIGN = "UNDEFINED_SIGN";

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IFileService fileService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.HTTP_REQUEST_468.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchHttp468Request(dispatchData);
    }

    private Map<String, Object> dispatchHttp468Request(ModuleDispatchProps props) {


        HttpServletRequest httpServletRequest = SpringMvcUtil.getHttpServletRequest();
        log.error("httpServletRequest:{}", httpServletRequest);
        Map<String, String> requestHeaders = SpringMvcUtil.getRequestHeaders();
        log.error("requestHeaders:{}", requestHeaders);

        // 提取参数
        RunningAppInfo runningAppInfo = props.getRunningAppInfo();
        Long appId = runningAppInfo.getId();
        String teamId = runningAppInfo.getTeamId();
        String tmbId = runningAppInfo.getTmbId();
        String chatId = props.getChatId();
        String responseChatItemId = props.getResponseChatItemId();
        Map<String, Object> variables = props.getVariables();
        RuntimeNodeItemType node = props.getNode();
        List<RuntimeNodeItemType> runtimeNodes = props.getRuntimeNodes();
        Consumer<WorkflowStreamResponse> workflowStreamResponse = props.getWorkflowStreamResponse();
        Map<String, Object> params = props.getParams();

        // 获取HTTP参数
        String httpMethod = (String) params.getOrDefault("system_httpMethod", "POST");
        String httpReqUrl = (String) params.get("system_httpReqUrl");
        List<Map> httpHeader = getListParam(params, "system_httpHeader");
        List<Map> httpParams = getListParam(params, "system_httpParams");
        String httpJsonBody = (String) params.getOrDefault("system_httpJsonBody", "");
        List<Map> httpFormBody = getListParam(params, "system_httpFormBody");
        String httpContentType = (String) params.getOrDefault("system_httpContentType", ContentTypes.JSON.getValue());
        Double httpTimeout = (Double) params.getOrDefault("system_httpTimeout", 60);
        Object obj = params.get("system_header_secret");
        Map<String, SecretValue> headerSecret = convertToTypedMap(obj, String.class, SecretValue.class);
        @SuppressWarnings("unchecked")
        Map<String, Object> dynamicInput = (Map<String, Object>) params.get(NodeInputKeyEnum.ADD_INPUT_PARAM.getValue());

        Object key = params.get("key");
        String url = (String) params.get("url");

        if (StrUtil.isBlank(httpReqUrl)) {
            throw new BussinessException(WorkflowErrorCodeConstant.HTTP_URL_IS_EMPTY);
        }

        // 构建系统变量
        Map<String, Object> systemVariables = new HashMap<>();
        systemVariables.put("appId", appId);
        systemVariables.put("chatId", chatId);
        systemVariables.put("responseChatItemId", responseChatItemId);
        systemVariables.put("histories", props.getHistories() != null ?
                props.getHistories().subList(Math.max(0, props.getHistories().size() - 10), props.getHistories().size()) :
                new ArrayList<>());

        Map<String, Object> concatVariables = new HashMap<>();
        concatVariables.putAll(variables);
        concatVariables.put("key", key);
        concatVariables.put("url", url);
        concatVariables.putAll(systemVariables);

        // 合并所有变量
        Map<String, Object> allVariables = new HashMap<>();
        if (dynamicInput != null) {
            allVariables.put(NodeInputKeyEnum.ADD_INPUT_PARAM.getValue(), concatVariables);
            allVariables.putAll(concatVariables);
        }

        // 替换URL中的变量
        httpReqUrl = replaceStringVariables(httpReqUrl, runtimeNodes, allVariables);

        // 处理请求头
        Map<String, String> publicHeaders = buildHeaders(httpHeader, httpContentType, runtimeNodes, allVariables);

        // 处理密钥值
        Map<String, String> sensitiveHeaders = SecurityUtils.getSecretValue(headerSecret);

        // 处理请求参数
        Map<String, String> requestParams = buildRequestParams(httpParams, runtimeNodes, allVariables);

        // 处理请求体
        Object requestBody = buildRequestBody(httpContentType, httpJsonBody, httpFormBody, runtimeNodes, allVariables);

        // 格式化请求体用于显示
        Map<String, Object> formattedRequestBody = formatRequestBodyForDisplay(requestBody);

        try {
            // TODO 需要对照原本逻辑重新梳理以下代码 执行HTTP请求
            ResponseEntity<Map> response = executeHttpRequest(httpMethod, httpReqUrl, publicHeaders, requestBody, requestParams, httpTimeout.intValue());
            Map<String, Object> responseBody = response.getBody();

            // 处理响应
            Map<String, Object> formatResponse = responseBody instanceof Map ?
                    (Map<String, Object>) responseBody : new HashMap<>();

            if (formatResponse.get("answerText") instanceof String) {
                // 构建textAdaptGptResponse参数
                TextAdaptGptResponseParams gptParams = TextAdaptGptResponseParams.builder()
                        .text((String) formatResponse.get("answerText"))
                        .build();
                // 创建流式响应
                WorkflowStreamResponse streamResponse = WorkflowStreamResponse.builder()
                        .event(SseResponseEventEnum.FAST_ANSWER.getValue())
                        .data(WorkflowUtil.textAdaptGptResponse(gptParams))
                        .build();
                workflowStreamResponse.accept(streamResponse);
            }

            // 处理输出结果
            Map<String, Object> results = processOutputResults(node, formatResponse);

            // 构建成功响应
            return buildSuccessResponse(results, requestParams, formattedRequestBody, publicHeaders, responseBody);

        } catch (Exception error) {
            log.error("Http request error", error);
            return buildErrorResponse(error, requestParams, formattedRequestBody, publicHeaders);
        }
    }

    private String replaceStringVariables(String text, List<RuntimeNodeItemType> runtimeNodes, Map<String, Object> variables) {
        return (String) WorkflowUtil.replaceEditorVariable(text, runtimeNodes, variables);
    }

    private Object getVariableValue(String nodeId, String key, List<RuntimeNodeItemType> runtimeNodes, Map<String, Object> variables) {
        if (WorkflowConstant.VARIABLE_NODE_ID.equals(nodeId)) {
            return variables.get(key);
        }

        // 查找对应的节点
        for (RuntimeNodeItemType node : runtimeNodes) {
            if (nodeId.equals(node.getNodeId())) {
                // 查找输出
                if (node.getOutputs() != null) {
                    for (FlowNodeOutputItemType output : node.getOutputs()) {
                        if (key.equals(output.getId())) {
                            return output.getValue();
                        }
                    }
                }
                // 查找输入
                if (node.getInputs() != null) {
                    for (FlowNodeInputItemType input : node.getInputs()) {
                        if (key.equals(input.getKey())) {
                            return input.getValue();
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取List参数
     */
    @SuppressWarnings("unchecked")
    private <T> List<T> getListParam(Map<String, Object> params, String key) {
        Object value = params.get(key);
        if (value instanceof List) {
            return (List<T>) value;
        }
        return new ArrayList<>();
    }

    private Map<String, String> buildHeaders(List<Map> httpHeader, String httpContentType,
                                             List<RuntimeNodeItemType> runtimeNodes, Map<String, Object> allVariables) {

        // 创建结果映射
        Map<String, String> headers = new HashMap<>();
        // 创建头部列表的副本，避免修改原始列表
        List<PropsArrType> headerList = new ArrayList<>();
        for(Map map : httpHeader) {
            PropsArrType type = new PropsArrType();
            type.setKey((String) map.get("key"));
            type.setValue(String.valueOf(map.get("value")));
            type.setType((String) map.get("type"));
            headerList.add(type);
        }

        // 添加Content-Type
        ContentTypes contentType = ContentTypes.fromValue(httpContentType);
        if (StrUtil.isNotBlank(contentType.getMimeType())) {
            PropsArrType header = new PropsArrType();
            header.setKey("Content-Type");
            header.setValue(contentType.getMimeType());
            header.setType("string");
            headerList.add(0, header);
        }

        // 处理所有头部
        for (PropsArrType item : headerList) {
            String key = replaceStringVariables(item.getKey(), runtimeNodes, allVariables);
            String value = replaceStringVariables(item.getValue(), runtimeNodes, allVariables);
            if (StrUtil.isNotBlank(key)) {
                // 使用valueTypeFormat确保值格式化为字符串类型
                String formattedValue = (String) WorkflowUtil.valueTypeFormat(value, WorkflowIOValueTypeEnum.STRING.getValue());
                headers.put(key, formattedValue);
            }
        }

        return headers;
    }

    private Map<String, String> buildRequestParams(List<Map> httpParams,
                                                   List<RuntimeNodeItemType> runtimeNodes, Map<String, Object> allVariables) {
        Map<String, String> params = new HashMap<>();

        List<PropsArrType> httpParamsList = new ArrayList<>();
        for(Map map : httpParams) {
            PropsArrType type = new PropsArrType();
            type.setKey((String) map.get("key"));
            type.setValue(String.valueOf(map.get("value")));
            type.setType((String) map.get("type"));
            httpParamsList.add(type);
        }
        for (PropsArrType param : httpParamsList) {
            String key = replaceStringVariables(param.getKey(), runtimeNodes, allVariables);
            String value = replaceStringVariables(param.getValue(), runtimeNodes, allVariables);
            if (StrUtil.isNotBlank(key)) {
                // 使用valueTypeFormat确保值格式化为字符串类型
                String formattedValue = (String) WorkflowUtil.valueTypeFormat(value, WorkflowIOValueTypeEnum.STRING.getValue());
                params.put(key, formattedValue);
            }
        }
        return params;
    }

    /**
     * 处理文件URL列表，转换为FileSystemResource列表
     */
    private List<FileSystemResource> processFileUrlList(String fileUrls) {
        // 获取fileUrlList变量
        List<FileSystemResource> resourceList = new LinkedList<>();
        try {
            List<String> fileUrlList = objectMapper.readValue(fileUrls, new TypeReference<List<String>>() {
            });

            if (CollUtil.isEmpty(fileUrlList)) {
                return List.of();
            }
            for (String url : fileUrlList) {
                // 获取token
                URI uri = new URI(url);
                String query = uri.getQuery();
                if (query == null) {
                    return null;
                }
                String realToken = "";
                for (String param : query.split("&")) {
                    String[] kv = param.split("=", 2);
                    if (kv.length == 2 && "token".equals(kv[0])) {
                        realToken = kv[1];
                        break;
                    }
                }
                File file = fileService.getResourceByToken(realToken);
                if (file != null) {
                    resourceList.add(new FileSystemResource(file));
                }
            }
            return resourceList;
        } catch (Exception e) {
            log.error("http节点，获取文件内容出错", e);
            throw new BussinessException("获取文件内容出错");
        }
    }

    private Object buildRequestBody(String httpContentType, String httpJsonBody,
                                    List<Map> httpFormBody,
                                    List<RuntimeNodeItemType> runtimeNodes, Map<String, Object> allVariables) {
        ContentTypes contentType = ContentTypes.fromValue(httpContentType);
        List<PropsArrType> httpFormBodyList = new ArrayList<>();
        for(Map map : httpFormBody) {
            PropsArrType type = new PropsArrType();
            type.setKey((String) map.get("key"));
            type.setValue(String.valueOf(map.get("value")));
            type.setType((String) map.get("type"));
            httpFormBodyList.add(type);
        }

        switch (contentType) {
            case NONE:
                return null;
            case FORM_DATA:
                MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
                
                // 处理普通表单字段
                for (PropsArrType item : httpFormBodyList) {
                    String key = replaceStringVariables(item.getKey(), runtimeNodes, allVariables);
                    if (item.getValue().contains("userFilesStream")) {
                        // 文件输入流，需要拿到具体的文件流
                        String value = replaceStringVariables(item.getValue(), runtimeNodes, allVariables);
                        List<FileSystemResource> resourceList = processFileUrlList(value);
                        if (CollUtil.isEmpty(resourceList)) {
                            continue;
                        }
                        resourceList.forEach(resource -> formData.add(key, resource));
                        continue;
                    }
                    String value = replaceStringVariables(item.getValue(), runtimeNodes, allVariables);
                    if (StrUtil.isNotBlank(key)) {
                        formData.add(key, value);
                    }
                }
                return formData;
            case X_WWW_FORM_URLENCODED:
                MultiValueMap<String, String> urlEncodedData = new LinkedMultiValueMap<>();
                for (PropsArrType item : httpFormBodyList) {
                    String key = replaceStringVariables(item.getKey(), runtimeNodes, allVariables);
                    String value = replaceStringVariables(item.getValue(), runtimeNodes, allVariables);
                    if (StrUtil.isNotBlank(key)) {
                        urlEncodedData.add(key, value);
                    }
                }
                return urlEncodedData;
            case JSON:
                if (StrUtil.isBlank(httpJsonBody)) return new HashMap<>();
                try {
                    String processedJsonBody = replaceJsonBodyString(httpJsonBody, runtimeNodes, allVariables);
                    return objectMapper.readValue(processedJsonBody, Object.class);
                } catch (Exception e) {
                    log.error("无效的JSON请求体", e);
                    throw new BussinessException(WorkflowErrorCodeConstant.INVALID_JSON_BODY, e);
                }
            case XML:
            case RAW:
            default:
                return replaceStringVariables(httpJsonBody, runtimeNodes, allVariables).replace(UNDEFINED_SIGN, "null");
        }
    }

    private String replaceJsonBodyString(String text, List<RuntimeNodeItemType> runtimeNodes, Map<String, Object> allVariables) {
        if (StrUtil.isBlank(text)) return text;

        // 1. 替换 {{$nodeId.key$}} 格式的变量
        Pattern pattern1 = Pattern.compile("\\{\\{\\$([^.]+)\\.([^$]+)\\$\\}\\}");
        Matcher matcher1 = pattern1.matcher(text);
        StringBuffer sb1 = new StringBuffer();

        while (matcher1.find()) {
            String nodeId = matcher1.group(1);
            String id = matcher1.group(2);
            String fullMatch = matcher1.group(0);

            // 检查变量是否在引号内
            boolean isInQuotes = isVariableInQuotes(text, fullMatch);

            Object variableVal = null;
            if (WorkflowConstant.VARIABLE_NODE_ID.equals(nodeId)) {
                variableVal = allVariables.get(id);
            } else {
                // 查找上游节点的输入/输出
                RuntimeNodeItemType node = runtimeNodes.stream()
                        .filter(n -> nodeId.equals(n.getNodeId()))
                        .findFirst()
                        .orElse(null);

                if (node != null) {
                    // 查找输出
                    if (node.getOutputs() != null) {
                        for (FlowNodeOutputItemType output : node.getOutputs()) {
                            if (id.equals(output.getId())) {
                                variableVal = WorkflowUtil.formatVariableValByType(output.getValue(),
                                        output.getValueType());
                                break;
                            }
                        }
                    }

                    // 如果没找到输出，查找输入
                    if (variableVal == null && node.getInputs() != null) {
                        for (FlowNodeInputItemType input : node.getInputs()) {
                            if (id.equals(input.getKey())) {
                                variableVal = WorkflowUtil.getReferenceVariableValue(input.getValue(),
                                        runtimeNodes, allVariables);
                                break;
                            }
                        }
                    }
                }
            }

            String formatVal = valToStr(variableVal, isInQuotes);
            matcher1.appendReplacement(sb1, Matcher.quoteReplacement(formatVal));
        }
        matcher1.appendTail(sb1);
        text = sb1.toString();

        // 2. 替换 {{key}} 格式的变量
        Pattern pattern2 = Pattern.compile("\\{\\{([^}]+)\\}\\}");
        Matcher matcher2 = pattern2.matcher(text);
        StringBuffer sb2 = new StringBuffer();

        while (matcher2.find()) {
            String key = matcher2.group(1);
            String fullMatch = matcher2.group(0);

            // 检查变量是否在引号内
            boolean isInQuotes = isVariableInQuotes(text, fullMatch);

            String formatVal = valToStr(allVariables.get(key), isInQuotes);
            matcher2.appendReplacement(sb2, Matcher.quoteReplacement(formatVal));
        }
        matcher2.appendTail(sb2);
        text = sb2.toString();

        // 替换 undefined 为 null
        return text.replaceAll("(\".*?\")\\s*:\\s*undefined\\b", "$1:null");
    }

    /**
     * 将值转换为字符串
     */
    private String valToStr(Object value, boolean isInQuotes) {
        if (value == null) {
            return "null";
        }

        if (value instanceof String) {
            return isInQuotes ? (String) value : "\"" + value + "\"";
        }

        return value.toString();
    }

    private boolean isVariableInQuotes(String text, String variable) {
        int index = text.indexOf(variable);
        if (index == -1) {
            return false;
        }

        // 计算变量前面的引号数量
        String textBeforeVar = text.substring(0, index);
        int quoteCount = 0;
        for (char c : textBeforeVar.toCharArray()) {
            if (c == '"') {
                quoteCount++;
            }
        }

        // 如果引号数量为奇数，则变量在引号内
        return quoteCount % 2 == 1;
    }

    private Map<String, Object> formatRequestBodyForDisplay(Object requestBody) {
        if (requestBody == null) return new HashMap<>();

        if (requestBody instanceof MultiValueMap<?, ?> multiValueMap) {
            Map<String, Object> result = new HashMap<>();
            multiValueMap.forEach((key, value) -> {
                if (value != null && !value.isEmpty()) {
                    Object firstValue = value.get(0);
                    if (firstValue instanceof Resource) {
                        // 对于文件资源，显示文件名
                        result.put(key.toString(), "[File: " + ((Resource) firstValue).getFilename() + "]");
                    } else {
                        result.put(key.toString(), firstValue);
                    }
                }
            });
            return result;
        } else if (requestBody instanceof String) {
            try {
                return objectMapper.readValue((String) requestBody, Map.class);
            } catch (Exception e) {
                Map<String, Object> result = new HashMap<>();
                result.put("content", requestBody);
                return result;
            }
        } else if (requestBody instanceof Map) {
            return (Map<String, Object>) requestBody;
        }

        return new HashMap<>();
    }

    private ResponseEntity<Map> executeHttpRequest(String method, String url, Map<String, String> headers,
                                                      Object body, Map<String, String> params, Integer timeout) {
        // 构建请求头
        HttpHeaders httpHeaders = new HttpHeaders();
        headers.forEach(httpHeaders::set);

        // 构建请求实体
        HttpEntity<Object> requestEntity = new HttpEntity<>(body, httpHeaders);

        // 构建完整URL（包含查询参数）
        if (!params.isEmpty()) {
            StringBuilder urlBuilder = new StringBuilder(url);
            urlBuilder.append(url.contains("?") ? "&" : "?");
            params.forEach((key, value) -> {
                urlBuilder.append(key).append("=").append(value).append("&");
            });
            url = urlBuilder.toString();
            if (url.endsWith("&")) {
                url = url.substring(0, url.length() - 1);
            }
        }

        // 执行请求
        HttpMethod httpMethod = HttpMethod.valueOf(method.toUpperCase());
        return restTemplate.exchange(url, httpMethod, requestEntity, Map.class);
    }

    private Map<String, Object> processOutputResults(RuntimeNodeItemType node, Map<String, Object> formatResponse) {
        Map<String, Object> results = new HashMap<>();

        if (node.getOutputs() != null) {
            for (FlowNodeOutputItemType output : node.getOutputs()) {
                String outputId = output.getId();
                String outputKey = output.getKey();

                // 跳过特殊输出
                if (NodeOutputKeyEnum.ERROR.getValue().equals(outputId) ||
                        NodeOutputKeyEnum.HTTP_RAW_RESPONSE.getValue().equals(outputId) ||
                        NodeInputKeyEnum.ADD_INPUT_PARAM.getValue().equals(outputId)) {
                    continue;
                }

                // 使用JSONNODE提取值
                String jsonPath = outputKey.startsWith("$") ? outputKey : "$." + outputKey;

                JsonNode rootNode = objectMapper.valueToTree(formatResponse);
                try {

                    // 处理简单路径如 "$.key" 或 "key"
                    String path = jsonPath.startsWith("$.") ? jsonPath.substring(2) : jsonPath;
                    JsonNode resultNode = rootNode.path(path);
                    Object result = objectMapper.treeToValue(resultNode, Object.class);
                    results.put(outputKey, result);
                } catch (Exception e) {
                    // JSONNODE解析失败时返回undefined
                    results.put(outputKey, null);
                }
            }
        }

        return results;
    }

    private Map<String, Object> buildSuccessResponse(Map<String, Object> results, Map<String, String> params,
                                                     Map<String, Object> formattedRequestBody, Map<String, String> headers, Map<String, Object> rawResponse) {
        Map<String, Object> response = new HashMap<>();

        // 添加结果
        response.putAll(results);

        response.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                .totalPoints(Double.valueOf(0.0))
                .params(!params.isEmpty() ? new HashMap<>(params) : null)
                .body(!formattedRequestBody.isEmpty() ? formattedRequestBody : null)
                .headers(!headers.isEmpty() ? new HashMap<>(headers) : null)
                .httpResult(rawResponse)
                .build());
        response.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), !results.isEmpty() ? results : rawResponse);
        response.put(NodeOutputKeyEnum.HTTP_RAW_RESPONSE.getValue(), rawResponse);

        return response;
    }

    private Map<String, Object> buildErrorResponse(Exception error, Map<String, String> params,
                                                   Map<String, Object> formattedRequestBody, Map<String, String> headers) {
        Map<String, Object> response = new HashMap<>();

        Map<String, Object> errorInfo = new HashMap<>();
        errorInfo.put("message", error.getMessage());
        errorInfo.put("type", error.getClass().getSimpleName());

        response.put(NodeOutputKeyEnum.ERROR.getValue(), errorInfo);

        Map<String, Object> httpResult = new HashMap<>();
        httpResult.put("error", errorInfo);

        response.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                .params(!params.isEmpty() ? new HashMap<>(params) : null)
                .body(!formattedRequestBody.isEmpty() ? formattedRequestBody : null)
                .headers(!headers.isEmpty() ? new HashMap<>(headers) : null)
                .httpResult(httpResult)
                .build());
        response.put(NodeOutputKeyEnum.HTTP_RAW_RESPONSE.getValue(), error.getMessage());

        return response;
    }

    // 简化版（忽略类型不匹配的条目）
    public static <K, V> Map<K, V> convertToTypedMap(
            Object obj,
            Class<K> keyType,
            Class<V> valueType
    ) {
        return convertToTypedMap(obj, keyType, valueType, entry -> {
            // 默认行为：跳过无效条目
            return null;
        });
    }

    /**
     * 将对象安全转换为指定类型的Map
     *
     * @param obj       要转换的对象
     * @param keyType   键的Class类型
     * @param valueType 值的Class类型
     * @param exceptionHandler 类型不匹配时的异常处理器（可返回null跳过当前条目）
     * @return 转换后的Map，如果输入为null则返回空Map
     * @throws IllegalArgumentException 如果输入不是Map类型
     */
    public static <K, V> Map<K, V> convertToTypedMap(
            Object obj,
            Class<K> keyType,
            Class<V> valueType,
            Function<Map.Entry<?, ?>, ? extends RuntimeException> exceptionHandler
    ) {
        if (obj == null) {
            return Collections.emptyMap();
        }

        if (!(obj instanceof Map)) {
            throw new BussinessException(WorkflowErrorCodeConstant.OBJECT_NOT_MAP);
        }

        Map<?, ?> rawMap = (Map<?, ?>) obj;
        Map<K, V> resultMap = new HashMap<>(rawMap.size());

        for (Map.Entry<?, ?> entry : rawMap.entrySet()) {
            Object key = entry.getKey();
            Object value = entry.getValue();

            boolean keyValid = keyType.isInstance(key);
            boolean valueValid = valueType.isInstance(value);

            if (keyValid && valueValid) {
                resultMap.put(keyType.cast(key), valueType.cast(value));
            } else if (exceptionHandler != null) {
                RuntimeException ex = exceptionHandler.apply(entry);
                if (ex != null) throw ex;
            }
        }
        return resultMap;
    }
}
