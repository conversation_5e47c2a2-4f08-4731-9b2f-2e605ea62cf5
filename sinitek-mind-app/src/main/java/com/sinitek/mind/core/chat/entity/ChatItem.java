package com.sinitek.mind.core.chat.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.sinitek.data.mybatis.base.BaseEntity;
import com.sinitek.mind.common.typehandler.ChatHistoryItemResTypeListTypeHandler;
import com.sinitek.mind.common.typehandler.ChatItemValueItemTypeListTypeHandler;
import com.sinitek.mind.core.chat.model.AdminFbkType;
import com.sinitek.mind.core.chat.model.ChatHistoryItemResType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "mind_chat_item", autoResultMap = true)
public class ChatItem extends BaseEntity {

    @Schema(description = "用户id")
    private String orgId;

    @Schema(description = "聊天id")
    private String chatId;

    @Schema(description = "数据id")
    private String dataId;

    @Schema(description = "应用id")
    private Long appId;

    @Schema(description = "时间")
    private Date time;

    @Schema(description = "是否隐藏前端ui")
    @TableField("hide_in_ui")
    private Boolean hideInUI;

    @Schema(description = "对话主角-ChatRoleEnum")
    private String obj;

    @Schema(description = "具体的内容")
    @TableField(typeHandler = ChatItemValueItemTypeListTypeHandler.class)
    private List<ChatItemValueItemType> value;

    @Schema(description = "记忆")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> memories;

    @Schema(description = "错误信息")
    private String errorMsg;

    @Schema(description = "好反馈")
    private String userGoodFeedback;

    @Schema(description = "坏反馈")
    private String userBadFeedback;

    @Schema(description = "自定义反馈")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> customFeedbacks;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private AdminFbkType adminFeedback;

    @TableField(typeHandler = ChatHistoryItemResTypeListTypeHandler.class)
    private List<ChatHistoryItemResType> responseData;

    @Schema(description = "消耗时长")
    private double durationSeconds;
}
