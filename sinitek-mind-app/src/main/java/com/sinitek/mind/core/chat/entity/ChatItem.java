package com.sinitek.mind.core.chat.entity;

import com.sinitek.mind.core.chat.model.AdminFbkType;
import com.sinitek.mind.core.chat.model.ChatHistoryItemResType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "chatitems")
public class ChatItem {
    @Id
    private String id;

    private String teamId;

    private String tmbId;

    private String userId;

    private String chatId;

    private String dataId;

    @Field(targetType = FieldType.OBJECT_ID)
    private String appId;

    private Date time;

    private Boolean hideInUI;

    private String obj;

    private List<ChatItemValueItemType> value;

    private Map<String, Object> memories;

    private String errorMsg;

    private String userGoodFeedback;

    private String userBadFeedback;

    private List<String> customFeedbacks;

    private AdminFbkType adminFeedback;

    private List<ChatHistoryItemResType> responseData;

    private double durationSeconds;
}
