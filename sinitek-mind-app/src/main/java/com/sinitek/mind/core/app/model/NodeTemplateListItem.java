package com.sinitek.mind.core.app.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sinitek.mind.support.account.dto.SourceMemberDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 节点模板列表项DTO
 * 对应原始的 NodeTemplateListItemType
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "节点模板列表项")
public class NodeTemplateListItem {

    @Schema(description = "节点ID")
    public Long id;

    @Schema(description = "流程节点类型")
    public String flowNodeType;

    @Schema(description = "父级ID")
    public Long parentId;

    @Schema(description = "是否为文件夹")
    public Boolean isFolder;

    @Schema(description = "模板类型")
    public String templateType;

    @Schema(description = "头像URL")
    public String avatar;

    @Schema(description = "名称")
    public String name;

    @Schema(description = "介绍")
    public String intro;

    @Schema(description = "是否为工具")
    public Boolean isTool;

    @Schema(description = "作者头像")
    public String authorAvatar;

    @Schema(description = "作者")
    public String author;

    @Schema(description = "是否唯一")
    public Boolean unique;

    @Schema(description = "当前积分消耗")
    public Integer currentCost;

    @Schema(description = "是否配置积分")
    public Boolean hasTokenFee;

    @Schema(description = "使用说明")
    public String instructions;

    @Schema(description = "教程链接")
    public String courseUrl;

    public SourceMemberDTO sourceMember;

}