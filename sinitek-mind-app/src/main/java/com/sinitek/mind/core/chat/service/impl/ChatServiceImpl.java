package com.sinitek.mind.core.chat.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dao.AppDAO;
import com.sinitek.mind.core.app.dto.AuthAppDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.VariableItemType;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.app.util.PluginUtils;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.constant.ChatErrorCodeConstant;
import com.sinitek.mind.core.chat.dao.ChatDAO;
import com.sinitek.mind.core.chat.dao.ChatItemDAO;
import com.sinitek.mind.core.chat.dto.*;
import com.sinitek.mind.core.chat.entity.Chat;
import com.sinitek.mind.core.chat.entity.ChatItem;
import com.sinitek.mind.core.chat.enumerate.*;
import com.sinitek.mind.core.chat.model.*;
import com.sinitek.mind.core.chat.service.IAuthChatService;
import com.sinitek.mind.core.chat.service.IChatService;
import com.sinitek.mind.core.chat.util.ChatUtil;
import com.sinitek.mind.core.dataset.model.SearchDataResponseItemType;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.model.sse.WorkflowResponseWriter;
import com.sinitek.mind.core.workflow.service.IWorkflowService;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.support.outlink.service.IAuthOutLinkService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.dto.OpenaiAccountType;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatServiceImpl implements IChatService {

    private final ChatDAO chatDAO;
    private final IPermissionService permissionService;
    private final IAuthService authService;
    private final AppDAO appDAO;
    private final ChatItemDAO chatItemDAO;
    private final IWorkflowService workflowService;
    private final IAuthOutLinkService authOutLinkService;
    private final IAuthAppService authAppService;
    private final IAuthChatService authChatService;

    @Override
    public InitChatResponse initChat(InitChatRequest request) {
        String appId = request.getAppId();
        String chatId = request.getChatId();

        try {
            // 获取当前用户认证信息
            AuthDTO authDTO = authService.authCert();

            // 验证应用权限
            AuthAppDTO authAppDTO = authAppService.authApp(Long.parseLong(appId), PermissionConstant.READ_PER);

            App app = authAppDTO.getApp();
            String tmbId = authAppDTO.getTmbId();

            if (app == null) {
                throw new BussinessException(ChatErrorCodeConstant.APP_NOT_EXISTS_OR_NO_PERMISSION);
            }

            // 查找聊天记录（如果提供了chatId）
            Chat chat = null;
            if (StringUtils.hasText(chatId)) {
                Optional<Chat> chatOpt = chatDAO.getByAppIdAndChatId(Long.valueOf(appId), chatId);
                if (chatOpt.isPresent()) {
                    chat = chatOpt.get();
                    // 验证聊天权限
                    if (!ChatUtil.validateChatPermission(app, chat, tmbId)) {
                        throw new BussinessException(ChatErrorCodeConstant.CHAT_NO_PERMISSION);
                    }
                }
            }

            // 构建响应
            InitChatResponse response = new InitChatResponse();
            response.setChatId(chatId);
            response.setAppId(appId);
            response.setTitle(chat != null ? chat.getTitle() : null);
            response.setUserAvatar(null); // 根据需要设置用户头像
            response.setVariables(chat != null ? chat.getVariables() : null);

            // 构建应用信息
            InitChatResponse.AppInfo appInfo = new InitChatResponse.AppInfo();
            appInfo.setChatConfig(ChatUtil.getAppChatConfig(app, chat));
            appInfo.setChatModels(ChatUtil.getChatModelNameListByModules(app.getModules()));
            appInfo.setName(app.getName());
            appInfo.setAvatar(app.getAvatar());
            appInfo.setIntro(app.getIntro());
            appInfo.setCanUse(true); // 根据实际业务逻辑设置
            appInfo.setType(app.getType());
            appInfo.setPluginInputs(ChatUtil.getPluginInputs(app, chat));

            response.setApp(appInfo);

            return response;

        } catch (Exception e) {
            log.error("初始化聊天失败: appId={}, chatId={}, error={}", appId, chatId, e.getMessage(), e);
            throw new BussinessException(ChatErrorCodeConstant.INIT_FAILED, e.getMessage());
        }
    }

    @Override
    public PageResult<ChatItemType> getPaginationRecords(GetPaginationRecordsRequest request) {

        Long appId = request.getAppId();
        String chatId = request.getChatId();

        if (appId == null || !StringUtils.hasText(chatId)) {
            return PageResult.of(0, List.of());
        }

        // 获取应用信息

        App app = appDAO.getById(request.getAppId());
        if (app == null) {
            throw new BussinessException(ChatErrorCodeConstant.APP_NOT_EXISTS);
        }

        // TODO 根据聊天类型进行不同的权限验证 authChatCrud

        authChatService.authChatCrud(AuthChatCrudParams.builder()
                .appId(appId)
                .chatId(chatId)
                .shareId(request.getShareId())
                .chatId(request.getChatId())
                .build());

        // 如果存在shareId，需要进行权限校验
        if (StringUtils.hasText(request.getShareId())) {
            String shareId = request.getShareId();
            // 身份验证
            authOutLinkService.authOutLinkChatInit(shareId, request.getAuthToken());
        }

        // 验证聊天记录是否存在
        boolean exists = chatDAO.getByAppIdAndChatId(Long.valueOf(request.getAppId()), request.getChatId()).isPresent();
        if (!exists) {
            return PageResult.of(0, List.of());
        }

        PageResult<ChatItemType> chatItems = getChatItems(request.getChatId(), request.getAppId(), request.getOffset(), request.getPageSize());

        // 处理聊天记录数据
        List<ChatItemType> processedItems = processChatItems(
                chatItems.getList(),
                app,
                request.getType(),
                request.getLoadCustomFeedbacks()
        );

        // 构建响应
        chatItems.setList(processedItems);
        return chatItems;
    }

    @Override
    public void processChatTest(ChatTestRequest request, SseEmitter emitter) {
//        try {
            // 参数验证
            validateChatTestRequest(request);

            List<ChatItemType> chatMessages = ChatAdaptor.gptMessages2Chats(request.getMessages(), true);

            // 验证应用权限
            AuthAppDTO authAppDTO = authAppService.authApp(request.getAppId(), PermissionConstant.READ_PER);

            App app = authAppDTO.getApp();
            String tmbId = authAppDTO.getTmbId();
            String teamId = authAppDTO.getTeamId();

            if (app == null) {
                throw new BussinessException(ChatErrorCodeConstant.APP_NOT_EXISTS_OR_NO_PERMISSION);
            }

            boolean isPlugin = AppTypeEnum.PLUGIN.getValue().equals(app.getType());
            boolean isTool = AppTypeEnum.TOOL.getValue().equals(app.getType());

            // 获取或创建聊天记录
            Chat chat = getOrCreateChat(request, app, tmbId, teamId);

            // 处理用户问题
            ChatItemType userQuestion = extractUserQuestion(chatMessages, app, isPlugin, isTool, request.getVariables());

            // 获取数量限制
            int limit = WorkflowUtil.getMaxHistoryLimitFromNodes(request.getNodes());

            // 获取聊天历史
            List<ChatItemType> histories = getChatHistories(request.getAppId(), request.getChatId(), 0, limit);

            // 合并变量
            Map<String, Object> variables = mergeVariables(request.getVariables(), chat != null ? chat.getVariables() : null);
            request.setVariables(variables);

            List<ChatItemType> newHistories = ChatUtil.concatHistories(histories, chatMessages);

            WorkflowInteractiveResponseType interactive = WorkflowUtil.getLastInteractiveValue(newHistories);

            List<String> nodeIds = WorkflowUtil.getWorkflowEntryNodeIds(request.getNodes(), interactive);
            // Get runtimeNodes
            List<RuntimeNodeItemType> runtimeNodes = WorkflowUtil.storeNodes2RuntimeNodes(request.getNodes(), nodeIds);
            List<RuntimeEdgeItemType> runtimeEdges = WorkflowUtil.storeEdges2RuntimeEdges(request.getEdges(), interactive);
            // 如果是插件根据变量更新插件输入
            if (isPlugin) {
                runtimeNodes = WorkflowUtil.updatePluginInputByVariables(runtimeNodes, variables);
                variables = new HashMap<>();
            }

            // 使用历史交互数据重写节点输出
            runtimeNodes = WorkflowUtil.rewriteNodeOutputByHistories(runtimeNodes, interactive);

            WorkflowResponseConfig config = WorkflowResponseConfig.builder()
                    .sseEmitter(emitter)
                    .detail(true)
                    .streamResponse(true)
                    .id(request.getChatId())
                    .showNodeStatus(true)
                    .build();
            // TODO getUserChatInfoAndAuthTeamPoints方法实现
            ExternalProviderType externalProviderType = new ExternalProviderType();
            externalProviderType.setExternalWorkflowVariables(new HashMap<>());
            externalProviderType.setOpenaiAccount(new OpenaiAccountType());

            // 构建工作流调度参数
            ChatDispatchProps dispatchProps = ChatDispatchProps.builder()
                    .res(emitter)
                    .mode("test")
                    .timezone("Asia/Shanghai")
                    .externalProvider(externalProviderType)
                    .uid(tmbId)
                    .runningAppInfo(RunningAppInfo.builder()
                            .id(app.getId())
                            .teamId(teamId)
                            .tmbId(tmbId)
                            .build())
                    .runningUserInfo(RunningUserInfo.builder()
                            .teamId(teamId)
                            .tmbId(tmbId)
                            .build())
                    .chatId(request.getChatId())
                    .responseChatItemId(request.getResponseChatItemId())
                    .runtimeNodes(runtimeNodes)
                    .runtimeEdges(runtimeEdges)
                    .variables(variables)
                    .query(ChatUtil.removeEmptyUserInput(userQuestion.getValue()))
                    .lastInteractive(interactive)
                    .chatConfig(request.getChatConfig())
                    .histories(newHistories)
                    .stream(true)
                    .maxRunTimes(500)
                    .workflowStreamResponse(WorkflowUtil.getWorkflowResponseWrite(config))
                    .responseDetail(true)
                    .build();

            // 执行工作流并获取结果
            DispatchFlowResponse dispatchResult;
            try {
                dispatchResult = workflowService.dispatchWorkFlow(dispatchProps).get();
            } catch (ExecutionException | InterruptedException e) {
                log.error("工作流执行失败: {}", e.getMessage(), e);
                throw new BussinessException(ChatErrorCodeConstant.WORKFLOW_FAILED, e.getMessage());
            }

            // 发送工作流结束信号
            WorkflowResponseWriter.sseComplete(emitter);


            // 转换为ChatTestResponse格式
//            ChatTestResponse workflowResult = convertDispatchResultToChatTestResponse(dispatchResult);

            // 保存聊天记录
            boolean isInteractiveRequest = WorkflowUtil.getLastInteractiveValue(histories) != null;
            String userInteractiveVal = ChatAdaptor.chatValue2RuntimePrompt(userQuestion.getValue()).getText();

            String newTitle = isPlugin
                    ? (variables.containsKey("cTime") ? variables.get("cTime").toString() : getCurrentTime())
                    : ChatUtil.getChatTitleFromChatMessage(userQuestion);

            ChatItemType aiResponse = createAiResponse(request.getResponseChatItemId(), dispatchResult);

            if (isInteractiveRequest) {
                updateInteractiveChat(request.getChatId(), request.getAppId(), userInteractiveVal,
                        aiResponse, dispatchResult.getNewVariables(), dispatchResult.getDurationSeconds());
            } else {
                SaveChatDTO dto = SaveChatDTO.builder()
                        .chatId(request.getChatId())
                        .appId(request.getAppId())
                        .teamId(teamId)
                        .tmbId(tmbId)
                        .nodes(request.getNodes())
                        .appChatConfig(request.getChatConfig())
                        .variables(dispatchResult.getNewVariables())
                        .isUpdateUseTime(false)
                        .newTitle(newTitle)
                        .source("test")
                        .content(List.of(userQuestion, aiResponse))
                        .durationSeconds(dispatchResult.getDurationSeconds())
                        .build();
                saveChat(dto);
            }

            // 创建使用统计
            createChatUsage(request.getAppName(), request.getAppId(), teamId, tmbId,
                    "fastgpt", dispatchResult.getFlowUsage());

//        } catch (Exception e) {
//            log.error("聊天测试失败: {}", e.getMessage(), e);
//        }
    }

    @Override
    public PageResult<Chat> getHistories(GetHistoriesRequest request) {
        log.info("获取聊天历史记录，请求参数：{}", request);

        try {
            String outLinkUid = request.getOutLinkUid();

            Long appId = request.getAppId();
            String teamId = request.getTeamId();
            String teamToken = request.getTeamToken();
            String source = request.getSource();
            Integer offset = request.getOffset();
            Integer pageSize = request.getPageSize();


            ChatPageParamDTO paramDTO = new ChatPageParamDTO();

            // 当前只支持外链认证方式
            if (StringUtils.hasText(request.getShareId()) && StringUtils.hasText(request.getOutLinkUid())) {
                // 进行外链身份验证
                authOutLinkService.authOutLinkChatInit(request.getShareId(), request.getAuthToken());
                paramDTO.setOutLinkUid(outLinkUid);
                paramDTO.setShareId(request.getShareId());
                paramDTO.setStartUpdateTime(new Date(System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000));
            } else if (appId != null && StringUtils.hasText(teamId) && StringUtils.hasText(teamToken)) {
                // TODO 需要进行验证teamId和teamToken，但是目前没有对应概念，不实现
                throw new BussinessException(ChatErrorCodeConstant.TEAM_TOKEN_INVALID);
            } else if (appId != null) {
                AuthDTO authDTO = authService.authCert();
                String orgId = authDTO.getTmbId();
                paramDTO.setOrgId(orgId);
                paramDTO.setAppId(appId);
                if (StringUtils.hasText(source)) {
                    paramDTO.setSource(source);
                }
            } else {
                return PageResult.of(0, List.of());
            }

            Date startCreateTime = request.getStartCreateTime();
            Date endCreateTime = request.getEndCreateTime();
            Date startUpdateTime = request.getStartUpdateTime();
            Date endUpdateTime = request.getEndUpdateTime();

            if (!Objects.isNull(startCreateTime) || !Objects.isNull(endCreateTime)) {
                paramDTO.setStartCreateTime(startCreateTime);
                paramDTO.setEndCreateTime(endCreateTime);
            }

            if (!Objects.isNull(startUpdateTime) || !Objects.isNull(endUpdateTime)) {
                paramDTO.setStartUpdateTime(startUpdateTime);
                paramDTO.setEndUpdateTime(endUpdateTime);
            }
            // 构建分页参数和排序
            Page<Chat> page = new Page<>(offset / pageSize, pageSize);
            page.setOrders(List.of(OrderItem.desc("top"), OrderItem.desc("updateTimeStamp")));

            page = chatDAO.pageByParamDTO(paramDTO, page);

            // 构建响应
            return PageResult.of((int) page.getTotal(), page.getRecords());

        } catch (Exception e) {
            log.error("获取聊天历史记录失败", e);
            if (e instanceof BussinessException) {
                throw e;
            }
            throw new BussinessException(ChatErrorCodeConstant.GET_HISTORY_FAILED, e.getMessage());
        }
    }

    /**
     * 获取或创建聊天记录
     */
    private Chat getOrCreateChat(ChatTestRequest request, App app, String tmbId, String teamId) {
        String chatId = request.getChatId();

        if (StringUtils.hasText(chatId)) {
            Optional<Chat> chatOpt = chatDAO.getByAppIdAndChatId(Long.valueOf(request.getAppId()), chatId);
            if (chatOpt.isPresent()) {
                Chat chat = chatOpt.get();
                if (!ChatUtil.validateChatPermission(app, chat, tmbId)) {
                    throw new BussinessException(ChatErrorCodeConstant.CHAT_NO_PERMISSION);
                }
                return chat;
            }
        }

        // 创建新的聊天记录
        Chat newChat = new Chat();
        newChat.setAppId(Long.valueOf(request.getAppId()));
        newChat.setChatId(chatId != null ? chatId : generateChatId());
        newChat.setOrgId(tmbId);
        newChat.setTitle("测试聊天");
        newChat.setVariables(request.getVariables());

        chatDAO.save(newChat);
        return newChat;
    }

    /**
     * 验证聊天测试请求参数
     */
    private void validateChatTestRequest(ChatTestRequest request) {
        if (request == null) {
            throw new BussinessException(ChatErrorCodeConstant.PARAM_EMPTY);
        }
        if (request.getAppId() == null) {
            throw new BussinessException(ChatErrorCodeConstant.APP_ID_EMPTY);
        }
        if (request.getNodes() == null || request.getNodes().isEmpty()) {
            throw new BussinessException(ChatErrorCodeConstant.NODES_EMPTY);
        }
        if (request.getEdges() == null) {
            throw new BussinessException(ChatErrorCodeConstant.EDGES_EMPTY);
        }
        if (request.getMessages() == null) {
            throw new BussinessException(ChatErrorCodeConstant.MESSAGES_EMPTY);
        }
    }

    /**
     * 提取用户问题
     */
    private ChatItemType extractUserQuestion(List<ChatItemType> chatMessages, App app,
                                             boolean isPlugin, boolean isTool, Map<String, Object> variables) {
        if (isPlugin) {

            List<FlowNodeInputItemType> pluginInputs = PluginUtils.getPluginInputsFromStoreNodes(app.getModules());
            List<ChatItemValueItemFileInfo> files = (List<ChatItemValueItemFileInfo>)variables.get("files");

            // 使用WorkflowUtil方法获取查询文本
            return WorkflowUtil.getPluginRunUserQuery(pluginInputs, variables, files);
        }

        if (isTool) {
            // 工具类型应用的处理逻辑
            ChatItemType toolQuestion = new ChatItemType();
            toolQuestion.setObj(ChatRoleEnum.HUMAN);
            List<ChatItemValueItemType> value = new ArrayList<>();
            ChatItemValueItemType itemType = new ChatItemValueItemType();
            itemType.setType(ChatItemValueType.TEXT.getValue());
            ChatItemValueItemTextInfo textContent = new ChatItemValueItemTextInfo();
            textContent.setContent("tool");
            itemType.setText(textContent);
            value.add(itemType);
            toolQuestion.setValue(value);
            return toolQuestion;
        }

        // 普通聊天应用，从消息列表中获取最后一条用户消息
        if (chatMessages != null && !chatMessages.isEmpty()) {
            for (int i = chatMessages.size() - 1; i >= 0; i--) {
                ChatItemType message = chatMessages.get(i);
                if ("Human".equals(message.getObj().getValue())) {
                    return message;
                }
            }
        }

        throw new BussinessException(ChatErrorCodeConstant.BUSINESS_USER_QUESTION_EMPTY);
    }

    /**
     * 合并变量
     */
    private Map<String, Object> mergeVariables(Map<String, Object> requestVariables, Map<String, Object> chatVariables) {
        Map<String, Object> result = new HashMap<>();
        if (chatVariables != null) {
            result.putAll(chatVariables);
        }
        if (requestVariables != null) {
            result.putAll(requestVariables);
        }
        return result;
    }

    /**
     * 获取聊天历史
     */
    private List<ChatItemType> getChatHistories(Long appId, String chatId, Integer offset, Integer limit) {
        if (!StringUtils.hasText(chatId)) {
            return new ArrayList<>();
        }
        Page<ChatItem> page = new Page<>(offset / limit, limit);
        page.setOrders(List.of(OrderItem.desc("id")));

        page = chatItemDAO.pageByChatIdAndAppId(chatId, appId, page);

        IPage<ChatItemType> typePage = page.convert(this::convertChatItem2ChatItemType);

        List<ChatItemType> histories = typePage.getRecords();
        // 反转列表以获得正确的时间顺序
        Collections.reverse(histories);
        return histories;
    }

    /**
     * 获取当前时间
     */
    private String getCurrentTime() {
        return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date());
    }

    /**
     * 创建AI响应
     */
    private ChatItemType createAiResponse(String responseChatItemId, DispatchFlowResponse workflowResult) {
        try {
            List<ChatItemValueItemType> newType = new ArrayList<>();
            for (AIChatItemValueItemType type : workflowResult.getAssistantResponses()) {
                ChatItemValueItemType itemValueItemType = new ChatItemValueItemType();
                BeanUtils.copyProperties(type, itemValueItemType);
                newType.add(itemValueItemType);
            }
            ChatItemType aiResponse = new ChatItemType();
            aiResponse.setDataId(responseChatItemId);
            aiResponse.setObj(ChatRoleEnum.AI);
            aiResponse.setValue(newType);
            aiResponse.setMemories(workflowResult.getSystem_memories());
            aiResponse.setResponseData(workflowResult.getFlowResponses());
            return aiResponse;
        } catch (Exception e) {
            throw new BussinessException(ChatErrorCodeConstant.WORKFLOW_EXECUTION_FAILED);
        }
    }

    /**
     * 更新交互式聊天
     */
    public void updateInteractiveChat(String chatId, Long appId, String userInteractiveVal,
                                       ChatItemType aiResponse, Map<String, Object> newVariables,
                                       Double durationSeconds) {
        // TODO 逻辑需要重新写
        try {
            // 查找现有的聊天记录
            Optional<Chat> chatOpt = chatDAO.getByAppIdAndChatId(appId, chatId);
            if (chatOpt.isPresent()) {
                Chat chat = chatOpt.get();
                chat.setVariables(newVariables);
                chat.setUpdateTimeStamp(new java.util.Date());
                chatDAO.save(chat);

                // 更新最后的聊天项
                // 这里可以添加更新聊天项的逻辑
                log.info("更新交互式聊天: chatId={}, userVal={}", chatId, userInteractiveVal);
            }
        } catch (Exception e) {
            log.error("更新交互式聊天失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存聊天
     */
    @Override
    public void saveChat(SaveChatDTO dto) {

        String chatId = dto.getChatId();
        if (chatId == null || "NO_RECORD_HISTORIES".equals(chatId)) {
            return;
        }

        try {
            // 查找现有聊天记录
            Chat chat = chatDAO.getByAppIdAndChatId(Long.valueOf(dto.getAppId()), dto.getChatId()).orElse(null);
            Map<String, Object> metadataUpdate = chat == null ? new HashMap<>() : chat.getMetadata();

            if (dto.getMetadata() != null) {
                metadataUpdate.putAll(dto.getMetadata());
            }

            // 获取应用聊天配置
            GetAppChatConfigReq req = GetAppChatConfigReq.builder()
                    .chatConfig(dto.getAppChatConfig())
                    .systemConfigNode(WorkflowUtil.getGuideModule(dto.getNodes()))
                    .isPublicFetch(false)
                    .build();

            AppChatConfigType appChatConfig = WorkflowUtil.getAppChatConfig(req);

            String welcomeText = appChatConfig.getWelcomeText();
            List<VariableItemType> variableList = appChatConfig.getVariables();

            // 获取插件输入
            List<FlowNodeInputItemType> pluginInputs = dto.getNodes().stream()
                    .filter(node -> FlowNodeTypeEnum.PLUGIN_INPUT.getValue().equals(node.getFlowNodeType()))
                    .findFirst()
                    .map(StoreNodeItemType::getInputs)
                    .orElse(null);

            // 处理聊天内容：移除引用问答
            List<ChatItemType> processedContent = dto.getContent().stream().map(item -> {
                if (ChatRoleEnum.AI.equals(item.getObj())) {
                    // 处理AI响应的nodeResponse
                    List<ChatHistoryItemResType> nodeResponse = item.getResponseData();
                    if (nodeResponse != null) {
                        List<ChatHistoryItemResType> processedNodeResponse = nodeResponse.stream().map(responseItem -> {
                            if (FlowNodeTypeEnum.DATASET_SEARCH_NODE.getValue().equals(responseItem.getModuleType()) &&
                                    responseItem.getQuoteList() != null) {
                                // 处理引用列表，只保留必要字段
                                List<SearchDataResponseItemType> quoteList = responseItem.getQuoteList();
                                List<SearchDataResponseItemType> processedQuoteList = quoteList.stream()
                                        .map(quote -> {
                                            SearchDataResponseItemType processedQuote = new SearchDataResponseItemType();
                                            processedQuote.setId(quote.getId());
                                            processedQuote.setChunkIndex(quote.getChunkIndex());
                                            processedQuote.setDatasetId(quote.getDatasetId());
                                            processedQuote.setCollectionId(quote.getCollectionId());
                                            processedQuote.setSourceId(quote.getSourceId());
                                            processedQuote.setSourceName(quote.getSourceName());
                                            processedQuote.setScore(quote.getScore());
                                            // todo processedQuote.setTokens(quote.getTokens());
                                            return processedQuote;
                                        })
                                        .collect(Collectors.toList());

                                // 创建新的响应项
                                ChatHistoryItemResType newResponseItem = new ChatHistoryItemResType();
                                try {
                                    BeanUtils.copyProperties(responseItem, newResponseItem);
                                } catch (Exception e) {
                                    log.error("复制ChatHistoryItemResType属性失败: {}", e.getMessage(), e);
                                    newResponseItem = responseItem;
                                }
                                newResponseItem.setQuoteList(processedQuoteList);
                                return newResponseItem;
                            }
                            return responseItem;
                        }).collect(Collectors.toList());

                        // 创建新的ChatItemType对象
                        ChatItemType newItem = new ChatItemType();
                        newItem.setValue(item.getValue());
                        newItem.setMemories(item.getMemories());
                        newItem.setDataId(item.getDataId());
                        newItem.setHideInUI(item.getHideInUI());
                        newItem.setObj(item.getObj());
                        newItem.setAdminFeedback(item.getAdminFeedback());
                        newItem.setCustomFeedbacks(item.getCustomFeedbacks());
                        newItem.setLlmModuleAccount(item.getLlmModuleAccount());
                        newItem.setTotalQuoteList(item.getTotalQuoteList());
                        newItem.setHistoryPreviewLength(item.getHistoryPreviewLength());
                        newItem.setUserBadFeedback(item.getUserBadFeedback());
                        newItem.setUserGoodFeedback(item.getUserGoodFeedback());
                        newItem.setResponseData(processedNodeResponse);
                        return newItem;
                    }
                }
                return item;
            }).toList();

            // 使用事务保存聊天项和聊天记录
            List<ChatItem> savedChatItems = new ArrayList<>();

            // 保存聊天项
            for (ChatItemType contentItem : processedContent) {
                ChatItem chatItem = new ChatItem();
                chatItem.setChatId(dto.getChatId());
                chatItem.setOrgId(dto.getTmbId());
                chatItem.setAppId(Long.valueOf(dto.getAppId()));
                chatItem.setDataId(contentItem.getDataId());
                chatItem.setObj(contentItem.getObj() != null ? contentItem.getObj().getValue() : null);
                chatItem.setValue(contentItem.getValue());
                chatItem.setMemories(contentItem.getMemories());
                chatItem.setResponseData(contentItem.getResponseData());
                chatItem.setDurationSeconds(dto.getDurationSeconds());
                chatItem.setErrorMsg(dto.getErrorMsg());
                chatItem.setTime(new Date());

                chatItemDAO.save(chatItem);
                savedChatItems.add(chatItem);
            }

            // 更新或创建聊天记录
            if (chat == null) {
                chat = new Chat();
                chat.setChatId(dto.getChatId());
                chat.setAppId(Long.valueOf(dto.getAppId()));
                chat.setCreateTimeStamp(new Date());
            }

            chat.setOrgId(dto.getTmbId());
            chat.setVariableList(variableList);
            chat.setWelcomeText(welcomeText);
            chat.setVariables(dto.getVariables() != null ? dto.getVariables() : new HashMap<>());
            chat.setPluginInputs(pluginInputs);
            chat.setTitle(dto.getNewTitle());
            chat.setSource(dto.getSource());
            chat.setSourceName(dto.getSourceName());
            chat.setShareId(dto.getShareId());
            chat.setOutLinkUid(dto.getOutLinkUid());
            chat.setMetadata(metadataUpdate);
            chat.setUpdateTimeStamp(new Date());

            chatDAO.saveOrUpdate(chat);

            // 推送聊天日志（如果有人工和AI响应）
            if (savedChatItems.size() >= 2) {
                Long chatItemIdHuman = null;
                Long chatItemIdAi = null;

                for (ChatItem item : savedChatItems) {
                    if (ChatRoleEnum.HUMAN.getValue().equals(item.getObj())) {
                        chatItemIdHuman = item.getId();
                    } else if (ChatRoleEnum.AI.getValue().equals(item.getObj())) {
                        chatItemIdAi = item.getId();
                    }
                }

                if (chatItemIdHuman != null && chatItemIdAi != null) {
                    pushChatLog(dto.getChatId(), chatItemIdHuman, chatItemIdAi, dto.getAppId());
                }
            }

            // 如果需要更新使用时间，更新应用的updateTime
            if (dto.isUpdateUseTime()) {
                App app = appDAO.getById(dto.getAppId());
                if (app != null) {
                    app.setUpdateTimeStamp(new Date());
                    appDAO.save(app);
                }
            }
            log.info("保存聊天成功: chatId={}, title={}", dto.getChatId(), dto.getNewTitle());
        } catch (Exception e) {
            log.error("保存聊天失败: chatId={}, error={}", dto.getChatId(), e.getMessage(), e);
        }
    }

    /**
     * 推送聊天日志 packages\service\core\chat\pushChatLog.ts
     */
    private void pushChatLog(String chatId, Long chatItemIdHuman, Long chatItemIdAi, Long appId) {
        try {
            // TODO: 实现聊天日志推送逻辑
            log.info("推送聊天日志: chatId={}, humanId={}, aiId={}, appId={}",
                    chatId, chatItemIdHuman, chatItemIdAi, appId);
        } catch (Exception e) {
            log.error("推送聊天日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建聊天使用统计
     */
    @Override
    public int createChatUsage(String appName, Long appId, String teamId, String tmbId,
                                 String source, List<ChatNodeUsageType> flowUsages) {
        try {
            // TODO 这里可以添加使用统计的逻辑
            log.info("创建聊天使用统计: appId={}, source={}", appId, source);
        } catch (Exception e) {
            log.error("创建聊天使用统计失败: {}", e.getMessage(), e);
        }
        return 1;
    }

    /**
     * 生成聊天ID
     */
    private String generateChatId() {
        return "chat_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    /**
     * 处理聊天记录数据
     */
    private List<ChatItemType> processChatItems(List<ChatItemType> items, App app, String chatType, Boolean loadCustomFeedbacks) {
        if (items == null || items.isEmpty()) {
            return items;
        }

        boolean isPlugin = AppTypeEnum.PLUGIN.getValue().equals(app.getType());
        boolean isOutLink = chatType.equals(GetChatTypeEnum.OUT_LINK.getValue());

        // 根据不同的聊天类型和应用类型处理数据
        for (ChatItemType item : items) {
            // 如果是外链聊天且不是插件应用，需要过滤敏感信息
            if (isOutLink && !isPlugin) {
                filterSensitiveData(item);
            }

            // 如果不需要加载自定义反馈，清除相关字段
            if (!Boolean.TRUE.equals(loadCustomFeedbacks)) {
                item.setCustomFeedbacks(null);
            }
        }

        return items;
    }

    /**
     * 过滤敏感数据
     */
    private void filterSensitiveData(ChatItemType item) {
        // TODO 这里可以根据需要过滤敏感信息
        // 例如：移除某些响应数据、隐藏错误信息等
        if (ChatRoleEnum.AI.equals(item.getObj())) {
            // 可以在这里添加具体的过滤逻辑
            // 例如：过滤responseData中的敏感信息
        }
    }

    @Override
    public void addCustomFeedbacks(Long appId, String chatId, String dataId, List<String> feedbacks) {
        if (chatId == null || chatId.isEmpty() || dataId == null || dataId.isEmpty() || CollUtil.isEmpty(feedbacks)) {
            return;
        }

        ChatItem chatItem = chatItemDAO.getByAppIdAndChatIdAndDataId(appId, chatId, dataId);

        if (chatItem == null) {
           return;
        }

        List<String> customFeedbacks = chatItem.getCustomFeedbacks();
        if (CollUtil.isEmpty(customFeedbacks)) {
            customFeedbacks = new LinkedList<>();
        }

        customFeedbacks.addAll(feedbacks);

        chatItem.setCustomFeedbacks(customFeedbacks);

        chatItemDAO.updateById(chatItem);
        log.info("成功添加自定义反馈: appId={}, chatId={}, dataId={}, feedbacks={}",
                appId, chatId, dataId, feedbacks);
    }

    @Override
    public void updateHistory(UpdateHistoryRequest request) {
        Boolean top = request.getTop();
        String title = request.getTitle();
        String customTitle = request.getCustomTitle();
        Long appId = request.getAppId();
        String chatId = request.getChatId();

        authChatService.authChatCrud(AuthChatCrudParams.builder()
                .appId(appId)
                .chatId(chatId)
                .build());

        chatDAO.lambdaUpdate()
                .eq(Chat::getAppId, appId)
                .eq(Chat::getChatId, chatId)
                .set(title != null, Chat::getTitle, title)
                .set(customTitle != null, Chat::getCustomTitle, customTitle)
                .set(top != null, Chat::getTop, top)
                .update();
    }

    @Override
    public void delHistory(DelHistoryRequest request) {
        Long appId = request.getAppId();
        String chatId = request.getChatId();

        // 校验权限
        authChatService.authChatCrud(AuthChatCrudParams.builder()
                .appId(appId)
                .chatId(chatId)
                .build());

        // 删除对话文件
        // TODO 暂未实现删除对话文件,deleteChatFiles

        // 删除对话item和对话
        chatItemDAO.deleteByAppIdAndChatId(appId, chatId);
        chatDAO.deleteByAppIdAndChatId(appId, chatId);
    }

    @Override
    public void clearHistories(ClearHistortiesRequest request) {
        Long appId = request.getAppId();
        String shareId = request.getShareId();
        String outLinkUid = request.getOutLinkUid();
        String teamId = request.getTeamId();
        String teamToken = request.getTeamToken();

        // 校验权限
        AuthChatCrudDTO authChatCrudDTO = authChatService.authChatCrud(AuthChatCrudParams.builder()
                .appId(request.getAppId())
                .shareId(request.getShareId())
                .outLinkUid(request.getOutLinkUid())
                .teamToken(request.getTeamToken())
                .teamId(request.getTeamId())
                .build());

        String chatTeamId = authChatCrudDTO.getTeamId();
        String tmbId = authChatCrudDTO.getTmbId();
        String uid = authChatCrudDTO.getUid();
        AuthUserTypeEnum authType = authChatCrudDTO.getAuthType();

        // 构建查询条件
        List<String> idList;
        if (shareId != null && !shareId.isEmpty() && outLinkUid != null && !outLinkUid.isEmpty() && authType == AuthUserTypeEnum.OUT_LINK) {
            idList = chatDAO.findChatIdByAppIdAndOutLinkUid(appId, outLinkUid);
        } else if (teamId != null && !teamId.isEmpty() && teamToken != null && !teamToken.isEmpty() && authType == AuthUserTypeEnum.TEAM_DOMAIN) {
            idList = chatDAO.findChatIdByAppIdAndOutLinkUid(appId, outLinkUid);
        } else if (authType == AuthUserTypeEnum.TOKEN) {
            idList = chatDAO.findChatIdByOrgIdAndAppIdAndSource(tmbId, appId, ChatSourceEnum.ONLINE.toString());
        } else if (authType == AuthUserTypeEnum.API_KEY) {
            idList = chatDAO.findChatIdByAppIdAndSource(appId, ChatSourceEnum.API.toString());
        } else {
            throw new BussinessException(ChatErrorCodeConstant.PARAM_ERROR);
        }

        // 删除聊天文件
        // TODO: 实现deleteChatFiles方法
        // deleteChatFiles(idList);

        // 使用事务删除聊天项和聊天记录
        chatItemDAO.deleteByAppIdAndChatIdIn(appId, idList);
        chatDAO.removeBatchByIds(idList);
    }

    @Override
    public PageResult<ChatItemType> getChatItems(String chatId, Long appId, int offset, int limit) {
        if (!StringUtils.hasText(chatId)) {
            return PageResult.of(0, new ArrayList<>());
        }
        // 构建分页参数
        Page<ChatItem> page = new Page<>(offset / limit, limit);
        page.setOrders(List.of(OrderItem.desc("id")));

        Page<ChatItem> chatItemPage = chatItemDAO.pageByChatIdAndAppId(
                chatId,
                appId,
                page
        );

        List<ChatItem> histories = new ArrayList<>(chatItemPage.getRecords());
        Collections.reverse(histories);

        List<ChatItemType> itemTypeHistories = histories.stream()
                .map(this::convertChatItem2ChatItemType)
                .toList();

        // 构建响应
        PageResult<ChatItemType> response = new PageResult<>();
        response.setTotal((int) chatItemPage.getTotal());
        response.setList(itemTypeHistories);

        return response;
    }

    private ChatItemType convertChatItem2ChatItemType(ChatItem item) {
        ChatItemType chatItemType = new ChatItemType();
        org.springframework.beans.BeanUtils.copyProperties(item, chatItemType);
        chatItemType.setObj(ChatRoleEnum.getByValue(item.getObj()));

        // TODO 没找到从哪来的值，展示设置为空列表，防止前端报错
        chatItemType.setTotalQuoteList(List.of());
        return chatItemType;
    }
}
