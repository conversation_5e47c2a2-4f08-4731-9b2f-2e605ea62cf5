package com.sinitek.mind.core.app.enumerate;

import com.sinitek.mind.core.app.constant.AppErrorCodeConstant;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Getter;

@Getter
public enum HeaderSecretType {
    BEARER("Bearer"),
    CUSTOM("custom");

    private final String value;

    HeaderSecretType(String value) {
        this.value = value;
    }

    public static HeaderSecretType fromValue(String value) {
        for (HeaderSecretType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new BussinessException(AppErrorCodeConstant.UNKNOWN_HEADER_SECRET_TYPE, value);
    }
}