package com.sinitek.mind.core.app.model;

import com.sinitek.mind.support.account.dto.SourceMemberDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 版本列表项
 * 对应原始的VersionListItemType类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VersionListItemType {

    /**
     * 版本ID
     */
    private String id;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 创建时间
     */
    private Date time;

    /**
     * 是否已发布
     */
    private Boolean isPublish;

    /**
     * 模板ID
     */
    private String tmbId;

    /**
     * 操作人员
     */
    private SourceMemberDTO sourceMember;
}