package com.sinitek.mind.core.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 更新应用版本请求DTO
 */
@Data
@Schema(description = "更新应用版本请求")
public class UpdateAppVersionDTO {

    @NotNull(message = "应用ID不能为空")
    @Schema(description = "应用ID", required = true)
    private Long appId;

    @NotNull(message = "版本ID不能为空")
    @Schema(description = "版本ID", required = true)
    private Long versionId;

    @NotBlank(message = "版本名称不能为空")
    @Schema(description = "版本名称", required = true)
    private String versionName;
}