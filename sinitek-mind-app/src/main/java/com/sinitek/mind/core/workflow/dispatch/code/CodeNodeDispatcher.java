package com.sinitek.mind.core.workflow.dispatch.code;

import cn.hutool.core.util.ObjectUtil;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.*;
import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import com.sinitek.mind.system.support.SystemGlobals;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 代码节点
 *
 * <AUTHOR>
 * @date 2025/7/17
 */
@Component
public class CodeNodeDispatcher implements NodeDispatcher {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.CODE.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        Map<String, Object> params = dispatchData.getParams();
        String codeType = (String) params.get(NodeInputKeyEnum.CODE_TYPE.getValue());
        String code = (String) params.get(NodeInputKeyEnum.CODE.getValue());
        Map<String, Object> customVariables = (Map<String, Object>) params.get(
            NodeInputKeyEnum.ADD_INPUT_PARAM.getValue());

        Map<String, Object> result = new HashMap<>();
        String sandboxRequestUrl = getURL(codeType);
        try {
            // 调用独立的请求方法
            SandboxResponse runResult = sendSandboxRequest(sandboxRequestUrl, code, customVariables);
            if (runResult.getSuccess()) {
                Map<String, Object> codeReturn = (Map<String, Object>) runResult.getData().get("codeReturn");
                result.put(NodeOutputKeyEnum.RAW_RESPONSE.getValue(), codeReturn);
                result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(),
                    DispatchNodeResponseType.builder()
                        .customInputs(customVariables)
                        .customOutputs(codeReturn)
                        .codeLog((String) runResult.getData().get("log"))
                        .build());
                result.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), codeReturn);
                result.putAll(codeReturn);
            } else {
                throw new BussinessException(WorkflowErrorCodeConstant.RUN_CODE_FAILED);
            }
        } catch (Exception e) {
            result.put(NodeOutputKeyEnum.ERROR.getValue(), e.getMessage());
            result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(),
                DispatchNodeResponseType.builder()
                    .customInputs(customVariables)
                    .error(e.getMessage())
                    .build());
        }
        return result;
    }

    /**
     * 向沙箱服务发送 POST 请求
     * @param url 请求的 URL
     * @param code 要执行的代码
     * @param variables 自定义变量
     * @return 沙箱服务的响应对象
     */
    private SandboxResponse sendSandboxRequest(String url, String code, Map<String, Object> variables) {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 设置请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("code", code);
        requestBody.put("variables", variables);

        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        // 发起 POST 请求
        return restTemplate.postForObject(url, requestEntity, SandboxResponse.class);
    }

    /**
     * 获取代码运行url
     * @param codeType 代码类型
     * @return url
     */
    private String getURL(String codeType) {
        if (ObjectUtil.equals(SandboxCodeTypeEnum.PY.getValue(), codeType)) {
            return SystemGlobals.getSandboxUrl() + "/sandbox/python";
        } else if (ObjectUtil.equals(SandboxCodeTypeEnum.JS.getValue(), codeType)) {
            return SystemGlobals.getSandboxUrl() + "/sandbox/js";
        } else {
            throw new BussinessException(WorkflowErrorCodeConstant.INVALID_CODE_TYPE, codeType);
        }
    }

    @Data
    private static class SandboxResponse{
        Boolean success = false;
        Map<String, Object> data = new HashMap<>();
    }
}
