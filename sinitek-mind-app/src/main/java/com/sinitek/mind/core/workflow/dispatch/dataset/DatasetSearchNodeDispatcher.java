package com.sinitek.mind.core.workflow.dispatch.dataset;

import com.sinitek.mind.core.ai.util.PromptUtil;
import com.sinitek.mind.core.app.util.ImageUtils;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.dataset.enumerate.DatasetSearchModeEnum;
import com.sinitek.mind.core.dataset.model.*;
import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.ChatNodeUsageType;
import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.dataset.repository.DatasetRepository;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.support.ModelConfigManager;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.service.ITeamMemberService;
import com.sinitek.mind.support.wallet.model.ModelPointsRequest;
import com.sinitek.mind.support.wallet.util.WalletUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

@Slf4j
@Component
public class DatasetSearchNodeDispatcher implements NodeDispatcher {

    @Autowired
    private DatasetRepository datasetRepository;

    @Autowired
    private ITeamMemberService teamMemberService;

    @Autowired
    private IPermissionService permissionService;

    @Override
    public String getNodeType() {
        return FlowNodeTypeEnum.DATASET_SEARCH_NODE.getValue();
    }

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchDatasetSearch(dispatchData);
    }

    private Map<String, Object> dispatchDatasetSearch(ModuleDispatchProps props) {
        // 提取参数
        String teamId = props.getTeamId();
        String tmbId = props.getUserId();
        List<ChatItemType> histories = props.getHistories();
        Map<String, Object> params = props.getParams();

        // 获取输入参数
        List<Map<String, Object>> datasets = getListParam(params, NodeInputKeyEnum.DATASET_SELECT_LIST.getValue(), new ArrayList<>());
        Double similarity = getDoubleParam(params, NodeInputKeyEnum.DATASET_SIMILARITY.getValue(), 0.0);
        Integer limit = getIntParam(params, NodeInputKeyEnum.DATASET_MAX_TOKENS.getValue(), 5000);
        String userChatInput = getStringParam(params, NodeInputKeyEnum.USER_CHAT_INPUT.getValue(), "");
        Boolean authTmbId = getBooleanParam(params, NodeInputKeyEnum.AUTH_TMB_ID.getValue(), false);
        String collectionFilterMatch = getStringParam(params, NodeInputKeyEnum.COLLECTION_FILTER_MATCH.getValue(), "");
        String searchMode = getStringParam(params, NodeInputKeyEnum.DATASET_SEARCH_MODE.getValue(), DatasetSearchModeEnum.EMBEDDING.getValue());
        Double embeddingWeight = getDoubleParam(params, NodeInputKeyEnum.DATASET_SEARCH_EMBEDDING_WEIGHT.getValue(), 0.5);
        Boolean usingReRank = getBooleanParam(params, NodeInputKeyEnum.DATASET_SEARCH_USING_RE_RANK.getValue(), false);
        String rerankModel = getStringParam(params, NodeInputKeyEnum.DATASET_SEARCH_RERANK_MODEL.getValue(), "");
        Double rerankWeight = getDoubleParam(params, NodeInputKeyEnum.DATASET_SEARCH_RERANK_WEIGHT.getValue(), 0.5);
        Boolean datasetSearchUsingExtensionQuery = getBooleanParam(params, NodeInputKeyEnum.DATASET_SEARCH_USING_EXTENSION_QUERY.getValue(), false);
        String datasetSearchExtensionModel = getStringParam(params, NodeInputKeyEnum.DATASET_SEARCH_EXTENSION_MODEL.getValue(), "");
        String datasetSearchExtensionBg = getStringParam(params, NodeInputKeyEnum.DATASET_SEARCH_EXTENSION_BG.getValue(), "");
        Boolean datasetDeepSearch = getBooleanParam(params, NodeInputKeyEnum.DATASET_DEEP_SEARCH.getValue(), false);
        String datasetDeepSearchModel = getStringParam(params, NodeInputKeyEnum.DATASET_DEEP_SEARCH_MODEL.getValue(), "");
        Integer datasetDeepSearchMaxTimes = getIntParam(params, NodeInputKeyEnum.DATASET_DEEP_SEARCH_MAX_TIMES.getValue(), 3);
        String datasetDeepSearchBg = getStringParam(params, NodeInputKeyEnum.DATASET_DEEP_SEARCH_BG.getValue(), "");

        // 验证输入
        if (CollectionUtils.isEmpty(datasets)) {
            throw new BussinessException(WorkflowErrorCodeConstant.DATASET_LIST_EMPTY);
        }

        // 构建空结果
        Map<String, Object> emptyResult = buildEmptyResult(limit, searchMode);

        if (!StringUtils.hasText(userChatInput)) {
            return emptyResult;
        }

        // 获取数据集ID列表
        List<String> datasetIds = extractDatasetIds(datasets, authTmbId, tmbId);

        if (CollectionUtils.isEmpty(datasetIds)) {
            return emptyResult;
        }

        // 获取向量模型
        SystemModelDTO vectorModel = getVectorModel(datasetIds.get(0));

        SystemModelDTO rerankModelData = getRerankModel(rerankModel);


        // 执行搜索
        SearchDatasetDataResponse searchResponse = datasetDeepSearch ? deepRagSearch(
                DeepRagSearchProps.builder()
                        .histories(histories)
                        .teamId(teamId)
                        .reRankQuery(userChatInput)
                        .queries(List.of(userChatInput))
                        .model(vectorModel.getModel())
                        .similarity(similarity)
                        .limit(limit)
                        .datasetIds(datasetIds)
                        .searchMode(searchMode)
                        .embeddingWeight(embeddingWeight)
                        .usingReRank(usingReRank)
                        .rerankWeight(rerankWeight)
                        .collectionFilterMatch(collectionFilterMatch)
                        .datasetDeepSearchMaxTimes(datasetDeepSearchMaxTimes)
                        .datasetDeepSearchModel(datasetDeepSearchModel)
                        .datasetDeepSearchBg(datasetDeepSearchBg)
                .build()
        ) : defaultSearchDatasetData(
                DefaultSearchDatasetDataProps.builder()
                        .histories(histories)
                        .teamId(teamId)
                        .reRankQuery(userChatInput)
                        .queries(List.of(userChatInput))
                        .model(vectorModel.getModel())
                        .similarity(similarity)
                        .limit(limit)
                        .datasetIds(datasetIds)
                        .searchMode(searchMode)
                        .embeddingWeight(embeddingWeight)
                        .usingReRank(usingReRank)
                        .rerankWeight(rerankWeight)
                        .collectionFilterMatch(collectionFilterMatch)
                        .datasetSearchUsingExtensionQuery(datasetSearchUsingExtensionQuery)
                        .datasetSearchExtensionModel(datasetSearchExtensionModel)
                        .datasetSearchExtensionBg(datasetSearchExtensionBg)
                        .build()
        );
        // 计算费用
        List<ChatNodeUsageType> nodeDispatchUsages = new ArrayList<>();
        // vector
        ModelPointsRequest request = new ModelPointsRequest();
        request.setModel(vectorModel.getModel());
        request.setInputTokens(searchResponse==null?0:searchResponse.getEmbeddingTokens());
        request.setModelType("embedding");
        ChatNodeUsageType chatNodeUsageType = WalletUtil.formatModelChars2Points(request);
        double totalPoints = chatNodeUsageType.getTotalPoints();
        String moduleName = chatNodeUsageType.getModuleName();
        ChatNodeUsageType chatNodeUsageTypeNew = ChatNodeUsageType.builder()
                .totalPoints(totalPoints)
                .moduleName(props.getNode().getName())
                .model(moduleName)
                .inputTokens(searchResponse==null?0:searchResponse.getEmbeddingTokens())
                .build();
        nodeDispatchUsages.add(chatNodeUsageTypeNew);
        // Rerank
        ModelPointsRequest requestRerank = new ModelPointsRequest();
        requestRerank.setModel(rerankModelData.getModel());
        requestRerank.setInputTokens(searchResponse==null?0:searchResponse.getReRankInputTokens());
        requestRerank.setModelType("rerank");
        ChatNodeUsageType chatNodeUsageTypeRerank = WalletUtil.formatModelChars2Points(requestRerank);

        if (usingReRank) {
            double totalPointsReRank = chatNodeUsageTypeRerank.getTotalPoints();
            String moduleNameReRank = chatNodeUsageTypeRerank.getModuleName();
            ChatNodeUsageType chatNodeUsageTypeReRankNew = ChatNodeUsageType.builder()
                    .totalPoints(totalPointsReRank)
                    .moduleName(props.getNode().getName())
                    .model(moduleNameReRank)
                    .inputTokens(searchResponse==null?0:searchResponse.getReRankInputTokens())
                    .build();
            nodeDispatchUsages.add(chatNodeUsageTypeReRankNew);
        }

        // Query extension
        if (searchResponse!= null && searchResponse.getQueryExtensionResult()!= null) {
            QueryExtensionResult queryExtensionResult = searchResponse.getQueryExtensionResult();
            ModelPointsRequest requestQuery = new ModelPointsRequest();
            requestQuery.setModel(queryExtensionResult.getModel());
            requestQuery.setInputTokens(queryExtensionResult.getInputTokens());
            requestQuery.setOutputTokens(queryExtensionResult.getOutputTokens());
            requestQuery.setModelType("llm");
            ChatNodeUsageType chatNodeUsageTypeQuery = WalletUtil.formatModelChars2Points(requestQuery);
            double totalPointsQuery = chatNodeUsageTypeQuery.getTotalPoints();
            String moduleNameQuery = chatNodeUsageTypeQuery.getModuleName();
            ChatNodeUsageType chatNodeUsageTypeQueryNew = ChatNodeUsageType.builder()
                    .totalPoints(totalPointsQuery)
                    .moduleName("common:core.module.template.Query extension")
                    .model(moduleNameQuery)
                    .inputTokens(queryExtensionResult.getInputTokens())
                    .outputTokens(queryExtensionResult.getOutputTokens())
                    .build();
            nodeDispatchUsages.add(chatNodeUsageTypeQueryNew);

            totalPoints = totalPointsQuery;
        } else {
            totalPoints = 0;
        }

        // Deep search
        if (searchResponse!= null && searchResponse.getDeepSearchResult()!= null) {
            DispatchNodeResponseType.DeepSearchResult deepSearchResult = searchResponse.getDeepSearchResult();
            ModelPointsRequest requestDeep = new ModelPointsRequest();
            requestDeep.setModel(deepSearchResult.getModel());
            requestDeep.setInputTokens(deepSearchResult.getInputTokens());
            requestDeep.setOutputTokens(deepSearchResult.getOutputTokens());
            requestDeep.setModelType("llm");
            ChatNodeUsageType chatNodeUsageTypeDeep = WalletUtil.formatModelChars2Points(requestDeep);
            double totalPointsDeep = chatNodeUsageTypeDeep.getTotalPoints();
            String moduleNameDeep = chatNodeUsageTypeDeep.getModuleName();
            ChatNodeUsageType chatNodeUsageTypeDeepNew = ChatNodeUsageType.builder()
                    .totalPoints(totalPointsDeep)
                    .moduleName("common:core.module.template.Query extension")
                    .model(moduleNameDeep)
                    .inputTokens(deepSearchResult.getInputTokens())
                    .outputTokens(deepSearchResult.getOutputTokens())
                    .build();
            nodeDispatchUsages.add(chatNodeUsageTypeDeepNew);

            totalPoints = totalPointsDeep;
        } else {
            totalPoints = 0;
        }

        totalPoints = nodeDispatchUsages.stream().mapToDouble(ChatNodeUsageType::getTotalPoints)
                .sum();

        // 构建响应数据
        assert searchResponse != null;
        DispatchNodeResponseType responseData = DispatchNodeResponseType.builder()
                .totalPoints(totalPoints)
                .query(userChatInput)
                .embeddingModel(vectorModel.getName())
                .embeddingTokens(searchResponse.getEmbeddingTokens())
                .similarity(searchResponse.getUsingSimilarityFilter()? similarity: null)
                .limit(limit)
                .searchMode(searchMode)
                .embeddingWeight(searchMode.equals(DatasetSearchModeEnum.MIXED_RECALL.getValue())?embeddingWeight:null)
                .searchUsingReRank(searchResponse.getUsingReRank())
                .rerankModel(rerankModelData.getName())
                .rerankWeight(rerankWeight)
                .reRankInputTokens(searchResponse.getReRankInputTokens())
                .quoteList(searchResponse.getSearchRes())
                .deepSearchResult(searchResponse.getDeepSearchResult())
                .build();

        // 构建最终结果
        Map<String, Object> result = new HashMap<>();
        result.put(NodeOutputKeyEnum.DATASET_QUOTE_QA.getValue(), searchResponse.getSearchRes());
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), responseData);
        result.put("nodeDispatchUsages", nodeDispatchUsages);
        result.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), buildToolResponses(searchResponse.getSearchRes()));

        return result;
    }

    /**
     * 对应defaultSearchDatasetData
     */
    private SearchDatasetDataResponse defaultSearchDatasetData(DefaultSearchDatasetDataProps props) {
        // TODO
        return null;
    }

    /**
     * 对应deepRagSearch
     */
    private SearchDatasetDataResponse deepRagSearch(DeepRagSearchProps props) {
        // TODO
        return null;
    }

    private Map<String, Object> buildEmptyResult(Integer limit, String searchMode) {
        Map<String, Object> emptyResult = new HashMap<>();
        emptyResult.put(NodeOutputKeyEnum.DATASET_QUOTE_QA.getValue(), new ArrayList<>());

        DispatchNodeResponseType nodeResponse = DispatchNodeResponseType.builder()
                .totalPoints(0.0)
                .query("")
                .limit(limit)
                .searchMode(searchMode)
                .build();

        emptyResult.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), nodeResponse);

        emptyResult.put("nodeDispatchUsages", new ArrayList<>());
        emptyResult.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), new ArrayList<>());

        return emptyResult;
    }

    private List<String> extractDatasetIds(List<Map<String, Object>> datasets, Boolean authTmbId, String tmbId) {
        List<String> datasetIds = datasets.stream()
                .map(dataset -> (String) dataset.get("datasetId"))
                .filter(Objects::nonNull)
                .toList();

        if (authTmbId) {
            // 这里应该调用过滤数据集的方法，暂时返回原列表
            return filterDatasetsByTmbId(datasetIds, tmbId);
        }

        return datasetIds;
    }

    /**
     * 根据团队成员ID过滤数据集
     */
    private List<String> filterDatasetsByTmbId(List<String> datasetIds, String tmbId) {
        Map<String, Long> perMap = permissionService.getResourcePerByOrgId(tmbId, datasetIds, ResourceTypeEnum.DATASET, true);

        return datasetIds.stream()
                .filter(item -> (perMap.getOrDefault(item, PermissionConstant.NULL_PERMISSION) & PermissionConstant.READ_PER) == PermissionConstant.READ_PER )
                .toList();
    }

    private SystemModelDTO getVectorModel(String datasetId) {
        // 从数据集获取向量模型
        Dataset dataset = datasetRepository.findById(datasetId).orElse(null);
        String vectorModel = dataset.getVectorModel();
        SystemModelDTO embedding = null;
        if (dataset == null) {
            embedding = ModelConfigManager.getDefaultModels().getEmbedding();
        } else {
            Map<String, SystemModelDTO> embeddingModelMap = ModelConfigManager.getEmbeddingModelMap();
            embedding = embeddingModelMap.get(vectorModel);
        }
        return embedding;
    }

    private SystemModelDTO getRerankModel(String rerankModel) {
        // 从数据集获取向量模型
        SystemModelDTO embedding = null;
        if (rerankModel.isEmpty()) {
            embedding = ModelConfigManager.getDefaultModels().getReRank();
        } else {
            Map<String, SystemModelDTO> embeddingModelMap = ModelConfigManager.getReRankModelMap();
            embedding = embeddingModelMap.get(rerankModel);
        }
        return embedding;
    }

    private List<Map<String, Object>> performEmbeddingSearch(
            float[] queryVector, List<String> datasetIds, Double similarity,
            Integer limit, String collectionFilterMatch) {

        // TODO: 实现向量搜索逻辑
        // 这里应该调用向量数据库进行相似度搜索
        List<Map<String, Object>> results = new ArrayList<>();

        // 模拟搜索结果
        for (int i = 0; i < Math.min(3, limit); i++) {
            Map<String, Object> result = new HashMap<>();
            result.put("id", "search_result_" + i);
            result.put("datasetId", datasetIds.get(0));
            result.put("collectionId", "collection_" + i);
            result.put("q", "这是搜索结果问题 " + i);
            result.put("a", "这是搜索结果答案 " + i);
            result.put("score", 0.9 - i * 0.1);
            result.put("sourceName", "数据源 " + i);
            result.put("updateTime", System.currentTimeMillis());
            results.add(result);
        }

        return results;
    }

    private Map<String, Object> buildToolResponses(List<SearchDataResponseItemType> searchRes) {
        Map<String, Object> toolResponses = new HashMap<>();
        toolResponses.put("prompt", PromptUtil.getDatasetSearchToolResponsePrompt());

        List<Map<String, Object>> cites = searchRes.stream()
                .map(item -> {
                    Map<String, Object> cite = new HashMap<>();
                    cite.put("id", item.getId());
                    cite.put("sourceName", item.getSourceName());
                    cite.put("updateTime", item.getUpdateTime());
                    String q = item.getQ();
                    String a = item.getA();
                    String content = (q + "\n" + (a != null ? a : "")).trim();
                    cite.put("content", ImageUtils.addEndpointToImageUrl(content));
                    return cite;
                })
                .toList();

        toolResponses.put("cites", cites);
        return toolResponses;
    }

    // 辅助方法
    private String getStringParam(Map<String, Object> params, String key, String defaultValue) {
        Object value = params.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    private Integer getIntParam(Map<String, Object> params, String key, Integer defaultValue) {
        Object value = params.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    private Double getDoubleParam(Map<String, Object> params, String key, Double defaultValue) {
        Object value = params.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }

    private Boolean getBooleanParam(Map<String, Object> params, String key, Boolean defaultValue) {
        Object value = params.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }

    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getListParam(Map<String, Object> params, String key, List<Map<String, Object>> defaultValue) {
        Object value = params.get(key);
        if (value instanceof List) {
            return (List<Map<String, Object>>) value;
        }
        return defaultValue;
    }

}
