<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.mind.core.chat.mapper.ChatMapper">

    <select id="pageByParamDTO" resultType="com.sinitek.mind.core.chat.entity.Chat">
        select
            *
        from chat
        where
            <choose>
                <when test="@org.apache.commons.lang3.StringUtils@isNotBlank(params.appId)">
                    app_id = #{params.appId}
                    and org_id = #{params.orgId}
                </when>
                <otherwise>
                    share_id = #{params.shareId}
                    and out_link_uid = #{params.outLinkUid}
                </otherwise>
            </choose>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(params.source)">
                and source = #{params.source}
            </if>
            <if test="params.startCreateTime != null">
                and createtimestamp <![CDATA[>=]]> #{params.startCreateTime}
            </if>
            <if test="params.endCreateTime != null">
                and createtimestamp <![CDATA[<=]]> #{params.endCreateTime}
            </if>
            <if test="params.startUpdateTime != null">
                and updatetimestamp <![CDATA[>=]]> #{params.startUpdateTime}
            </if>
            <if test="params.endUpdateTime != null">
                and updatetimestamp <![CDATA[<=]]> #{params.endUpdateTime}
            </if>
            <if test="@org.apache.commons.collections.CollectionUtils@isNotEmpty(page.orders)">
                order by top desc, updatetimestamp desc
            </if>
    </select>

    <select id="getChatLogs" resultType="com.sinitek.mind.core.chat.dto.ChatLogResponse">
        SELECT 
            c.id as _id,
            c.chat_id as chatId,
            c.title as title,
            c.custom_title as customTitle,
            c.source as source,
            c.source_name as sourceName,
            c.updatetimestamp as time,
            COALESCE(ci.message_count, 0) as messageCount,
            COALESCE(ci.good_feedback, 0) as userGoodFeedbackCount,
            COALESCE(ci.bad_feedback, 0) as userBadFeedbackCount,
            COALESCE(ci.custom_feedback, 0) as customFeedbacksCount,
            COALESCE(ci.admin_mark, 0) as markCount,
            c.out_link_uid as outLinkUid,
            c.share_id as shareId,
            c.org_id as tmbId
        FROM mind_chat c
        LEFT JOIN (
            SELECT 
                ci.chat_id,
                ci.app_id,
                COUNT(*) as message_count,
                SUM(CASE WHEN ci.user_good_feedback IS NOT NULL AND ci.user_good_feedback != '' THEN 1 ELSE 0 END) as good_feedback,
                SUM(CASE WHEN ci.user_bad_feedback IS NOT NULL AND ci.user_bad_feedback != '' THEN 1 ELSE 0 END) as bad_feedback,
                SUM(CASE WHEN ci.custom_feedbacks IS NOT NULL AND JSON_LENGTH(ci.custom_feedbacks) > 0 THEN 1 ELSE 0 END) as custom_feedback,
                SUM(CASE WHEN ci.admin_feedback IS NOT NULL AND JSON_EXTRACT(ci.admin_feedback, '$.feedback') = true THEN 1 ELSE 0 END) as admin_mark
            FROM mind_chat_item ci
            GROUP BY ci.chat_id, ci.app_id
        ) ci ON c.chat_id = ci.chat_id AND c.app_id = ci.app_id
        WHERE c.app_id = #{params.appId}
        AND c.updatetimestamp <![CDATA[>=]]> #{params.dateStart}
        AND c.updatetimestamp <![CDATA[<=]]> #{params.dateEnd}
        <if test="params.sources != null and params.sources.size() > 0">
            AND c.source IN
            <foreach collection="params.sources" item="source" open="(" separator="," close=")">
                #{source}
            </foreach>
        </if>
        <if test="params.logTitle != null and params.logTitle.trim() != ''">
            AND (
                c.title LIKE CONCAT('%', #{params.logTitle}, '%')
                OR c.custom_title LIKE CONCAT('%', #{params.logTitle}, '%')
            )
        </if>
        <if test="page.orders == null">
            ORDER BY c.updatetimestamp DESC
        </if>
    </select>

    <select id="getChatLogsForExport" resultType="com.sinitek.mind.core.chat.dto.ChatLogExportDTO">
        SELECT 
            c.id as id,
            c.chat_id as chatId,
            c.title as title,
            c.custom_title as customTitle,
            c.source as source,
            c.updatetimestamp as time,
            c.out_link_uid as outLinkUid,
            c.org_id as tmbId,
            COUNT(ci.id) as messageCount,
            CASE 
                WHEN ci.user_good_feedback IS NOT NULL AND ci.user_good_feedback != '' 
                THEN JSON_ARRAY(ci.user_good_feedback) 
                ELSE JSON_ARRAY() 
            END as userGoodFeedbackItems,
            CASE 
                WHEN ci.user_bad_feedback IS NOT NULL AND ci.user_bad_feedback != '' 
                THEN JSON_ARRAY(ci.user_bad_feedback) 
                ELSE JSON_ARRAY() 
            END as userBadFeedbackItems,
            CASE 
                WHEN ci.custom_feedbacks IS NOT NULL AND JSON_LENGTH(ci.custom_feedbacks) > 0 
                THEN ci.custom_feedbacks 
                ELSE JSON_ARRAY() 
            END as customFeedbackItems,
            CASE 
                WHEN ci.admin_feedback IS NOT NULL AND JSON_EXTRACT(ci.admin_feedback, '$.feedback') = true 
                THEN JSON_ARRAY(ci.admin_feedback) 
                ELSE JSON_ARRAY() 
            END as markItems,
            JSON_ARRAYAGG(
                JSON_OBJECT(
                    'id', ci.id,
                    'value', ci.value
                )
            ) as chatDetails
        FROM mind_chat c
        LEFT JOIN mind_chat_item ci ON c.chat_id = ci.chat_id AND c.app_id = ci.app_id
        WHERE c.app_id = #{params.appId}
        AND c.updatetimestamp <![CDATA[>=]]> #{params.dateStart}
        AND c.updatetimestamp <![CDATA[<=]]> #{params.dateEnd}
        <if test="params.sources != null and params.sources.size() > 0">
            AND c.source IN
            <foreach collection="params.sources" item="source" open="(" separator="," close=")">
                #{source}
            </foreach>
        </if>
        <if test="params.logTitle != null and params.logTitle.trim() != ''">
            AND (
                c.title LIKE CONCAT('%', #{params.logTitle}, '%')
                OR c.custom_title LIKE CONCAT('%', #{params.logTitle}, '%')
            )
        </if>
        GROUP BY c.id, c.chat_id, c.title, c.custom_title, c.source, c.updatetimestamp, c.out_link_uid, c.org_id
        ORDER BY c.updatetimestamp DESC
        LIMIT 50000
    </select>

    
</mapper>