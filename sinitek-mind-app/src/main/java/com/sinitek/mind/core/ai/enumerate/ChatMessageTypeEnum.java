package com.sinitek.mind.core.ai.enumerate;

import com.sinitek.mind.core.workflow.constant.WorkflowErrorCodeConstant;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Getter;

/**
 * 聊天消息类型枚举
 * 对应 TypeScript 中的 ChatMessageTypeEnum
 */
@Getter
public enum ChatMessageTypeEnum {
    TEXT("text"),
    IMAGE_URL("image_url");
    
    private final String value;
    
    ChatMessageTypeEnum(String value) {
        this.value = value;
    }

    public static ChatMessageTypeEnum fromValue(String value) {
        for (ChatMessageTypeEnum type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new BussinessException(WorkflowErrorCodeConstant.UNKNOWN_CHAT_MESSAGE_TYPE, value);
    }
}