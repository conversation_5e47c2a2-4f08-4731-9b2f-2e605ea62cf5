package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.VariableItemType;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class GetAppChatConfigReq {

    private AppChatConfigType chatConfig;

    private StoreNodeItemType systemConfigNode;

    private List<VariableItemType> storeVariables;

    private String storeWelcomeText;

    private boolean isPublicFetch;
}
