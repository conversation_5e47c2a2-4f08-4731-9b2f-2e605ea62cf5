package com.sinitek.mind.core.app.constant;

/**
 * 应用模块错误码常量类
 * 模块编码：30-02
 */
public final class AppErrorCodeConstant {

    // ==================== 应用基础错误 (30020001-30020099) ====================
    
    /**
     * 缺失必要参数
     */
    public static final String MISSING_REQUIRED_PARAMS = "30020001";
    
    /**
     * appId不存在
     */
    public static final String APP_ID_NOT_EXISTS = "30020002";
    
    /**
     * 应用不存在
     */
    public static final String APP_NOT_FOUND = "30020003";
    
    /**
     * 目标文件夹不存在
     */
    public static final String TARGET_FOLDER_NOT_EXISTS = "30020004";
    
    /**
     * 缺少参数
     */
    public static final String MISSING_PARAMS = "30020005";
    
    /**
     * 应用不存在
     */
    public static final String APP_NOT_EXIST = "30020006";
    
    /**
     * 数据集不存在
     */
    public static final String DATASET_NOT_FOUND = "30020007";
    
    /**
     * 目前只支持工作流的简单创建
     */
    public static final String ONLY_WORKFLOW_SIMPLE_CREATE = "30020008";
    
    // ==================== 应用验证错误 (30020101-30020199) ====================
    
    /**
     * 应用ID不能为空
     */
    public static final String APP_ID_CANNOT_BE_EMPTY = "30020101";
    
    /**
     * 应用名称不能为空
     */
    public static final String APP_NAME_CANNOT_BE_EMPTY = "30020102";
    
    /**
     * 应用类型不能为空
     */
    public static final String APP_TYPE_CANNOT_BE_EMPTY = "30020103";
    
    /**
     * 应用模块列表不能为空
     */
    public static final String APP_MODULES_CANNOT_BE_EMPTY = "30020104";
    
    /**
     * 应用版本不能为空
     */
    public static final String APP_VERSION_CANNOT_BE_EMPTY = "30020105";
    
    /**
     * 应用插件数据不能为空
     */
    public static final String APP_PLUGIN_DATA_CANNOT_BE_EMPTY = "30020106";
    
    // ==================== 应用权限错误 (30020201-30020299) ====================
    
    /**
     * 无权限访问应用
     */
    public static final String NO_PERMISSION_TO_ACCESS_APP = "30020201";
    
    /**
     * 无权限删除应用
     */
    public static final String NO_PERMISSION_TO_DELETE_APP = "30020202";
    
    /**
     * 无权限修改应用
     */
    public static final String NO_PERMISSION_TO_UPDATE_APP = "30020203";
    
    /**
     * 无权限创建应用
     */
    public static final String NO_PERMISSION_TO_CREATE_APP = "30020204";
    
    // ==================== 应用操作错误 (30020301-30020399) ====================
    
    /**
     * 应用创建失败
     */
    public static final String APP_CREATE_FAILED = "30020301";
    
    /**
     * 应用更新失败
     */
    public static final String APP_UPDATE_FAILED = "30020302";
    
    /**
     * 应用删除失败
     */
    public static final String APP_DELETE_FAILED = "30020303";
    
    /**
     * 应用复制失败
     */
    public static final String APP_COPY_FAILED = "30020304";
    
    /**
     * 应用移动失败
     */
    public static final String APP_MOVE_FAILED = "30020305";
    
    // ==================== 应用版本错误 (30020401-30020499) ====================
    
    /**
     * 应用版本不存在
     */
    public static final String APP_VERSION_NOT_FOUND = "30020401";
    
    /**
     * 应用版本创建失败
     */
    public static final String APP_VERSION_CREATE_FAILED = "30020402";
    
    /**
     * 应用版本更新失败
     */
    public static final String APP_VERSION_UPDATE_FAILED = "30020403";
    
    /**
     * 应用版本删除失败
     */
    public static final String APP_VERSION_DELETE_FAILED = "30020404";
    
    // ==================== 应用插件错误 (30020501-30020599) ====================
    
    /**
     * 应用插件不存在
     */
    public static final String APP_PLUGIN_NOT_FOUND = "30020501";
    
    /**
     * 应用插件创建失败
     */
    public static final String APP_PLUGIN_CREATE_FAILED = "30020502";
    
    /**
     * 应用插件更新失败
     */
    public static final String APP_PLUGIN_UPDATE_FAILED = "30020503";
    
    /**
     * 应用插件删除失败
     */
    public static final String APP_PLUGIN_DELETE_FAILED = "30020504";
    
    // ==================== 应用文件夹错误 (30020601-30020699) ====================
    
    /**
     * 应用文件夹不存在
     */
    public static final String APP_FOLDER_NOT_FOUND = "30020601";
    
    /**
     * 应用文件夹创建失败
     */
    public static final String APP_FOLDER_CREATE_FAILED = "30020602";
    
    /**
     * 应用文件夹更新失败
     */
    public static final String APP_FOLDER_UPDATE_FAILED = "30020603";
    
    /**
     * 应用文件夹删除失败
     */
    public static final String APP_FOLDER_DELETE_FAILED = "30020604";
    
    // ==================== HTTP插件错误 (30020701-30020799) ====================
    
    /**
     * HTTP插件URL不能为空
     */
    public static final String HTTP_PLUGIN_URL_CANNOT_BE_EMPTY = "30020701";
    
    /**
     * HTTP插件方法不能为空
     */
    public static final String HTTP_PLUGIN_METHOD_CANNOT_BE_EMPTY = "30020702";
    
    /**
     * HTTP插件请求头不能为空
     */
    public static final String HTTP_PLUGIN_HEADERS_CANNOT_BE_EMPTY = "30020703";
    
    /**
     * HTTP插件请求体不能为空
     */
    public static final String HTTP_PLUGIN_BODY_CANNOT_BE_EMPTY = "30020704";
    
    /**
     * HTTP插件创建失败
     */
    public static final String HTTP_PLUGIN_CREATE_FAILED = "30020705";
    
    /**
     * HTTP插件更新失败
     */
    public static final String HTTP_PLUGIN_UPDATE_FAILED = "30020706";
    
    /**
     * HTTP插件删除失败
     */
    public static final String HTTP_PLUGIN_DELETE_FAILED = "30020707";
    
    // ==================== MCP工具错误 (30020801-30020899) ====================
    
    /**
     * MCP工具名称不能为空
     */
    public static final String MCP_TOOLS_NAME_CANNOT_BE_EMPTY = "30020801";
    
    /**
     * MCP工具描述不能为空
     */
    public static final String MCP_TOOLS_DESCRIPTION_CANNOT_BE_EMPTY = "30020802";
    
    /**
     * MCP工具配置不能为空
     */
    public static final String MCP_TOOLS_CONFIG_CANNOT_BE_EMPTY = "30020803";
    
    /**
     * MCP工具创建失败
     */
    public static final String MCP_TOOLS_CREATE_FAILED = "30020804";
    
    /**
     * MCP工具更新失败
     */
    public static final String MCP_TOOLS_UPDATE_FAILED = "30020805";
    
    /**
     * MCP工具删除失败
     */
    public static final String MCP_TOOLS_DELETE_FAILED = "30020806";
    
    /**
     * MCP连接失败
     */
    public static final String MCP_CONNECTION_FAILED = "30020807";
    
    /**
     * MCP URL格式错误
     */
    public static final String MCP_URL_FORMAT_ERROR = "30020808";
    
    // ==================== 应用模板错误 (30020901-30020999) ====================
    
    /**
     * 模板ID不能为空
     */
    public static final String TEMPLATE_ID_CANNOT_BE_EMPTY = "30020901";
    
    /**
     * 模板不存在
     */
    public static final String TEMPLATE_NOT_FOUND = "30020902";
    
    /**
     * 获取模板列表失败
     */
    public static final String GET_TEMPLATE_LIST_FAILED = "30020903";
    
    /**
     * 获取模板详情失败
     */
    public static final String GET_TEMPLATE_DETAIL_FAILED = "30020904";
    
    // ==================== 枚举类错误 (30021001-30021099) ====================
    
    /**
     * 未知的节点输入类型
     */
    public static final String UNKNOWN_FLOW_NODE_INPUT_TYPE = "30021001";
    
    /**
     * 未知的节点输出类型
     */
    public static final String UNKNOWN_FLOW_NODE_OUTPUT_TYPE = "30021002";
    
    /**
     * 未知的节点类型
     */
    public static final String UNKNOWN_FLOW_NODE_TYPE = "30021003";
    
    /**
     * 未知的插件来源类型
     */
    public static final String UNKNOWN_PLUGIN_SOURCE_TYPE = "30021004";
    
    /**
     * 未知的大模型模型类型
     */
    public static final String UNKNOWN_LLM_MODEL_TYPE = "30021005";
    
    /**
     * 未知的工作流IO值类型
     */
    public static final String UNKNOWN_WORKFLOW_IO_VALUE_TYPE = "30021006";
    
    /**
     * 未知的变量输入类型
     */
    public static final String UNKNOWN_VARIABLE_INPUT_TYPE = "30021007";
    
    /**
     * 无效的cron表达式
     */
    public static final String INVALID_CRON_EXPRESSION = "30021008";
    
    /**
     * 未知的HeaderSecretType
     */
    public static final String UNKNOWN_HEADER_SECRET_TYPE = "30021009";
    
    /**
     * 未知的AppTypeEnum
     */
    public static final String UNKNOWN_APP_TYPE_ENUM = "30021010";
    
    // ==================== 工具类错误 (30021101-30021199) ====================
    
    /**
     * 组合ID不能为空
     */
    public static final String COMBINE_ID_CANNOT_BE_EMPTY = "30021101";
    
    /**
     * 插件来源和插件ID不能为空
     */
    public static final String PLUGIN_SOURCE_AND_ID_CANNOT_BE_EMPTY = "30021102";
    
    /**
     * 无效的插件来源
     */
    public static final String INVALID_PLUGIN_SOURCE = "30021103";
    
    /**
     * 密钥加密失败
     */
    public static final String SECRET_ENCRYPTION_FAILED = "30021104";
    
    /**
     * 密钥解密失败
     */
    public static final String SECRET_DECRYPTION_FAILED = "30021105";
    
    /**
     * 生成密钥字节数组失败
     */
    public static final String GENERATE_SECRET_KEY_BYTES_FAILED = "30021106";
    
    /**
     * 生成随机密钥失败
     */
    public static final String GENERATE_RANDOM_SECRET_FAILED = "30021107";
    
    /**
     * 计算哈希值失败
     */
    public static final String CALCULATE_HASH_FAILED = "30021108";
    
    /**
     * 生成安全随机字符串失败
     */
    public static final String GENERATE_SECURE_RANDOM_STRING_FAILED = "30021109";
    
    private AppErrorCodeConstant() {
        // 工具类，不允许实例化
    }
}