package com.sinitek.mind.core.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "清空指定聊天历史记录")
public class ClearHistortiesRequest {

    @Schema(description = "appId")
    private String appId;

    @Schema(description = "shareId-分享链接，免登录窗口")
    private String shareId;

    @Schema(description = "outLinkUid-免登录窗口中的用户id")
    private String outLinkUid;

    @Schema(description = "teamId-团队id")
    private String teamId;

    @Schema(description = "teamToken-团队令牌")
    private String teamToken;

}
