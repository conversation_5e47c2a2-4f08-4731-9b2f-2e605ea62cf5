package com.sinitek.mind.core.chat.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * Chat模块错误码常量类
 * 
 * 错误码规则：3003XXXX
 * - 30: Mind项目固定前缀
 * - 03: Chat模块编码
 * - XXXX: 具体错误编码，从0001开始递增
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public final class ChatErrorCodeConstant {


    // ==================== 参数验证错误 (300301XX) ====================

    /**
     * 参数不能为空
     */
    public static final String PARAM_EMPTY = "30030001";

    /**
     * 应用ID不能为空
     */
    public static final String APP_ID_EMPTY = "30030002";

    /**
     * 聊天ID不能为空
     */
    public static final String CHAT_ID_EMPTY = "30030003";

    /**
     * 消息列表不能为空
     */
    public static final String MESSAGES_EMPTY = "30030004";

    /**
     * API密钥不能为空
     */
    public static final String API_KEY_EMPTY = "30030005";

    /**
     * 用户问题不能为空
     */
    public static final String USER_QUESTION_EMPTY = "30030006";

    /**
     * 节点列表不能为空
     */
    public static final String NODES_EMPTY = "30030007";

    /**
     * 边列表不能为空
     */
    public static final String EDGES_EMPTY = "30030008";

    /**
     * 消息格式错误，必须为数组
     */
    public static final String MESSAGES_NOT_ARRAY = "30030009";

    // ==================== 权限验证错误 (300302XX) ====================

    /**
     * 无权限访问
     */
    public static final String NO_PERMISSION = "30030010";

    /**
     * 应用不存在或无权限访问
     */
    public static final String APP_NOT_EXISTS_OR_NO_PERMISSION = "30030011";

    /**
     * 无权限访问此聊天
     */
    public static final String CHAT_NO_PERMISSION = "30030012";

    /**
     * 权限校验失败
     */
    public static final String AUTH_FAILED = "30030013";

    /**
     * 应用ID为空
     */
    public static final String AUTH_APP_ID_EMPTY = "30030014";

    /**
     * 用户ID不匹配
     */
    public static final String UID_MISMATCH = "30030015";

    // ==================== 业务逻辑错误 (300303XX) ====================

    /**
     * 应用不存在
     */
    public static final String APP_NOT_EXISTS = "30030016";

    /**
     * 分享链接不存在或已过期
     */
    public static final String SHARE_LINK_INVALID = "30030017";

    /**
     * 工作流版本过低，请重新发布一次
     */
    public static final String WORKFLOW_VERSION_LOW = "30030018";

    /**
     * 您还没有应用
     */
    public static final String NO_APP = "30030019";

    /**
     * 用户问题为空
     */
    public static final String BUSINESS_USER_QUESTION_EMPTY = "30030020";

    // ==================== 系统错误 (300304XX) ====================

    /**
     * 初始化失败
     */
    public static final String INIT_FAILED = "30030021";

    /**
     * 工作流执行失败
     */
    public static final String WORKFLOW_FAILED = "30030022";

    /**
     * 导出失败
     */
    public static final String EXPORT_FAILED = "30030023";

    /**
     * 加载消息失败
     */
    public static final String LOAD_MESSAGE_FAILED = "30030024";

    /**
     * 获取历史记录失败
     */
    public static final String GET_HISTORY_FAILED = "30030025";

    /**
     * 无法使用teamId和teamToken，获取聊天记录
     */
    public static final String TEAM_TOKEN_INVALID = "30030026";

    /**
     * 参数错误
     */
    public static final String PARAM_ERROR = "30030027";

    // ==================== 工作流错误 (300305XX) ====================

    /**
     * 工作流执行失败
     */
    public static final String WORKFLOW_EXECUTION_FAILED = "30030028";

    // ==================== 角色错误 (300306XX) ====================

    /**
     * 角色未找到
     */
    public static final String ROLE_NOT_FOUND = "30030029";

    // ==================== 聊天处理错误 (300307XX) ====================

    /**
     * 处理消息时发生错误
     */
    public static final String MESSAGE_PROCESSING_FAILED = "30030030";

    /**
     * 加载消息时发生错误
     */
    public static final String MESSAGE_LOADING_FAILED = "30030031";

    /**
     * 获取图片base64失败
     */
    public static final String GET_IMAGE_BASE64_FAILED = "30030032";

    /**
     * 更新交互式聊天失败
     */
    public static final String UPDATE_INTERACTIVE_CHAT_FAILED = "30030033";

    /**
     * 复制聊天历史属性失败
     */
    public static final String COPY_CHAT_HISTORY_FAILED = "30030034";

    /**
     * 保存聊天失败
     */
    public static final String SAVE_CHAT_FAILED = "30030035";

    /**
     * 推送聊天日志失败
     */
    public static final String PUSH_CHAT_LOG_FAILED = "30030036";

    /**
     * 创建聊天使用统计失败
     */
    public static final String CREATE_CHAT_STATS_FAILED = "30030037";

    /**
     * 添加自定义反馈失败
     */
    public static final String ADD_CUSTOM_FEEDBACK_FAILED = "30030038";

    /**
     * 获取聊天日志失败
     */
    public static final String GET_CHAT_LOG_FAILED = "30030039";

    /**
     * 处理导出记录时出错
     */
    public static final String PROCESS_EXPORT_RECORD_FAILED = "30030040";

    /**
     * 导出聊天日志失败
     */
    public static final String EXPORT_CHAT_LOG_FAILED = "30030041";

    /**
     * 格式化JSON字符串失败
     */
    public static final String FORMAT_JSON_FAILED = "30030042";

    /**
     * 聊天测试执行异常
     */
    public static final String CHAT_TEST_EXCEPTION = "30030043";

    /**
     * 分页获取聊天记录失败
     */
    public static final String GET_CHAT_PAGE_FAILED = "30030044";

    /**
     * 统计聊天输入引导总数失败
     */
    public static final String COUNT_CHAT_INPUT_GUIDE_FAILED = "30030045";

}