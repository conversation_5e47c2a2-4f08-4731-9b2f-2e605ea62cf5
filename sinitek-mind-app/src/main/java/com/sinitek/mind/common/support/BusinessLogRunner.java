package com.sinitek.mind.common.support;

import com.sinitek.mind.common.constant.MindConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BusinessLogRunner implements ApplicationRunner {

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.debug("新增智搭 - 工作流相关的 业务日志分类: {}", MindConstant.APP.getModuleName());
    }
}
