package com.sinitek.mind.common.util;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.security.spec.KeySpec;

public class SecretUtil {

    private static final String AES256_SECRET_KEY = "fastgptkey"; // 需要配置实际的密钥
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 16;
    private static final int GCM_TAG_LENGTH = 16;

    public static String encryptSecret(String text) throws Exception {
        // 生成随机IV
        byte[] iv = new byte[GCM_IV_LENGTH];
        SecureRandom.getInstanceStrong().nextBytes(iv);

        // 使用PBKDF2生成密钥（等同于Node.js的scryptSync）
        SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
        KeySpec spec = new PBEKeySpec(AES256_SECRET_KEY.toCharArray(), "salt".getBytes(), 16384, 256);
        byte[] keyBytes = factory.generateSecret(spec).getEncoded();
        SecretKeySpec key = new SecretKeySpec(keyBytes, ALGORITHM);

        // 创建加密器
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.ENCRYPT_MODE, key, gcmParameterSpec);

        // 加密文本
        byte[] encryptedData = cipher.doFinal(text.getBytes("UTF-8"));

        // 分离加密数据和认证标签
        byte[] encrypted = new byte[encryptedData.length - GCM_TAG_LENGTH];
        byte[] authTag = new byte[GCM_TAG_LENGTH];
        System.arraycopy(encryptedData, 0, encrypted, 0, encrypted.length);
        System.arraycopy(encryptedData, encrypted.length, authTag, 0, authTag.length);

        // 转换为十六进制字符串并返回格式化结果
        return bytesToHex(iv) + ":" + bytesToHex(encrypted) + ":" + bytesToHex(authTag);
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
}
