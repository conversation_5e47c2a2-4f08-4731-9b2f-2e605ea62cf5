package com.sinitek.mind.common.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 通用模块错误码常量类
 * 
 * 错误码规则：30-00-YYYY
 * 30 - 固定前缀
 * 00 - 通用模块
 * YYYY - 具体错误码
 *
 * <AUTHOR>
 * date 2025-08-14
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public final class CommonErrorCodeConstant {

    // ==================== 通用模块错误码 ====================
    
    /**
     * 系统未设置FILE_TOKEN_KEY
     */
    public static final String FILE_TOKEN_KEY_NOT_SET = "30000001";
    
    /**
     * 未提供文件令牌
     */
    public static final String FILE_TOKEN_NOT_PROVIDED = "30000002";
    
    /**
     * 无效的文件令牌
     */
    public static final String FILE_TOKEN_INVALID = "30000003";
    
    /**
     * 无效或过期的文件令牌
     */
    public static final String FILE_TOKEN_INVALID_OR_EXPIRED = "30000004";
    
    /**
     * 图片上传失败
     */
    public static final String IMAGE_UPLOAD_FAILED = "30000005";
    
    /**
     * 文件ID格式错误
     */
    public static final String FILE_ID_FORMAT_ERROR = "30000006";
    
    /**
     * 文件下载失败
     */
    public static final String FILE_DOWNLOAD_FAILED = "30000007";
    
    /**
     * 文件上传失败
     */
    public static final String FILE_UPLOAD_FAILED = "30000008";
    
    /**
     * 文件大小超过限制
     */
    public static final String FILE_SIZE_EXCEEDED = "30000009";
    
    /**
     * 文件大小无效
     */
    public static final String FILE_SIZE_INVALID = "30000010";
    
    /**
     * base64数据不能为空
     */
    public static final String BASE64_DATA_EMPTY = "30000011";
    
    /**
     * base64转换为文件失败
     */
    public static final String BASE64_CONVERT_FAILED = "30000012";
}