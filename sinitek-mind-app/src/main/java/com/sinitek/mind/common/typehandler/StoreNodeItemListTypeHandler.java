package com.sinitek.mind.common.typehandler;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.io.IOException;
import java.util.List;

import static com.sinitek.mind.core.chat.constant.ChatErrorCodeConstant.FORMAT_JSON_FAILED;

@Slf4j
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(List.class)
public class StoreNodeItemListTypeHandler extends AbstractJsonTypeHandler<List<StoreNodeItemType>> {

    private static ObjectMapper OBJECT_MAPPER;

    private static final TypeReference<List<StoreNodeItemType>> TYPE_REF =
            new TypeReference<List<StoreNodeItemType>>() {};


    @Override
    protected List<StoreNodeItemType> parse(String json) {
        try {
            return getObjectMapper().readValue(json, TYPE_REF);
        } catch (IOException e) {
            log.error("StoreNodeItemListTypeHandler， json格式化失败", e);
            throw new BussinessException(FORMAT_JSON_FAILED);
        }
    }

    @Override
    protected String toJson(List<StoreNodeItemType> obj) {
        try {
            return getObjectMapper().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("StoreNodeItemListTypeHandler， json格式化失败", e);
            throw new BussinessException(FORMAT_JSON_FAILED);
        }
    }

    public static ObjectMapper getObjectMapper() {
        if (null == OBJECT_MAPPER) {
            OBJECT_MAPPER = SpringFactory.getBean(ObjectMapper.class);
        }
        return OBJECT_MAPPER;
    }
}