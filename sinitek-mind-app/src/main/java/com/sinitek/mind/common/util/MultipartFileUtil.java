package com.sinitek.mind.common.util;

import com.sinitek.sirm.common.utils.IOUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * MultipartFile工具类
 *
 * <AUTHOR>
 * date 2025-08-22
 * 描述：提供MultipartFile相关的工具方法，支持文件转换等操作
 */
@Slf4j
public class MultipartFileUtil {

    /**
     * 将MultipartFile转换为File
     * 创建临时文件并将MultipartFile的内容复制到该临时文件中
     *
     * @param multipartFile 要转换的MultipartFile
     * @return 转换后的File对象
     * @throws IOException 文件操作异常
     */
    public static File convertToFile(MultipartFile multipartFile) throws IOException {
        if (multipartFile == null || multipartFile.isEmpty()) {
            throw new IllegalArgumentException("MultipartFile不能为空");
        }

        // 获取文件扩展名
        String originalFilename = multipartFile.getOriginalFilename();
        String extension = getFileExtension(originalFilename);

        // 创建临时文件
        File tempFile = IOUtil.createTempFile(extension);
        log.debug("创建临时文件: {}", tempFile.getAbsolutePath());

        // 将MultipartFile的内容复制到临时文件
        multipartFile.transferTo(tempFile);

        return tempFile;
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 文件扩展名（不包含点号）
     */
    private static String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }

        return filename.substring(lastDotIndex + 1);
    }
}
