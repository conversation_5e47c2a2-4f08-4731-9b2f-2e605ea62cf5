package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 文件预览分块请求DTO
 *
 * <AUTHOR>
 * date 2025/07/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件预览分块请求DTO")
public class PreviewChunksRequestDTO extends BaseDatasetCollectionParamsDTO {

    @NotBlank(message = "{" + DatasetErrorCodeConstant.DATASET_ID_EMPTY + "}")
    @Schema(description = "数据集ID")
    private String datasetId;

    @NotBlank(message = "{" + DatasetErrorCodeConstant.FILE_TYPE_EMPTY + "}")
    @Schema(description = "文件类型")
    private String type;

    @NotBlank(message = "{" + DatasetErrorCodeConstant.FILE_SOURCE_ID_EMPTY + "}")
    @Schema(description = "文件源ID")
    private String sourceId;

    @Schema(description = "是否自定义PDF解析")
    private boolean customPdfParse = false;

    @Schema(description = "网页选择器")
    private String webSelector;

    @Schema(description = "选择器")
    private String selector;

    @NotNull(message = "{" + DatasetErrorCodeConstant.OVERLAP_RATIO_EMPTY + "}")
    @Schema(description = "重叠比例")
    private BigDecimal overlapRatio;
}