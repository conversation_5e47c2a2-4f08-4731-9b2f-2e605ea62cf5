package com.sinitek.mind.dataset.core.reader;

import dev.langchain4j.data.document.Document;

import java.io.File;
import java.util.Set;

/**
 * 向量文件解析
 *
 * <AUTHOR>
 * date 2025/07/22
 */
public interface IVectorFileReader {

    /**
     * 支持的文件类型
     * @return
     */
    Set<String> supportFileType();

    /**
     * 解析文件内容为 List<Document>
     * @param sourceFile
     * @return
     */
    Document readFile(File sourceFile);
}
