package com.sinitek.mind.dataset.controller;

import com.sinitek.mind.common.dto.FileTokenQuery;
import com.sinitek.mind.common.dto.IdDTO;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.common.util.MindFileTokenUtil;
import com.sinitek.mind.dataset.constant.DatasetConstant;
import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import com.sinitek.mind.dataset.enumerate.DatasetTrainingModeEnum;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.entity.DatasetCollection;
import com.sinitek.mind.dataset.service.IDatasetCollectionService;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import com.sinitek.mind.support.common.dto.BaseFileDTO;
import com.sinitek.mind.support.common.service.IFileService;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

/**
 * dataset_collections 控制器
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库集合（文件夹/文件）表Controller
 */
@RestController
@RequestMapping("/mind/api/core/dataset/collection")
@Tag(name = "知识库集合管理")
@Slf4j
public class DatasetCollectionController {

    @Autowired
    private IDatasetTrainingService datasetTrainingService;

    @Autowired
    private IDatasetCollectionService service;

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private IAuthService authService;

    @Autowired
    private IFileService fileService;

    @Autowired
    private MindFileTokenUtil mindFileTokenUtil;

    @GetMapping("/paths")
    @Operation(summary = "路径列表")
    public ApiResponse<List<DatasetPathDTO>> paths(@RequestParam String parentId) {
        List<DatasetPathDTO> pathList = service.getPaths(parentId);
        return ApiResponse.success(pathList);
    }

    @PostMapping("/create")
    @Operation(summary = "创建")
    public ApiResponse<String> create(@RequestBody DatasetCollectionDTO dto) {
        AuthDTO authDTO = authService.authCert();
        dto.setTeamId(authDTO.getTeamId());
        dto.setTmbId(authDTO.getTmbId());

        String id = service.save(dto);
        return ApiResponse.success(id);
    }

    @PostMapping("/update")
    @Operation(summary = "更新名称")
    public ApiResponse<Void> updateName(@RequestBody @Validated DatasetCollectionUpdateDTO dto) {
        service.updateName(dto);
        return ApiResponse.success();
    }

    @PostMapping("/move")
    @Operation(summary = "移动集合")
    public ApiResponse<Void> move(@RequestBody @Validated DatasetCollectionMoveDTO dto) {
        service.move(dto);
        return ApiResponse.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除")
    public ApiResponse<Void> delete(@RequestBody IdDTO dto) {
        service.delete(dto.getId());
        return ApiResponse.success();
    }

    /**
     * 上传图片并识别内容创建知识库集合
     */
    @PostMapping("/create/images")
    @Operation(summary = "上传图片并识别内容创建知识库集合")
    public ApiResponse<CreateCollectionResponseDTO> createByImage(
            @RequestPart("file") MultipartFile file,
            @RequestPart("data") String data) {
        CreateDatasetCollectionImageDTO dataDTO = JsonUtil.toJavaObject(data,  CreateDatasetCollectionImageDTO.class);
        CreateCollectionResponseDTO response = service.createByImage(file, dataDTO);
        return ApiResponse.success(response);
    }

    @GetMapping("/detail")
    @Operation(summary = "详情")
    public ApiResponse<DatasetCollectionDetailDTO> detail(@Parameter(description = "主键ID") @RequestParam String id) {
        DatasetCollectionDetailDTO result = service.detail(id);
        return ApiResponse.success(result);
    }

    @PostMapping("/listV2")
    @Operation(summary = "知识库集合列表V2-分页")
    public ApiResponse<PageResult<DatasetCollectionListV2ItemDTO>> listV2(@RequestBody DatasetCollectionListV2PageParamDTO param) {
        PageResult<DatasetCollectionDTO> pageResult = service.pageListV2(param);
        PageResult<DatasetCollectionListV2ItemDTO> vo = new PageResult<>();
        vo.setTotal(pageResult.getTotal());
        vo.setList(format(pageResult.getList()));
        return ApiResponse.success(vo);
    }

    @PostMapping("/create/fileId")
    @Operation(summary = "基于文件ID创建知识库集合")
    public ApiResponse<CreateCollectionResponseDTO> createByFileId(@RequestBody FileIdCreateDatasetCollectionParamsDTO params) throws Exception {
        CreateCollectionResponseDTO response = service.createByFileId(params);
        return ApiResponse.success(response);
    }

    @PostMapping("/create/reTrainingCollection")
    @Operation(summary = "重新训练知识库集合")
    public ApiResponse<CreateCollectionResponseDTO> reTrainingCollection(@RequestBody FileIdCreateDatasetCollectionParamsDTO params) throws Exception {
        AuthDTO authDTO = authService.authCert();
        // 创建新的DTO对象并复制属性
        FileIdCreateDatasetCollectionParamsDTO newParams = new FileIdCreateDatasetCollectionParamsDTO();
        BeanUtils.copyProperties(params, newParams);
        // 设置团队ID和成员ID
        newParams.setTeamId(authDTO.getTeamId());
        newParams.setTmbId(authDTO.getTmbId());

        CreateCollectionResponseDTO response = service.reTrainingCollection(newParams);
        return ApiResponse.success(response);
    }

    @PostMapping("/create/text")
    @Operation(summary = "基于文本内容创建知识库集合")
    public ApiResponse<CreateCollectionResponseDTO> createByText(@Validated @RequestBody TextCreateDatasetCollectionParamsDTO params) throws Exception {
        CreateCollectionResponseDTO response = service.createByText(params);
        return ApiResponse.success(response);
    }

    @PostMapping("/read")
    @Operation(summary = "读取集合源")
    public ApiResponse<ReadCollectionSourceResponse> read(@RequestBody ReadCollectionSourceBody body) throws Exception {
        String collectionId = body.getCollectionId();
        if (collectionId == null || collectionId.isEmpty()) {
            throw new BussinessException(DatasetErrorCodeConstant.COLLECTION_ID_EMPTY);
        }

        AuthDTO auth = authService.authCert();

        // 简单权限检查，假设读权限
        Optional<DatasetCollection> optional = service.getById(collectionId);
        if (!optional.isPresent()) {
            throw new BussinessException(DatasetErrorCodeConstant.COLLECTION_NOT_EXIST);
        }
        DatasetCollection collection = optional.get();

        // 检查权限 (简化版)
        if (!collection.getTeamId().equals(auth.getTeamId())) {
            throw new BussinessException(DatasetErrorCodeConstant.NO_COLLECTION_ACCESS_PERMISSION);
        }

        ReadCollectionSourceResponse response = new ReadCollectionSourceResponse();
        response.setType("url");
        String sourceUrl = "";

        if ("file".equals(collection.getType()) && collection.getFileId() != null) {
            BaseFileDTO baseFileDTO = fileService.getBaseFileById(DatasetConstant.DATASET_BUCKET_NAME, collection.getFileId());
            if (ObjectUtils.isNotEmpty(baseFileDTO)) {
                AuthDTO authDTO = authService.authCert();
                FileTokenQuery fileTokenQuery = new FileTokenQuery();
                fileTokenQuery.setTeamId(authDTO.getTeamId());
                fileTokenQuery.setUid(authDTO.getTmbId());
                fileTokenQuery.setBucketName(DatasetConstant.DATASET_BUCKET_NAME);
                fileTokenQuery.setFileId(collection.getFileId());

                String fileToken = mindFileTokenUtil.createFileToken(fileTokenQuery);
                sourceUrl = "/mind/api/common/file/read?token=" + fileToken;
            }
        } else if ("link".equals(collection.getType()) && collection.getMetadata() != null) {
            // 假设 rawLink 在 metadata 中
            sourceUrl = (String) collection.getMetadata().get("rawLink");
        } // 添加其他类型处理...

        response.setValue(sourceUrl);
        return ApiResponse.success(response);
    }

    @GetMapping("/trainingDetail")
    @Operation(summary = "获取集合训练详情")
    public ApiResponse<TrainingDetailDTO> trainingDetail(@Parameter(description = "集合ID") @RequestParam String collectionId) {
        if (StringUtils.isBlank(collectionId)) {
            throw new BussinessException(DatasetErrorCodeConstant.COLLECTION_ID_EMPTY);
        }

        AuthDTO auth = authService.authCert();

        // 权限检查
        Optional<DatasetCollection> optional = service.getById(collectionId);
        if (optional.isEmpty()) {
            throw new BussinessException(DatasetErrorCodeConstant.COLLECTION_NOT_EXIST);
        }
        DatasetCollection collection = optional.get();
        if (!collection.getTeamId().equals(auth.getTeamId())) {
            throw new BussinessException(DatasetErrorCodeConstant.NO_COLLECTION_ACCESS_PERMISSION);
        }

        // 获取集合详情
        DatasetCollectionDetailDTO detail = service.detail(collectionId);

        // 构建训练详情响应
        TrainingDetailDTO result = new TrainingDetailDTO();
        result.setTrainingType(detail.getTrainingType());

        // 高级训练设置
        TrainingDetailDTO.AdvancedTraining advancedTraining = new TrainingDetailDTO.AdvancedTraining();
        advancedTraining.setCustomPdfParse(false);
        advancedTraining.setImageIndex(false);
        advancedTraining.setAutoIndexes(false);
        result.setAdvancedTraining(advancedTraining);

        // 队列计数
        TrainingDetailDTO.Counts queuedCounts = new TrainingDetailDTO.Counts();
        queuedCounts.setParse(0);
        queuedCounts.setQa(0);
        queuedCounts.setChunk(0);
        queuedCounts.setImage(0);
        queuedCounts.setAuto(0);
        queuedCounts.setImageParse(0);
        result.setQueuedCounts(queuedCounts);

        // 训练计数 
        TrainingDetailDTO.Counts trainingCounts = new TrainingDetailDTO.Counts();
        trainingCounts.setParse(datasetTrainingService.countTrainingByCollectionIdAndMode(collectionId, DatasetTrainingModeEnum.PARSE.getValue()));
        trainingCounts.setQa(datasetTrainingService.countTrainingByCollectionIdAndMode(collectionId, DatasetTrainingModeEnum.QA.getValue()));
        trainingCounts.setChunk(datasetTrainingService.countTrainingByCollectionIdAndMode(collectionId, DatasetTrainingModeEnum.CHUNK.getValue()));
        trainingCounts.setImage(datasetTrainingService.countTrainingByCollectionIdAndMode(collectionId, DatasetTrainingModeEnum.IMAGE.getValue()));
        trainingCounts.setAuto(datasetTrainingService.countTrainingByCollectionIdAndMode(collectionId, DatasetTrainingModeEnum.AUTO.getValue()));
        trainingCounts.setImageParse(datasetTrainingService.countTrainingByCollectionIdAndMode(collectionId, DatasetTrainingModeEnum.IMAGE_PARSE.getValue()));
        result.setTrainingCounts(trainingCounts);

        // 错误计数
        TrainingErrorQueryDTO trainingErrorQueryDTO = new TrainingErrorQueryDTO();
        trainingErrorQueryDTO.setCollectionId(collectionId);
        int errorCount = datasetTrainingService.countFailedTrainings(trainingErrorQueryDTO);

        TrainingDetailDTO.Counts errorCounts = new TrainingDetailDTO.Counts();
        errorCounts.setParse(errorCount);
        errorCounts.setQa(0);
        errorCounts.setChunk(0);
        errorCounts.setImage(0);
        errorCounts.setAuto(0);
        errorCounts.setImageParse(0);
        result.setErrorCounts(errorCounts);

        // 已训练数量
        result.setTrainedCount(0);

        return ApiResponse.success(result);
    }

    /**
     * DTO转DTO格式化方法
     */
    private List<DatasetCollectionListV2ItemDTO> format(List<DatasetCollectionDTO> dtoList) {
        List<DatasetCollectionListV2ItemDTO> itemList = new java.util.ArrayList<>();
        if (CollectionUtils.isNotEmpty(dtoList)) {
            for (DatasetCollectionDTO dto : dtoList) {
                DatasetCollectionListV2ItemDTO item = new DatasetCollectionListV2ItemDTO();
                BeanUtils.copyProperties(dto, item);
                item.set_id(dto.getId());

                // 权限组装
                DatasetCollectionListV2PermissionDTO permission = new DatasetCollectionListV2PermissionDTO();
                permission.setIsOwner(true);
                permission.setHasManagePer(true);
                permission.setHasWritePer(true);
                permission.setHasReadPer(true);
                permission.setValue(com.sinitek.mind.support.permission.constant.PermissionConstant.OWNER_PERMISSION_VAL);
                permission.set_permissionList(com.sinitek.mind.support.permission.constant.PermissionConstant.PERMISSION_LIST);
                item.setPermission(permission);
                itemList.add(item);
            }
        }
        return itemList;
    }
}