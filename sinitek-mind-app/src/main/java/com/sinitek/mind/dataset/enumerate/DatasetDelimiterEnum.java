package com.sinitek.mind.dataset.enumerate;

/**
 * 知识库分块分隔符类型枚举
 *
 * <AUTHOR>
 * date 2025-08-13
 */
public enum DatasetDelimiterEnum {
    
    /**
     * 不设置
     */
    NONE("none"),
    
    /**
     * 1个换行符
     */
    NEWLINE_1("\n"),
    
    /**
     * 2个换行符
     */
    NEWLINE_2("\n\n"),
    
    /**
     * 句号
     */
    PERIOD("."),
    
    /**
     * 感叹号
     */
    EXCLAMATION_MARK("!"),
    
    /**
     * 问号
     */
    QUESTION_MARK("?"),
    
    /**
     * 分号
     */
    SEMICOLON(";"),
    
    /**
     * 等号分隔符
     */
    EQUAL_SIGNS("======"),
    
    /**
     * 自定义
     */
    CUSTOM("custom");
    
    private final String value;
    
    DatasetDelimiterEnum(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
}