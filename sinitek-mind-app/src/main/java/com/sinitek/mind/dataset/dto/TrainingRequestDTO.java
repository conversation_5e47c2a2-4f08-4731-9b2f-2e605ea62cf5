package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "训练数据请求基础参数DTO")
public class TrainingRequestDTO {

    @NotBlank(message = "{" + DatasetErrorCodeConstant.DATASET_ID_FIELD_EMPTY + "}")
    @Schema(description = "数据集ID")
    private String datasetId;

    @NotBlank(message = "{" + DatasetErrorCodeConstant.COLLECTION_ID_FIELD_EMPTY + "}")
    @Schema(description = "集合ID")
    private String collectionId;

    @NotBlank(message = "{" + DatasetErrorCodeConstant.DATA_ID_FIELD_EMPTY + "}")
    @Schema(description = "数据ID")
    private String dataId;

    @Schema(description = "问题内容")
    private String q;
}