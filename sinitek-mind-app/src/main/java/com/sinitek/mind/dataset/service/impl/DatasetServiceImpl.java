package com.sinitek.mind.dataset.service.impl;

import cn.hutool.core.util.ZipUtil;
import com.sinitek.mind.dataset.constant.DatasetConstant;
import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import com.sinitek.mind.dataset.convert.DatasetConvert;
import com.sinitek.mind.dataset.core.training.FileTrainingServiceFactory;
import com.sinitek.mind.dataset.core.training.IFileTrainingService;
import com.sinitek.mind.dataset.core.vector.IDatasetVectorStore;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.dataset.entity.DatasetCollection;
import com.sinitek.mind.dataset.enumerate.DatasetCollectionTypeEnum;
import com.sinitek.mind.dataset.enumerate.DatasetStatusEnum;
import com.sinitek.mind.dataset.repository.DatasetCollectionRepository;
import com.sinitek.mind.dataset.repository.DatasetDataRepository;
import com.sinitek.mind.dataset.repository.DatasetRepository;
import com.sinitek.mind.dataset.service.IDatasetCollectionService;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import com.sinitek.mind.model.core.rerank.RerankModelFactory;
import com.sinitek.mind.model.core.rerank.RerankModelSupport;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.mind.support.common.dto.FullFileDTO;
import com.sinitek.mind.support.common.dto.UploadFileResponse;
import com.sinitek.mind.support.common.service.IFileService;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.dto.PermissionDTO;
import com.sinitek.mind.support.permission.enumerate.AuthTypeEnum;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.FileNameUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 知识库服务实现类
 *
 * <AUTHOR>
 * date 2025-07-16
 */
@Slf4j
@Service
public class DatasetServiceImpl implements IDatasetService {

    @Autowired
    private DatasetRepository datasetRepository;
    
    @Autowired
    private MongoTemplate mongoTemplate;
    
    @Autowired
    private ISystemModelService systemModelService;

    @Autowired
    private IDatasetTrainingService datasetTrainingService;

    @Autowired
    private DatasetConvert datasetConvert;

    @Autowired
    private IAuthService authService;

    @Autowired
    private DatasetCollectionRepository datasetCollectionRepository;

    @Autowired
    private DatasetDataRepository datasetDataRepository;

    @Autowired
    private RerankModelFactory rerankModelFactory;

    @Autowired
    private RerankModelSupport rerankModelSupport;

    @Autowired
    private IDatasetVectorStore datasetVectorStore;

    @Autowired
    private IDatasetCollectionService datasetCollectionService;

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private IFileService fileService;

    @Autowired
    private FileTrainingServiceFactory fileTrainingServiceFactory;

    /**
     * 创建知识库
     */
    @Override
    @Transactional
    public String createDataset(DatasetCreateRequest request, String userId, String teamId, String tmbId) throws Exception {
        if (StringUtils.isBlank(request.getName())) {
            throw new BussinessException(DatasetErrorCodeConstant.DATASET_NAME_EMPTY);
        }

        // 处理parentId: 如果parentId为空，使用datasetId

        // 创建知识库实体
        Dataset dataset = new Dataset();
        dataset.setName(request.getName());
        dataset.setType(request.getType());
        String parentId = request.getParentId();
        dataset.setParentId(StringUtils.isNotBlank(parentId) ? parentId : null);

        dataset.setTeamId(teamId);
        dataset.setTmbId(tmbId);
        dataset.setAvatar(request.getAvatar());
        dataset.setIntro(request.getIntro());
        dataset.setVectorModel(request.getVectorModel());
        dataset.setAgentModel(request.getAgentModel());
        dataset.setVlmModel(request.getVlmModel());
        dataset.setUpdateTime(new Date());
        dataset.setInheritPermission(true);

        // 如果是folder类型，不设置模型
        if (!"folder".equals(request.getType())) {
            // 可以添加默认模型设置，如果需要
        }

        // 保存知识库
        Dataset savedDataset = datasetRepository.save(dataset);

        return savedDataset.getId();
    }

    /**
     * 更新知识库
     *
     * @param request 更新请求
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param tmbId 成员ID
     * @throws Exception 异常
     */
    @Override
    @Transactional
    public void updateDataset(DatasetUpdateRequest request, String userId, String teamId, String tmbId) throws Exception {
        String datasetId = request.getId();
        Dataset dataset = checkDatasetUpdate(datasetId, teamId);

        // 只允许更新name、intro和avatar字段
        if (StringUtils.isNotBlank(request.getName())) {
            dataset.setName(request.getName());
        }

        if (request.getIntro() != null) {
            dataset.setIntro(request.getIntro());
        }

        if (StringUtils.isNotBlank(request.getAvatar())) {
            dataset.setAvatar(request.getAvatar());
        }

        dataset.setUpdateTime(new Date());

        // 保存更新
        datasetRepository.save(dataset);
    }

    @Override
    @Transactional
    public void updateDatasetModel(DatasetUpdateModelRequest request, String userId, String teamId, String tmbId) throws Exception {
        String datasetId = request.getId();
        Dataset dataset = checkDatasetUpdate(datasetId, teamId);

        // 只允许更新agentModel和vlmModel字段
        if (StringUtils.isNotBlank(request.getAgentModel())) {
            dataset.setAgentModel(request.getAgentModel());
        }

        if (StringUtils.isNotBlank(request.getVlmModel())) {
            dataset.setVlmModel(request.getVlmModel());
        }

        dataset.setUpdateTime(new Date());

        // 保存更新
        datasetRepository.save(dataset);
    }

    @Override
    @Transactional
    public void updateDatasetVectorModel(String datasetId, String vectorModel) {
        // 检查知识库是否存在并验证权限
        AuthDTO authDTO = authService.authCert();
        Dataset dataset = checkDatasetUpdate(datasetId, authDTO.getTeamId());

        // 如果vectorModel有值，则更新该字段
        if (StringUtils.isNotBlank(vectorModel)) {
            dataset.setVectorModel(vectorModel);
            dataset.setUpdateTime(new Date());

            // 保存更新
            datasetRepository.save(dataset);
        }
    }

    @NotNull
    private Dataset checkDatasetUpdate(String datasetId, String teamId) {
        if (StringUtils.isBlank(datasetId)) {
            throw new BussinessException(DatasetErrorCodeConstant.DATASET_ID_EMPTY_LEGACY);
        }

        // 查询知识库
        Optional<Dataset> optionalDataset = datasetRepository.findById(datasetId);
        if (optionalDataset.isEmpty()) {
            throw new BussinessException(DatasetErrorCodeConstant.DATASET_NOT_EXIST);
        }

        Dataset dataset = optionalDataset.get();

        // 验证权限
        if (!dataset.getTeamId().equals(teamId)) {
            throw new BussinessException(DatasetErrorCodeConstant.NO_DATASET_OPERATION_PERMISSION);
        }
        return dataset;
    }

    /**
     * 删除知识库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataset(String datasetId, String userId, String teamId, String tmbId) throws Exception {
        validateAndGetDatasetOrThrow(datasetId, teamId, "操作");

        // 1. 查询该知识库下所有集合
        List<DatasetCollection> collections = datasetCollectionRepository.findAllByDatasetId(datasetId);
        List<String> collectionIds = collections.stream().map(DatasetCollection::getId).collect(Collectors.toList());

        // 2. 删除所有集合下的数据
        if (!collectionIds.isEmpty()) {
            datasetDataRepository.deleteAllByCollectionIdIn(collectionIds);
        }

        // 3. 删除所有集合
        datasetCollectionRepository.deleteAllByDatasetId(datasetId);

        // 4. 删除与该知识库相关的所有训练数据
        datasetTrainingService.deleteByDatasetId(datasetId);

        // 5. 删除知识库本身
        datasetRepository.deleteById(datasetId);
    }

    /**
     * 获取知识库详情
     */
    @Override
    public DatasetDTO getDatasetDetail(String datasetId, String userId, String teamId, String tmbId) {
        Dataset dataset = validateAndGetDatasetOrThrow(datasetId, teamId, "访问");
        // 转换为DTO
        DatasetDTO datasetDTO = datasetConvert.convertToDTO(dataset);
        // 设置状态
        datasetDTO.setStatus(DatasetStatusEnum.ACTIVE);
        // 设置权限
        long per = permissionService.getResourcePerByOrgId(tmbId, datasetId, ResourceTypeEnum.DATASET, true);
        datasetDTO.setPermission(new PermissionDTO(per, tmbId.equals(dataset.getTmbId())));
        return datasetDTO;
    }

    /**
     * 获取知识库列表
     */
    @Override
    public List<Dataset> getDatasetList(DatasetListRequest request, String userId, String teamId, String tmbId) throws Exception {
        // 构建查询条件
        Query query = new Query();

        // 构建所有查询条件的列表
        List<Criteria> criteriaList = new ArrayList<>();

        List<String> idList = permissionService.findAllAuthedResourceId(tmbId, ResourceTypeEnum.DATASET, List.of(AuthTypeEnum.READ, AuthTypeEnum.WRITE, AuthTypeEnum.MANAGE));

        // 查询有权限或拥有者为自身的知识库
        criteriaList.add(new Criteria().orOperator(
                Criteria.where("tmbId").is(tmbId),
                Criteria.where("id").in(idList)));
        
        // 父级ID条件
        if (StringUtils.isNotBlank(request.getParentId())) {
            criteriaList.add(Criteria.where("parentId").is(new ObjectId(request.getParentId())));
        } else {
            criteriaList.add(Criteria.where("parentId").is(null));
        }
        
        // 类型条件
        if (StringUtils.isNotBlank(request.getType())) {
            criteriaList.add(Criteria.where("type").is(request.getType()));
        }
        
        // 搜索关键词
        if (StringUtils.isNotBlank(request.getSearchKey())) {
            Pattern pattern = Pattern.compile(request.getSearchKey(), Pattern.CASE_INSENSITIVE);
            criteriaList.add(new Criteria().orOperator(
                    Criteria.where("name").regex(pattern),
                    Criteria.where("intro").regex(pattern)
            ));
        }
        query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));

        // 添加根据updateTime字段从新到旧排序
        query.with(Sort.by(Sort.Order.desc("updateTime")));
        
        // 查询数据
        return mongoTemplate.find(query, Dataset.class);
    }

    /**
     * 获取知识库路径
     */
    @Override
    public List<DatasetPathDTO> getDatasetPaths(String datasetId, String userId, String teamId, String tmbId) throws Exception {
        List<DatasetPathDTO> paths = new ArrayList<>();
        String currentId = datasetId;
        while (StringUtils.isNotBlank(currentId)) {
            Optional<Dataset> optionalDataset = datasetRepository.findById(currentId);
            if (optionalDataset.isEmpty()) {
                break;
            }
            Dataset dataset = optionalDataset.get();
            if (!dataset.getTeamId().equals(teamId)) {
                break;
            }
            DatasetPathDTO dto = new DatasetPathDTO();
            dto.setParentId(dataset.getId());
            dto.setParentName(dataset.getName());
            paths.add(0, dto);
            currentId = dataset.getParentId() != null ? dataset.getParentId() : null;
        }
        return paths;
    }

    /**
     * 通过知识库名称查询知识库ID
     *
     * @param name 知识库名称
     * @return 知识库ID
     * <AUTHOR>
     * date 2025-07-23
     */
    @Override
    public String getDatasetIdByName(String name) {
        if (StringUtils.isBlank(name)) {
            throw new BussinessException(DatasetErrorCodeConstant.DATASET_NAME_EMPTY);
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("name").is(name));
        Dataset dataset = mongoTemplate.findOne(query, Dataset.class);
        if (dataset == null) {
            return "";
        }
        return dataset.getId();
    }

    @Override
    public DatasetDTO checkDatasetAuthByCurrentUser(String datasetId) {
        AuthDTO auth = authService.authCert();
        DatasetDTO dataset = this.getDatasetDetail(datasetId, auth.getUserId(), auth.getTeamId(), auth.getTmbId());
        if (!dataset.getPermission().getHasWritePer()) {
            throw new BussinessException(DatasetErrorCodeConstant.NO_WRITE_PERMISSION);
        }
        return dataset;
    }

    @Override
    public String getVectorModelByDatasetId(String datasetId) {
        if (StringUtils.isBlank(datasetId)) {
            return "";
        }
        Optional<Dataset> optional = datasetRepository.findById(datasetId);
        if (optional.isEmpty()) {
            return "";
        }
        return optional.get().getVectorModel();
    }

    @Override
    public Dataset getDatasetById(String datasetId) {
        if (StringUtils.isBlank(datasetId)) {
            return null;
        }
        Optional<Dataset> optional = datasetRepository.findById(datasetId);
        return optional.orElse(null);
    }

    /**
     * 校验知识库ID、存在性和团队权限，返回Dataset对象
     *
     * @param datasetId 知识库ID
     * @param teamId 团队ID
     * @param actionDesc 操作描述（用于异常信息）
     * @return Dataset
     */
    private Dataset validateAndGetDatasetOrThrow(String datasetId, String teamId, String actionDesc) {
        if (StringUtils.isBlank(datasetId)) {
            throw new BussinessException(DatasetErrorCodeConstant.DATASET_ID_EMPTY_LEGACY);
        }
        Optional<Dataset> optionalDataset = datasetRepository.findById(datasetId);
        if (optionalDataset.isEmpty()) {
            throw new BussinessException(DatasetErrorCodeConstant.DATASET_NOT_EXIST);
        }
        Dataset dataset = optionalDataset.get();
        if (!dataset.getTeamId().equals(teamId)) {
            throw new BussinessException(DatasetErrorCodeConstant.NO_DATASET_OPERATION_PERMISSION, actionDesc);
        }
        return dataset;
    }

    /**
     * 递归查找所有子知识库
     *
     * @param teamId 团队ID
     * @param parentId 父ID
     * @param result 结果集
     */
    private void findAllChildren(String teamId, String parentId, List<Dataset> result) {
        List<Dataset> children = datasetRepository.findAllByTeamIdAndParentId(new ObjectId(teamId), new ObjectId(parentId));
        if (children != null && !children.isEmpty()) {
            result.addAll(children);
            
            for (Dataset child : children) {
                findAllChildren(teamId, child.getId(), result);
            }
        }
    }

    /**
     * 检查知识库权限
     */
    @Override
    public void checkDatasetPermission(String datasetId, String userId, String teamId, String tmbId) {
        // 调用已有的验证方法
        validateAndGetDatasetOrThrow(datasetId, teamId, "操作");
    }

    /**
     * 导出知识库为ZIP（仅导出集合中文件本体，保持层级结构）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public File exportDatasetToZip(String datasetId, String userId, String teamId, String tmbId) {
        try {
            // 权限校验
            validateAndGetDatasetOrThrow(datasetId, teamId, "访问");

            // 查询集合
            List<DatasetCollection> collections = datasetCollectionRepository.findAllByDatasetId(datasetId);
            if (collections == null || collections.isEmpty()) {
                throw new BussinessException("知识库为空，无法导出");
            }

            // 创建临时ZIP文件（使用IOUtil规范创建临时文件）
            File zipFile = IOUtil.createTempFile("zip");

            // 构建父子映射
            Map<String, List<DatasetCollection>> parentChildMap = new HashMap<>();
            for (DatasetCollection c : collections) {
                String parentId = StringUtils.defaultString(c.getParentId(), "");
                parentChildMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(c);
            }

            try (ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFile))) {
                // 递归写入目录与文件（仅文件本体）
                writeCollectionsToZip(zipOut, parentChildMap, "", "");
            }

            return zipFile;
        } catch (Exception e) {
            throw new BussinessException("导出知识库失败: " + e.getMessage());
        }
    }

    /**
     * 递归写入集合到ZIP（仅写入文件本体，不包含元数据文件）
     */
    private void writeCollectionsToZip(ZipOutputStream zipOut,
                                       Map<String, List<DatasetCollection>> parentChildMap,
                                       String currentPath,
                                       String parentId) throws java.io.IOException {
        List<DatasetCollection> children = parentChildMap.get(parentId);
        if (children == null || children.isEmpty()) {
            return;
        }

        for (DatasetCollection collection : children) {
            String entryPath = currentPath + collection.getName();

            if (DatasetCollectionTypeEnum.FOLDER.getCode().equals(collection.getType())) {
                // 目录
                entryPath += "/";
                ZipEntry dirEntry = new ZipEntry(entryPath);
                zipOut.putNextEntry(dirEntry);
                zipOut.closeEntry();

                // 递归子级
                writeCollectionsToZip(zipOut, parentChildMap, entryPath, collection.getId());
            } else {
                // 文件：根据fileId取文件本体写入
                String fileId = collection.getFileId();
                if (StringUtils.isNotBlank(fileId)) {
                    try {
                        FullFileDTO fileInfo = fileService.getFileById(DatasetConstant.DATASET_BUCKET_NAME, fileId);
                        File sourceFile = fileInfo.getSourceFile();
                        if (sourceFile != null && sourceFile.exists()) {
                            ZipEntry fileEntry = new ZipEntry(entryPath);
                            zipOut.putNextEntry(fileEntry);
                            try (FileInputStream fis = new FileInputStream(sourceFile)) {
                                byte[] buffer = new byte[1024];
                                int length;
                                while ((length = fis.read(buffer)) > 0) {
                                    zipOut.write(buffer, 0, length);
                                }
                            }
                            zipOut.closeEntry();
                        }
                    } catch (Exception ex) {
                        // 忽略单个文件错误，继续
                        log.warn("导出文件失败 fileId={} msg={}", fileId, ex.getMessage());
                    }
                }
            }
        }
    }

    /**
     * 从ZIP文件导入知识库（依据 ZIP 的目录层级创建 folder/file 集合；文件内容上传到文件服务）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importDatasetFromZip(File zipFile, String datasetId, String userId, String teamId, String tmbId) {
        try {
            // 校验权限
            validateAndGetDatasetOrThrow(datasetId, teamId, "访问");

            // 解压至临时目录（使用规范的 createTempDir）
            File tempDir = IOUtil.createTempDir();

            extractZipToDirectory(zipFile, tempDir);

            // 遍历目录并创建集合
            importDirectoryAsCollections(tempDir, null, datasetId, teamId, tmbId, tempDir);
        } catch (Exception e) {
            throw new BussinessException("导入知识库失败: " + e.getMessage());
        }
    }

    /**
     * 解压 ZIP 到指定目录
     */
    private void extractZipToDirectory(File zipFile, File destDir) throws java.io.IOException {
        ZipUtil.unzip(zipFile, destDir);
    }

    /**
     * 递归导入目录内容为集合
     *
     * @param currentDir 当前处理目录
     * @param parentCollectionId 父集合ID（null 表示顶层）
     * @param datasetId 目标知识库ID
     * @param teamId 团队ID
     * @param tmbId 成员ID
     * @param rootDir 解压根目录，用于忽略根名
     */
    private void importDirectoryAsCollections(File currentDir, String parentCollectionId, String datasetId, String teamId, String tmbId, File rootDir) {
        File[] files = currentDir.listFiles();
        if (files == null) {
            return;
        }

        for (File f : files) {
            if (f.isDirectory()) {
                // 创建文件夹集合
                DatasetCollection folder = new DatasetCollection();
                folder.setParentId(parentCollectionId);
                folder.setTeamId(teamId);
                folder.setTmbId(tmbId);
                folder.setDatasetId(datasetId);
                folder.setType(DatasetCollectionTypeEnum.FOLDER.getCode());
                folder.setName(f.getName());
                folder.setCreateTime(new Date());
                folder.setUpdateTime(new Date());
                DatasetCollection saved = datasetCollectionRepository.save(folder);
                importDirectoryAsCollections(f, saved.getId(), datasetId, teamId, tmbId, rootDir);
            } else {
                // 创建文件集合并上传内容
                String parentId = parentCollectionId;
                // 确保父目录对应的集合已创建：由于我们顺序处理目录，parentCollectionId 已成立

                // 根据扩展名决定集合类型
                String fileName = f.getName();
                String ext = FileNameUtils.getExtension(fileName);
                IFileTrainingService fileTrainingService = fileTrainingServiceFactory.getServiceByExtension(ext);
                if (ObjectUtils.isEmpty(fileTrainingService)) {
                    log.warn("集合名称: {}系统不支持该类型的文件", fileName);
                    continue;
                }

                // 上传文件
                Map<String, String> metadata = new HashMap<>();
                metadata.put("datasetId", datasetId);
                metadata.put("importTime", String.valueOf(System.currentTimeMillis()));
                UploadFileResponse upload = fileService.uploadFile(f, f.getName(), DatasetConstant.DATASET_BUCKET_NAME, metadata);

                DatasetCollectionDTO collectionDTO = new DatasetCollectionDTO();
                collectionDTO.setParentId(parentId);
                collectionDTO.setTeamId(teamId);
                collectionDTO.setTmbId(tmbId);
                collectionDTO.setDatasetId(datasetId);

                // 默认训练参数
                collectionDTO.setTrainingType("chunk");
                collectionDTO.setChunkTriggerType("minSize");
                collectionDTO.setChunkTriggerMinSize(1000);
                collectionDTO.setDataEnhanceCollectionName(false);
                collectionDTO.setImageIndex(false);
                collectionDTO.setAutoIndexes(false);
                collectionDTO.setChunkSettingMode("auto");
                collectionDTO.setChunkSplitMode("paragraph");
                collectionDTO.setParagraphChunkAIMode("forbid");
                collectionDTO.setParagraphChunkDeep(5);
                collectionDTO.setParagraphChunkMinSize(100);
                collectionDTO.setChunkSize(1000);
                collectionDTO.setIndexSize(8096);


                String supportedType = fileTrainingService.getSupportedType();
                collectionDTO.setType(supportedType);
                collectionDTO.setName(f.getName());
                collectionDTO.setFileId(upload.getFileId());
                collectionDTO.setCreateTime(new Date());
                collectionDTO.setUpdateTime(new Date());
                String id = datasetCollectionService.save(collectionDTO);
                collectionDTO.setId(id);

                try {
                    fileTrainingService.training(collectionDTO, f);
                } catch (Exception ex) {
                    log.warn("导入训练失败 file={}", fileName, ex);
                }
            }
        }
    }
}