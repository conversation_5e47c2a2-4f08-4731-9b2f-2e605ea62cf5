package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "重建嵌入向量请求DTO")
public class RebuildEmbeddingRequestDTO {

    @NotBlank(message = "{" + DatasetErrorCodeConstant.DATASET_ID_FIELD_EMPTY + "}")
    @Schema(description = "数据集ID")
    private String datasetId;

    @NotBlank(message = "{" + DatasetErrorCodeConstant.VECTOR_MODEL_FIELD_EMPTY + "}")
    @Schema(description = "向量模型")
    private String vectorModel;
}