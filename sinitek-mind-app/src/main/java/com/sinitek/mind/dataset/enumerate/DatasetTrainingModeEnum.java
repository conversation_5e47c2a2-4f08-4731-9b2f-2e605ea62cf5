package com.sinitek.mind.dataset.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库训练类型枚举
 *
 * <AUTHOR>
 * date 2025-08-12
 */
@Getter
@AllArgsConstructor
public enum DatasetTrainingModeEnum {

    PARSE("parse", "内容解析中"),

    CHUNK("chunk", "索引向量化"),

    QA("qa", "问答对提取"),

    IMAGE("image", "图片索引生成"),

    IMAGE_PARSE("imageParse", "图片索引生成"),

    AUTO("auto", "自动索引生成");

    private final String value;
    private final String label;

    /**
     * 根据value获取枚举
     * @param value
     * @return
     */
    public static DatasetTrainingModeEnum getByValue(String value) {
        for (DatasetTrainingModeEnum typeEnum : values()) {
            if (typeEnum.value.equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据label获取枚举
     * @param label
     * @return
     */
    public static DatasetTrainingModeEnum getByLabel(String label) {
        for (DatasetTrainingModeEnum typeEnum : values()) {
            if (typeEnum.label.equals(label)) {
                return typeEnum;
            }
        }
        return null;
    }
}