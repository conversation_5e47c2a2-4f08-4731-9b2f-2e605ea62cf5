package com.sinitek.mind.dataset.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.util.Date;
import java.util.Map;

/**
 * 知识库实体类
 *
 * <AUTHOR>
 * date 2025-07-16
 */
@Data
@Document(collection = "datasets")
public class Dataset {

    @Id
    @Schema(description = "知识库ID")
    private String id;
    
    @Schema(description = "父级知识库ID")
    private String parentId;
    
    @Indexed
    @Schema(description = "团队ID")
    private String teamId;

    @Schema(description = "团队成员ID")
    private String tmbId;

    @Schema(description = "知识库类型")
    private String type;
    
    @Schema(description = "知识库头像")
    private String avatar;
    
    @Schema(description = "知识库名称")
    private String name;
    
    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "索引模型")
    private String vectorModel;

    @Schema(description = "文本理解模型")
    private String agentModel;

    @Schema(description = "图片理解模型")
    private String vlmModel;
    
    @Schema(description = "知识库简介")
    private String intro;
    
    @Schema(description = "网站配置")
    private WebsiteConfig websiteConfig;
    
    @Schema(description = "分块设置")
    private ChunkSettings chunkSettings;
    
    @Schema(description = "是否继承权限")
    private Boolean inheritPermission;
    
    @Schema(description = "API知识库服务配置")
    private Map<String, Object> apiDatasetServer;
    
    @Data
    public static class WebsiteConfig {
        
        @Schema(description = "URL地址")
        private String url;
        
        @Schema(description = "选择器")
        private String selector;
    }
    
    @Data
    public static class ChunkSettings {
        
        @Schema(description = "训练类型")
        private String trainingType;
        
        @Schema(description = "分块触发类型")
        private String chunkTriggerType;
        
        @Schema(description = "分块触发最小大小")
        private Integer chunkTriggerMinSize;
        
        @Schema(description = "数据增强集合名称")
        private Boolean dataEnhanceCollectionName;
        
        @Schema(description = "图像索引")
        private Boolean imageIndex;
        
        @Schema(description = "自动索引")
        private Boolean autoIndexes;
        
        @Schema(description = "索引前缀标题")
        private Boolean indexPrefixTitle;
        
        @Schema(description = "分块设置模式")
        private String chunkSettingMode;
        
        @Schema(description = "分块拆分模式")
        private String chunkSplitMode;
        
        @Schema(description = "段落分块AI模式")
        private String paragraphChunkAIMode;
        
        @Schema(description = "段落分块深度")
        private Integer paragraphChunkDeep;
        
        @Schema(description = "段落分块最小大小")
        private Integer paragraphChunkMinSize;
        
        @Schema(description = "分块大小")
        private Integer chunkSize;
        
        @Schema(description = "分块分割符")
        private String chunkSplitter;
        
        @Schema(description = "索引大小")
        private Integer indexSize;
        
        @Schema(description = "QA提示词")
        private String qaPrompt;
    }
} 