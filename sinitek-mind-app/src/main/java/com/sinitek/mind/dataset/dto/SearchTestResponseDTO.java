package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "搜索测试响应DTO")
public class SearchTestResponseDTO {

    @Schema(description = "搜索结果列表")
    private List<DatasetSearchResultDTO> list = new ArrayList<>();

    @Schema(description = "搜索耗时")
    private String duration;

    @Schema(description = "是否使用重排")
    private Boolean usingReRank;

    @Schema(description = "搜索模式")
    private String searchMode;

    @Schema(description = "搜索限制数量")
    private Integer limit;

    @Schema(description = "相似度阈值")
    private Double similarity;

    @Schema(description = "是否使用相似度过滤")
    private Boolean usingSimilarityFilter;

    @Schema(description = "重排序权重")
    private Double rerankWeight;

    @Schema(description = "嵌入权重")
    private Double embeddingWeight;
}