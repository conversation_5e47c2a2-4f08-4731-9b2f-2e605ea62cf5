package com.sinitek.mind.dataset.core.training;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文件训练服务工厂类
 *
 * <AUTHOR>
 * date 2025-08-22
 * 描述：根据集合类型获取对应的文件训练服务
 */
@Component
@Slf4j
public class FileTrainingServiceFactory {

    private final Map<String, IFileTrainingService> serviceMap;

    @Autowired
    public FileTrainingServiceFactory(List<IFileTrainingService> services) {
        this.serviceMap = services.stream()
                .collect(Collectors.toMap(
                        IFileTrainingService::getSupportedType,
                        Function.identity()
                ));
    }

    /**
     * 根据类型获取对应的训练服务
     *
     * @param type 集合类型
     * @return 对应的训练服务
     */
    public IFileTrainingService getService(String type) {
        IFileTrainingService service = serviceMap.get(type);
        if (service == null) {
            throw new IllegalArgumentException("不支持的文件类型: " + type);
        }
        return service;
    }

    /**
     * 根据文件扩展名选择合适的训练服务
     *
     * @param extension 文件扩展名（不含点）
     * @return 匹配的训练服务，若不存在则抛出异常
     */
    public IFileTrainingService getServiceByExtension(String extension) {
        for (IFileTrainingService svc : serviceMap.values()) {
            if (svc.supportsExtension(extension)) {
                return svc;
            }
        }
        return null;
    }

    /**
     * 检查是否支持指定类型
     *
     * @param type 集合类型
     * @return 是否支持
     */
    public boolean isSupported(String type) {
        return serviceMap.containsKey(type);
    }
}
