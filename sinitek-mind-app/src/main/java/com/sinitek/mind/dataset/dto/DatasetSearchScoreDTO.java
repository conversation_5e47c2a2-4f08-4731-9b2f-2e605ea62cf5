package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "评分项")
public class DatasetSearchScoreDTO {

    @Schema(description = "评分类型")
    private String type;

    @Schema(description = "评分值")
    private Double value;

    @Schema(description = "索引")
    private Integer index;
}