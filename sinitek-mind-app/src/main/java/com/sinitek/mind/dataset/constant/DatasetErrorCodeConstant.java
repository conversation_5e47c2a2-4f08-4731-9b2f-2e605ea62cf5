package com.sinitek.mind.dataset.constant;

/**
 * 数据集模块错误码常量类
 * 
 * 错误码规则：30-08-YYYY
 * 30 - 固定前缀
 * 08 - 数据集模块
 * YYYY - 具体错误码
 *
 * <AUTHOR>
 * date 2025-08-15
 */
public class DatasetErrorCodeConstant {

    // ==================== 通用验证错误 (300800XX) ====================
    
    /**
     * ID不能为空
     */
    public static final String ID_EMPTY = "30080001";
    
    /**
     * 名称不能为空
     */
    public static final String NAME_EMPTY = "30080002";
    
    /**
     * 数据不存在
     */
    public static final String DATA_NOT_EXIST = "30080003";
    
    /**
     * 集合不存在
     */
    public static final String COLLECTION_NOT_EXIST = "30080004";
    
    /**
     * 知识库不存在
     */
    public static final String DATASET_NOT_EXIST = "30080005";
    
    /**
     * 缺少必要参数
     */
    public static final String MISSING_REQUIRED_PARAMS = "30080006";
    
    /**
     * 数据ID不能为空
     */
    public static final String DATA_ID_EMPTY = "30080007";
    
    /**
     * 数据集ID不能为空
     */
    public static final String DATASET_ID_EMPTY = "30080008";
    
    /**
     * 集合ID不能为空
     */
    public static final String COLLECTION_ID_EMPTY = "30080009";
    
    /**
     * 训练数据不存在
     */
    public static final String TRAINING_DATA_NOT_EXIST = "30080010";
    
    /**
     * 向量模型不能为空
     */
    public static final String VECTOR_MODEL_EMPTY = "30080011";
    
    /**
     * 知识库名称不能为空
     */
    public static final String DATASET_NAME_EMPTY = "30080012";
    
    /**
     * 知识库ID不能为空
     */
    public static final String DATASET_ID_EMPTY_LEGACY = "30080013";
    
    /**
     * 文件类型不能为空
     */
    public static final String FILE_TYPE_EMPTY = "30080014";
    
    /**
     * 文件源ID不能为空
     */
    public static final String FILE_SOURCE_ID_EMPTY = "30080015";
    
    /**
     * 搜索文本不能为空
     */
    public static final String SEARCH_TEXT_EMPTY = "30080016";
    
    /**
     * 模式不能为空
     */
    public static final String MODE_EMPTY = "30080017";

    /**
     * 文本内容不能为空
     */
    public static final String TEXT_CONTENT_EMPTY = "30080017";
    
    /**
     * datasetId不能为空
     */
    public static final String DATASET_ID_FIELD_EMPTY = "30080018";
    
    /**
     * collectionId不能为空
     */
    public static final String COLLECTION_ID_FIELD_EMPTY = "30080019";
    
    /**
     * dataId不能为空
     */
    public static final String DATA_ID_FIELD_EMPTY = "30080020";
    
    /**
     * vectorModel不能为空
     */
    public static final String VECTOR_MODEL_FIELD_EMPTY = "30080021";
    
    /**
     * 重叠比例不能为空
     */
    public static final String OVERLAP_RATIO_EMPTY = "30080022";
    
    /**
     * 文件内容无法解析或为空
     */
    public static final String FILE_CONTENT_UNPARSABLE = "30080023";
    
    /**
     * 文件处理失败
     */
    public static final String FILE_PROCESS_FAILED = "30080024";
    
    /**
     * 不支持的分块预览类型: {0}
     */
    public static final String UNSUPPORTED_CHUNK_PREVIEW_TYPE = "30080025";
    
    /**
     * 限制数量不能为空
     */
    public static final String LIMIT_FIELD_EMPTY = "30080026";

    // ==================== 权限错误 (300801XX) ====================
    
    /**
     * 无权限访问该集合
     */
    public static final String NO_COLLECTION_ACCESS_PERMISSION = "30080101";
    
    /**
     * 无权更新该数据
     */
    public static final String NO_DATA_UPDATE_PERMISSION = "30080102";
    
    /**
     * 无权操作该知识库
     */
    public static final String NO_DATASET_OPERATION_PERMISSION = "30080103";
    
    /**
     * 无写入权限
     */
    public static final String NO_WRITE_PERMISSION = "30080104";

    // ==================== 业务逻辑错误 (300802XX) ====================
    
    /**
     * 上传的文件找不到
     */
    public static final String UPLOADED_FILE_NOT_FOUND = "30080201";
    
    /**
     * 集合关联的文件找不到
     */
    public static final String COLLECTION_ASSOCIATED_FILE_NOT_FOUND = "30080202";
    
    /**
     * 未找到失败的训练数据
     */
    public static final String FAILED_TRAINING_DATA_NOT_FOUND = "30080203";
    
    /**
     * 数据集ID、集合ID和数据ID不能为空
     */
    public static final String DATASET_COLLECTION_DATA_IDS_EMPTY = "30080204";
    
    /**
     * 未找到指定ID的知识库: {0}
     */
    public static final String DATASET_NOT_FOUND_WITH_ID = "30080205";

    // ==================== 训练相关错误 (300803XX) ====================
    
    /**
     * 训练失败
     */
    public static final String TRAINING_FAILED = "30080301";
    
    /**
     * 训练任务不存在
     */
    public static final String TRAINING_TASK_NOT_EXIST = "30080302";
    
    private DatasetErrorCodeConstant() {
        // 工具类，不允许实例化
    }
}