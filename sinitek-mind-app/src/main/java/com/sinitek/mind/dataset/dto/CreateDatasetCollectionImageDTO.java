package com.sinitek.mind.dataset.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 创建数据集集合图片请求DTO
 * 用于接收图片文件和相关元数据
 */
@Data
@Schema(description = "创建数据集集合图片请求DTO")
public class CreateDatasetCollectionImageDTO {

    @Schema(description = "父级ID")
    @JsonProperty("parentId")
    private String parentId;

    @Schema(description = "数据集ID")
    @JsonProperty("datasetId")
    @NotBlank(message = "数据集ID不能为空")
    private String datasetId;

    @Schema(description = "集合名称")
    @JsonProperty("collectionName")
    @NotBlank(message = "集合名称不能为空")
    private String collectionName;
}