package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基于Text创建知识库集合参数
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TextCreateDatasetCollectionParamsDTO extends BaseDatasetCollectionParamsDTO {

    @Schema(description = "文本内容")
    @NotBlank(message = "{" + DatasetErrorCodeConstant.TEXT_CONTENT_EMPTY + "}")
    private String text;
}
