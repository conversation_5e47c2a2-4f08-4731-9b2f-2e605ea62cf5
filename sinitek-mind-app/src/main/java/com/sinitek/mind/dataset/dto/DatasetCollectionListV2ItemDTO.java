package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * listV2返回-单项DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库集合listV2单项结构
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "知识库集合listV2单项结构")
public class DatasetCollectionListV2ItemDTO extends DatasetCollectionDTO {

    @Schema(description = "主键ID")
    private String _id;

    @Schema(description = "权限")
    private DatasetCollectionListV2PermissionDTO permission;
} 