package com.sinitek.mind.dataset.core;

import com.sinitek.mind.dataset.convert.DatasetTrainingConvert;
import com.sinitek.mind.dataset.core.training.ImageTrainingSupport;
import com.sinitek.mind.dataset.core.vector.IDatasetVectorStore;
import com.sinitek.mind.dataset.dto.DatasetTrainingDTO;
import com.sinitek.mind.dataset.entity.DatasetTraining;
import com.sinitek.mind.dataset.enumerate.DatasetTrainingModeEnum;
import com.sinitek.mind.dataset.repository.DatasetTrainingRepository;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * DatasetTraining向量化定时任务
 *
 * <AUTHOR>
 * date 2025-07-22
 * 描述：定时将pending状态的DatasetTraining数据存入向量数据库
 */
@Slf4j
@Component
public class DatasetTrainingVectorTask {

    @Autowired
    private IDatasetTrainingService datasetTrainingService;

    @Autowired
    private DatasetTrainingRepository datasetTrainingRepository;

    @Autowired
    private IDatasetVectorStore datasetVectorStore;

    @Autowired
    private DatasetTrainingConvert datasetTrainingConvert;

    @Autowired
    private ImageTrainingSupport imageTrainingSupport;

    /**
     * 每2分钟执行一次，每次最多处理100条pending训练数据
     */
    @Scheduled(fixedRate = 120000)
    public void processPendingTrainings() {
        List<DatasetTraining> pendingList = datasetTrainingService.findPendingTrainings();
        if (pendingList == null || pendingList.isEmpty()) {
            return;
        }
        for (DatasetTraining training : pendingList) {
            try {
                DatasetTrainingDTO trainingDTO = datasetTrainingConvert.convertToDTO(training);
                String trainingType = trainingDTO.getMode();

                // 图片解析
                if (DatasetTrainingModeEnum.IMAGE_PARSE.getValue().equals(trainingType)) {
                    this.imageParse(training);
                } else {
                    this.parse(trainingDTO);
                }
            } catch (Exception e) {
                String errorMsg = e.getMessage();
                log.error("向量化失败，id={}, err={}", training.getId(), errorMsg, e);
                datasetTrainingService.updateStatusForFailed(training.getId(), errorMsg);
            }
        }
    }

    /**
     * 图片解析
     * @param training
     */
    private void imageParse(DatasetTraining training) {
        imageTrainingSupport.recognizeImageContent(training);
        this.parse(datasetTrainingConvert.convertToDTO(training));
    }

    /**
     * 基础的向量解析
     * @param trainingDTO
     */
    private void parse(DatasetTrainingDTO trainingDTO) {
        // 普通文本
        datasetVectorStore.addDatasetTraining(trainingDTO);
        datasetTrainingRepository.deleteById(trainingDTO.get_id());
    }
}