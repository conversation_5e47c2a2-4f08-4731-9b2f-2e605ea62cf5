package com.sinitek.mind.dataset.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.dataset.constant.DatasetConstant;
import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import com.sinitek.mind.dataset.convert.DatasetConvert;
import com.sinitek.mind.dataset.core.reader.VectorFileReaderCore;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.dataset.enumerate.DatasetStatusEnum;
import com.sinitek.mind.dataset.enumerate.PreviewChunksTypeEnum;
import com.sinitek.mind.dataset.service.IDatasetSearchService;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.support.account.dto.SourceMemberDTO;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.common.dto.FullFileDTO;
import com.sinitek.mind.support.common.service.IFileService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.dto.PermissionDTO;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.common.utils.HttpUtils;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.apache.commons.collections4.MapUtils;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import jakarta.servlet.http.HttpServletResponse;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 知识库控制器
 *
 * <AUTHOR>
 * date 2025-07-02
 * 描述：知识库相关API接口
 */
@RestController
@RequestMapping("/mind/api/core/dataset")
@Tag(name = "知识库管理")
public class DatasetController {

    @Autowired
    private IAuthService authService;

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private IDatasetSearchService datasetSearchService;

    @Autowired
    private DatasetConvert datasetConvert;

    @Autowired
    private VectorFileReaderCore vectorFileReaderCore;

    @Autowired
    private IFileService fileService;

    

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private IAccountService accountService;

    @PostMapping("/create")
    @Operation(summary = "创建知识库")
    public ApiResponse<String> createDataset(
            @RequestBody DatasetCreateRequest createRequest) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        String datasetId = datasetService.createDataset(createRequest, userId, teamId, tmbId);
        return ApiResponse.success(datasetId);
    }

    @PostMapping("/update")
    @Operation(summary = "更新知识库")
    public ApiResponse<Void> updateDataset(
            @RequestBody DatasetUpdateRequest updateRequest) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        datasetService.updateDataset(updateRequest, userId, teamId, tmbId);
        return ApiResponse.success();
    }

    @PostMapping("/update-model")
    @Operation(summary = "更新知识库的模型")
    public ApiResponse<Void> updateDatasetModel(
            @RequestBody DatasetUpdateModelRequest updateRequest) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        datasetService.updateDatasetModel(updateRequest, userId, teamId, tmbId);
        return ApiResponse.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除知识库")
    public ApiResponse<Void> deleteDataset(
            @Parameter(description = "知识库ID") @RequestParam String id) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        datasetService.deleteDataset(id, userId, teamId, tmbId);
        return ApiResponse.success();
    }

    @GetMapping("/detail")
    @Operation(summary = "获取知识库详情")
    public ApiResponse<DatasetDTO> getDatasetDetail(
            @Parameter(description = "知识库ID") @RequestParam String id) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        DatasetDTO dataset = datasetService.getDatasetDetail(id, userId, teamId, tmbId);
        return ApiResponse.success(dataset);
    }

    @PostMapping("/list")
    @Operation(summary = "获取知识库列表")
    public ApiResponse<List<DatasetDTO>> getDatasetList(
            @RequestBody DatasetListRequest listRequest) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        List<Dataset> datasets = datasetService.getDatasetList(listRequest, userId, teamId, tmbId);
        List<DatasetDTO> datasetDTOList = this.format(datasets, authInfo);
        return ApiResponse.success(datasetDTOList);
    }

    @GetMapping("/paths")
    @Operation(summary = "获取知识库路径")
    public ApiResponse<List<DatasetPathDTO>> getDatasetPaths(
            @Parameter(description = "知识库ID") @RequestParam(required = false) String id) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        List<DatasetPathDTO> paths = datasetService.getDatasetPaths(id, userId, teamId, tmbId);

        return ApiResponse.success(paths);
    }

    /**
     * 格式化知识库的列表
     * @param datasets
     * @return
     */
    public List<DatasetDTO> format(List<Dataset> datasets, AuthDTO authInfo) {
        String tmbId = authInfo.getTmbId();
        List<DatasetDTO> resultList = new ArrayList<>();

        List<String> idList = datasets.stream().map(Dataset::getId).toList();
        List<String> orgIdList = datasets.stream().map(Dataset::getTmbId).distinct().toList();

        Map<String, Long> perMap = permissionService.getResourcePerByOrgId(tmbId, idList, ResourceTypeEnum.DATASET, true);
        Map<String, Integer> orgNumMap = permissionService.findAllAuthedOrgNumber(idList, ResourceTypeEnum.DATASET);
        Map<String, SourceMemberDTO> sourceMemberMap = accountService.getSourceMemberMapByOrgIds(orgIdList);

        for (Dataset dataset : datasets) {
            boolean isOwner = tmbId.equals(dataset.getTmbId());
            DatasetDTO dto = datasetConvert.convertToDTO(dataset);

            long per = MapUtils.getLongValue(perMap, dataset.getId(), PermissionConstant.READ_PER);
            dto.setPermission(new PermissionDTO(per, isOwner));

            int isPrivate = MapUtils.getIntValue(orgNumMap, dataset.getId(), 0);
            dto.setIsPrivate(isPrivate == 0);
            dto.setStatus(DatasetStatusEnum.ACTIVE);

            SourceMemberDTO sourceMemberDTO = MapUtils.getObject(sourceMemberMap, dataset.getTmbId(), new SourceMemberDTO());
            dto.setSourceMember(sourceMemberDTO);

            resultList.add(dto);
        }
        return resultList;
    }

    @PostMapping("/preview-chunks")
    @Operation(summary = "获取文件预览分块")
    public ApiResponse<PreviewChunksResponseDTO> getPreviewChunks(@RequestBody @Valid PreviewChunksRequestDTO request) {
        String type = request.getType();
        List<Document> documents;
        if (PreviewChunksTypeEnum.FILE_LOCAL.getValue().equals(type)) {
            String fileId = request.getSourceId();
            FullFileDTO fileInfo = fileService.getFileById(DatasetConstant.DATASET_BUCKET_NAME, fileId);
            File sourceFile = fileInfo.getSourceFile();
            documents = vectorFileReaderCore.process(sourceFile, request);
        } else {
            throw new BussinessException(DatasetErrorCodeConstant.UNSUPPORTED_CHUNK_PREVIEW_TYPE);
        }

        // 转换Document为PreviewChunksResponse格式
        PreviewChunksResponseDTO response = new PreviewChunksResponseDTO();
        List<PreviewChunksResponseDTO.ChunkDTO> chunks = new ArrayList<>();
        for (Document doc : documents) {
            PreviewChunksResponseDTO.ChunkDTO chunk = new PreviewChunksResponseDTO.ChunkDTO();
            chunk.setQ(doc.getText());
            chunk.setA("");
            chunks.add(chunk);
        }
        response.setChunks(chunks);
        response.setTotal(chunks.size());

        return ApiResponse.success(response);
    }

    @PostMapping("/file/getPreviewChunks")
    @Operation(summary = "获取文件预览分块")
    public ApiResponse<PreviewChunksResponseDTO> getPreviewChunksByFile(@RequestBody @Valid PreviewChunksRequestDTO request) {
        String type = request.getType();
        List<Document> documents;
        if (PreviewChunksTypeEnum.FILE_LOCAL.getValue().equals(type)) {
            String fileId = request.getSourceId();
            FullFileDTO fileInfo = fileService.getFileById(DatasetConstant.DATASET_BUCKET_NAME, fileId);
            File sourceFile = fileInfo.getSourceFile();
            documents = vectorFileReaderCore.process(sourceFile, request);
        } else {
            throw new BussinessException(DatasetErrorCodeConstant.UNSUPPORTED_CHUNK_PREVIEW_TYPE);
        }

        // 转换Document为PreviewChunksResponse格式
        PreviewChunksResponseDTO response = new PreviewChunksResponseDTO();
        List<PreviewChunksResponseDTO.ChunkDTO> chunks = new ArrayList<>();
        for (Document doc : documents) {
            PreviewChunksResponseDTO.ChunkDTO chunk = new PreviewChunksResponseDTO.ChunkDTO();
            chunk.setQ(doc.getText());
            chunk.setA("");
            chunks.add(chunk);
        }
        response.setChunks(chunks);
        response.setTotal(chunks.size());

        return ApiResponse.success(response);
    }


    @PostMapping("/searchTest")
    @Operation(summary = "搜索向量数据库测试")
    public ApiResponse<SearchTestResponseDTO> searchTest(@RequestBody @Valid SearchTestRequestDTO request) throws Exception {
        SearchTestResponseDTO response = datasetSearchService.searchTest(request);
        return ApiResponse.success(response);
    }

    @GetMapping("/export")
    @Operation(summary = "导出知识库")
    public void exportDataset(
            @Parameter(description = "知识库ID") @RequestParam String datasetId,
            HttpServletResponse response) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        // 查询名称用于文件名
        DatasetDTO datasetDTO = datasetService.getDatasetDetail(datasetId, userId, teamId, tmbId);
        String datasetName = datasetDTO != null ? datasetDTO.getName() : datasetId;
        String nowStr = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(new java.util.Date());
        String downloadName = "知识库-" + datasetName + "-" + nowStr + ".zip";

        File zipFile = datasetService.exportDatasetToZip(datasetId, userId, teamId, tmbId);
        try (java.io.FileInputStream inputStream = new java.io.FileInputStream(zipFile)) {
            HttpUtils.download(response, downloadName, inputStream);
        }
    }

    @PostMapping("/import")
    @Operation(summary = "导入知识库")
    public ApiResponse<String> importDataset(
            @Parameter(description = "ZIP文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "新知识库名称") @RequestParam("datasetId") String datasetId) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();

        DatasetDTO datasetDTO = datasetService.getDatasetDetail(datasetId, userId, teamId, tmbId);

        // 将MultipartFile转换为临时文件
        File tempZipFile = IOUtil.createTempFile("zip");
        file.transferTo(tempZipFile);

        datasetService.importDatasetFromZip(tempZipFile, datasetDTO.get_id(), userId, teamId, tmbId);
        return ApiResponse.success(datasetDTO.get_id());
    }
}