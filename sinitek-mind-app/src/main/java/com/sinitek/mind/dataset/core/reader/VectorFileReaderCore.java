package com.sinitek.mind.dataset.core.reader;

import com.sinitek.mind.dataset.dto.BaseDatasetCollectionParamsDTO;
import com.sinitek.mind.dataset.enumerate.DatasetChunkSettingModeEnum;
import com.sinitek.mind.dataset.enumerate.DatasetChunkSplitModeEnum;
import com.sinitek.mind.dataset.enumerate.DatasetTrainingModeEnum;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.Metadata;
import dev.langchain4j.data.document.splitter.DocumentByCharacterSplitter;
import dev.langchain4j.data.document.splitter.DocumentByParagraphSplitter;
import dev.langchain4j.data.document.splitter.DocumentByRegexSplitter;
import dev.langchain4j.data.segment.TextSegment;
import org.apache.commons.compress.utils.FileNameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 向量文件解析
 *
 * <AUTHOR>
 * date 2025/07/22
 */
@Component
public class VectorFileReaderCore {

    /**
     * 默认的最大分块长度
     */
    public static final Integer DEFAULT_MAX_CHUNK_SIZE = 1000;

    @Autowired
    private List<IVectorFileReader> vectorFileReaderList;

    /**
     * 拆分出最终的List<Document>
     * @param sourceFile
     * @param dto 分块的规则
     * @return
     */
    public List<Document> process(File sourceFile, BaseDatasetCollectionParamsDTO dto) {
        dev.langchain4j.data.document.Document document = this.readFile(sourceFile);
        if (document == null) {
            return new ArrayList<>();
        }
        List<TextSegment> textSegments = this.splitByRules(document, dto);

        // 转换为Spring AI的Document类型
        List<Document> finalDocumentList = new ArrayList<>();
        for (TextSegment segment : textSegments) {
            Metadata segmentMetadata = segment.metadata();
            Map<String, Object> metadata = segmentMetadata.toMap();
            Document springDoc = new Document(segment.text(), metadata);

            finalDocumentList.add(springDoc);
        }
        return finalDocumentList;
    }

    /**
     * 根据配置的规则来进行拆分
     *
     * @param document
     * @param dto
     * @return
     */
    public List<TextSegment> splitByRules(dev.langchain4j.data.document.Document document, BaseDatasetCollectionParamsDTO dto) {
        String trainingType = dto.getTrainingType();
        String chunkSettingMode = dto.getChunkSettingMode();
        String chunkSplitMode = dto.getChunkSplitMode();
        Integer chunkSize = dto.getChunkSize();
        if (StringUtils.isBlank(trainingType)) {
            trainingType = DatasetTrainingModeEnum.CHUNK.getValue();
        }
        // 最终分块数据
        List<TextSegment> textSegmentList = new ArrayList<>();

        // 分块存储
        if (DatasetTrainingModeEnum.CHUNK.getValue().equals(trainingType)) {
            if (DatasetChunkSettingModeEnum.CUSTOM.getValue().equals(chunkSettingMode)) {
                if (DatasetChunkSplitModeEnum.PARAGRAPH.getValue().equals(chunkSplitMode)) {
                    // 段落拆分
                    textSegmentList = this.paragraphSplitter(document, chunkSize);
                } else if (DatasetChunkSplitModeEnum.SIZE.getValue().equals(chunkSplitMode)) {
                    // 长度拆分
                    textSegmentList = this.characterSplitter(document, chunkSize);
                } else if (DatasetChunkSplitModeEnum.CHAR.getValue().equals(chunkSplitMode)) {
                    // 分隔符拆分
                    String chunkSplitter = dto.getChunkSplitter();
                    textSegmentList = this.delimiterSplitter(document, chunkSplitter);
                }
            } else {
                // 自动的情况下，则先按段落分，在按长度分
                textSegmentList = this.paragraphSplitter(document, DEFAULT_MAX_CHUNK_SIZE);
            }
        }
        return textSegmentList;
    }


    /**
     * 计算重叠大小
     * @param maxSegmentSizeInChars 最大段落长度
     * @return 重叠大小
     */
    private int calculateOverlapSize(int maxSegmentSizeInChars) {
        int overlapSize = maxSegmentSizeInChars / 5;
        return Math.max(overlapSize, 1);
    }

    /**
     * 按字符拆分
     * @param document
     * @param maxSegmentSizeInChars 最大字符的长度
     * @return
     */
    public List<TextSegment> characterSplitter(dev.langchain4j.data.document.Document document, Integer maxSegmentSizeInChars) {
        int overlapSize = calculateOverlapSize(maxSegmentSizeInChars);
        DocumentSplitter characterSplitter = new DocumentByCharacterSplitter(maxSegmentSizeInChars, overlapSize);
        return characterSplitter.split(document);
    }

    /**
     * 段落拆分
     * @param document 拆分的代码
     * @param maxSegmentSizeInChars 每个段落最大的长度
     * @return
     */
    public List<TextSegment> paragraphSplitter(dev.langchain4j.data.document.Document document, Integer maxSegmentSizeInChars) {
        int overlapSize = calculateOverlapSize(maxSegmentSizeInChars);
        DocumentSplitter paragraphSplitter = new DocumentByParagraphSplitter(maxSegmentSizeInChars, overlapSize);
        return paragraphSplitter.split(document);
    }

    /**
     * 分隔符拆分
     * @param document 要拆分的文档
     * @param chunkSplitter 分隔符
     * @return 拆分后的文本片段列表
     */
    public List<TextSegment> delimiterSplitter(dev.langchain4j.data.document.Document document, String chunkSplitter) {
        DocumentByRegexSplitter documentByRegexSplitter = new DocumentByRegexSplitter(chunkSplitter, "", DEFAULT_MAX_CHUNK_SIZE, calculateOverlapSize(DEFAULT_MAX_CHUNK_SIZE));
        return documentByRegexSplitter.split(document);
    }

    /**
     * 向量文件解析，根据不同的文件类型找到不同的解析逻辑
     * @param sourceFile
     * @return
     */
    public dev.langchain4j.data.document.Document readFile(File sourceFile) {
        String name = sourceFile.getName();
        String extension = FileNameUtils.getExtension(name);

        for (IVectorFileReader vectorFileReader : vectorFileReaderList) {
            if (vectorFileReader.supportFileType().contains(extension)) {
                return vectorFileReader.readFile(sourceFile);
            }
        }
        return null;
    }
}
