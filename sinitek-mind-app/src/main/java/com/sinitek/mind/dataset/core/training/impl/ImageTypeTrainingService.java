package com.sinitek.mind.dataset.core.training.impl;

import com.sinitek.mind.dataset.core.training.IFileTrainingService;
import com.sinitek.mind.dataset.core.training.support.ImageTrainingSupport;
import com.sinitek.mind.dataset.dto.CreateCollectionResponseDTO;
import com.sinitek.mind.dataset.dto.DatasetCollectionDTO;
import com.sinitek.mind.dataset.enumerate.DatasetCollectionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 图片类型训练服务实现类
 *
 * <AUTHOR>
 * date 2025-08-22
 * 描述：处理图片类型的知识库集合训练
 */
@Service
@Slf4j
public class ImageTypeTrainingService implements IFileTrainingService {

    @Autowired
    private ImageTrainingSupport imageTrainingSupport;

    @Override
    public CreateCollectionResponseDTO training(DatasetCollectionDTO dto, File file) {
        return imageTrainingSupport.createImagesTraining(file, dto);
    }

    @Override
    public String getSupportedType() {
        return DatasetCollectionTypeEnum.IMAGES.getCode();
    }
}
