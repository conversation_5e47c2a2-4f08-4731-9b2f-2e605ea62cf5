package com.sinitek.mind.dataset.core.reader.impl;

import com.sinitek.mind.dataset.core.reader.IVectorFileReader;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.loader.FileSystemDocumentLoader;
import dev.langchain4j.data.document.parser.TextDocumentParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashSet;
import java.util.Set;

/**
 * Markdown 文件解析
 *
 * <AUTHOR>
 * date 2025/07/22
 */
@Slf4j
@Service
public class MarkdownVectorFileReader implements IVectorFileReader {

    @Override
    public Set<String> supportFileType() {
        Set<String> fileTypeSet = new HashSet<>();
        fileTypeSet.add("md");
        return fileTypeSet;
    }

    @Override
    public Document readFile(File sourceFile) {
        return FileSystemDocumentLoader.loadDocument(sourceFile.toPath(), new TextDocumentParser());
    }
}
