package com.sinitek.mind.dataset.service;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.entity.DatasetCollection;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.Optional;

/**
 * dataset_collections 服务接口
 *
 * <AUTHOR>
 * date 2025-07-17
 * 描述：知识库集合（文件夹/文件）表Service接口
 */
public interface IDatasetCollectionService {

    /**
     * 创建
     */
    String save(DatasetCollectionDTO dto);


    /**
     * 更新数据集集合名称
     * @param dto 更新DTO
     */
    void updateName(DatasetCollectionUpdateDTO dto);

    /**
     * 删除
     */
    void delete(String id);

    /**
     * 查询详情
     */
    DatasetCollectionDetailDTO detail(String id);

    /**
     * 分页查询知识库集合
     */
    PageResult<DatasetCollectionDTO> pageListV2(DatasetCollectionListV2PageParamDTO param);

    /**
     * 基于文件ID创建知识库集合
     *
     * @param params 创建参数
     * @return 创建响应
     */
    CreateCollectionResponseDTO createByFileId(FileIdCreateDatasetCollectionParamsDTO params);

    /**
     * 重新训练知识库集合
     *
     * @param params 训练参数
     * @return 训练响应
     */
    CreateCollectionResponseDTO reTrainingCollection(FileIdCreateDatasetCollectionParamsDTO params);

    /**
     * 基于文本创建知识库集合
     *
     * @param textCreateDatasetCollectionParamsDTO 创建参数
     * @return 创建响应
     */
    CreateCollectionResponseDTO createByText(TextCreateDatasetCollectionParamsDTO textCreateDatasetCollectionParamsDTO);

    /**
     * 从文件创建集合并插入数据
     *
     * @param dto 集合DTO
     * @param file 文件输入流
     * @return 创建响应
     */
    CreateCollectionResponseDTO createFromCommonFile(DatasetCollectionDTO dto, File file);

    /**
     * 通过ID获取集合名称
     * @param id 集合ID
     * @return 集合名称
     */
    String getNameById(String id);

    /**
     * 通过名称获取集合ID
     * @param name 集合名称
     * @return 集合ID
     */
    String getIdByName(String name);

    /**
     * 根据 ID 获取集合
     * @param id 集合 ID
     * @return 集合实体
     */
    Optional<DatasetCollection> getById(String id);

    /**
     * 通过 ID 获取集合DTO
     * @param id 集合 ID
     * @return 集合DTO
     */
    DatasetCollectionDTO getDatasetCollectionDTOById(String id);

    /**
     * 移动数据集集合
     * @param dto 移动DTO
     */
    void move(DatasetCollectionMoveDTO dto);

    /**
     * 获取路径列表
     * @param parentId 父级ID
     * @return 路径列表
     */
    List<DatasetPathDTO> getPaths(String parentId);

    /**
     * 通过集合ID列表获取集合文件信息列表
     * @param collectionIdList 集合ID列表
     * @return 集合文件信息列表
     */
    List<CollectionFileDTO> getCollectionFilesByIds(List<String> collectionIdList);

    /**
     * 根据知识库ID查询所有集合
     * @param datasetId 知识库ID
     * @return 集合列表
     */
    List<DatasetCollection> findAllByDatasetId(String datasetId);

    /**
     * 上传图片并识别内容创建知识库集合
     * 
     * @param file 图片文件
     * @param data 创建参数
     * @return 创建响应
     */
    CreateCollectionResponseDTO createByImage(MultipartFile file, CreateDatasetCollectionImageDTO data);
}