package com.sinitek.mind.dataset.service.impl;

import com.sinitek.mind.common.util.MongoDBUtil;
import com.sinitek.mind.core.dataset.enumerate.DatasetSearchModeEnum;
import com.sinitek.mind.core.dataset.enumerate.SearchScoreTypeEnum;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.entity.DatasetData;
import com.sinitek.mind.dataset.service.IDatasetCollectionService;
import com.sinitek.mind.dataset.service.IDatasetSearchService;
import com.sinitek.mind.dataset.core.vector.IDatasetVectorStore;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.model.core.llm.service.IModelChatService;
import com.sinitek.mind.model.core.rerank.RerankModelFactory;
import com.sinitek.mind.model.core.rerank.RerankModelSupport;
import com.sinitek.mind.model.util.TokenCalculatorUtil;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import dev.langchain4j.data.document.Metadata;
import dev.langchain4j.model.scoring.ScoringModel;
import dev.langchain4j.rag.content.Content;
import dev.langchain4j.rag.content.ContentMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.TextCriteria;
import org.springframework.data.mongodb.core.query.TextQuery;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 知识库搜索服务实现类
 *
 * <AUTHOR>
 * date 2025-08-15
 */
@Slf4j
@Service
public class DatasetSearchServiceImpl implements IDatasetSearchService {

    @Autowired
    private IAuthService authService;

    @Autowired
    private IDatasetVectorStore datasetVectorStore;

    @Autowired
    private RerankModelFactory rerankModelFactory;

    @Autowired
    private RerankModelSupport rerankModelSupport;

    @Autowired
    private IDatasetCollectionService datasetCollectionService;

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private IModelChatService modelChatService;

    @Override
    public SearchTestResponseDTO searchTest(SearchTestRequestDTO request) {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();
        datasetService.checkDatasetPermission(request.getDatasetId(), userId, teamId, tmbId);

        String searchMode = request.getSearchMode();
        if (StringUtils.isBlank(searchMode)) {
            searchMode = DatasetSearchModeEnum.EMBEDDING.getValue();
        }
        Boolean usingReRank = request.getUsingReRank();
        Integer limit = request.getLimit();
        Double similarity = request.getSimilarity();
        String rerankModel = request.getRerankModel();
        long startTime = System.currentTimeMillis();

        String query = request.getText();

        // 问题优化
        query = this.optimizeQuery(query, request);

        // 调用抽取的方法获取搜索结果
        List<Document> datasetDocumentList = this.getSearchResultsByMode(searchMode, query, request);

        // 获取集合文件映射
        Map<String, CollectionFileDTO> collectionFileMap = this.getCollectionFileMap(datasetDocumentList);
        List<DatasetSearchResultDTO> resultList = this.buildSearchResultList(datasetDocumentList, collectionFileMap, request);

        // 进行重排模型的排序
        Boolean finalUsingReRank = applyRerankModelSort(request, usingReRank, rerankModel, query, datasetDocumentList, resultList);

        // 最低相关度过滤
        resultList = this.filterBySimilarity(request, finalUsingReRank, resultList);

        // 搜索耗时
        long endTime = System.currentTimeMillis();
        double duration = (endTime - startTime) / 1000.0;

        // 转换结果
        SearchTestResponseDTO response = new SearchTestResponseDTO();
        // 设置新增属性值
        response.setDuration(String.format("%.3fs", duration));
        response.setUsingReRank(finalUsingReRank);
        response.setSearchMode(searchMode);
        response.setLimit(limit);
        response.setSimilarity(similarity);
        response.setUsingSimilarityFilter(true);
        response.setRerankWeight(request.getRerankWeight());
        response.setEmbeddingWeight(request.getEmbeddingWeight());

        response.setList(resultList);
        return response;
    }

    /**
     * 优化查询问题
     * @param originalQuery 原始查询
     * @param request 知识库搜索信息
     * @return 优化后的查询
     */
    private String optimizeQuery(String originalQuery, SearchTestRequestDTO request) {
        Boolean usingExtensionQuery = request.getDatasetSearchUsingExtensionQuery();
        if (!usingExtensionQuery) {
            return originalQuery;
        }

        String extensionModel = request.getDatasetSearchExtensionModel();
        String extensionBg = request.getDatasetSearchExtensionBg();
        if (StringUtils.isNotBlank(extensionModel) && StringUtils.isNotBlank(extensionBg)) {
            try {
                // 构造问题优化的提示词
                String prompt = String.format("基于以下对话背景：\n%s\n优化用户的查询：%s\n要求：快速补充缺失信息，使查询更完整、明确，提高知识库搜索精度，返回优化后的结果，如果用户的问题已完善则不处理直接返回。",
                        extensionBg, originalQuery);

                // 调用模型服务进行问题优化
                String optimizedQuery = modelChatService.chat(prompt, extensionModel);
                if (StringUtils.isNotBlank(optimizedQuery)) {
                    log.error("优化后的问题: {}", optimizedQuery);
                    return optimizedQuery;
                }
            } catch (Exception e) {
                log.error("知识库问题优化失败, AI模型为: {}", extensionModel, e);
            }
        }

        // 优化失败或未开启优化，返回原始查询
        return originalQuery;
    }

    /**
     * 根据最低相关度过滤搜索结果
     * @param request 搜索请求DTO
     * @param finalUsingReRank 是否使用了重排模型
     * @param resultList 搜索结果列表
     * @return 过滤后的搜索结果列表
     */
    private List<DatasetSearchResultDTO> filterBySimilarity(SearchTestRequestDTO request, Boolean finalUsingReRank, List<DatasetSearchResultDTO> resultList) {
        Double filterSimilarity = request.getSimilarity();
        if (ObjectUtils.isEmpty(filterSimilarity) || filterSimilarity <= 0) {
            return resultList;
        }

        List<DatasetSearchResultDTO> filteredList = new ArrayList<>();
        String currentSearchMode = request.getSearchMode();

        for (DatasetSearchResultDTO result : resultList) {
            boolean passFilter = false;
            List<DatasetSearchScoreDTO> scores = result.getScore();

            if (finalUsingReRank) {
                // 使用重排模型评分过滤
                for (DatasetSearchScoreDTO score : scores) {
                    if (SearchScoreTypeEnum.RE_RANK.getValue().equals(score.getType()) && score.getValue() >= filterSimilarity) {
                        passFilter = true;
                        break;
                    }
                }
            } else if (DatasetSearchModeEnum.EMBEDDING.getValue().equals(currentSearchMode)) {
                // 使用索引模型评分过滤
                for (DatasetSearchScoreDTO score : scores) {
                    if (SearchScoreTypeEnum.EMBEDDING.getValue().equals(score.getType()) && score.getValue() >= filterSimilarity) {
                        passFilter = true;
                        break;
                    }
                }
            } else {
                // 其他情况不进行过滤
                passFilter = true;
            }

            if (passFilter) {
                filteredList.add(result);
            }
        }
        return filteredList;
    }

    @NotNull
    private Boolean applyRerankModelSort(SearchTestRequestDTO request, Boolean usingReRank, String rerankModel, String query, List<Document> datasetDocumentList, List<DatasetSearchResultDTO> resultList) {
        // 重排模型排序优化
        Map<String, Content> rerankMap = new HashMap<>();
        if (usingReRank && StringUtils.isNotBlank(rerankModel)) {
            ScoringModel scoringModel = rerankModelFactory.getRerankModel(rerankModel);
            rerankMap = rerankModelSupport.rerankDocumentMap(query, datasetDocumentList, scoringModel, null);
        } else {
            usingReRank = false;
        }

        // 重排模型非生效
        if (!usingReRank || MapUtils.isEmpty(rerankMap)) {
            return false;
        }

        // 组装重排模型的结果
        for (DatasetSearchResultDTO datasetSearchResultDTO : resultList) {
            String id = datasetSearchResultDTO.getId();

            List<DatasetSearchScoreDTO> scoreList = datasetSearchResultDTO.getScore();
            Content content = rerankMap.get(id);
            if (ObjectUtils.isNotEmpty(content)) {
                Metadata metadata = content.textSegment().metadata();

                Object rerankedScoreObj = content.metadata().get(ContentMetadata.RERANKED_SCORE);
                DatasetSearchScoreDTO score = new DatasetSearchScoreDTO();
                score.setType(SearchScoreTypeEnum.RE_RANK.getValue());
                if (metadata.containsKey("index")) {
                    int index = metadata.getInteger("index");
                    score.setIndex(index);
                }
                score.setValue((Double) rerankedScoreObj);
                scoreList.add(score);
            }
        }

        // 应用RRF综合排名算法
        Double rerankWeight = request.getRerankWeight() != null ? request.getRerankWeight() : 0.5;
        Double embeddingWeight = request.getEmbeddingWeight() != null ? request.getEmbeddingWeight() : 0.5;
        Double fulltextWeight = request.getFulltextWeight() != null ? request.getFulltextWeight() : 0.5;

        // 归一化权重
        double totalWeight = rerankWeight + embeddingWeight + fulltextWeight;
        double normalizedRerankWeight = rerankWeight / totalWeight;
        double normalizedEmbeddingWeight = embeddingWeight / totalWeight;
        double normalizedFulltextWeight = fulltextWeight / totalWeight;

        // 定义RRF常数
        final int RRF_K = 60;

        // 计算每个结果的综合RRF得分
        for (DatasetSearchResultDTO result : resultList) {
            List<DatasetSearchScoreDTO> scores = result.getScore();
            double rrfScore = 0.0;

            // 查找embedding、reRank和fulltext的排名
            Integer embeddingRank = null;
            Integer reRankRank = null;
            Integer fulltextRank = null;

            for (DatasetSearchScoreDTO score : scores) {
                if (SearchScoreTypeEnum.EMBEDDING.getValue().equals(score.getType())) {
                    // embedding排名是原始索引+1（排名从1开始）
                    embeddingRank = score.getIndex() + 1;
                } else if (SearchScoreTypeEnum.RE_RANK.getValue().equals(score.getType()) && score.getIndex() != null) {
                    // reRank排名是重排后的索引+1
                    reRankRank = score.getIndex() + 1;
                } else if (SearchScoreTypeEnum.FULL_TEXT.getValue().equals(score.getType()) && score.getIndex() != null) {
                    // fulltext排名是原始索引+1
                    fulltextRank = score.getIndex() + 1;
                }
            }

            // 计算RRF得分
            if (embeddingRank != null) {
                rrfScore += normalizedEmbeddingWeight * (1.0 / (embeddingRank + RRF_K));
            }
            if (reRankRank != null) {
                rrfScore += normalizedRerankWeight * (1.0 / (reRankRank + RRF_K));
            }
            if (fulltextRank != null) {
                rrfScore += normalizedFulltextWeight * (1.0 / (fulltextRank + RRF_K));
            }

            // 添加RRF综合得分到score列表
            DatasetSearchScoreDTO rrfScoreDTO = new DatasetSearchScoreDTO();
            rrfScoreDTO.setType(SearchScoreTypeEnum.RRF.getValue());
            rrfScoreDTO.setValue(rrfScore);
            scores.add(rrfScoreDTO);
        }

        // 按RRF得分排序
        resultList.sort((a, b) -> {
            // 查找a和b的RRF得分
            double aScore = 0.0;
            double bScore = 0.0;

            for (DatasetSearchScoreDTO score : a.getScore()) {
                if (SearchScoreTypeEnum.RRF.getValue().equals(score.getType())) {
                    aScore = score.getValue();
                    break;
                }
            }

            for (DatasetSearchScoreDTO score : b.getScore()) {
                if (SearchScoreTypeEnum.RRF.getValue().equals(score.getType())) {
                    bScore = score.getValue();
                    break;
                }
            }

            // 降序排序
            return Double.compare(bScore, aScore);
        });

        for (int i = 0; i < resultList.size(); i++) {
            DatasetSearchResultDTO datasetSearchResultDTO = resultList.get(i);
            List<DatasetSearchScoreDTO> score = datasetSearchResultDTO.getScore();
            if (score.size() == 3) {
                DatasetSearchScoreDTO datasetSearchScoreDTO = score.get(2);
                datasetSearchScoreDTO.setIndex(i);
            }
        }
        return usingReRank;
    }


    /**
     * 全文搜索实现
     * @param query 搜索查询文本
     * @param datasetId 数据集ID
     * @return 搜索结果列表
     */
    private List<Document> fulltextSearch(String query, String datasetId) {
        List<Document> results = new ArrayList<>();

        // 创建全文检索查询
        // 使用matchingAny进行分词匹配，这样可以匹配包含任一关键词的文档
        String[] queryResult = query.chars()
                .mapToObj(c -> String.valueOf((char) c))
                .toArray(String[]::new);

        TextCriteria criteria = TextCriteria.forDefaultLanguage().matchingAny(queryResult);
        TextQuery textQuery = TextQuery.queryText(criteria).sortByScore();

        // 添加数据集ID条件
        textQuery.addCriteria(Criteria.where("datasetId").is(datasetId));

        // 执行查询
        List<DatasetData> datasetDataList = mongoTemplate.find(textQuery, DatasetData.class);

        // 转换为Document对象并获取得分
        for (int i = 0; i < datasetDataList.size(); i++) {
            DatasetData data = datasetDataList.get(i);
            String q = data.getQ();
            String a = data.getA();

            // 获取匹配得分
            // 使用Document构造器创建投影文档，避免转义问题
            org.bson.Document projection = new org.bson.Document("score", new org.bson.Document("$meta", "textScore"));
            org.bson.Document datasetDatas = mongoTemplate.getCollection("dataset_datas")
                    .find(textQuery.getQueryObject())
                    .projection(projection)
                    .skip(i)
                    .limit(1)
                    .first();
            Double score = null;
            if (ObjectUtils.isNotEmpty(datasetDatas) && !datasetDatas.isEmpty()) {
                score = datasetDatas.getDouble("score");
            }

            // 设置元数据
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("datasetId", data.getDatasetId());
            metadata.put("collectionId", data.getCollectionId());
            metadata.put("dataId", data.getId());
            metadata.put("score", score);
            metadata.put("content", a);

            Document doc = Document.builder()
                    .id(data.getId())
                    .text(q)
                    .metadata(metadata)
                    .score(score)
                    .build();

            results.add(doc);
        }

        return results;
    }

    /**
     * 合并搜索结果
     * @param vectorResults 向量搜索结果
     * @param fulltextResults 全文搜索结果
     * @param request 搜索请求
     * @return 合并后的结果
     */
    private List<Document> mergeSearchResults(List<Document> vectorResults, List<Document> fulltextResults, SearchTestRequestDTO request) {
        // 获取权重参数
        double embeddingWeight = request.getEmbeddingWeight() != null ? request.getEmbeddingWeight() : 0.5;
        double fulltextWeight = request.getFulltextWeight() != null ? request.getFulltextWeight() : 0.5;

        // 归一化权重
        double totalWeight = embeddingWeight + fulltextWeight;
        double normalizedEmbeddingWeight = embeddingWeight / totalWeight;
        double normalizedFulltextWeight = fulltextWeight / totalWeight;

        // 定义RRF常数
        final int RRF_K = 60;

        // 创建一个Map来存储合并后的结果，使用dataId作为键
        Map<String, Document> mergedMap = new HashMap<>();

        // 处理向量搜索结果，计算RRF分数
        for (int index = 0; index < vectorResults.size(); index++) {
            Document doc = vectorResults.get(index);
            String dataId = doc.getMetadata().get("dataId").toString();
            int rank = index + 1;
            double rrfScore = normalizedEmbeddingWeight * (1.0 / (rank + RRF_K));

            // 存储RRF分数到文档元数据
            doc.getMetadata().put("embeddingRrfScore", rrfScore);
            doc.getMetadata().put("rank", rank);
            mergedMap.put(dataId, doc);
        }

        // 处理全文搜索结果，计算RRF分数
        for (int index = 0; index < fulltextResults.size(); index++) {
            Document doc = fulltextResults.get(index);
            String dataId = doc.getMetadata().get("dataId").toString();
            int rank = index + 1;
            double rrfScore = normalizedFulltextWeight * (1.0 / (rank + RRF_K));

            if (mergedMap.containsKey(dataId)) {
                // 如果文档已存在，累加RRF分数
                Document existingDoc = mergedMap.get(dataId);
                double existingRrfScore = existingDoc.getMetadata().containsKey("embeddingRrfScore") ?
                        (double) existingDoc.getMetadata().get("embeddingRrfScore") : 0.0;
                double totalRrfScore = existingRrfScore + rrfScore;
                existingDoc.getMetadata().put("fulltextRrfScore", rrfScore);
                existingDoc.getMetadata().put("totalRrfScore", totalRrfScore);
            } else {
                // 如果文档不存在，直接添加
                doc.getMetadata().put("fulltextRrfScore", rrfScore);
                doc.getMetadata().put("totalRrfScore", rrfScore);
                doc.getMetadata().put("rank", rank);
                mergedMap.put(dataId, doc);
            }
        }

        // 转换为列表并按综合RRF分数排序
        List<Document> mergedResults = new ArrayList<>(mergedMap.values());
        mergedResults.sort((d1, d2) -> {
            // 获取综合RRF分数
            double score1 = d1.getMetadata().containsKey("totalRrfScore") ?
                    (double) d1.getMetadata().get("totalRrfScore") : 0.0;
            double score2 = d2.getMetadata().containsKey("totalRrfScore") ?
                    (double) d2.getMetadata().get("totalRrfScore") : 0.0;

            // 如果分数相同，根据scope属性排序
            if (Double.compare(score1, score2) == 0) {
                String scope1 = d1.getMetadata().getOrDefault("scope", "").toString();
                String scope2 = d2.getMetadata().getOrDefault("scope", "").toString();
                return scope1.compareTo(scope2);
            }

            return Double.compare(score2, score1); // 降序排序
        });

        return mergedResults;
    }

    /**
     * 构建搜索结果列表
     * @param datasetDocumentList 文档列表
     * @param collectionFileMap 集合文件映射
     * @param request 搜索请求
     * @return 搜索结果列表
     */
    private List<DatasetSearchResultDTO> buildSearchResultList(List<Document> datasetDocumentList, Map<String, CollectionFileDTO> collectionFileMap, SearchTestRequestDTO request) {
        List<DatasetSearchResultDTO> resultList = new ArrayList<>();
        for (int i = 0; i < datasetDocumentList.size(); i++) {
            Document doc = datasetDocumentList.get(i);
            DatasetSearchResultDTO result = new DatasetSearchResultDTO();
            result.setId(doc.getId());
            result.setUpdateTime(doc.getMetadata().getOrDefault("updateTime", "").toString());
            result.setQ(doc.getText());
            result.setA("");
            result.setChunkIndex(i);
            result.setDatasetId(request.getDatasetId());
            String collectionId = doc.getMetadata().getOrDefault("collectionId", "").toString();
            result.setCollectionId(collectionId);

            // 通过collectionId获取文件信息
            CollectionFileDTO collectionFileDTO = collectionFileMap.get(collectionId);
            if (collectionFileDTO != null) {
                result.setSourceId(collectionFileDTO.getFileId());
                result.setSourceName(collectionFileDTO.getFileName());
            } else {
                result.setSourceId("");
                result.setSourceName("");
            }

            // 设置分数
            List<DatasetSearchScoreDTO> scores = new ArrayList<>();
            String searchMode = request.getSearchMode();

            // 添加向量搜索分数
            if (DatasetSearchModeEnum.EMBEDDING.getValue().equals(searchMode) || DatasetSearchModeEnum.MIXED_RECALL.getValue().equals(searchMode)) {
                DatasetSearchScoreDTO embeddingScore = new DatasetSearchScoreDTO();
                embeddingScore.setType(SearchScoreTypeEnum.EMBEDDING.getValue());
                embeddingScore.setValue(doc.getScore());
                embeddingScore.setIndex(i);
                scores.add(embeddingScore);
            }

            // 添加全文搜索分数
            if (DatasetSearchModeEnum.FULL_TEXT_RECALL.getValue().equals(searchMode) || DatasetSearchModeEnum.MIXED_RECALL.getValue().equals(searchMode)) {
                DatasetSearchScoreDTO fulltextScore = new DatasetSearchScoreDTO();
                fulltextScore.setType(SearchScoreTypeEnum.FULL_TEXT.getValue());
                fulltextScore.setValue(doc.getScore());
                fulltextScore.setIndex(i);
                scores.add(fulltextScore);
            }

            result.setScore(scores);
            result.setTokens(TokenCalculatorUtil.estimateTokens(doc.getText()));

            // 过滤 tokens 在引用上限的范围内
            if (result.getTokens() <= request.getLimit()) {
                resultList.add(result);
            }
        }
        return resultList;
    }


    /**
     * 根据文档列表获取集合文件映射
     * @param documentList 文档列表
     * @return 集合ID到集合文件DTO的映射
     */
    private Map<String, CollectionFileDTO> getCollectionFileMap(List<Document> documentList) {
        // 收集所有collectionId
        Set<String> collectionIdSet = new HashSet<>();
        for (Document doc : documentList) {
            String collectionId = doc.getMetadata().getOrDefault("collectionId", "").toString();
            if (StringUtils.isNotBlank(collectionId)) {
                collectionIdSet.add(collectionId);
            }
        }

        // 通过collectionIdList获取集合文件信息
        List<CollectionFileDTO> collectionFileDTOList = datasetCollectionService.getCollectionFilesByIds(new ArrayList<>(collectionIdSet));

        // 创建collectionId到CollectionFileDTO的映射
        Map<String, CollectionFileDTO> collectionFileMap = new HashMap<>();
        for (CollectionFileDTO dto : collectionFileDTOList) {
            collectionFileMap.put(dto.getCollectionId(), dto);
        }

        return collectionFileMap;
    }



    /**
     * 根据搜索模式获取搜索结果
     *
     * @param searchMode 搜索模式
     * @param query 搜索查询
     * @param request 搜索请求
     * @return 搜索结果列表
     */
    private List<Document> getSearchResultsByMode(String searchMode, String query, SearchTestRequestDTO request) {
        List<Document> datasetDocumentList = new ArrayList<>();

        // 语义检索
        if (DatasetSearchModeEnum.EMBEDDING.getValue().equals(searchMode) || DatasetSearchModeEnum.MIXED_RECALL.getValue().equals(searchMode)) {
            datasetDocumentList = searchVectorResults(query, request.getDatasetId());
        }

        // 全文检索
        if (DatasetSearchModeEnum.FULL_TEXT_RECALL.getValue().equals(searchMode) || DatasetSearchModeEnum.MIXED_RECALL.getValue().equals(searchMode)) {
            List<Document> fulltextResults = fulltextSearch(query, request.getDatasetId());
            if (DatasetSearchModeEnum.FULL_TEXT_RECALL.getValue().equals(searchMode)) {
                datasetDocumentList = fulltextResults;
            } else if (DatasetSearchModeEnum.MIXED_RECALL.getValue().equals(searchMode)) {
                // 混合搜索：合并两种搜索结果
                datasetDocumentList = mergeSearchResults(datasetDocumentList, fulltextResults, request);
            }
        }

        return datasetDocumentList;
    }

    /**
      * 搜索向量结果
      * 
      * @param query 搜索查询
      * @param datasetId 数据集ID
      * @return 调整后的文档列表
      */
     private List<Document> searchVectorResults(String query, String datasetId) {
         List<Document> results = datasetVectorStore.similaritySearch(query, Collections.singletonList(datasetId));
         List<Document> adjustedResults = new ArrayList<>();
         
         // 统一返回结果的id为 dataId
         for (Document doc : results) {
             Map<String, Object> metadata = doc.getMetadata();

             if (metadata.containsKey("dataId")) {
                 String id = metadata.get("dataId").toString();
                 Document adjustedDoc = Document.builder()
                         .text(doc.getText())
                         .id(id)
                         .metadata(metadata)
                         .score(doc.getScore())
                         .build();
                 adjustedResults.add(adjustedDoc);
             } else {
                 adjustedResults.add(doc);
             }
         }
         
         return adjustedResults;
     }

}