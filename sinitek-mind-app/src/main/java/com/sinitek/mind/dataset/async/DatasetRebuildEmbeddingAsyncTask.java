package com.sinitek.mind.dataset.async;

import com.sinitek.mind.dataset.core.training.FileTrainingServiceFactory;
import com.sinitek.mind.dataset.core.training.IFileTrainingService;
import com.sinitek.mind.dataset.dto.DatasetCollectionDTO;
import com.sinitek.mind.dataset.entity.DatasetCollection;
import com.sinitek.mind.dataset.enumerate.DatasetCollectionTypeEnum;
import com.sinitek.mind.dataset.repository.DatasetDataRepository;
import com.sinitek.mind.dataset.service.IDatasetCollectionService;
import com.sinitek.mind.support.common.service.IFileService;
import com.sinitek.mind.support.common.constant.FileConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * 知识库向量重建异步任务
 *
 * <AUTHOR>
 * date 2025-08-22
 * 描述：异步处理知识库向量重建任务，根据集合类型使用不同的解析器重新解析，解析前删除已有的DatasetData
 */
@Slf4j
@Component
public class DatasetRebuildEmbeddingAsyncTask {

    @Autowired
    private IDatasetCollectionService datasetCollectionService;

    @Autowired
    private DatasetDataRepository datasetDataRepository;

    @Autowired
    private FileTrainingServiceFactory fileTrainingServiceFactory;

    @Autowired
    private IFileService fileService;

    /**
     * 支持的类型列表
     */
    private static final List<String> SUPPORTED_TYPES = Arrays.asList(
        DatasetCollectionTypeEnum.FILE.getCode(),
        DatasetCollectionTypeEnum.IMAGES.getCode()
    );

    /**
     * 异步重建向量数据
     * @param datasetId 知识库ID
     */
    @Async
    public void rebuildEmbeddingAsync(String datasetId) {
        try {
            log.info("开始异步重建知识库向量数据，datasetId: {}", datasetId);
            
            // 1. 查询出该知识库下的所有集合
            List<DatasetCollection> collections = datasetCollectionService.findAllByDatasetId(datasetId);
            if (CollectionUtils.isEmpty(collections)) {
                log.warn("知识库下没有找到任何集合，datasetId: {}", datasetId);
                return;
            }
            
            log.info("找到知识库集合数量: {}, datasetId: {}", collections.size(), datasetId);
            
            // 2. 遍历每个集合，根据类型使用不同的解析方案
            for (DatasetCollection collection : collections) {
                try {
                    processCollectionForRebuilding(collection);
                } catch (Exception e) {
                    log.error("处理集合重建失败，collectionId: {}, collectionName: {}, error: {}", 
                        collection.getId(), collection.getName(), e.getMessage(), e);
                }
            }
            
            log.info("知识库向量数据重建完成，datasetId: {}", datasetId);
        } catch (Exception e) {
            log.error("异步重建知识库向量数据失败，datasetId: {}, error: {}", datasetId, e.getMessage(), e);
        }
    }

    /**
     * 处理单个集合的重建
     * @param collection 集合信息
     */
    private void processCollectionForRebuilding(DatasetCollection collection) {
        String collectionId = collection.getId();
        String collectionName = collection.getName();
        String collectionType = collection.getType();
        
        log.info("开始处理集合重建，collectionId: {}, collectionName: {}, collectionType: {}", 
            collectionId, collectionName, collectionType);
        
        // 检查是否支持该类型
        if (!SUPPORTED_TYPES.contains(collectionType)) {
            log.info("跳过不支持的类型，collectionId: {}, collectionName: {}, collectionType: {}", 
                collectionId, collectionName, collectionType);
            return;
        }
        
        // 检查是否有文件ID
        String fileId = collection.getFileId();
        if (StringUtils.isBlank(fileId)) {
            log.warn("集合没有文件ID，跳过处理，collectionId: {}, collectionName: {}", 
                collectionId, collectionName);
            return;
        }
        
        try {
            // 1. 删除该集合下已有的DatasetData
            deleteExistingDatasetData(collectionId);
            
            // 2. 获取文件
            File file = getFileById(fileId);
            if (file == null) {
                log.error("无法获取文件，fileId: {}, collectionId: {}", fileId, collectionId);
                return;
            }
            
            // 3. 转换为DTO
            DatasetCollectionDTO collectionDTO = convertToDTO(collection);
            
            // 4. 使用对应的训练服务重新解析
            IFileTrainingService trainingService = fileTrainingServiceFactory.getService(collectionType);
            trainingService.training(collectionDTO, file);
            
            log.info("集合重建处理完成，collectionId: {}, collectionName: {}", collectionId, collectionName);
        } catch (Exception e) {
            log.error("集合重建处理失败，collectionId: {}, collectionName: {}, error: {}", 
                collectionId, collectionName, e.getMessage(), e);
        }
    }

    /**
     * 删除集合下已有的DatasetData
     * @param collectionId 集合ID
     */
    private void deleteExistingDatasetData(String collectionId) {
        try {
            datasetDataRepository.deleteAllByCollectionIdIn(Arrays.asList(collectionId));
            log.info("删除集合下已有的DatasetData成功，collectionId: {}", collectionId);
        } catch (Exception e) {
            log.error("删除集合下已有的DatasetData失败，collectionId: {}, error: {}", 
                collectionId, e.getMessage(), e);
        }
    }

    /**
     * 根据文件ID获取文件
     * @param fileId 文件ID
     * @return 文件对象
     */
    private File getFileById(String fileId) {
        try {
            // 使用dataset bucket获取文件信息
            com.sinitek.mind.support.common.dto.FullFileDTO fileInfo = fileService.getFileById(FileConstant.DATASET_NAME, fileId);
            if (fileInfo != null && fileInfo.getSourceFile() != null) {
                return fileInfo.getSourceFile();
            } else {
                log.error("无法获取文件信息或文件为空，fileId: {}", fileId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取文件失败，fileId: {}, error: {}", fileId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将DatasetCollection转换为DatasetCollectionDTO
     * @param collection 集合实体
     * @return 集合DTO
     */
    private DatasetCollectionDTO convertToDTO(DatasetCollection collection) {
        DatasetCollectionDTO dto = new DatasetCollectionDTO();
        BeanUtils.copyProperties(collection, dto);
        return dto;
    }
}
