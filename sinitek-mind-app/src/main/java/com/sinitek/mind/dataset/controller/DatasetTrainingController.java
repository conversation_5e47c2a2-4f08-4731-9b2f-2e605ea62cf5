package com.sinitek.mind.dataset.controller;

import java.util.Map;
import java.util.HashMap;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.dataset.dto.DatasetTrainingDTO;
import com.sinitek.mind.dataset.dto.DatasetTrainingQueueDTO;
import com.sinitek.mind.dataset.dto.TrainingErrorQueryDTO;
import com.sinitek.mind.dataset.dto.TrainingRequestDTO;
import com.sinitek.mind.dataset.dto.RebuildEmbeddingRequestDTO;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * dataset_training 控制器
 *
 * <AUTHOR>
 * date 2025-07-25
 */
@RestController
@RequestMapping("/mind/api/core/dataset/training")
@Tag(name = "知识库训练管理")
public class DatasetTrainingController {

    @Autowired
    private IDatasetTrainingService datasetTrainingService;

    @PostMapping("/getTrainingError")
    @Operation(summary = "查询失败的训练数据")
    public ApiResponse<PageResult<DatasetTrainingDTO>> getTrainingError(
            @RequestBody @Valid TrainingErrorQueryDTO queryDTO) {
        PageResult<DatasetTrainingDTO> result = datasetTrainingService.searchFailedTrainings(queryDTO);
        return ApiResponse.success(result);
    }

    @PostMapping("/deleteTrainingData")
    @Operation(summary = "删除失败的训练数据")
    public ApiResponse<Object> deleteFailedTrainings(
            @RequestBody @Valid TrainingRequestDTO requestDTO) {

        datasetTrainingService.removeFailedTraining(
            requestDTO.getDatasetId(),
            requestDTO.getCollectionId(),
            requestDTO.getDataId()
        );
        return ApiResponse.success(null);
    }

    @PostMapping("/getTrainingDataDetail")
    @Operation(summary = "查询单个训练数据详情")
    public ApiResponse<DatasetTrainingDTO> getTrainingDataDetail(
            @RequestBody @Valid TrainingRequestDTO requestDTO) {

        DatasetTrainingDTO detail = datasetTrainingService.getTrainingDataDetail(
            requestDTO.getDatasetId(),
            requestDTO.getCollectionId(),
            requestDTO.getDataId()
        );
        return ApiResponse.success(detail);
    }

   @PostMapping("/updateTrainingData")
    @Operation(summary = "更新训练数据状态为待处理")
    public ApiResponse<Object> updateTrainingData(
            @RequestBody @Valid TrainingRequestDTO requestDTO) {
        datasetTrainingService.updateTrainingStatus(requestDTO.getDatasetId(), requestDTO.getCollectionId(), requestDTO.getDataId(), requestDTO.getQ());
        return ApiResponse.success(null);
    }

    @GetMapping("/getDatasetTrainingQueue")
    @Operation(summary = "获取数据集训练队列状态")
    public ApiResponse<DatasetTrainingQueueDTO> getDatasetTrainingQueue(
            @RequestParam @Parameter(description = "数据集ID") String datasetId) {
        int rebuildingCount = datasetTrainingService.countRebuildingDocuments(datasetId);
        int trainingCount = datasetTrainingService.countTrainingDocuments(datasetId);
        return ApiResponse.success(new DatasetTrainingQueueDTO(rebuildingCount, trainingCount));
    }

    @GetMapping("/getQueueLen")
    @Operation(summary = "获取训练队列长度")
    public ApiResponse<Map<String, Integer>> getQueueLen() {
        Map<String, Integer> result = new HashMap<>();
        result.put("vectorTrainingCount", 0);
        result.put("qaTrainingCount", 0);
        result.put("autoTrainingCount", 0);
        result.put("imageTrainingCount", 0);
        return ApiResponse.success(result);
    }

    @GetMapping("/reset-failed-by-dataset")
    @Operation(summary = "将指定知识库下失败的训练数据批量重置为待处理")
    public ApiResponse<Integer> resetFailedByDataset(
            @RequestParam @Parameter(description = "知识库ID") String datasetId) {
        int updated = datasetTrainingService.resetFailedTrainingsByDatasetId(datasetId);
        return ApiResponse.success(updated);
    }

    @PostMapping("/rebuildEmbedding")
    @Operation(summary = "切换索引模型")
    public ApiResponse<Void> rebuildEmbedding(
            @RequestBody @Valid RebuildEmbeddingRequestDTO requestDTO) {
        datasetTrainingService.rebuildEmbedding(requestDTO.getDatasetId(), requestDTO.getVectorModel());
        return ApiResponse.success();
    }

}