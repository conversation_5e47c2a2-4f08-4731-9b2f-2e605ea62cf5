package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.common.support.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "训练错误查询参数DTO")
public class TrainingErrorQueryDTO extends PageParam {

    @Schema(description = "集合ID")
    private String collectionId;
}