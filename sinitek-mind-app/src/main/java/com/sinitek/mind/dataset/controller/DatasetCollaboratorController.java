package com.sinitek.mind.dataset.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.dataset.dto.DatasetChangeOwnerDTO;
import com.sinitek.mind.dataset.dto.DatasetClbDeleteDTO;
import com.sinitek.mind.dataset.dto.DatasetClbUpdateDTO;
import com.sinitek.mind.dataset.repository.DatasetRepository;
import com.sinitek.mind.support.permission.dto.CollaboratorDTO;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.ICollaboratorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedList;
import java.util.List;

/**
 * 协作者Controller 层
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@RestController
@RequestMapping("/mind/api/core/dataset/collaborator")
@Tag(name = "Dataset协作者管理")
@RequiredArgsConstructor
public class DatasetCollaboratorController {

    @Autowired
    private ICollaboratorService collaboratorService;

    @Autowired
    private DatasetRepository datasetRepository;

    @GetMapping("/list")
    @Operation(summary = "获取Dataset协作者列表")
    public ApiResponse<List<CollaboratorDTO>> getCollaboratorList(@RequestParam String datasetId) {
        return ApiResponse.success(collaboratorService.findClb(datasetId, ResourceTypeEnum.DATASET));
    }

    @PostMapping("/update")
    @Operation(summary = "更新Dataset协作者")
    public ApiResponse<Void> updateCollaborator(@RequestBody DatasetClbUpdateDTO updateDTO) {

        List<String> orgIdList = new LinkedList<>();
        orgIdList.addAll(updateDTO.getOrgs());
        orgIdList.addAll(updateDTO.getMembers());
        orgIdList.addAll(updateDTO.getGroups());
        collaboratorService.updateClb(updateDTO.getDatasetId(), ResourceTypeEnum.DATASET, orgIdList, updateDTO.getPermission());
        return ApiResponse.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除Dataset协作者")
    public ApiResponse<Void> updateCollaborator(@RequestBody DatasetClbDeleteDTO deleteDTO) {

        String orgId = "";
        if (StringUtils.isNotBlank(deleteDTO.getTmbId())) {
            orgId = deleteDTO.getTmbId();
        } else if (StringUtils.isNotBlank(deleteDTO.getGroupId())) {
            orgId = deleteDTO.getGroupId();
        } else if (StringUtils.isNotBlank(deleteDTO.getOrgId())) {
            orgId = deleteDTO.getOrgId();
        }

        collaboratorService.deleteClb(deleteDTO.getDatasetId(), ResourceTypeEnum.DATASET, orgId);
        return ApiResponse.success();
    }


    @PostMapping("/changeOwner")
    @Operation(summary = "更新Dataset拥有者")
    public ApiResponse<Void> changeOwner(@RequestBody DatasetChangeOwnerDTO ownerDTO) {
        if (StringUtils.isBlank(ownerDTO.getDatasetId()) || StringUtils.isBlank(ownerDTO.getOwnerId())) {
            return ApiResponse.error("datasetId或tmbId不能为空");
        }
        datasetRepository.updateTmbIdById(ownerDTO.getDatasetId(), ownerDTO.getOwnerId());
        return ApiResponse.success();
    }
}
