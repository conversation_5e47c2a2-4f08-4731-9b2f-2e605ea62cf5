package com.sinitek.mind.dataset.core.training;

import com.sinitek.mind.dataset.dto.CreateCollectionResponseDTO;
import com.sinitek.mind.dataset.dto.DatasetCollectionDTO;

import java.io.File;

/**
 * 文件训练通用Service层
 *
 * <AUTHOR>
 * date 2025-08-22
 * 描述：文件训练服务接口，支持多种文件类型的训练处理
 */
public interface IFileTrainingService {

    /**
     * 训练知识库集合
     * 注意：传递的DatasetCollectionDTO默认已经有id（保存后再传递进来）
     *
     * @param dto 集合DTO（已保存，包含id）
     * @param file 文件
     * @return 训练响应
     */
    CreateCollectionResponseDTO training(DatasetCollectionDTO dto, File file);

    /**
     * 获取支持的类型
     *
     * @return 支持的类型代码
     */
    String getSupportedType();

    /**
     * 判断是否支持给定文件扩展名（不含点，大小写不敏感）
     *
     * @param extension 文件扩展名，例如: jpg、pdf
     * @return 是否支持
     */
    boolean supportsExtension(String extension);
}
