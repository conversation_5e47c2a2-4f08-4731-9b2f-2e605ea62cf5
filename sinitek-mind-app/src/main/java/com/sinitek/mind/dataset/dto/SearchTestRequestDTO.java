package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "搜索测试请求DTO")
public class SearchTestRequestDTO {

    @NotBlank(message = "{" + DatasetErrorCodeConstant.DATASET_ID_EMPTY + "}")
    @Schema(description = "数据集ID")
    private String datasetId;

    @NotBlank(message = "{" + DatasetErrorCodeConstant.SEARCH_TEXT_EMPTY + "}")
    @Schema(description = "搜索文本")
    private String text;

    @Schema(description = "搜索模式")
    private String searchMode = "embedding";

    @Schema(description = "嵌入权重")
    private Double embeddingWeight = 0.5;

    @Schema(description = "是否使用重排序")
    private Boolean usingReRank = true;

    @Schema(description = "重排序权重")
    private Double rerankWeight = 0.5;

    @Schema(description = "全文搜索权重")
    private Double fulltextWeight = 0.5;

    @Schema(description = "重排序模型")
    private String rerankModel;

    @NotNull(message = "{" + DatasetErrorCodeConstant.LIMIT_FIELD_EMPTY + "}")
    @Schema(description = "限制数量")
    private Integer limit = 5000;

    @Schema(description = "相似度阈值")
    private Double similarity = 0.0;

    @Schema(description = "是否使用问题优化")
    private Boolean datasetSearchUsingExtensionQuery = false;

    @Schema(description = "问题优化的AI模型")
    private String datasetSearchExtensionModel = "Qwen3-32B-AWQ";

    @Schema(description = "问题优化的对话背景描述")
    private String datasetSearchExtensionBg = "";
}