package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.dataset.enumerate.DatasetCollectionTypeEnum;
import com.sinitek.mind.dataset.enumerate.DatasetTrainingModeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * dataset_collections DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库集合（文件夹/文件）表DTO
 */
@Data
public class DatasetCollectionDTO {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "父级ID")
    private String parentId;

    @Schema(description = "团队ID")
    private String teamId;

    @Schema(description = "团队成员ID")
    private String tmbId;

    @Schema(description = "知识库ID")
    private String datasetId;

    @Schema(description = "分块的训练类型")
    private String trainingType;

    /**
     * @see DatasetCollectionTypeEnum
     */
    @Schema(description = "类型（folder/file/images）")
    private String type;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "标签")
    private List<String> tags;

    @Schema(description = "文件ID（type=file时有值）")
    private String fileId;

    @Schema(description = "元数据")
    private Map<String, Object> metadata;

    @Schema(description = "自定义PDF解析")
    private Boolean customPdfParse;

    /**
     * @see DatasetTrainingModeEnum
     */
    @Schema(description = "训练类型")
    private String mode;

    private String chunk;

    @Schema(description = "分块触发类型")
    private String chunkTriggerType;

    @Schema(description = "分块触发最小值")
    private Integer chunkTriggerMinSize;

    @Schema(description = "数据增强集合名")
    private Object dataEnhanceCollectionName;

    @Schema(description = "图片索引")
    private Boolean imageIndex;

    @Schema(description = "自动索引")
    private Boolean autoIndexes;

    @Schema(description = "分块设置模式")
    private String chunkSettingMode;

    @Schema(description = "分块拆分模式")
    private String chunkSplitMode;

    @Schema(description = "段落分块AI模式")
    private String paragraphChunkAIMode;

    @Schema(description = "段落分块深度")
    private Integer paragraphChunkDeep;

    @Schema(description = "段落分块最小值")
    private Integer paragraphChunkMinSize;

    @Schema(description = "分块大小")
    private Integer chunkSize;

    @Schema(description = "索引大小")
    private Integer indexSize;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "hash原文")
    private String hashRawText;

    @Schema(description = "原文长度")
    private Integer rawTextLength;

    @Schema(description = "是否禁用")
    private Boolean forbid;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "集合的数据量")
    private Integer dataAmount;

    @Schema(description = "集合当前的训练数量")
    private Integer trainingAmount;

    @Schema(description = "集合当前是否存在训练异常")
    private boolean hasError;

}