package com.sinitek.mind.dataset.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分块拆分模式枚举
 *
 * <AUTHOR>
 * date 2025-08-12
 */
@Getter
@AllArgsConstructor
public enum DatasetChunkSplitModeEnum {
    /**
     * 段落拆分
     */
    PARAGRAPH("paragraph", "段落拆分"),
    
    /**
     * 长度拆分
     */
    SIZE("size", "长度拆分"),
    
    /**
     * 分隔符拆分
     */
    CHAR("char", "分隔符拆分");
    
    private final String value;
    private final String desc;
}