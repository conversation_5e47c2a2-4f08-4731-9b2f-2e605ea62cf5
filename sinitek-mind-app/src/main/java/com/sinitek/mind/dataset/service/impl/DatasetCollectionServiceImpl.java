package com.sinitek.mind.dataset.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.dataset.constant.DatasetConstant;
import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import com.sinitek.mind.dataset.core.training.support.ImageTrainingSupport;
import com.sinitek.mind.dataset.core.training.IFileTrainingService;
import com.sinitek.mind.dataset.core.training.FileTrainingServiceFactory;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.entity.DatasetCollection;
import com.sinitek.mind.dataset.enumerate.DatasetCollectionTypeEnum;
import com.sinitek.mind.dataset.enumerate.DatasetTrainingModeEnum;
import com.sinitek.mind.dataset.repository.DatasetCollectionRepository;
import com.sinitek.mind.dataset.service.IDatasetCollectionService;
import com.sinitek.mind.dataset.service.IDatasetDataService;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import com.sinitek.mind.support.common.constant.FileConstant;
import com.sinitek.mind.support.common.dto.BaseFileDTO;
import com.sinitek.mind.support.common.dto.FullFileDTO;
import com.sinitek.mind.support.common.dto.UploadFileResponse;
import com.sinitek.mind.support.common.service.IFileService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.common.util.MultipartFileUtil;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * dataset_collections 服务实现类
 *
 * <AUTHOR>
 * date 2025-07-17
 * 描述：知识库集合（文件夹/文件）表Service实现
 */
@Service
@Slf4j
public class DatasetCollectionServiceImpl implements IDatasetCollectionService {

    @Autowired
    private DatasetCollectionRepository datasetCollectionRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private IDatasetDataService datasetDataService;

    @Autowired
    private IDatasetTrainingService datasetTrainingService;

    @Autowired
    private IAuthService authService;

    @Autowired
    private IFileService fileService;



    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ImageTrainingSupport imageTrainingSupport;

    @Autowired
    private FileTrainingServiceFactory fileTrainingServiceFactory;

    /**
     * 图片类型列表
     */
    public List<String> imageTypeList = Arrays.asList("jpg", "jpeg", "png");

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String save(DatasetCollectionDTO dto) {
        DatasetCollection entity = new DatasetCollection();
        BeanUtils.copyProperties(dto, entity);
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());
        }

        if (MapUtils.isEmpty(entity.getMetadata())) {
            entity.setMetadata(new HashMap<>());
        }

        DatasetCollection saved = datasetCollectionRepository.save(entity);
        return saved.getId();
    }

    @Override
    public void updateName(DatasetCollectionUpdateDTO dto) {
        if (StringUtils.isBlank(dto.getId())) {
            throw new BussinessException(DatasetErrorCodeConstant.ID_EMPTY);
        }
        if (StringUtils.isBlank(dto.getName())) {
            throw new BussinessException(DatasetErrorCodeConstant.NAME_EMPTY);
        }

        // 直接更新，不查询整条数据
        Query query = new Query(Criteria.where("_id").is(dto.getId()));
        Update update = new Update()
            .set("name", dto.getName())
            .set("updateTime", new Date());

        mongoTemplate.updateFirst(query, update, DatasetCollection.class);
    }

    @Override
    public void delete(String id) {
        if (StringUtils.isBlank(id)) {
            throw new BussinessException(DatasetErrorCodeConstant.ID_EMPTY);
        }
        datasetCollectionRepository.deleteById(id);
        datasetTrainingService.deleteByCollectionId(id);
    }

    @Override
    public DatasetCollectionDetailDTO detail(String id) {
        if (StringUtils.isBlank(id)) {
            throw new BussinessException(DatasetErrorCodeConstant.ID_EMPTY);
        }
        Optional<DatasetCollection> optional = datasetCollectionRepository.findById(id);
        if (!optional.isPresent()) {
            throw new BussinessException(DatasetErrorCodeConstant.DATA_NOT_EXIST);
        }
        DatasetCollection collection = optional.get();
        String datasetId = collection.getDatasetId();
        String teamId = collection.getTeamId();
        String tmbId = collection.getTmbId();

        DatasetCollectionDetailDTO result = new DatasetCollectionDetailDTO();
        BeanUtils.copyProperties(collection, result);
        result.set_id(collection.getId());

        DatasetDTO dataset = datasetService.getDatasetDetail(datasetId, null, teamId, tmbId);

        result.setDataset(dataset);
        result.setIndexAmount(2); // TODO: 根据实际计算
        result.setSourceName(collection.getName());

        String fileId = result.getFileId();
        if (StringUtils.isNotBlank(fileId)) {
            BaseFileDTO baseFileDTO = fileService.getBaseFileById(DatasetConstant.DATASET_BUCKET_NAME, fileId);
            result.setFile(baseFileDTO);
            result.setSourceId(fileId);
        }

        DatasetCollectionListV2PermissionDTO permission = new DatasetCollectionListV2PermissionDTO();
        permission.setIsOwner(true);
        permission.setHasManagePer(true);
        permission.setHasWritePer(true);
        permission.setHasReadPer(true);
        permission.setValue(PermissionConstant.OWNER_PERMISSION_VAL);
        permission.set_permissionList(PermissionConstant.TEAM_PERMISSION_LIST);
        result.setPermission(permission);
        result.setErrorCount(0); // TODO: 根据实际计算

        return result;
    }

    @Override
    public PageResult<DatasetCollectionDTO> pageListV2(DatasetCollectionListV2PageParamDTO param) {
        PageResult<DatasetCollectionDTO> result = new PageResult<>();
        Query query = new Query();

        String datasetId = param.getDatasetId();
        if (StringUtils.isNotBlank(datasetId)) {
            query.addCriteria(Criteria.where("datasetId").is(datasetId));
        }

        String parentId = param.getParentId();
        if (StringUtils.isNotBlank(parentId)) {
            query.addCriteria(Criteria.where("parentId").is(parentId));
        } else {
            // parentId为空时，匹配parentId为null或空字符串的数据
            query.addCriteria(new Criteria().orOperator(
                Criteria.where("parentId").is(null),
                Criteria.where("parentId").is("")
            ));
        }
        if (StringUtils.isNotBlank(param.getSearchText())) {
            query.addCriteria(Criteria.where("name").regex(param.getSearchText()));
        }
        if (param.getFilterTags() != null && !param.getFilterTags().isEmpty()) {
            query.addCriteria(Criteria.where("tags").in(param.getFilterTags()));
        }
        int pageNum = param.getPageNum() != null && param.getPageNum() > 0 ? param.getPageNum() : 1;
        int pageSize = param.getPageSize() != null && param.getPageSize() > 0 ? param.getPageSize() : 20;
        long total = mongoTemplate.count(query, DatasetCollection.class);
        query.with(Sort.by(Sort.Order.desc("updateTime")));

        query.skip((pageNum - 1L) * pageSize).limit(pageSize);
        List<DatasetCollection> entityList = mongoTemplate.find(query, DatasetCollection.class);
        List<DatasetCollectionDTO> dtoList = new ArrayList<>();
        for (DatasetCollection entity : entityList) {
            String collectionId = entity.getId();
            DatasetCollectionDTO dto = new DatasetCollectionDTO();
            BeanUtils.copyProperties(entity, dto);

            // 数据量
            int dataCount = datasetDataService.countByCollectionId(collectionId);
            dto.setDataAmount(dataCount);

            // 当前训练量
            Integer trainingAmount = datasetTrainingService.countTrainingByCollectionId(collectionId);
            dto.setTrainingAmount(trainingAmount);

            // 训练状态
            TrainingErrorQueryDTO trainingErrorQueryDTO = new TrainingErrorQueryDTO();
            trainingErrorQueryDTO.setCollectionId(collectionId);
            int errorCount = datasetTrainingService.countFailedTrainings(trainingErrorQueryDTO);
            if (errorCount > 0) {
                dto.setHasError(true);
            } else {
                dto.setHasError(false);
            }

            dtoList.add(dto);
        }
        result.setTotal((int) total);
        result.setList(dtoList);
        return result;
    }

    @Override
    public CreateCollectionResponseDTO createByFileId(FileIdCreateDatasetCollectionParamsDTO params) {
        datasetService.checkDatasetAuthByCurrentUser(params.getDatasetId());
        FullFileDTO file = fileService.getFileById(FileConstant.DATASET_NAME, params.getFileId());
        if (file == null) {
            throw new BussinessException(DatasetErrorCodeConstant.UPLOADED_FILE_NOT_FOUND);
        }
        DatasetCollectionDTO dto = new DatasetCollectionDTO();
        BeanUtils.copyProperties(params, dto);
        String fileName = StringUtils.isNotBlank(params.getName()) ? params.getName() : file.getFilename();
        this.buildDatasetCollectionDTOByFile(dto, fileName);
        dto.setMetadata(Map.of("relatedImgId", params.getFileId()));

        return this.createFromCommonFile(dto, file.getSourceFile());
    }

    @Override
    public CreateCollectionResponseDTO reTrainingCollection(FileIdCreateDatasetCollectionParamsDTO params) {
        // 检查数据集权限
        datasetService.checkDatasetAuthByCurrentUser(params.getDatasetId());

        // 检查集合是否存在
        String collectionId = params.getCollectionId();
        if (StringUtils.isBlank(collectionId)) {
            throw new BussinessException(DatasetErrorCodeConstant.COLLECTION_ID_EMPTY);
        }
        Optional<DatasetCollection> collectionOpt = this.getById(collectionId);
        if (!collectionOpt.isPresent()) {
            throw new BussinessException(DatasetErrorCodeConstant.COLLECTION_NOT_EXIST);
        }
        DatasetCollection collection = collectionOpt.get();

        // 获取文件信息
        FullFileDTO file = fileService.getFileById(FileConstant.DATASET_NAME, collection.getFileId());
        if (file == null) {
            throw new BussinessException(DatasetErrorCodeConstant.COLLECTION_ASSOCIATED_FILE_NOT_FOUND);
        }

        // 更新集合信息
        DatasetCollectionDTO dto = new DatasetCollectionDTO();
        BeanUtils.copyProperties(collection, dto);
        BeanUtils.copyProperties(params, dto);
        dto.setId(collectionId);
        // 获取当前用户的团队ID和成员ID
        AuthDTO authDTO = authService.authCert();
        dto.setTeamId(authDTO.getTeamId());
        dto.setTmbId(authDTO.getTmbId());

        // 保存更新后的集合信息
        this.save(dto);

        // 删除旧数据
        datasetDataService.deleteByCollectionId(collectionId);
        datasetTrainingService.deleteByCollectionId(collectionId);

        // 重新处理文件并创建新数据
        return this.createFromCommonFile(dto, file.getSourceFile());
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CreateCollectionResponseDTO createByText(TextCreateDatasetCollectionParamsDTO params) {
        datasetService.checkDatasetAuthByCurrentUser(params.getDatasetId());

        String fileName = params.getName() + ".txt";
        DatasetCollectionDTO collectionDto = new DatasetCollectionDTO();
        BeanUtils.copyProperties(params, collectionDto);
        this.buildDatasetCollectionDTOByFile(collectionDto, fileName);

        // 自定义文本固定为txt文件的方式存储
        String text = params.getText();
        File file = IOUtil.createTempFile("txt");
        FileUtils.writeStringToFile(file, text, StandardCharsets.UTF_8);
        UploadFileResponse uploadFileResponse = fileService.uploadFile(file, fileName, FileConstant.DATASET_NAME, null);
        String fileId = uploadFileResponse.getFileId();
        collectionDto.setFileId(fileId);

        return this.createFromCommonFile(collectionDto, file);
    }

    @Override
    public String getNameById(String id) {
        if (StringUtils.isBlank(id)) {
            throw new BussinessException(DatasetErrorCodeConstant.ID_EMPTY);
        }
        return datasetCollectionRepository.findById(id).map(DatasetCollection::getName).orElse(null);
    }

    @Override
    public String getIdByName(String name) {
        if (StringUtils.isBlank(name)) {
            throw new BussinessException(DatasetErrorCodeConstant.NAME_EMPTY);
        }
        DatasetCollection collection = datasetCollectionRepository.findFirstByName(name);
        return collection != null ? collection.getId() : null;
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateCollectionResponseDTO createByImage(MultipartFile file, CreateDatasetCollectionImageDTO data) {
        imageTrainingSupport.checkImagesFileType(file.getOriginalFilename());
        imageTrainingSupport.checkImagesDatasetAuthByCurrentUser(data.getDatasetId());

        // 创建知识库集合
        DatasetCollectionDTO collectionDto = new DatasetCollectionDTO();
        collectionDto.setDatasetId(data.getDatasetId());
        collectionDto.setParentId(data.getParentId());
        this.buildDatasetCollectionDTOByFile(collectionDto, data.getCollectionName());
        collectionDto.setType(DatasetCollectionTypeEnum.IMAGES.getCode());
        collectionDto.setMode(DatasetTrainingModeEnum.IMAGE_PARSE.getValue());

        // 保存集合
        String collectionId = this.save(collectionDto);
        collectionDto.setId(collectionId);

        IFileTrainingService trainingService = fileTrainingServiceFactory.getService(collectionDto.getType());
        
        // 将MultipartFile转换为File
        File convertedFile = MultipartFileUtil.convertToFile(file);
        return trainingService.training(collectionDto, convertedFile);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CreateCollectionResponseDTO createFromCommonFile(DatasetCollectionDTO dto, File file) {
        // 先保存集合，获取id
        String collectionId = save(dto);
        dto.setId(collectionId);
        
        // 使用统一的文件训练服务
        IFileTrainingService trainingService = fileTrainingServiceFactory.getService(dto.getType());
        return trainingService.training(dto, file);
    }

    @Override
    public Optional<DatasetCollection> getById(String id) {
        if (StringUtils.isBlank(id)) {
            throw new BussinessException(DatasetErrorCodeConstant.ID_EMPTY);
        }
        return datasetCollectionRepository.findById(id);
    }

    @Override
    public DatasetCollectionDTO getDatasetCollectionDTOById(String id) {
        Optional<DatasetCollection> datasetCollectionOptional = datasetCollectionRepository.findById(id);
        if (datasetCollectionOptional.isEmpty()) {
            return null;
        }
        DatasetCollection datasetCollection = datasetCollectionOptional.get();
        return this.toDTO(datasetCollection);
    }

    @Override
    public void move(DatasetCollectionMoveDTO dto) {
        if (StringUtils.isBlank(dto.getId())) {
            throw new BussinessException(DatasetErrorCodeConstant.ID_EMPTY);
        }

        // 直接更新，不查询整条数据
        Query query = new Query(Criteria.where("_id").is(dto.getId()));
        Update update = new Update()
            .set("parentId", dto.getParentId())
            .set("updateTime", new Date());

        mongoTemplate.updateFirst(query, update, DatasetCollection.class);
    }

    @Override
    public List<DatasetPathDTO> getPaths(String parentId) {
        List<DatasetPathDTO> pathList = new ArrayList<>();
        if (StringUtils.isBlank(parentId)) {
            return pathList;
        }

        // 循环向上查找父级
        String currentParentId = parentId;
        while (StringUtils.isNotBlank(currentParentId)) {
            Optional<DatasetCollection> optional = datasetCollectionRepository.findById(currentParentId);
            if (optional.isEmpty()) {
                break;
            }

            DatasetCollection collection = optional.get();
            DatasetPathDTO pathDTO = new DatasetPathDTO();
            pathDTO.setParentId(collection.getId());
            pathDTO.setParentName(collection.getName());
            pathList.add(pathDTO);

            // 获取上一级父ID
            currentParentId = collection.getParentId();
        }

        // 反转列表，使得顺序从高到低
        Collections.reverse(pathList);
        return pathList;
    }

    @Override
    public List<CollectionFileDTO> getCollectionFilesByIds(List<String> collectionIdList) {
        if (CollectionUtils.isEmpty(collectionIdList)) {
            return new ArrayList<>();
        }

        List<DatasetCollection> collections = datasetCollectionRepository.findAllById(collectionIdList);
        List<CollectionFileDTO> resultList = new ArrayList<>();

        for (DatasetCollection collection : collections) {
            CollectionFileDTO dto = CollectionFileDTO.builder()
                    .collectionId(collection.getId())
                    .fileId(collection.getFileId())
                    .fileName(collection.getName())
                    .build();
            resultList.add(dto);
        }

        return resultList;
    }

    @Override
    public List<DatasetCollection> findAllByDatasetId(String datasetId) {
        if (StringUtils.isBlank(datasetId)) {
            return new ArrayList<>();
        }
        return datasetCollectionRepository.findAllByDatasetId(datasetId);
    }

    /**
     * 实体转DTO
     * @param datasetCollection
     * @return
     */
    private DatasetCollectionDTO toDTO(DatasetCollection datasetCollection) {
        DatasetCollectionDTO datasetCollectionDTO = new DatasetCollectionDTO();
        BeanUtils.copyProperties(datasetCollection, datasetCollectionDTO);
        return datasetCollectionDTO;
    }

    /**
     * 组装DatasetCollectionDTO基于File时的通用属性
     * @param dto
     * @return
     */
    private DatasetCollectionDTO buildDatasetCollectionDTOByFile(DatasetCollectionDTO dto, String fileName) {
        AuthDTO auth = authService.authCert();
        // 如果没有设置类型，默认设置为FILE类型
        if (StringUtils.isBlank(dto.getType())) {
            dto.setType(DatasetCollectionTypeEnum.FILE.getCode());
        }
        dto.setTeamId(auth.getTeamId());
        dto.setTmbId(auth.getTmbId());
        dto.setName(fileName);
        return dto;
    }

    /**
     * 将MultipartFile转换为File
     * @param multipartFile
     * @return
     * @throws IOException
     */
    private File convertMultipartFileToFile(MultipartFile multipartFile) throws IOException {
        File tempFile = File.createTempFile("temp_", "_" + multipartFile.getOriginalFilename());
        multipartFile.transferTo(tempFile);
        return tempFile;
    }
}