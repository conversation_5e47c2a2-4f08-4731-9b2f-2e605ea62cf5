package com.sinitek.mind.dataset.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分块模式枚举
 *
 * <AUTHOR>
 * date 2025-08-12
 */
@Getter
@AllArgsConstructor
public enum DatasetChunkSettingModeEnum {
    /**
     * 自定义
     */
    CUSTOM("custom", "自定义"),

    /**
     * 默认（系统默认配置）
     */
    AUTO("auto", "默认（系统默认配置）");

    private final String value;
    private final String label;

    /**
     * 根据value获取枚举
     * @param value
     * @return
     */
    public static DatasetChunkSettingModeEnum getByValue(String value) {
        for (DatasetChunkSettingModeEnum modeEnum : values()) {
            if (modeEnum.value.equals(value)) {
                return modeEnum;
            }
        }
        return null;
    }

    /**
     * 根据label获取枚举
     * @param label
     * @return
     */
    public static DatasetChunkSettingModeEnum getByLabel(String label) {
        for (DatasetChunkSettingModeEnum modeEnum : values()) {
            if (modeEnum.label.equals(label)) {
                return modeEnum;
            }
        }
        return null;
    }
}