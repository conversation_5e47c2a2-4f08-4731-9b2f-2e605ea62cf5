package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 创建知识库数据图片请求DTO
 *
 * <AUTHOR>
 * date 2025/08/21
 */
@Data
@Schema(description = "创建知识库数据图片请求DTO")
public class CreateDatasetDataImageDTO {

    @Schema(description = "集合Id")
    @NotBlank(message = "集合Id不能为空")
    private String collectionId;
}