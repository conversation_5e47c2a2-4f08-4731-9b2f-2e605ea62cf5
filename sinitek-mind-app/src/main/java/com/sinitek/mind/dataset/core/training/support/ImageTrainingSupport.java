package com.sinitek.mind.dataset.core.training.support;

import com.sinitek.mind.common.dto.FileTokenQuery;
import com.sinitek.mind.common.util.MindFileTokenUtil;
import com.sinitek.mind.dataset.constant.DatasetConstant;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.dataset.entity.DatasetData;
import com.sinitek.mind.dataset.entity.DatasetTraining;
import com.sinitek.mind.dataset.enumerate.DatasetCollectionTypeEnum;
import com.sinitek.mind.dataset.enumerate.DatasetTrainingModeEnum;
import com.sinitek.mind.dataset.repository.DatasetTrainingRepository;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import com.sinitek.mind.dataset.service.IDatasetDataService;
import com.sinitek.mind.model.core.llm.service.IModelChatService;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.support.common.constant.FileConstant;
import com.sinitek.mind.support.common.dto.FullFileDTO;
import com.sinitek.mind.support.common.dto.UploadFileResponse;
import com.sinitek.mind.support.common.service.IFileService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图片训练支持层
 *
 * <AUTHOR>
 * date 2025-08-21
 */
@Slf4j
@Component
public class ImageTrainingSupport {

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private IFileService fileService;

    @Autowired
    private IDatasetTrainingService datasetTrainingService;

    @Autowired
    private IDatasetDataService datasetDataService;

    @Autowired
    private MindFileTokenUtil mindFileTokenUtil;

    @Autowired
    private IModelChatService modelChatService;

    @Autowired
    private DatasetTrainingRepository trainingRepository;

    /**
     * 图片类型列表
     */
    public List<String> imageTypeList = Arrays.asList("jpg", "jpeg", "png");

    /**
     * 图片解析
     * @param training
     * @return
     */
    public DatasetDataDTO recognizeImageContent(DatasetTraining training) {
        String datasetId = training.getDatasetId();
        Dataset dataset = datasetService.getDatasetById(datasetId);
        if (ObjectUtils.isEmpty(dataset)) {
            log.warn("知识库: {}不存在", datasetId);
            return null;
        }
        String vlmModel = dataset.getVlmModel();
        if (StringUtils.isBlank(vlmModel)) {
            log.warn("知识库: {}没有图片理解模型，无法训练理解图片内容", datasetId);
            return null;
        }

        // 1) 获取文件本体（临时文件）
        String fileId = training.getFileId();
        FullFileDTO fileInfo = fileService.getFileById(DatasetConstant.DATASET_BUCKET_NAME, fileId);
        if (fileInfo == null || fileInfo.getSourceFile() == null || !fileInfo.getSourceFile().exists()) {
            log.warn("文件:{} 未找到或不可读取，无法进行图片识别", fileId);
            return null;
        }

        // 2) 计算mimeType并调用多模态聊天
        File sourceFile = fileInfo.getSourceFile();

        // 3) 写回识别结果
        DatasetDataDTO dataDto = new DatasetDataDTO();
        try {
            ChatResponse chatResponse = modelChatService.recognizeImageContent(sourceFile, vlmModel);
            String resultText = chatResponse.getResult().getOutput().getText();
            if (StringUtils.isNotBlank(resultText)) {
                // 创建并保存DatasetDataDTO对象
                dataDto.setTeamId(training.getTeamId());
                dataDto.setType(training.getType());
                dataDto.setFileId(fileId);
                dataDto.setDatasetId(training.getDatasetId());
                dataDto.setCollectionId(training.getCollectionId());
                dataDto.setQ(resultText);
                dataDto.setA("");
                dataDto.setChunkIndex(training.getChunkIndex());
                String dataId = datasetDataService.create(dataDto);
                dataDto.setId(dataId);

                // 更新模式为内容解析中
                training.setMode(DatasetTrainingModeEnum.PARSE.getValue());
                training.setText(resultText);
                trainingRepository.save(training);

            }
        } catch (Exception e) {
            log.warn("图片识别结果写回失败, trainingId={}, err={}", training.getId(), e);
            throw e;
        }
        return dataDto;
    }

    /**
     * 新增图片训练逻辑
     * @param file
     * @param collectionDTO
     * @return
     */
    public CreateCollectionResponseDTO createImagesTraining(File file, DatasetCollectionDTO collectionDTO) {
        // 上传图片文件
        long size = file.length();
        String fileName = file.getName();
        UploadFileResponse uploadFileResponse = fileService.uploadFile(file, fileName, FileConstant.DATASET_NAME, null);
        String fileId = uploadFileResponse.getFileId();

        // 保存图片训练任务
        String collectionId = collectionDTO.getId();
        DatasetTrainingDTO trainingDTO = new DatasetTrainingDTO();
        BeanUtils.copyProperties(collectionDTO, trainingDTO);
        trainingDTO.setCollectionId(collectionId);
        trainingDTO.setFileId(fileId);
        trainingDTO.setFileSize(size);

        // 设置chunkIndex为当前集合数据数量+1
        int dataCount = datasetDataService.countByCollectionId(collectionId);
        trainingDTO.setChunkIndex(dataCount);

        datasetTrainingService.create(trainingDTO);

        // 创建响应
        CreateCollectionResponseDTO response = new CreateCollectionResponseDTO();
        response.setCollectionId(collectionId);
        Map<String, Object> results = new HashMap<>();
        results.put("insertLen", 1);
        response.setResults(results);

        return response;
    }

    /**
     * 检查知识库权限和图片理解模型模型
     * @param datasetId
     * @return
     */
    public DatasetDTO checkImagesDatasetAuthByCurrentUser(String datasetId) {
        DatasetDTO datasetDTO = datasetService.checkDatasetAuthByCurrentUser(datasetId);
        // 知识库的图片理解模型检查
        SystemModelDTO vlmModel = datasetDTO.getVlmModel();
        if (ObjectUtils.isEmpty(vlmModel)) {
            throw new BussinessException("没有配置图片理解模型，请配置后在上传图片");
        }
        return datasetDTO;
    }

    /**
     * 验证文件类型
     * @param originalFilename
     */
    public void checkImagesFileType(String originalFilename) {
        String extension = FilenameUtils.getExtension(originalFilename);
        if (StringUtils.isBlank(originalFilename) || !imageTypeList.contains(extension.toLowerCase())) {
            throw new BussinessException("只支持jpg、jpeg和png格式的图片");
        }
    }

    /**
     * 组装Image类型时DatasetData额外的返回值
     * @param entity
     * @param dto
     */
    public void buildImageDatasetDataDTO(DatasetData entity, DatasetDataDTO dto) {
        String type = entity.getType();
        if (DatasetCollectionTypeEnum.IMAGES.getCode().equals(type)) {
            long fileSize = entity.getFileSize();
            String fileId = entity.getFileId();

            FileTokenQuery fileTokenQuery = new FileTokenQuery();
            fileTokenQuery.setBucketName(DatasetConstant.DATASET_BUCKET_NAME);
            fileTokenQuery.setFileId(fileId);
            String fileToken = mindFileTokenUtil.createFileToken(fileTokenQuery);
            dto.setImageId(fileId);
            dto.setImageSize(fileSize);
            dto.setImagePreviewUrl("/mind/api/common/file/read?token="+fileToken);
        }
    }
}
