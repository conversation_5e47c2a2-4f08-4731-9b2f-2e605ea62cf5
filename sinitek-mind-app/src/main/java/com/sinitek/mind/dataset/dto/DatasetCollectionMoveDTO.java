package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 数据集集合移动DTO
 *
 * <AUTHOR>
 * date 2025-07-17
 * 描述：用于移动数据集集合的DTO
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class DatasetCollectionMoveDTO {

    @NotBlank(message = "{" + DatasetErrorCodeConstant.ID_EMPTY + "}")
    @Schema(description = "集合ID")
    private String id;

    @Schema(description = "父ID")
    private String parentId;

}