package com.sinitek.mind.dataset.core.training.support;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import com.sinitek.mind.dataset.dto.CreateCollectionResponseDTO;
import com.sinitek.mind.dataset.dto.DatasetCollectionDTO;
import com.sinitek.mind.dataset.dto.DatasetDataDTO;
import com.sinitek.mind.dataset.dto.DatasetTrainingDTO;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.dataset.enumerate.DatasetTrainingModeEnum;
import com.sinitek.mind.dataset.service.IDatasetDataService;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import com.sinitek.mind.model.core.llm.service.IModelChatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * QA 训练支持层
 *
 * <AUTHOR>
 * date 2025-08-22
 * 描述：负责将文档抽取为问答对并落库
 */
@Slf4j
@Component
public class QaTrainingSupport {

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private IModelChatService modelChatService;

    @Autowired
    private IDatasetDataService datasetDataService;

    @Autowired
    private IDatasetTrainingService datasetTrainingService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 创建 QA 训练数据：从原文文本抽取问答对写入 DatasetData，并创建对应的 DatasetTraining
     * @param text 原文内容
     * @param collectionDTO 集合信息
     * @return CreateCollectionResponseDTO
     */
    public CreateCollectionResponseDTO createQaTraining(String text, DatasetCollectionDTO collectionDTO) {
        String collectionId = collectionDTO.getId();
        int insertLen = 0;

        if (StringUtils.isBlank(text)) {
            throw new RuntimeException(DatasetErrorCodeConstant.FILE_CONTENT_UNPARSABLE);
        }

        // 获取模型与提示词
        String datasetId = collectionDTO.getDatasetId();
        Dataset dataset = datasetService.getDatasetById(datasetId);
        if (ObjectUtils.isEmpty(dataset)) {
            throw new RuntimeException(DatasetErrorCodeConstant.DATA_NOT_EXIST);
        }
        String agentModel = dataset.getAgentModel();
        String qaPrompt = null;
        if (dataset.getChunkSettings() != null) {
            qaPrompt = dataset.getChunkSettings().getQaPrompt();
        }
        if (StringUtils.isBlank(qaPrompt)) {
            qaPrompt = "请从以下文档中抽取尽可能多的高质量问答对，用中文返回一个JSON数组，每个元素包含q和a两个字段，不要添加多余说明。";
        }

        String message = qaPrompt + "\n\n文档内容开始：\n" + text + "\n文档内容结束。\n" +
                "请严格只输出JSON数组，每个元素包含键 q 和 a，不要任何解释。";

        String aiResult = modelChatService.chat(message, agentModel);
        if (StringUtils.isBlank(aiResult)) {
            throw new RuntimeException(DatasetErrorCodeConstant.FILE_PROCESS_FAILED);
        }

        try {
            List<Map<String, String>> qaList = objectMapper.readValue(aiResult, new TypeReference<List<Map<String, String>>>() {});
            if (qaList == null || qaList.isEmpty()) {
                throw new RuntimeException(DatasetErrorCodeConstant.FILE_PROCESS_FAILED);
            }

            int index = 0;
            for (Map<String, String> qa : qaList) {
                String q = qa.get("q");
                String a = qa.get("a");
                if (StringUtils.isBlank(q)) {
                    continue;
                }

                DatasetDataDTO dataDto = new DatasetDataDTO();
                dataDto.setTeamId(collectionDTO.getTeamId());
                dataDto.setTmbId(collectionDTO.getTmbId());
                dataDto.setDatasetId(datasetId);
                dataDto.setCollectionId(collectionId);
                dataDto.setQ(q);
                dataDto.setA(a != null ? a : "");
                dataDto.setChunkIndex(index);
                dataDto.setSourceName(collectionDTO.getName());
                String dataId = datasetDataService.create(dataDto);
                insertLen++;

                DatasetTrainingDTO trainingDTO = new DatasetTrainingDTO();
                BeanUtils.copyProperties(dataDto, trainingDTO);
                trainingDTO.setDataId(dataId);
                trainingDTO.setText(q);
                trainingDTO.setMode(DatasetTrainingModeEnum.QA.getValue());
                datasetTrainingService.create(trainingDTO);

                index++;
            }
        } catch (Exception e) {
            throw new RuntimeException(DatasetErrorCodeConstant.FILE_PROCESS_FAILED, e);
        }

        return new CreateCollectionResponseDTO(collectionId, Map.of("insertLen", insertLen));
    }
}


