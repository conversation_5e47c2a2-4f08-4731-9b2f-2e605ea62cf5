package com.sinitek.mind.dataset.repository;

import com.sinitek.mind.dataset.entity.Dataset;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 知识库Repository接口
 *
 * <AUTHOR>
 * date 2025-07-02
 */
@Repository
public interface DatasetRepository extends MongoRepository<Dataset, String> {
    
    /**
     * 根据团队ID查询知识库列表
     *
     * @param teamId 团队ID
     * @return 知识库列表
     */
    List<Dataset> findByTeamId(ObjectId teamId);
    
    /**
     * 根据团队ID和父级ID查询知识库列表
     *
     * @param teamId 团队ID
     * @param parentId 父级ID
     * @return 知识库列表
     */
    List<Dataset> findByTeamIdAndParentId(ObjectId teamId, ObjectId parentId);
    
    /**
     * 根据团队ID和类型查询知识库列表
     *
     * @param teamId 团队ID
     * @param type 知识库类型
     * @return 知识库列表
     */
    List<Dataset> findByTeamIdAndType(ObjectId teamId, String type);
    
    /**
     * 根据团队ID和名称查询知识库列表（模糊查询）
     *
     * @param teamId 团队ID
     * @param name 知识库名称
     * @return 知识库列表
     */
    List<Dataset> findByTeamIdAndNameRegex(ObjectId teamId, String name);
    
    /**
     * 根据团队ID和父级ID和类型查询知识库列表
     *
     * @param teamId 团队ID
     * @param parentId 父级ID
     * @param type 知识库类型
     * @return 知识库列表
     */
    List<Dataset> findByTeamIdAndParentIdAndType(ObjectId teamId, ObjectId parentId, String type);
    
    /**
     * 查找指定ID的所有子知识库
     * 
     * @param teamId 团队ID
     * @param parentId 父级ID
     * @return 知识库列表
     */
    List<Dataset> findAllByTeamIdAndParentId(ObjectId teamId, ObjectId parentId);

    List<Dataset> findDatasetsByIdIn(ArrayList<String> strings);

    /**
     * 根据知识库ID更新tmbId
     */
    @Query("{ '_id': ?0 }")
    @Update("{ '$set': { 'tmbId': ?1 } }")
    void updateTmbIdById(String datasetId, String tmbId);

    List<Dataset> findByTypeInAndInheritPermissionTrue(List<String> folderTypeList);
}