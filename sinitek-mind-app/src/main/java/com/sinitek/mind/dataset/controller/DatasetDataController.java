package com.sinitek.mind.dataset.controller;

import com.sinitek.mind.common.dto.IdDTO;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.service.IDatasetCollectionService;
import com.sinitek.mind.dataset.service.IDatasetDataService;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.sirm.common.utils.JsonUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * dataset_datas 控制器
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库数据表Controller
 */
@RestController
@RequestMapping("/mind/api/core/dataset/data")
@Tag(name = "知识库数据管理")
public class DatasetDataController {

    @Autowired
    private IDatasetDataService service;

    @Autowired
    private IAuthService authService;

    @Autowired
    private IDatasetCollectionService collectionService;

    @PostMapping("/create")
    @Operation(summary = "创建数据")
    public ApiResponse<String> create(@RequestBody DatasetDataDTO dto) {
        String id = service.create(dto);
        return ApiResponse.success(id);
    }

    @PostMapping("/update")
    @Operation(summary = "更新数据")
    public ApiResponse<Void> update(@RequestBody DatasetDataUpdateRequest request) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();
        service.updateDatasetData(request, userId, teamId, tmbId);
        return ApiResponse.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除数据")
    public ApiResponse<Void> delete(@RequestBody IdDTO dto) {
        service.delete(dto.getId());
        return ApiResponse.success();
    }

    @GetMapping("/detail")
    @Operation(summary = "数据详情")
    public ApiResponse<DatasetDataDTO> detail(@Parameter(description = "主键ID") @RequestParam String id) {
        DatasetDataDTO dto = service.detail(id);
        if (dto != null) {
            dto.setIsOwner(true);
            String sourceName = collectionService.getNameById(dto.getCollectionId());
            if (StringUtils.isNotBlank(sourceName)) {
                dto.setSourceName(sourceName);
            }
        }
        return ApiResponse.success(dto);
    }

    @PostMapping("/v2/list")
    @Operation(summary = "分页获取集合数据列表")
    public ApiResponse<PageResult<DatasetDataDTO>> pageList(@RequestBody DatasetDataPageParamDTO param) {
        return ApiResponse.success(service.pageList(param));
    }

    @PostMapping("/insertData")
    @Operation(summary = "插入单个数据集数据")
    public ApiResponse<String> insertData(@RequestBody InsertOneDatasetDataDTO param) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();
        String insertId = service.insertData(param, teamId, tmbId);
        return ApiResponse.success(insertId);
    }

    @PostMapping("/insertImages")
    @Operation(summary = "在已有的知识库集合中插入图片")
    public ApiResponse<CreateCollectionResponseDTO> insertImages(
            @RequestPart("file") MultipartFile file,
            @RequestPart("data") String data) {
        CreateDatasetDataImageDTO createDatasetDataImageDTO = JsonUtil.toJavaObject(data, CreateDatasetDataImageDTO.class);
        CreateCollectionResponseDTO response = service.insertImages(file, createDatasetDataImageDTO);
        return ApiResponse.success(response);
    }

}