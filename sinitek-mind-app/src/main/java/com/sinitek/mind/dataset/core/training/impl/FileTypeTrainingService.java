package com.sinitek.mind.dataset.core.training.impl;

import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import com.sinitek.mind.dataset.core.reader.VectorFileReaderCore;
import com.sinitek.mind.dataset.core.training.IFileTrainingService;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.enumerate.DatasetTrainingModeEnum;
import com.sinitek.mind.dataset.repository.DatasetCollectionRepository;
import com.sinitek.mind.dataset.service.IDatasetCollectionService;
import com.sinitek.mind.dataset.service.IDatasetDataService;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import com.sinitek.mind.dataset.enumerate.DatasetCollectionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 文件类型训练服务实现类
 *
 * <AUTHOR>
 * date 2025-08-22
 * 描述：处理文件类型的知识库集合训练
 */
@Service
@Slf4j
public class FileTypeTrainingService implements IFileTrainingService {

    @Autowired
    private DatasetCollectionRepository datasetCollectionRepository;

    @Autowired
    private IDatasetCollectionService datasetCollectionService;

    @Autowired
    private IDatasetDataService datasetDataService;

    @Autowired
    private IDatasetTrainingService datasetTrainingService;

    @Autowired
    private VectorFileReaderCore vectorFileReaderCore;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateCollectionResponseDTO training(DatasetCollectionDTO dto, File file) {
        // 使用传入的DTO中的id，因为DTO已经保存过了
        String collectionId = dto.getId();
        int insertLen = 0;

        try {
            // 1. 用VectorFileReaderCore分块
            BaseDatasetCollectionParamsDTO baseDatasetCollectionParamsDTO = new BaseDatasetCollectionParamsDTO();
            BeanUtils.copyProperties(dto, baseDatasetCollectionParamsDTO);
            List<Document> documentList = vectorFileReaderCore.process(file, baseDatasetCollectionParamsDTO);

            if (documentList == null || documentList.isEmpty()) {
                throw new RuntimeException(DatasetErrorCodeConstant.FILE_CONTENT_UNPARSABLE);
            }

            // 增加元数据
            String datasetId = dto.getDatasetId();
            String teamId = dto.getTeamId();
            String tmbId = dto.getTmbId();

            for (Document document : documentList) {
                Map<String, Object> metadata = document.getMetadata();
                metadata.put("team_id", teamId);
                metadata.put("tmbId", tmbId);
                metadata.put("dataset_id", datasetId);
                metadata.put("collection_id", collectionId);
            }

            // 2. 遍历Document，转为DatasetDataDTO并保存
            for (int i = 0; i < documentList.size(); i++) {
                Document doc = documentList.get(i);
                DatasetDataDTO dataDto = new DatasetDataDTO();
                dataDto.setTeamId(dto.getTeamId());
                dataDto.setTmbId(dto.getTmbId());
                dataDto.setDatasetId(dto.getDatasetId());
                dataDto.setCollectionId(collectionId);
                dataDto.setQ(doc.getText());
                dataDto.setA("");
                dataDto.setChunkIndex(i);
                dataDto.setSourceName(dto.getName());
                String dataId = datasetDataService.create(dataDto);
                insertLen++;

                // 同步插入 DatasetTraining
                DatasetTrainingDTO trainingDTO = new DatasetTrainingDTO();
                BeanUtils.copyProperties(dataDto, trainingDTO);
                trainingDTO.setDataId(dataId);
                trainingDTO.setText(dataDto.getQ());
                trainingDTO.setMode(DatasetTrainingModeEnum.CHUNK.getValue());
                datasetTrainingService.create(trainingDTO);
            }

            return new CreateCollectionResponseDTO(collectionId, Map.of("insertLen", insertLen));
        } catch (Exception e) {
            throw new RuntimeException(DatasetErrorCodeConstant.FILE_PROCESS_FAILED, e);
        }
    }



    @Override
    public String getSupportedType() {
        return DatasetCollectionTypeEnum.FILE.getCode();
    }
}
