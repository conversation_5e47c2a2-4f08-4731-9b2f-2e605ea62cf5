package com.sinitek.mind.dataset.core.training.impl;

import com.sinitek.mind.dataset.constant.DatasetErrorCodeConstant;
import com.sinitek.mind.dataset.core.reader.VectorFileReaderCore;
import com.sinitek.mind.dataset.core.training.IFileTrainingService;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.enumerate.DatasetTrainingModeEnum;
import com.sinitek.mind.dataset.repository.DatasetCollectionRepository;
import com.sinitek.mind.dataset.service.IDatasetCollectionService;
import com.sinitek.mind.dataset.service.IDatasetDataService;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import com.sinitek.mind.dataset.enumerate.DatasetCollectionTypeEnum;
import com.sinitek.mind.dataset.core.training.support.QaTrainingSupport;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 文件类型训练服务实现类
 *
 * <AUTHOR>
 * date 2025-08-22
 * 描述：处理文件类型的知识库集合训练
 */
@Service
@Slf4j
public class FileTypeTrainingService implements IFileTrainingService {

    @Autowired
    private DatasetCollectionRepository datasetCollectionRepository;

    @Autowired
    private IDatasetCollectionService datasetCollectionService;

    @Autowired
    private IDatasetDataService datasetDataService;

    @Autowired
    private IDatasetTrainingService datasetTrainingService;

    @Autowired
    private VectorFileReaderCore vectorFileReaderCore;

    @Autowired
    private QaTrainingSupport qaTrainingSupport;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateCollectionResponseDTO training(DatasetCollectionDTO dto, File file) {
        String collectionId = dto.getId();
        int insertLen = 0;

        try {
            BaseDatasetCollectionParamsDTO baseParams = new BaseDatasetCollectionParamsDTO();
            BeanUtils.copyProperties(dto, baseParams);

            String trainingType = baseParams.getTrainingType();
            if (StringUtils.isBlank(trainingType)) {
                trainingType = DatasetTrainingModeEnum.CHUNK.getValue();
            }

            // 分支1：分块模式（保持现有逻辑）
            if (DatasetTrainingModeEnum.CHUNK.getValue().equals(trainingType)) {
                List<Document> documentList = vectorFileReaderCore.process(file, baseParams);
                if (documentList == null || documentList.isEmpty()) {
                    throw new RuntimeException(DatasetErrorCodeConstant.FILE_CONTENT_UNPARSABLE);
                }

                String datasetId = dto.getDatasetId();
                String teamId = dto.getTeamId();
                String tmbId = dto.getTmbId();

                for (Document document : documentList) {
                    Map<String, Object> metadata = document.getMetadata();
                    metadata.put("team_id", teamId);
                    metadata.put("tmbId", tmbId);
                    metadata.put("dataset_id", datasetId);
                    metadata.put("collection_id", collectionId);
                }

                for (int i = 0; i < documentList.size(); i++) {
                    Document doc = documentList.get(i);
                    DatasetDataDTO dataDto = new DatasetDataDTO();
                    dataDto.setTeamId(dto.getTeamId());
                    dataDto.setTmbId(dto.getTmbId());
                    dataDto.setDatasetId(dto.getDatasetId());
                    dataDto.setCollectionId(collectionId);
                    dataDto.setQ(doc.getText());
                    dataDto.setA("");
                    dataDto.setChunkIndex(i);
                    dataDto.setSourceName(dto.getName());
                    String dataId = datasetDataService.create(dataDto);
                    insertLen++;

                    DatasetTrainingDTO trainingDTO = new DatasetTrainingDTO();
                    BeanUtils.copyProperties(dataDto, trainingDTO);
                    trainingDTO.setDataId(dataId);
                    trainingDTO.setText(dataDto.getQ());
                    trainingDTO.setMode(DatasetTrainingModeEnum.CHUNK.getValue());
                    datasetTrainingService.create(trainingDTO);
                }

                return new CreateCollectionResponseDTO(collectionId, Map.of("insertLen", insertLen));
            }

            // 分支2：QA模式（问答对提取）
            if (DatasetTrainingModeEnum.QA.getValue().equals(trainingType)) {
                dev.langchain4j.data.document.Document fullDoc = vectorFileReaderCore.readFile(file);
                if (fullDoc == null || org.apache.commons.lang3.StringUtils.isBlank(fullDoc.text())) {
                    throw new RuntimeException(DatasetErrorCodeConstant.FILE_CONTENT_UNPARSABLE);
                }
                return qaTrainingSupport.createQaTraining(fullDoc.text(), dto);
            }

            // 其他类型暂不处理，按文件不可处理返回
            throw new RuntimeException(DatasetErrorCodeConstant.FILE_PROCESS_FAILED);
        } catch (Exception e) {
            throw new RuntimeException(DatasetErrorCodeConstant.FILE_PROCESS_FAILED, e);
        }
    }



    @Override
    public String getSupportedType() {
        return DatasetCollectionTypeEnum.FILE.getCode();
    }

    @Override
    public boolean supportsExtension(String extension) {
        if (extension == null) {
            return false;
        }
        String ext = extension.toLowerCase();
        return "txt".equals(ext) || "pdf".equals(ext) || "docx".equals(ext) || "md".equals(ext);
    }
}
