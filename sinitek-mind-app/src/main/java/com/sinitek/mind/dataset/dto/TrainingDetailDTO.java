package com.sinitek.mind.dataset.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 集合训练详情DTO
 *
 * <AUTHOR>
 * date 2025-08-20
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "集合训练详情DTO")
public class TrainingDetailDTO {

    @Schema(description = "训练类型")
    private String trainingType;

    @Schema(description = "高级训练设置")
    private AdvancedTraining advancedTraining;

    @Schema(description = "队列计数")
    private Counts queuedCounts;

    @Schema(description = "训练计数")
    private Counts trainingCounts;

    @Schema(description = "错误计数")
    private Counts errorCounts;

    @Schema(description = "已训练数量")
    private Integer trainedCount;

    @Data
    @Schema(description = "高级训练设置")
    public static class AdvancedTraining {

        @Schema(description = "自定义PDF解析")
        private Boolean customPdfParse;

        @Schema(description = "图片索引")
        private Boolean imageIndex;

        @Schema(description = "自动索引")
        private Boolean autoIndexes;
    }

    @Data
    @Schema(description = "计数信息")
    public static class Counts {

        @Schema(description = "解析计数")
        private Integer parse;

        @Schema(description = "问答计数")
        private Integer qa;

        @Schema(description = "分块计数")
        private Integer chunk;

        @Schema(description = "图片计数")
        private Integer image;

        @Schema(description = "自动计数")
        private Integer auto;

        @Schema(description = "图片解析计数")
        private Integer imageParse;
    }
}