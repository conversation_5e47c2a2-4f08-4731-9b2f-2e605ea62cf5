package com.sinitek.mind.model.core.stt.provider.impl;

import com.sinitek.mind.model.constant.ModelErrorCodeConstant;
import com.sinitek.mind.model.core.stt.provider.SpeechToTextProvider;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.sirm.framework.exception.BussinessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

/**
 * 默认STT服务提供者实现
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Component
public class DefaultSpeechToTextProvider implements SpeechToTextProvider {

    private static final Logger logger = LoggerFactory.getLogger(DefaultSpeechToTextProvider.class);
    private static final String PROVIDER_TYPE = "default";

    @Override
    public String getProviderType() {
        return PROVIDER_TYPE;
    }

    @Override
    public String recognizeSpeech(byte[] audioData, SystemModelDTO modelDTO) {
        logger.info("调用默认STT服务识别语音, 音频数据长度: {}, 模型: {}", 
                audioData.length, modelDTO.getModel());
                
        // 实际实现中，这里会调用第三方API或本地服务
        try {
            // 获取模型自定义配置
            Map<String, Object> config = modelDTO.getDefaultConfig();
            String apiKey = getConfigValue(config, "apiKey", "");
            String apiUrl = StringUtils.isNotBlank(modelDTO.getRequestUrl()) 
                    ? modelDTO.getRequestUrl() 
                    : getConfigValue(config, "apiUrl", "");
                    
            // 这里只是示例，实际需要根据不同供应商实现对接
            
            // 模拟返回一个文本
            return "这是语音识别的示例结果";
        } catch (Exception e) {
            logger.error("STT服务调用失败", e);
            throw new BussinessException(ModelErrorCodeConstant.STT_SERVICE_FAILED);
        }
    }

    @Override
    public String recognizeSpeech(InputStream audioStream, SystemModelDTO modelDTO) {
        try {
            // 将输入流转换为字节数组
            byte[] audioData = audioStream.readAllBytes();
            return recognizeSpeech(audioData, modelDTO);
        } catch (IOException e) {
            logger.error("从输入流读取音频数据失败", e);
            throw new RuntimeException(ModelErrorCodeConstant.STT_READ_FAILED, e);
        }
    }
    
    /**
     * 从配置中获取值
     * @param config 配置
     * @param key 键
     * @param defaultValue 默认值
     * @return 配置值
     */
    private String getConfigValue(Map<String, Object> config, String key, String defaultValue) {
        if (config == null) {
            return defaultValue;
        }
        Object value = config.get(key);
        return value != null ? value.toString() : defaultValue;
    }
} 