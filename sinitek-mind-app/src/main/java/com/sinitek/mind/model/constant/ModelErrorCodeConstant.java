package com.sinitek.mind.model.constant;

/**
 * 模型模块错误码常量类
 * 
 * 错误码规则：30-07-YYYY
 * 30 - 固定前缀
 * 07 - 模型模块
 * YYYY - 具体错误码
 *
 * <AUTHOR>
 * date 2025-08-15
 */
public class ModelErrorCodeConstant {

    // ==================== 基础模型错误 ====================
    
    /**
     * 模型标识不能为空
     */
    public static final String MODEL_ID_EMPTY = "30070001";
    
    /**
     * 找不到模型: {0}
     */
    public static final String MODEL_NOT_FOUND = "30070002";
    
    /**
     * 系统模型不允许删除
     */
    public static final String SYSTEM_MODEL_CANNOT_DELETE = "30070003";
    
    /**
     * 配置内容不能为空
     */
    public static final String CONFIG_CONTENT_EMPTY = "30070004";
    
    /**
     * 配置格式解析失败: {0}
     */
    public static final String CONFIG_PARSE_FAILED = "30070005";

    // ==================== 模型测试错误 ====================
    
    /**
     * 不支持的模型类型: {0}
     */
    public static final String UNSUPPORTED_MODEL_TYPE = "30070006";
    
    /**
     * 测试LLM模型失败: {0}
     */
    public static final String TEST_LLM_FAILED = "30070007";
    
    /**
     * 测试Embedding模型失败: {0}
     */
    public static final String TEST_EMBEDDING_FAILED = "30070008";
    
    /**
     * 测试TTS模型失败: {0}
     */
    public static final String TEST_TTS_FAILED = "30070009";
    
    /**
     * 测试STT模型失败: 无法获取测试音频数据
     */
    public static final String TEST_STT_NO_AUDIO = "30070010";
    
    /**
     * 测试STT模型失败: {0}
     */
    public static final String TEST_STT_FAILED = "30070011";
    
    /**
     * 测试Rerank模型失败: {0}
     */
    public static final String TEST_RERANK_FAILED = "30070012";

    // ==================== 模型配置错误 ====================
    
    /**
     * 模型配置不能为空
     */
    public static final String MODEL_CONFIG_EMPTY = "30070013";
    
    /**
     * 模型提供商不能为空
     */
    public static final String PROVIDER_EMPTY = "30070014";
    
    /**
     * 不支持的模型提供商: {0}
     */
    public static final String UNSUPPORTED_PROVIDER = "30070015";
    
    /**
     * 不支持的索引模型提供商: {0}
     */
    public static final String UNSUPPORTED_EMBEDDING_PROVIDER = "30070016";
    
    /**
     * 不支持的TTS模型提供商: {0}
     */
    public static final String UNSUPPORTED_TTS_PROVIDER = "30070017";
    
    /**
     * 不支持的STT模型提供商: {0}
     */
    public static final String UNSUPPORTED_STT_PROVIDER = "30070018";
    
    /**
     * 不支持的重排模型提供商: {0}
     */
    public static final String UNSUPPORTED_RERANK_PROVIDER = "30070019";

    // ==================== 模型服务错误 ====================
    
    /**
     * TTS服务调用失败: {0}
     */
    public static final String TTS_SERVICE_FAILED = "30070020";
    
    /**
     * 写入TTS数据到输出流失败: {0}
     */
    public static final String TTS_WRITE_FAILED = "30070021";
    
    /**
     * STT服务调用失败: {0}
     */
    public static final String STT_SERVICE_FAILED = "30070022";
    
    /**
     * 从输入流读取音频数据失败: {0}
     */
    public static final String STT_READ_FAILED = "30070023";
    
    /**
     * 自定义模型必须提供请求地址
     */
    public static final String CUSTOM_MODEL_REQUEST_URL_REQUIRED = "30070024";

    // ==================== 控制器错误 ====================
    
    /**
     * JSON格式化失败: {0}
     */
    public static final String JSON_FORMAT_FAILED = "30070025";
    
    /**
     * 模型测试失败: {0}
     */
    public static final String MODEL_TEST_FAILED = "30070026";
    
    /**
     * 清理过期模型调用日志失败
     */
    public static final String MODEL_LOG_CLEANUP_FAILED = "30070027";
    
    private ModelErrorCodeConstant() {
        // 工具类，不允许实例化
    }
}