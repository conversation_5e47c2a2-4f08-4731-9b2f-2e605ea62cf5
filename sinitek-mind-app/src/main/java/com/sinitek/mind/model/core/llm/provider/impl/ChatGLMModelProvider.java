package com.sinitek.mind.model.core.llm.provider.impl;

import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.IModelProviderService;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.zhipuai.ZhiPuAiChatModel;
import org.springframework.ai.zhipuai.ZhiPuAiChatOptions;
import org.springframework.ai.zhipuai.api.ZhiPuAiApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ChatGLM聊天模型提供商
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Component
public class ChatGLMModelProvider extends OpenAIChatModelProvider {

    @Autowired
    private IModelProviderService modelProviderService;

    @Override
    public String getProviderType() {
        return ModelProviderConstant.CHATGLM;
    }

    @Override
    public ChatModel createChatModel(SystemModelDTO modelDTO) {
        String apiKey = modelDTO.getRequestAuth();
        ZhiPuAiApi zhiPuAiApi = new ZhiPuAiApi(apiKey);

        OpenAiChatOptions chatOptions = super.createChatOptions(modelDTO);
        ZhiPuAiChatOptions zhiPuAiChatOptions = ZhiPuAiChatOptions.builder()
                .model(modelDTO.getModel())
                .temperature(chatOptions.getTemperature())
                .build();

        return new ZhiPuAiChatModel(zhiPuAiApi, zhiPuAiChatOptions);
    }
} 