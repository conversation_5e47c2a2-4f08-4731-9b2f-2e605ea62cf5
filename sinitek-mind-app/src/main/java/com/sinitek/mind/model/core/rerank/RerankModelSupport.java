package com.sinitek.mind.model.core.rerank;

import dev.langchain4j.data.document.Metadata;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.scoring.ScoringModel;
import dev.langchain4j.rag.content.Content;
import dev.langchain4j.rag.content.ContentMetadata;
import dev.langchain4j.rag.content.DefaultContent;
import dev.langchain4j.rag.content.aggregator.ContentAggregator;
import dev.langchain4j.rag.content.aggregator.ReRankingContentAggregator;
import dev.langchain4j.rag.query.Query;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 重排模型支持类
 *
 * <AUTHOR>
 * date 2025-08-06
 */
@Component
public class RerankModelSupport {

    private static final Logger log = LoggerFactory.getLogger(RerankModelSupport.class);

    /**
     * 组装ContentAggregator
     *  - 该类的作用：实现传递问题和向量结果片段，排序过滤出最终结果
     * @param scoringModel
     * @param minScore
     * @return
     */
    public ContentAggregator buildContentAggregator(ScoringModel scoringModel, Double minScore) {
        ReRankingContentAggregator.ReRankingContentAggregatorBuilder builder = ReRankingContentAggregator.builder();
        if (ObjectUtils.isNotEmpty(minScore)) {
            builder.minScore(minScore);
        }
        return builder
                .scoringModel(scoringModel)
                .build();
    }

    /**
     * 对文档进行重排
     * @param query 查询字符串
     * @param documents 文档列表
     * @param scoringModel 评分模型
     * @param minScore 最小分数阈值
     * @return 重排后的内容列表
     */
    public List<Content> rerankDocuments(String query, List<Document> documents, ScoringModel scoringModel, Double minScore) {
        Query queryObj = new Query(query);
        List<Content> contents = new ArrayList<>();
        for (Document document : documents) {
            Metadata metadata = new Metadata();
            metadata.put(ContentMetadata.EMBEDDING_ID.name(), document.getId());
            TextSegment textSegment = TextSegment.from(document.getText(), metadata);

            Content content1 = new DefaultContent(textSegment);

            contents.add(content1);
        }
        Collection<List<Content>> contentCollection = new ArrayList<>();
        contentCollection.add(contents);
        Map<Query, Collection<List<Content>>> queryToContents = new HashMap<>();
        queryToContents.put(queryObj, contentCollection);
        ContentAggregator contentAggregator = buildContentAggregator(scoringModel, minScore);
        return contentAggregator.aggregate(queryToContents);
    }

    /**
     * 对文档进行重排并返回映射
     * @param query 查询字符串
     * @param documents 文档列表
     * @param scoringModel 评分模型
     * @param minScore 最小分数阈值
     * @return 重排后的内容映射，key为ContentMetadata.EMBEDDING_ID的值
     */
    public Map<String, Content> rerankDocumentMap(String query, List<Document> documents, ScoringModel scoringModel, Double minScore) {
        List<Content> contentList = rerankDocuments(query, documents, scoringModel, minScore);
        Map<String, Content> contentMap = new HashMap<>();

        for (int i = 0; i < contentList.size(); i++) {
            Content content = contentList.get(i);
            TextSegment textSegment = content.textSegment();
            Metadata metadataMap = textSegment.metadata();
            metadataMap.put("index", i);

            String embeddingId = metadataMap.getString(ContentMetadata.EMBEDDING_ID.name());
            if (StringUtils.isNotBlank(embeddingId)) {
                contentMap.put(embeddingId, content);
            }
        }
        return contentMap;
    }
}
