package com.sinitek.mind.model.core.llm.service.impl;

import com.sinitek.mind.dataset.core.vector.IDatasetVectorStore;
import com.sinitek.mind.model.core.llm.LLMChatModelFactory;
import com.sinitek.mind.model.core.llm.service.IModelChatService;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.entity.ModelInvokeLog;
import com.sinitek.mind.model.enumerate.ModelTypeEnum;
import com.sinitek.mind.model.service.impl.AbstractModelInvokeLogService;
import com.sinitek.mind.model.util.PromptUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.content.Media;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 系统模型Chat服务层实现
 *
 * <AUTHOR>
 * date 2025-07-04
 */
@Slf4j
@Service
public class ModelChatServiceImpl extends AbstractModelInvokeLogService implements IModelChatService {

    @Autowired
    private LLMChatModelFactory chatModelFactory;

    @Autowired
    private IDatasetVectorStore datasetVectorStore;

    private final static String SYSTEM_PROMPT = """
            你绑定了系统知识库，同时知识库检索到了可能的文档内容
            文档内容如下:
            {documents}
            """;

    @Override
    public String chat(String message, String model) {
       return chat(message, model, new ArrayList<>());
    }

    @Override
    public String chat(String message, String model, List<String> datasetIdList) {
        // 创建只包含用户消息的Prompt并调用带Prompt的方法
        Prompt prompt = new Prompt(new UserMessage(message));
        return chat(prompt, model, datasetIdList).getResult().getOutput().getText();
    }

    @Override
    public ChatResponse chat(Prompt prompt, String model) {
        return chat(prompt, model, new ArrayList<>());
    }

    @Override
    public ChatResponse chat(Prompt prompt, String model, List<String> datasetIdList) {
        ModelInvokeLog modelInvokeLog = super.createModelInvokeLog(model);

        try {
            // 一次性获取模型和配置信息
            LLMChatModelFactory.ModelWithConfig modelWithConfig = chatModelFactory.getModelWithConfig(model);
            ChatModel chatModel = modelWithConfig.getChatModel();
            SystemModelDTO modelDTO = modelWithConfig.getModelDTO();
            Prompt finalPrompt = buildPrompt(prompt, modelDTO);

            // 进行知识库的向量搜索：条件必须相同向量模型的知识库，才能进行搜索
            String text = prompt.getUserMessage().getText();
            if (CollectionUtils.isNotEmpty(datasetIdList)) {
                List<Document> datasetDocumentList = datasetVectorStore.similaritySearch(text, datasetIdList);
                if (CollectionUtils.isNotEmpty(datasetDocumentList)) {
                    String documents = datasetDocumentList.stream().map(Document::getText).collect(Collectors.joining());
                    Message systemMessage = new SystemPromptTemplate(SYSTEM_PROMPT).createMessage(Map.of("documents", documents));
                    finalPrompt = PromptUtil.addMessage(finalPrompt, systemMessage);
                }
            }

            // 得到结果
            ChatResponse chatResponse = chatModel.call(finalPrompt);
            super.buildUsage(chatResponse.getMetadata().getUsage(), modelInvokeLog);

            return chatResponse;
        } catch (Exception e) {
            super.saveFailModelInvokeLog(modelInvokeLog, e.getMessage());
            throw e;
        } finally {
            super.saveSuccessModelInvokeLog(modelInvokeLog);
        }
    }

    @Override
    public Flux<ChatResponse> chatAsync(Prompt prompt, String model) {
        ModelInvokeLog modelInvokeLog = super.createModelInvokeLog(model);

        try {
            // 一次性获取模型和配置信息
            LLMChatModelFactory.ModelWithConfig modelWithConfig = chatModelFactory.getModelWithConfig(model);
            ChatModel chatModel = modelWithConfig.getChatModel();
            SystemModelDTO modelDTO = modelWithConfig.getModelDTO();
            Prompt finalPrompt = buildPrompt(prompt, modelDTO);
            Flux<ChatResponse> stream = chatModel.stream(finalPrompt);

            return stream.doOnNext(chatResponse -> {
                super.buildUsage(chatResponse.getMetadata().getUsage(), modelInvokeLog);
            });
        } catch (Exception e) {
            super.saveFailModelInvokeLog(modelInvokeLog, e.getMessage());
            throw e;
        } finally {
            super.saveSuccessModelInvokeLog(modelInvokeLog);
        }
    }

    @Override
    public ChatResponse recognizeImageContent(File imageFile, String model) {
        String IMAGE_RECOGNITION_PROMPT = "请帮我处理以下图片，完成3件事：" +
                "1. 识别图片核心内容：清晰描述图片的主题（如“产品包装”“手写笔记”“户外场景”“表格截图”等）、主要元素（如物体、人物、场景、颜色风格）；" +
                "2. 提取文本信息：若图片包含文字（无论打印体、手写体、截图文字），请逐行/按区域准确提取" +
                "3. 输出格式：用“图片内容描述”和“提取文本”两个标题分开呈现，语言简洁明了，不冗余。";

        FileSystemResource fileSystemResource = new FileSystemResource(imageFile);

        Optional<MediaType> mediaTypeOptional = MediaTypeFactory.getMediaType(fileSystemResource);
        MediaType mediaType = MediaType.IMAGE_PNG;
        if (mediaTypeOptional.isPresent()) {
            mediaType = mediaTypeOptional.get();
        }
        UserMessage userMessage = UserMessage.builder()
                .text(IMAGE_RECOGNITION_PROMPT)
                .media(new Media(mediaType, fileSystemResource))
                .build();

        Prompt prompt = new Prompt(userMessage);
        return this.chat(prompt, model);
    }

    /**
     * 构建出最终的Prompt
     * @param prompt
     * @param modelDTO
     * @return
     */
    private static Prompt buildPrompt(Prompt prompt, SystemModelDTO modelDTO) {
        // 获取默认提示词
        String defaultPrompt = modelDTO.getDefaultSystemChatPrompt();

        Prompt finalPrompt;
        // 如果有默认提示词且没有系统消息，添加系统消息
        if (StringUtils.isNotBlank(defaultPrompt)) {
            boolean hasSystemMessage = prompt.getInstructions().stream()
                    .anyMatch(msg -> msg instanceof SystemMessage);

            if (!hasSystemMessage) {
                finalPrompt = PromptUtil.addMessage(prompt, new SystemMessage(defaultPrompt));
            } else {
                finalPrompt = prompt;
            }
        } else {
            finalPrompt = prompt;
        }
        return finalPrompt;
    }

    @Override
    public ModelTypeEnum getCurrentModelTypeEnum() {
        return ModelTypeEnum.LLM;
    }
}
