package com.sinitek.mind.model.core.rerank.impl;

import com.sinitek.mind.model.core.rerank.IModelRerankService;
import com.sinitek.mind.model.core.rerank.RerankModelFactory;
import com.sinitek.mind.model.service.ISystemModelService;
import dev.langchain4j.data.document.Metadata;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.model.scoring.ScoringModel;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 系统评分模型服务层实现
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Service
public class ModelRerankServiceImpl implements IModelRerankService {

    @Autowired
    private RerankModelFactory rerankModelFactory;

    @Autowired
    private ISystemModelService systemModelService;

    @Override
    public Response<List<Double>> rerank(String query, List<Document> documents, String model) {
        ScoringModel scoringModel = rerankModelFactory.getRerankModel(model);

        List<TextSegment> segments = new ArrayList<>();
        for (Document document : documents) {
            String text = document.getText();
            Map<String, Object> metadataMap = document.getMetadata();

            Metadata metadata = new Metadata(metadataMap);
            TextSegment textSegment = new TextSegment(text, metadata);
            segments.add(textSegment);

        }
        return scoringModel.scoreAll(segments, query);
    }
}