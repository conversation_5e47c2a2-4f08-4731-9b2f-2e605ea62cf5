package com.sinitek.mind.model.core.rerank.provider.impl;

import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.core.rerank.provider.RerankModelProvider;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.IModelProviderService;
import dev.langchain4j.community.model.xinference.XinferenceScoringModel;
import dev.langchain4j.model.scoring.ScoringModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 其他评分模型提供商实现
 *
 * <AUTHOR>
 * date 2025-08-05
 */
@Component
public class OtherRerankModelProvider implements RerankModelProvider {

    @Autowired
    private IModelProviderService modelProviderService;

    @Override
    public String getProviderType() {
        return ModelProviderConstant.OTHER;
    }

    @Override
    public ScoringModel createRerankModel(SystemModelDTO modelDTO) {
        String defaultUrl = modelProviderService.getProviderDefaultUrl(getProviderType());
        String apiKey = modelDTO.getRequestAuth();
        String baseUrl = StringUtils.defaultIfBlank(modelDTO.getRequestUrl(), defaultUrl);
        String modelName = modelDTO.getModel();

        return XinferenceScoringModel.builder()
                .baseUrl(baseUrl)
                .apiKey(apiKey)
                .modelName(modelName)
                .timeout(Duration.ofSeconds(60))
                .maxRetries(1)
                .logRequests(true)
                .logResponses(true)
                .build();
    }
}
