package com.sinitek.mind.model.core.rerank.provider;

import com.sinitek.mind.model.dto.SystemModelDTO;
import dev.langchain4j.model.scoring.ScoringModel;

/**
 * 重排模型提供商接口
 *
 * <AUTHOR>
 * date 2025-07-07
 */
public interface RerankModelProvider {

    /**
     * 获取支持的提供商类型
     * @return 提供商类型
     */
    String getProviderType();

    /**
     * 创建重排模型
     * @param modelDTO 模型详情
     * @return 重排模型实例
     */
    ScoringModel createRerankModel(SystemModelDTO modelDTO);

    /**
     * 是否支持该提供商
     * @param providerType 提供商类型
     * @return 是否支持
     */
    default boolean supports(String providerType) {
        return getProviderType().equalsIgnoreCase(providerType);
    }
}