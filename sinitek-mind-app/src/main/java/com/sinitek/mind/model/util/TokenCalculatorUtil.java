package com.sinitek.mind.model.util;

import com.knuddels.jtokkit.Encodings;
import com.knuddels.jtokkit.api.Encoding;
import com.knuddels.jtokkit.api.EncodingRegistry;
import com.knuddels.jtokkit.api.ModelType;

/**
 * Token计算工具类
 *
 * <AUTHOR>
 * date 2025-07-08
 */
public class TokenCalculatorUtil {

    // 获取默认的编码注册表
    private static final EncodingRegistry registry = Encodings.newDefaultEncodingRegistry();

    // 默认使用GPT-4的编码方式
    private static final Encoding encoding = registry.getEncodingForModel(ModelType.GPT_4);

    /**
     * 估算文本的tokens数量（使用GPT-4编码）
     * @param text 要估算的文本
     * @return tokens数量
     */
    public static int estimateTokens(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        // 编码文本并返回tokens数量
        return encoding.encode(text).size();
    }
} 