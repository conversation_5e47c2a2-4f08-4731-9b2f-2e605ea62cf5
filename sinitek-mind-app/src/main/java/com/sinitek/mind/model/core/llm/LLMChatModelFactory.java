package com.sinitek.mind.model.core.llm;

import com.sinitek.mind.model.constant.ModelErrorCodeConstant;
import com.sinitek.mind.model.core.llm.provider.ChatModelProvider;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.sirm.framework.exception.BussinessException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * ChatModel 工厂层
 *
 * <AUTHOR>
 * date 2025-07-04
 */
@Component
public class LLMChatModelFactory {

    @Autowired
    private ISystemModelService systemModelService;
    
    @Autowired
    private List<ChatModelProvider> chatModelProviders;

    /**
     * 根据模型ID创建聊天模型
     * @param modelId 模型ID
     * @return 聊天模型实例
     */
    public ChatModel chatModel(String modelId) {
        SystemModelDTO modelDTO = getModelDTO(modelId);
        return createChatModel(modelDTO);
    }
    
    /**
     * 根据模型ID获取模型详情并创建聊天模型
     * @param modelId 模型ID
     * @return 包含聊天模型和模型详情的对象
     */
    public ModelWithConfig getModelWithConfig(String modelId) {
        SystemModelDTO modelDTO = getModelDTO(modelId);
        ChatModel chatModel = createChatModel(modelDTO);
        return new ModelWithConfig(chatModel, modelDTO);
    }
    
    /**
     * 根据模型配置创建聊天模型
     * @param modelDTO 模型配置
     * @return 聊天模型实例
     */
    public ChatModel createChatModel(SystemModelDTO modelDTO) {
        if (ObjectUtils.isEmpty(modelDTO)) {
            throw new BussinessException(ModelErrorCodeConstant.MODEL_CONFIG_EMPTY);
        }
        
        String provider = modelDTO.getProvider();
        if (StringUtils.isBlank(provider)) {
            throw new BussinessException(ModelErrorCodeConstant.PROVIDER_EMPTY);
        }
        
        // 查找对应的提供商实现
        for (ChatModelProvider chatModelProvider : chatModelProviders) {
            if (chatModelProvider.supports(provider)) {
                return chatModelProvider.createChatModel(modelDTO);
            }
        }
        
        throw new BussinessException(ModelErrorCodeConstant.UNSUPPORTED_PROVIDER);
    }
    
    /**
     * 获取模型配置信息
     * @param modelId 模型ID
     * @return 模型配置信息
     */
    private SystemModelDTO getModelDTO(String modelId) {
        // 直接从服务获取模型详情
        SystemModelDTO modelDTO = systemModelService.getModelDetail(modelId);
        
        if (ObjectUtils.isEmpty(modelDTO)) {
            throw new BussinessException(ModelErrorCodeConstant.MODEL_NOT_FOUND);
        }
        
        return modelDTO;
    }
    
    /**
     * 聊天模型与模型配置的包装类
     */
    public static class ModelWithConfig {

        private final ChatModel chatModel;
        private final SystemModelDTO modelDTO;
        
        public ModelWithConfig(ChatModel chatModel, SystemModelDTO modelDTO) {
            this.chatModel = chatModel;
            this.modelDTO = modelDTO;
        }
        
        public ChatModel getChatModel() {
            return chatModel;
        }
        
        public SystemModelDTO getModelDTO() {
            return modelDTO;
        }
    }
} 