package com.sinitek.mind.model.core.stt;

import com.sinitek.mind.model.constant.ModelErrorCodeConstant;
import com.sinitek.mind.model.core.stt.provider.SpeechToTextProvider;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.sirm.framework.exception.BussinessException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.List;

/**
 * STT模型工厂层
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Component
public class STTModelFactory {

    @Autowired
    private ISystemModelService systemModelService;
    
    @Autowired
    private List<SpeechToTextProvider> sttProviders;

    /**
     * 根据模型ID获取STT服务提供者
     * @param modelId 模型ID
     * @return STT服务提供者实例
     */
    public SpeechToTextProvider getProvider(String modelId) {
        SystemModelDTO modelDTO = systemModelService.getModelDetail(modelId);
        
        if (ObjectUtils.isEmpty(modelDTO)) {
            throw new BussinessException(ModelErrorCodeConstant.MODEL_NOT_FOUND);
        }

        String provider = modelDTO.getProvider();
        
        if (StringUtils.isBlank(provider)) {
            throw new BussinessException(ModelErrorCodeConstant.PROVIDER_EMPTY);
        }
        
        // 查找对应的提供商实现
        for (SpeechToTextProvider sttProvider : sttProviders) {
            if (sttProvider.supports(provider)) {
                return sttProvider;
            }
        }
        
        throw new BussinessException(ModelErrorCodeConstant.UNSUPPORTED_STT_PROVIDER);
    }
    
    /**
     * 识别语音数据
     * @param audioData 语音数据字节数组
     * @param modelId 模型ID
     * @return 识别结果文本
     */
    public String recognizeSpeech(byte[] audioData, String modelId) {
        SystemModelDTO modelDTO = systemModelService.getModelDetail(modelId);
        SpeechToTextProvider provider = getProvider(modelId);
        return provider.recognizeSpeech(audioData, modelDTO);
    }
    
    /**
     * 识别语音流数据
     * @param audioStream 语音数据流
     * @param modelId 模型ID
     * @return 识别结果文本
     */
    public String recognizeSpeech(InputStream audioStream, String modelId) {
        SystemModelDTO modelDTO = systemModelService.getModelDetail(modelId);
        SpeechToTextProvider provider = getProvider(modelId);
        return provider.recognizeSpeech(audioStream, modelDTO);
    }
} 