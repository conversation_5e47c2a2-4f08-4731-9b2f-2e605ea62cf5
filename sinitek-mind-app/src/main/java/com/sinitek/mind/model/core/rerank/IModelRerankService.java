package com.sinitek.mind.model.core.rerank;

import dev.langchain4j.model.output.Response;
import org.springframework.ai.document.Document;

import java.util.List;

/**
 * 系统评分模型服务层
 *
 * <AUTHOR>
 * date 2025-07-07
 */
public interface IModelRerankService {

    /**
     * 文档评分排序
     * @param query 查询语句
     * @param documents 待评分排序的文档列表
     * @param model 模型标识
     * @return 评分排序后的文档列表
     */
    Response<List<Double>> rerank(String query, List<Document> documents, String model);
}