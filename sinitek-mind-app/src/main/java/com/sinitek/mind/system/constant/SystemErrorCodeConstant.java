package com.sinitek.mind.system.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 系统模块错误码常量类
 * 
 * 错误码规则：30-01-YYYY
 * 30 - 固定前缀
 * 01 - 系统模块
 * YYYY - 具体错误码
 *
 * <AUTHOR>
 * date 2025-08-14
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public final class SystemErrorCodeConstant {

    // ==================== 系统模块错误码 ====================
    
    /**
     * 配置文件不存在
     */
    public static final String CONFIG_FILE_NOT_FOUND = "30010001";
    
    /**
     * 配置文件读取失败
     */
    public static final String CONFIG_FILE_READ_FAILED = "30010002";
    
    /**
     * 配置文件解析失败
     */
    public static final String CONFIG_FILE_PARSE_FAILED = "30010003";
    
    /**
     * 配置路径未配置
     */
    public static final String CONFIG_PATH_NOT_CONFIGURED = "30010004";
    
    /**
     * 系统配置不存在
     */
    public static final String SYSTEM_CONFIG_NOT_FOUND = "30010005";
    
    /**
     * 系统配置类型不支持
     */
    public static final String SYSTEM_CONFIG_TYPE_NOT_SUPPORTED = "30010006";
    
    /**
     * 系统配置更新失败
     */
    public static final String SYSTEM_CONFIG_UPDATE_FAILED = "30010007";
    
    /**
     * 系统配置格式错误
     */
    public static final String SYSTEM_CONFIG_FORMAT_ERROR = "30010008";
    
    /**
     * 系统初始化数据获取失败
     */
    public static final String SYSTEM_INIT_DATA_FAILED = "30010009";
    
    /**
     * 系统版本信息获取失败
     */
    public static final String SYSTEM_VERSION_GET_FAILED = "30010010";
    
    /**
     * 缓冲ID无效
     */
    public static final String BUFFER_ID_INVALID = "30010011";
    
    /**
     * 系统环境配置获取失败
     */
    public static final String SYSTEM_ENV_CONFIG_FAILED = "30010012";
    
    /**
     * 默认模型配置获取失败
     */
    public static final String DEFAULT_MODEL_CONFIG_FAILED = "30010013";
    
    /**
     * 激活模型列表获取失败
     */
    public static final String ACTIVE_MODEL_LIST_FAILED = "30010014";
    
    /**
     * 模型配置转换失败
     */
    public static final String MODEL_CONFIG_CONVERT_FAILED = "30010015";
    
    /**
     * FastGPT配置获取失败
     */
    public static final String FASTGPT_CONFIG_GET_FAILED = "30010016";
    
    /**
     * FastGPT配置更新失败
     */
    public static final String FASTGPT_CONFIG_UPDATE_FAILED = "30010017";
    
    /**
     * FastGPT配置缓存更新失败
     */
    public static final String FASTGPT_CACHE_UPDATE_FAILED = "30010018";
    
    /**
     * 许可证数据获取失败
     */
    public static final String LICENSE_DATA_GET_FAILED = "30010019";
    
    /**
     * 系统全局变量初始化失败
     */
    public static final String SYSTEM_GLOBALS_INIT_FAILED = "30010020";
    
    /**
     * 系统全局变量更新失败
     */
    public static final String SYSTEM_GLOBALS_UPDATE_FAILED = "30010021";
    
    /**
     * 系统全局变量获取失败
     */
    public static final String SYSTEM_GLOBALS_GET_FAILED = "30010022";
    
    /**
     * 系统安全环境配置获取失败
     */
    public static final String SYSTEM_SECURITY_ENV_FAILED = "30010023";
    
    /**
     * 系统安全验证失败
     */
    public static final String SYSTEM_SECURITY_VALIDATE_FAILED = "30010024";
}