#是否加入微服务中.
sirm.cloud.enable = false
#微服务调用，需要安全验证的拦截路径
sirm.security.path=/frontend/api/**
#微服务调用，需要安全验证的排除路径
sirm.security.excludepath=/frontend/api/security/public-key,/frontend/api/properties,/frontend/api/login,/frontend/api/captcha,/frontend/api/ue/exec,/frontend/api/user/current,/hystrix,/service/logon.action

appid=sirm

# 是否开启多租户模式
isMultiTenant=false

# 用户身份验证模式(0=本地（默认）1=表单验证 2=LDAP验证方式 3=自定义验证方式)
usercheckmode = 0
# 如果usercheckmode = 3 那么usercheckclassname 为必填
usercheckclassname= com.sinitek.sirm.framework.um.SimpleUserChecker
language.default = zh_cn
#开启国际化支持
language.enable = false
#是否压缩js和css   enable=true(压缩)、enable=false(不压缩)
compress.enable=false
#默认主题：可选default和blue和orange
defaulttheme=2014style

#登录超时的时间 （分钟）
userchecktimeout=120

#必填  管理员账号名
#ldap.principal = cn={username},dc=example,dc=com
#必填  LDAP服务器地址
#ldap.host=localhost
#必填  LDAP端口，默认389
#ldap.port=389
#必填  LDAP服务名
#ldap.basedn=dc=example,dc=com
#必填  帐号
#ldap.filter=(cn={username})
#必填  默认名称的属性名
#ldap.displaynameattr=cn
#可选
#ldap.searchbasedn=

#选填，本地登录的用户名
usercheck.loalcheckuser = admin

app.logonurl = /logon.jsp
app.firsturl = /first.jsp
app.js.contextpath = /
#app.logout.redirecturl=

#数据同步服务
#是否启用数据同步服务
datasyn.enabled=false
#数据同步id
datasyn.id=debtsystem
#数据同步目标，webservice，多个webserviceurl使用逗号“,”隔开
datasyn.targets=http://***********:8081/sirmapp/services/DataSynReceiveService
#datasyn.targets=http://*************:8080/sirmapp/services/DataSynReceiveService

sso.enable=false
sso.mainhost=false
#sso.sessionid=sirmapp


#是否启用跨站点请求验证
security.csrf.enable=false


qrtz.loadfrom=db,local

#登录是否使用验证码
logon.code = false
#登录验证码模式, 这个参数只有在logon.code = true 的时候才有效， 0为普通方式， 1为当错误 N次以后，后续登录需要输入验证码。
logon.code.mode = 0
#是否开启自动登录
logon.rememberMe = true
#存储sessionid的CookieName
sessionid.cookie.name=accesstoken
#session过期时间(用户登陆过期时间)
session.timeout=1800000
#存储rememberMe的CookieName
rememberMe.cookie.name=rememberMe
#rememberMe过期时间(自动登陆过期时间)
rememberMe.timeout=31536000
#rememberMe值的加解密Key
rememberMe.cipherKey=Vn3p+XWDvHbtKvXXYenifw==

#加密密码
despassword=sirm2012
#加密有效时间
destimeout=120

#是否启用cookie安全性检查
checker.cookie = true

#用户密码是否需要复杂加密
security.usrpwdsalt.enabled = false

#用户密码复杂加密的秘钥值(security.usrpwdsalt.enabled设置为true的时候有效，默认为”sirm”)
security.userpwdsalt.value = sirm

#支持本地关闭定时任务(取值有true(默认)|false，当quartz.enabled=false时，定时任务不生效)
quartz.enabled  = true

#支持本地指定不同的qrtz_表前缀(目前支持qrtz_(默认)，后续可能会新增)
quartz.prefix = qrtz_

#是否启用集群登陆模式
cluster.enable=false

#支持不同的数据库：orcale,mysql,sqlserver
db.type=mysql
#sqlserver的dbuser
#db.dbuser=dbo

#默认的aes.key
aes.key=sirmpasswordcryp

iframe.sameorigin.enable=true

#是否独立运行工作流
sinicube.workflow.dependent.enable=true

# Sql拦截器排除的url
#sql.interceptor.excludes=

#table默认限制最大导出行数
table.export.maxrow = 10000

# 公私钥,当Redis挂掉时，临时使用的公私钥
rsa.privateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMJvyftn+1AKE3KaXzGYpS4yuZyKHkO8liF3IpcINJFLWXI7yiIBLWWbFLbgwnV/aJV9iJmUQ6sjS2pw9fmxAvoDP+4uHJlAaUkzVHZOyW/mHaCdmREtEoq1PXo5ZJyKnK9Xs0B7wrUPjeyTUSrFZsyGm4gMJSEdPhriauuyzzPNAgMBAAECgYEAv84bSECZL6ng6WrTgU99kwdDBuNRW6zLxROLMcZMZRAZmpDUo5rZt6O2WXl7GwGmn+GIQUh7QHW+za/FVp2BunsT1qYgIXtRneocNNvhk668Y9vHzF1p0j6ThXFHf1aHQTZC7hTbKzqc7ju98JAx+EThB1XE8A9gLBNj4Dz9sEECQQDukED32WPKXhuMReMj99sRNijwOi/GZSaiSmaseUZ7NSOFUMiBysS4MCJeQFWUupdPLXtY6IQkfwWO40NcKipFAkEA0KXhlY47gYPH0OEzKHLxvB+hdc7tCb2OXOlJp13uC2MMhEX9EMuxHsfKOHBVaEopa8HahsqEQumIy5lBnkT/6QJAXF5QOWbbg3xikDJzGvcZxBVDTphI7Tk29zl1faxLROMzSKgZql93QZWpiPLB8B86/LYwfyEwIO1pmoSWDTZqPQJAAaLrbALbkJ55+LdrcUvFfZY56/Sdg1ALR8tEmp1v+oLiXD84RuNyFi229k5bPSAnxwKUNi5sbyHpwr8G9rjP0QJBAKjZBywSFSdcamDoLXkKXLwmv9pz/uLmPveAqVR+YnVhelUQMUcBqBy4zqRCAdYxbb0wjVLSvTDIa8lSefuzO8o=
rsa.publicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCb8n7Z/tQChNyml8xmKUuMrmcih5DvJYhdyKXCDSRS1lyO8oiAS1lmxS24MJ1f2iVfYiZlEOrI0tqcPX5sQL6Az/uLhyZQGlJM1R2Tslv5h2gnZkRLRKKtT16OWScipyvV7NAe8K1D43sk1EqxWbMhpuIDCUhHT4a4mrrss8zzQIDAQAB

# 配置文件指定 临时文件路径   注: 指定临时文件路径为Java默认的目录,使多个操作系统能兼容运行
localsettingpriority=true
setting.tempdir=${java.io.tmpdir}