-- MySQL建表语句
-- 字符集: utf8mb4, 排序规则: utf8mb4_general_ci
-- 包含BaseEntity通用字段和所有实体的特定字段
-- 设置typeHandler属性处理JSON类型数据

-- 删除已存在的表
DROP TABLE IF EXISTS `mind_open_api`;
DROP TABLE IF EXISTS `mind_out_link`;
DROP TABLE IF EXISTS `mind_app`;
DROP TABLE IF EXISTS `mind_app_version`;
DROP TABLE IF EXISTS `mind_image`;
DROP TABLE IF EXISTS `mind_app_template`;
DROP TABLE IF EXISTS `mind_chat`;
DROP TABLE IF EXISTS `mind_chat_item`;
DROP TABLE IF EXISTS `mind_chat_input_guide`;

-- 创建mind_open_api表
CREATE TABLE `mind_open_api` (
    `id` BIGINT NOT NULL COMMENT '主键',
    `createtimestamp` DATETIME NOT NULL COMMENT '创建时间',
    `updatetimestamp` DATETIME NOT NULL COMMENT '修改时间',
    `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `org_id` VARCHAR(255) COMMENT '所属用户',
    `api_key` VARCHAR(255) COMMENT 'api密钥',
    `app_id` BIGINT COMMENT '关联的应用ID（可选）',
    `name` VARCHAR(255) COMMENT '密钥名称',
    `expired_time` DATETIME COMMENT '过期时间',
    `last_used_time` DATETIME COMMENT '最后使用时间',
    PRIMARY KEY (`id`)
) COMMENT='OpenAPI密钥表';

-- 创建mind_out_link表
CREATE TABLE `mind_out_link` (
    `id` BIGINT NOT NULL COMMENT '主键',
    `createtimestamp` DATETIME NOT NULL COMMENT '创建时间',
    `updatetimestamp` DATETIME NOT NULL COMMENT '修改时间',
    `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `share_id` VARCHAR(255) COMMENT '分享链接ID',
    `org_id` VARCHAR(255) COMMENT '组织ID',
    `app_id` BIGINT COMMENT '应用ID',
    `type` VARCHAR(255) COMMENT '类型',
    `name` VARCHAR(255) COMMENT '名称',
    `last_time` DATETIME COMMENT '最后使用时间',
    `response_detail` TINYINT(1) COMMENT '是否响应详情',
    `show_node_status` TINYINT(1) COMMENT '是否显示节点状态',
    `show_raw_source` TINYINT(1) COMMENT '是否显示原始来源',
    `expired_time` DATETIME COMMENT '过期时间',
    `qpm` INT COMMENT '每分钟查询限制',
    `hook_url` TEXT COMMENT '钩子URL',
    `immediate_response` TEXT COMMENT '立即响应',
    `default_response` TEXT COMMENT '默认响应',
    PRIMARY KEY (`id`)
) COMMENT='免登录链接表';

-- 创建mind_app表
CREATE TABLE `mind_app` (
    `id` BIGINT NOT NULL COMMENT '主键',
    `createtimestamp` DATETIME NOT NULL COMMENT '创建时间',
    `updatetimestamp` DATETIME NOT NULL COMMENT '修改时间',
    `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `parent_id` BIGINT COMMENT '父应用ID',
    `org_id` VARCHAR(255) COMMENT '组织ID',
    `type` VARCHAR(255) COMMENT '应用类型',
    `name` VARCHAR(255) COMMENT '应用名称',
    `avatar` LONGTEXT COMMENT '头像',
    `intro` TEXT COMMENT '简介',
    `namespace` VARCHAR(255) COMMENT '命名空间',
    `modules` JSON COMMENT '模块列表',
    `edges` JSON COMMENT '边列表',
    `plugin_data` JSON COMMENT '插件数据',
    `chat_config` JSON COMMENT '聊天配置',
    `scheduled_trigger_config` JSON COMMENT '定时触发配置',
    `scheduled_trigger_next_time` DATETIME COMMENT '定时触发下次时间',
    `inited` TINYINT(1) COMMENT '是否初始化',
    `inherit_permission` TINYINT(1) COMMENT '是否继承权限',
    `default_permission` INT COMMENT '默认权限',
    `child_tool_ids` JSON COMMENT '子工具ID列表（用于MCP工具集）',
    `mcp_tools_config` JSON COMMENT 'MCP工具集配置',
    `tool_config` JSON COMMENT '工具配置（用于子工具）',
    PRIMARY KEY (`id`)
) COMMENT='应用表';

-- 创建mind_app_version表
CREATE TABLE `mind_app_version` (
    `id` BIGINT NOT NULL COMMENT '主键',
    `createtimestamp` DATETIME NOT NULL COMMENT '创建时间',
    `updatetimestamp` DATETIME NOT NULL COMMENT '修改时间',
    `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `org_id` VARCHAR(255) COMMENT '组织ID',
    `app_id` BIGINT COMMENT '应用ID',
    `nodes` JSON COMMENT '节点列表',
    `edges` JSON COMMENT '边列表',
    `chat_config` JSON COMMENT '聊天配置',
    `name` VARCHAR(255) COMMENT '版本名称',
    `username` VARCHAR(255) COMMENT '用户名',
    `avatar` LONGTEXT COMMENT '头像',
    `is_publish` TINYINT(1) COMMENT '是否发布',
    PRIMARY KEY (`id`)
) COMMENT='应用版本表';

-- 创建mind_image表
CREATE TABLE `mind_image` (
    `id` BIGINT NOT NULL COMMENT '主键',
    `createtimestamp` DATETIME NOT NULL COMMENT '创建时间',
    `updatetimestamp` DATETIME NOT NULL COMMENT '修改时间',
    `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `expired_time` DATETIME COMMENT '过期时间',
    `binary` LONGBLOB COMMENT '二进制数据',
    `metadata` JSON COMMENT '元数据',
    PRIMARY KEY (`id`)
) COMMENT='图片表';

-- 创建mind_app_template表
CREATE TABLE `mind_app_template` (
    `id` BIGINT NOT NULL COMMENT '主键',
    `createtimestamp` DATETIME NOT NULL COMMENT '创建时间',
    `updatetimestamp` DATETIME NOT NULL COMMENT '修改时间',
    `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `template_id` VARCHAR(255) COMMENT '模板ID',
    `name` VARCHAR(255) COMMENT '名称',
    `intro` TEXT COMMENT '介绍',
    `avatar` LONGTEXT COMMENT '头像',
    `author` VARCHAR(255) COMMENT '作者',
    `tags` JSON COMMENT '标签',
    `type` VARCHAR(255) COMMENT '类型',
    `is_active` TINYINT(1) COMMENT '是否激活',
    `user_guide` JSON COMMENT '用户指南',
    `is_quick_template` TINYINT(1) COMMENT '是否快速模板',
    `order` INT COMMENT '排序',
    `workflow` JSON COMMENT '工作流',
    `weight` INT COMMENT '权重',
    PRIMARY KEY (`id`)
) COMMENT='应用模板表';

-- 创建chat表
CREATE TABLE `mind_chat` (
    `id` BIGINT NOT NULL COMMENT '主键',
    `createtimestamp` DATETIME NOT NULL COMMENT '创建时间',
    `updatetimestamp` DATETIME NOT NULL COMMENT '修改时间',
    `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `chat_id` VARCHAR(255) COMMENT '聊天Id',
    `org_id` VARCHAR(255) COMMENT '用户Id',
    `app_id` BIGINT COMMENT '应用id',
    `title` VARCHAR(255) COMMENT '标题',
    `custom_title` VARCHAR(255) COMMENT '自定义标题',
    `top` TINYINT(1) COMMENT '是否置顶',
    `source` VARCHAR(255) COMMENT '来源-ChatSourceEnum',
    `source_name` VARCHAR(255) COMMENT '来源名称',
    `share_id` VARCHAR(255) COMMENT '分享链接id',
    `out_link_uid` VARCHAR(255) COMMENT '分享链接用户id',
    `variable_list` JSON COMMENT '变量列表',
    `welcome_text` TEXT COMMENT '欢迎语',
    `variables` JSON COMMENT '变量',
    `plugin_inputs` JSON COMMENT '插件输入',
    `metadata` JSON COMMENT '元数据',
    PRIMARY KEY (`id`)
) COMMENT='聊天表';

-- 创建chat_item表
CREATE TABLE `mind_chat_item` (
    `id` BIGINT NOT NULL COMMENT '主键',
    `createtimestamp` DATETIME NOT NULL COMMENT '创建时间',
    `updatetimestamp` DATETIME NOT NULL COMMENT '修改时间',
    `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `org_id` VARCHAR(255) COMMENT '用户id',
    `chat_id` VARCHAR(255) COMMENT '聊天id',
    `data_id` VARCHAR(255) COMMENT '数据id',
    `app_id` BIGINT COMMENT '应用id',
    `time` DATETIME COMMENT '时间',
    `hide_in_ui` TINYINT(1) COMMENT '是否隐藏前端ui',
    `obj` VARCHAR(255) COMMENT '对话主角-ChatRoleEnum',
    `value` JSON COMMENT '具体的内容',
    `memories` JSON COMMENT '记忆',
    `error_msg` TEXT COMMENT '错误信息',
    `user_good_feedback` TEXT COMMENT '好反馈',
    `user_bad_feedback` TEXT COMMENT '坏反馈',
    `custom_feedbacks` JSON COMMENT '自定义反馈',
    `admin_feedback` JSON COMMENT '管理员反馈',
    `response_data` JSON COMMENT '响应数据',
    `duration_seconds` DOUBLE COMMENT '消耗时长',
    PRIMARY KEY (`id`)
) COMMENT='聊天项表';

-- 创建chat_input_guide表
CREATE TABLE `mind_chat_input_guide` (
    `id` BIGINT NOT NULL COMMENT '主键',
    `createtimestamp` DATETIME NOT NULL COMMENT '创建时间',
    `updatetimestamp` DATETIME NOT NULL COMMENT '修改时间',
    `version` INT NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `app_id` BIGINT COMMENT '应用id',
    `text` TEXT COMMENT '内容',
    PRIMARY KEY (`id`)
) COMMENT='聊天输入引导表';