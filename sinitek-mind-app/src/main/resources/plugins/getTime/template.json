{"_id": {"$oid": "6892ce1c0ce6675021b52028"}, "parentId": {"$oid": "6892cc540ce6675021b52027"}, "teamId": "", "tmbId": "999000001", "type": "plugin", "version": "v2", "name": "55555555", "avatar": "/imgs/workflow/http.png", "intro": "66666666", "updateTime": {"$date": "2025-08-06T03:38:04.495Z"}, "modules": [{"nodeId": "c923e0c74f294e74", "position": {"x": 473.55206291900333, "y": -145.65080850146154}, "flowNodeType": "pluginInput", "abandon": false, "avatar": "core/workflow/template/workflowStart", "name": "插件输入", "intro": "插件输入模块", "showStatus": true, "inputs": [{"selectedTypeIndex": 0, "renderTypeList": ["input", "reference"], "key": "apikey", "valueType": "string", "label": "apikey", "required": true, "canEdit": true, "canSelectFile": true, "canSelectImg": true, "maxFiles": 5, "defaultValue": ""}], "outputs": [{"id": "37933a36bbf64ddb", "type": "hidden", "key": "apikey", "valueType": "string", "label": "apikey"}], "isFolder": false}, {"nodeId": "2e64aef95df747e4", "position": {"x": 1041.1505186414104, "y": -480.9168163795506}, "flowNodeType": "httpRequest468", "abandon": false, "avatar": "core/workflow/template/httpRequest", "name": "HTTP 请求", "intro": "发送HTTP请求", "showStatus": true, "inputs": [{"renderTypeList": ["addInputParam"], "key": "system_addInputParam", "valueType": "dynamic", "label": "", "description": "common:core.module.input.description.HTTP Dynamic Input", "required": false}, {"renderTypeList": ["custom"], "key": "system_httpTimeout", "valueType": "number", "value": 30, "label": "", "required": true}, {"renderTypeList": ["hidden"], "key": "system_header_secret", "valueType": "object", "label": "", "required": false}, {"renderTypeList": ["addInputParam"], "key": "system_addInputParam", "valueType": "dynamic", "label": "", "debugLabel": "", "description": "common:core.module.input.description.HTTP Dynamic Input", "required": false, "toolDescription": ""}, {"renderTypeList": ["custom"], "key": "system_httpMethod", "valueType": "string", "value": "POST", "label": "", "debugLabel": "", "required": true, "toolDescription": ""}, {"renderTypeList": ["custom"], "key": "system_httpTimeout", "valueType": "number", "value": 30, "label": "", "debugLabel": "", "required": true, "toolDescription": ""}, {"renderTypeList": ["hidden"], "key": "system_httpReqUrl", "valueType": "string", "value": "http://**************:9997/wxmanage/milvus/getuserknowledgelist.json", "label": "", "debugLabel": "", "description": "common:core.module.input.description.Http Request Url", "required": false, "toolDescription": "", "placeholder": "https://api.ai.com/getInventory"}, {"renderTypeList": ["hidden"], "key": "system_header_secret", "valueType": "object", "label": "", "debugLabel": "", "required": false, "toolDescription": ""}, {"renderTypeList": ["custom"], "key": "system_httpHeader", "valueType": "any", "value": [], "label": "", "debugLabel": "", "description": "common:core.module.input.description.Http Request Header", "required": false, "toolDescription": "", "placeholder": "common:core.module.input.description.Http Request Header"}, {"renderTypeList": ["hidden"], "key": "system_httpParams", "valueType": "any", "value": [{"type": "string", "value": "{{$c923e0c74f294e74.apikey$}}", "key": "apikey"}], "label": "", "debugLabel": "", "required": false, "toolDescription": ""}, {"renderTypeList": ["hidden"], "key": "system_httpJsonBody", "valueType": "any", "value": "{ }", "label": "", "debugLabel": "", "required": false, "toolDescription": ""}, {"renderTypeList": ["hidden"], "key": "system_httpFormBody", "valueType": "any", "value": [], "label": "", "debugLabel": "", "required": false, "toolDescription": ""}, {"renderTypeList": ["hidden"], "key": "system_httpContentType", "valueType": "string", "value": "json", "label": "", "debugLabel": "", "required": false, "toolDescription": ""}, {"renderTypeList": ["reference"], "key": "apikey", "valueType": "string", "value": ["c923e0c74f294e74", "37933a36bbf64ddb"], "label": "apikey", "canEdit": true}], "outputs": [{"id": "error", "type": "static", "key": "error", "valueType": "object", "label": "workflow:request_error", "description": "HTTP请求错误信息，成功时返回空"}, {"id": "httpRawResponse", "type": "static", "key": "httpRawResponse", "valueType": "string", "label": "HTTP响应", "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "required": true}], "isFolder": false}, {"nodeId": "cd8c62992ac2438b", "position": {"x": 1847.6, "y": 5.11}, "flowNodeType": "pluginOutput", "abandon": false, "avatar": "core/workflow/template/pluginOutput", "name": "插件输出", "intro": "插件输出模块", "showStatus": true, "inputs": [{"renderTypeList": ["reference"], "key": "result", "valueType": "string", "value": ["2e64aef95df747e4", "httpRawResponse"], "label": "result", "description": "", "required": false, "canEdit": true}], "outputs": [{"id": "cd8c62992ac2438b", "type": "static", "key": "result", "valueType": "string", "label": "result"}], "isFolder": false}], "edges": [{"source": "c923e0c74f294e74", "sourceHandle": "c923e0c74f294e74-source-right", "target": "2e64aef95df747e4", "targetHandle": "2e64aef95df747e4-target-left"}, {"source": "2e64aef95df747e4", "sourceHandle": "2e64aef95df747e4-source-right", "target": "cd8c62992ac2438b", "targetHandle": "cd8c62992ac2438b-target-left"}], "pluginData": {"pluginUniId": "55555555"}, "inheritPermission": true, "_class": "com.sinitek.mind.core.app.entity.App"}