{"name": "HTTP 请求", "intro": "发送HTTP请求", "avatar": "core/workflow/template/httpRequest", "folder": false, "inputs": [{"key": "system_addInputParam", "max": null, "min": null, "pro": null, "list": null, "step": null, "enums": null, "label": "", "value": null, "canEdit": null, "markList": null, "maxFiles": null, "required": false, "maxLength": null, "valueDesc": null, "valueType": "dynamic", "debugLabel": "", "deprecated": null, "toolOutput": null, "description": "common:core.module.input.description.HTTP Dynamic Input", "placeholder": null, "canSelectImg": null, "defaultValue": null, "llmModelType": null, "canSelectFile": null, "renderTypeList": ["addInputParam"], "toolDescription": "", "customInputConfig": {"showDescription": false, "showDefaultValue": true, "selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectDataset", "selectApp"]}, "selectedTypeIndex": null, "referencePlaceholder": null}, {"key": "system_httpMethod", "max": null, "min": null, "pro": null, "list": null, "step": null, "enums": null, "label": "", "value": "POST", "canEdit": null, "markList": null, "maxFiles": null, "required": true, "maxLength": null, "valueDesc": null, "valueType": "string", "debugLabel": "", "deprecated": null, "toolOutput": null, "description": null, "placeholder": null, "canSelectImg": null, "defaultValue": null, "llmModelType": null, "canSelectFile": null, "renderTypeList": ["custom"], "toolDescription": "", "customInputConfig": null, "selectedTypeIndex": null, "referencePlaceholder": null}, {"key": "system_httpTimeout", "max": 600, "min": 5, "pro": null, "list": null, "step": null, "enums": null, "label": "", "value": 30, "canEdit": null, "markList": null, "maxFiles": null, "required": true, "maxLength": null, "valueDesc": null, "valueType": "number", "debugLabel": "", "deprecated": null, "toolOutput": null, "description": null, "placeholder": null, "canSelectImg": null, "defaultValue": null, "llmModelType": null, "canSelectFile": null, "renderTypeList": ["custom"], "toolDescription": "", "customInputConfig": null, "selectedTypeIndex": null, "referencePlaceholder": null}, {"key": "system_httpReqUrl", "max": null, "min": null, "pro": null, "list": null, "step": null, "enums": null, "label": "", "value": "http://**************:9997/wxmanage/milvus/getknowledgefileslist.json", "canEdit": null, "markList": null, "maxFiles": null, "required": false, "maxLength": null, "valueDesc": null, "valueType": "string", "debugLabel": "", "deprecated": null, "toolOutput": null, "description": "common:core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "canSelectImg": null, "defaultValue": null, "llmModelType": null, "canSelectFile": null, "renderTypeList": ["hidden"], "toolDescription": "", "customInputConfig": null, "selectedTypeIndex": null, "referencePlaceholder": null}, {"key": "system_header_secret", "max": null, "min": null, "pro": null, "list": null, "step": null, "enums": null, "label": "", "value": null, "canEdit": null, "markList": null, "maxFiles": null, "required": false, "maxLength": null, "valueDesc": null, "valueType": "object", "debugLabel": "", "deprecated": null, "toolOutput": null, "description": null, "placeholder": null, "canSelectImg": null, "defaultValue": null, "llmModelType": null, "canSelectFile": null, "renderTypeList": ["hidden"], "toolDescription": "", "customInputConfig": null, "selectedTypeIndex": null, "referencePlaceholder": null}, {"key": "system_httpHeader", "max": null, "min": null, "pro": null, "list": null, "step": null, "enums": null, "label": "", "value": [], "canEdit": null, "markList": null, "maxFiles": null, "required": false, "maxLength": null, "valueDesc": null, "valueType": "any", "debugLabel": "", "deprecated": null, "toolOutput": null, "description": "common:core.module.input.description.Http Request Header", "placeholder": "common:core.module.input.description.Http Request Header", "canSelectImg": null, "defaultValue": null, "llmModelType": null, "canSelectFile": null, "renderTypeList": ["custom"], "toolDescription": "", "customInputConfig": null, "selectedTypeIndex": null, "referencePlaceholder": null}, {"key": "system_httpParams", "max": null, "min": null, "pro": null, "list": null, "step": null, "enums": null, "label": "", "value": [], "canEdit": null, "markList": null, "maxFiles": null, "required": false, "maxLength": null, "valueDesc": null, "valueType": "any", "debugLabel": "", "deprecated": null, "toolOutput": null, "description": null, "placeholder": null, "canSelectImg": null, "defaultValue": null, "llmModelType": null, "canSelectFile": null, "renderTypeList": ["hidden"], "toolDescription": "", "customInputConfig": null, "selectedTypeIndex": null, "referencePlaceholder": null}, {"key": "system_httpJsonBody", "max": null, "min": null, "pro": null, "list": null, "step": null, "enums": null, "label": "", "value": "{}", "canEdit": null, "markList": null, "maxFiles": null, "required": false, "maxLength": null, "valueDesc": null, "valueType": "any", "debugLabel": "", "deprecated": null, "toolOutput": null, "description": null, "placeholder": null, "canSelectImg": null, "defaultValue": null, "llmModelType": null, "canSelectFile": null, "renderTypeList": ["hidden"], "toolDescription": "", "customInputConfig": null, "selectedTypeIndex": null, "referencePlaceholder": null}, {"key": "system_httpFormBody", "max": null, "min": null, "pro": null, "list": null, "step": null, "enums": null, "label": "", "value": [{"key": "knowledgeid", "type": "number", "value": "{{$3dbd0a533f8b4693.f7bb5298b0434a2f$}}"}, {"key": "page", "type": "number", "value": "{{$3dbd0a533f8b4693.23823389e9ee41bc$}}"}, {"key": "pageSize", "type": "number", "value": "{{$3dbd0a533f8b4693.419379a5ae524d59$}}"}, {"key": "apikey", "type": "string", "value": "{{$3dbd0a533f8b4693.1fced5d5da4c416a$}}"}], "canEdit": null, "markList": null, "maxFiles": null, "required": false, "maxLength": null, "valueDesc": null, "valueType": "any", "debugLabel": "", "deprecated": null, "toolOutput": null, "description": null, "placeholder": null, "canSelectImg": null, "defaultValue": null, "llmModelType": null, "canSelectFile": null, "renderTypeList": ["hidden"], "toolDescription": "", "customInputConfig": null, "selectedTypeIndex": null, "referencePlaceholder": null}, {"key": "system_httpContentType", "max": null, "min": null, "pro": null, "list": null, "step": null, "enums": null, "label": "", "value": "form-data", "canEdit": null, "markList": null, "maxFiles": null, "required": false, "maxLength": null, "valueDesc": null, "valueType": "string", "debugLabel": null, "deprecated": null, "toolOutput": null, "description": null, "placeholder": null, "canSelectImg": null, "defaultValue": null, "llmModelType": null, "canSelectFile": null, "renderTypeList": ["hidden"], "toolDescription": null, "customInputConfig": null, "selectedTypeIndex": null, "referencePlaceholder": null}], "nodeId": "9fd941827f944322", "abandon": false, "outputs": [{"id": "httpRawResponse", "key": "httpRawResponse", "type": "static", "label": "workflow:raw_response", "value": null, "invalid": null, "required": true, "valueDesc": null, "valueType": "any", "deprecated": null, "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "defaultValue": null, "customFieldConfig": null}, {"id": "error", "key": "error", "type": "error", "label": "workflow:error_text", "value": null, "invalid": null, "required": null, "valueDesc": null, "valueType": "string", "deprecated": null, "description": "HTTP请求错误信息，成功时返回空", "defaultValue": null, "customFieldConfig": null}], "pluginId": null, "position": {"x": 1041.1505186414104, "y": -480.9168163795506}, "toolData": null, "appVersion": null, "pluginData": null, "showStatus": true, "flowNodeType": "httpRequest468", "parentNodeId": null, "versionLabel": null, "isLatestVersion": null}