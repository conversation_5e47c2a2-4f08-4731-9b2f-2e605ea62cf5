# ==================== 国际化资源文件 ====================
# 错误码规则：30-XX-YYYY
# 30 - 固定前缀
# XX - 模块代码
# YYYY - 具体错误码

# ==================== 通用模块消息码规则 ====================
# 30-00-0000
# [通用模块固定30-00-错误码]

# 文件令牌相关错误 (300000XX)
30000001=系统未设置FILE_TOKEN_KEY
30000002=未提供文件令牌
30000003=无效的文件令牌
30000004=无效或过期的文件令牌
30000005=图片上传失败
30000006=文件ID格式错误
30000007=文件下载失败
30000008=文件上传失败
30000009=文件大小超过限制，最大允许10MB
30000010=文件大小无效
30000011=base64数据不能为空
30000012=base64转换为文件失败

# ==================== 系统模块消息码规则 ====================
# 30-01-0000
# [系统模块固定30-01-错误码]

# 系统模块错误 (300100XX)
30010001=配置文件不存在: {0}
30010002=配置文件读取失败: {0}
30010003=配置文件解析失败: {0}
30010004=配置路径未配置
30010005=系统配置不存在
30010006=系统配置类型不支持: {0}
30010007=系统配置更新失败
30010008=系统配置格式错误: {0}
30010009=系统初始化数据获取失败
30010010=系统版本信息获取失败
30010011=缓冲ID无效: {0}
30010012=系统环境配置获取失败
30010013=默认模型配置获取失败
30010014=激活模型列表获取失败
30010015=模型配置转换失败: {0}
30010016=FastGPT配置获取失败
30010017=FastGPT配置更新失败
30010018=FastGPT配置缓存更新失败
30010019=许可证数据获取失败
30010020=系统全局变量初始化失败
30010021=系统全局变量更新失败
30010022=系统全局变量获取失败
30010023=系统安全环境配置获取失败
30010024=系统安全验证失败


# ==================== 应用模块消息码规则 ====================
# 30-02-0000
# [应用模块固定30-02-错误码]

# 基础错误 (300200XX)
30020001=缺失必要参数
30020002=appId不存在
30020003=应用不存在
30020004=目标文件夹不存在
30020005=缺少参数
30020006=应用不存在
30020007=数据集不存在
30020008=目前只支持工作流的简单创建

# 验证错误 (300201XX)
30020101=应用ID不能为空
30020102=应用名称不能为空
30020103=应用类型不能为空
30020104=应用模块列表不能为空
30020105=应用版本不能为空
30020106=应用插件数据不能为空

# 权限错误 (300202XX)
30020201=无权限访问应用
30020202=无权限删除应用
30020203=无权限修改应用
30020204=无权限创建应用

# 操作错误 (300203XX)
30020301=应用创建失败
30020302=应用更新失败
30020303=应用删除失败
30020304=应用复制失败
30020305=应用移动失败

# 版本错误 (300204XX)
30020401=应用版本不存在
30020402=应用版本创建失败
30020403=应用版本更新失败
30020404=应用版本删除失败

# 插件错误 (300205XX)
30020501=应用插件不存在
30020502=应用插件创建失败
30020503=应用插件更新失败
30020504=应用插件删除失败

# 文件夹错误 (300206XX)
30020601=应用文件夹不存在
30020602=应用文件夹创建失败
30020603=应用文件夹更新失败
30020604=应用文件夹删除失败

# HTTP插件错误 (300207XX)
30020701=HTTP插件URL不能为空
30020702=HTTP插件方法不能为空
30020703=HTTP插件请求头不能为空
30020704=HTTP插件请求体不能为空
30020705=HTTP插件创建失败
30020706=HTTP插件更新失败
30020707=HTTP插件删除失败

# MCP工具错误 (300208XX)
30020801=MCP工具名称不能为空
30020802=MCP工具描述不能为空
30020803=MCP工具配置不能为空
30020804=MCP工具创建失败
30020805=MCP工具更新失败
30020806=MCP工具删除失败
30020807=MCP连接失败
30020808=MCP URL格式错误

# 模板错误 (300209XX)
30020901=模板ID不能为空
30020902=模板不存在
30020903=获取模板列表失败
30020904=获取模板详情失败

# 枚举类错误 (300210XX)
30021001=未知的节点输入类型: {0}
30021002=未知的节点输出类型: {0}
30021003=未知的节点类型: {0}
30021004=未知的插件来源类型: {0}
30021005=未知的大模型模型类型: {0}
30021006=未知的工作流IO值类型: {0}
30021007=未知的变量输入类型
30021008=无效的cron表达式
30021009=未知的HeaderSecretType: {0}
30021010=未知的AppTypeEnum: {0}

# 工具类错误 (300211XX)
30021101=组合ID不能为空
30021102=插件来源和插件ID不能为空
30021103=无效的插件来源: {0}
30021104=密钥加密失败
30021105=密钥解密失败
30021106=生成密钥字节数组失败
30021107=生成随机密钥失败
30021108=计算哈希值失败
30021109=生成安全随机字符串失败

# ==================== 聊天模块消息码规则 ====================
# 30-03-0000
# [聊天模块固定30-03-错误码]

# 参数验证错误 (300300XX)
30030001=参数不能为空
30030002=应用ID不能为空
30030003=聊天ID不能为空
30030004=消息列表不能为空
30030005=API密钥不能为空
30030006=用户问题不能为空
30030007=节点列表不能为空
30030008=边列表不能为空
30030009=消息格式错误，必须为数组

# 权限验证错误 (300301XX)
30030010=无权限访问
30030011=应用不存在或无权限访问
30030012=无权限访问此聊天
30030013=权限校验失败
30030014=应用ID为空
30030015=用户ID不匹配

# 业务逻辑错误 (300302XX)
30030016=应用不存在
30030017=分享链接不存在或已过期
30030018=您的工作流版本过低，请重新发布一次
30030019=您还没有应用
30030020=用户问题为空

# 系统错误 (300303XX)
30030021=初始化失败: {0}
30030022=工作流执行失败: {0}
30030023=导出失败: {0}
30030024=加载消息失败
30030025=获取历史记录失败: {0}
30030026=无法使用teamId和teamToken，获取聊天记录
30030027=参数错误

# 工作流错误 (300304XX)
30030028=工作流执行失败

# 角色错误 (300305XX)
30030029=角色未找到

# 聊天处理错误 (300306XX)
30030030=处理消息时发生错误
30030031=加载消息时发生错误
30030032=获取图片base64失败: {0}
30030033=更新交互式聊天失败
30030034=复制ChatHistoryItemResType属性失败
30030035=保存聊天失败
30030036=推送聊天日志失败
30030037=创建聊天使用统计失败
30030038=添加自定义反馈失败
30030039=获取聊天日志失败
30030040=处理导出记录时出错
30030041=导出聊天日志失败
30030042=格式化JSON字符串失败
30030043=聊天测试执行异常
30030044=分页获取聊天记录失败
30030045=统计聊天输入引导总数失败

# ==================== 工作流模块消息码规则 ====================
# 30-50-0000
# [工作流模块固定30-50-错误码]

# LLM and Model related
30050001=请确保已正确配置大模型
30050002=请确认所选的AI模型【{0}】配置正确
30050003=AI模型返回值为空，请确认AI模型可以调用
30050004=未找到模型: {0}

# Node type related
30050005=未知的节点类型: {0}
30050006=不支持的节点类型: {0}

# Input validation
30050007=输入不能为空
30050008=插件ID不能为空
30050009=问题不能为空
30050010=HTTP URL不能为空
30050011=LOOP_INPUT_ARRAY必须是一个List
30050012=循环输入数组不能为空

# Configuration related
30050013=在mind属性中未找到sandboxUrl
30050014=无效的代码类型: {0}
30050015=无效的JSON请求体: {0}
30050016=对象不是一个Map

# Dataset related
30050017=数据集列表不能为空
30050018=过滤数据集失败

# Plugin related
30050019=未找到工具配置数据
30050020=工具调用失败: {0}
30050021=Stdio URL格式错误，应为: stdio://command arg1 arg2
30050022=不支持的协议: {0}. 支持的协议: http, https, stdio
30050023=创建MCP客户端失败: {0}
30050024=工具调用返回错误

# Code related
30050025=运行代码失败

# Conversion related
30050026=转换失败

# Dispatch failures
30050027=问题分类调度失败: {0}
30050028=插件模块调度失败: {0}
30050029=查询扩展失败: {0}
30050030=查询扩展AI调用失败: {0}

# Workflow execution related
30050031=工作流执行失败
30050032=节点执行失败: {0}
30050033=工作流初始化失败
30050034=设置SSE响应头失败
30050035=发送心跳消息失败
30050036=流式响应处理失败
30050037=流式响应错误
30050038=无效的JSON请求体
30050039=SSE消息发送失败
30050040=响应写入失败
30050041=Json序列化失败
30050042=JSON转换失败:{0}
30050043=提取 toolSet 配置数据时发生错误，节点ID: {0}
30050044=未知的CompletionFinishReason: {0}
30050045=未知的ChatMessageTypeEnum: {0}

# ==================== 权限模块消息码规则 ====================
# 30-06-0000
# [权限模块固定30-06-错误码]

# Permission related
30060001=成员没有对应权限
30060002=使用orgId找不到对应的组织
30060003=不支持团队资源类型使用

# ==================== 模型模块消息码规则 ====================
# 30-07-0000
# [模型模块固定30-07-错误码]

# 基础模型错误
30070001=模型标识不能为空
30070002=找不到模型: {0}
30070003=系统模型不允许删除
30070004=配置内容不能为空
30070005=配置格式解析失败: {0}

# 模型测试错误
30070006=不支持的模型类型: {0}
30070007=测试LLM模型失败: {0}
30070008=测试Embedding模型失败: {0}
30070009=测试TTS模型失败: {0}
30070010=测试STT模型失败: 无法获取测试音频数据
30070011=测试STT模型失败: {0}
30070012=测试Rerank模型失败: {0}

# 模型配置错误
30070013=模型配置不能为空
30070014=模型提供商不能为空
30070015=不支持的模型提供商: {0}
30070016=不支持的索引模型提供商: {0}
30070017=不支持的TTS模型提供商: {0}
30070018=不支持的STT模型提供商: {0}
30070019=不支持的重排模型提供商: {0}

# 模型服务错误
30070020=TTS服务调用失败: {0}
30070021=写入TTS数据到输出流失败: {0}
30070022=STT服务调用失败: {0}
30070023=从输入流读取音频数据失败: {0}
30070024=自定义模型必须提供请求地址

# 控制器错误
30070025=JSON格式化失败: {0}
30070026=模型测试失败: {0}
30070027=清理过期模型调用日志失败

# ==================== 数据集模块消息码规则 ====================
# 30-08-0000
# [数据集模块固定30-08-错误码]

# 通用验证错误 (300800XX)
30080001=ID不能为空
30080002=名称不能为空
30080003=数据不存在
30080004=集合不存在
30080005=知识库不存在
30080006=缺少必要参数
30080007=数据ID不能为空
30080008=数据集ID不能为空
30080009=集合ID不能为空
30080010=训练数据不存在
30080011=向量模型不能为空
30080012=知识库名称不能为空
30080013=知识库ID不能为空
30080014=文件类型不能为空
30080015=文件源ID不能为空
30080016=搜索文本不能为空
30080017=文本内容不能为空
30080018=datasetId不能为空
30080019=collectionId不能为空
30080020=dataId不能为空
30080021=vectorModel不能为空
30080022=重叠比例不能为空
30080023=文件内容无法解析或为空
30080024=文件处理失败
30080025=不支持的分块预览类型: {0}
30080026=限制数量不能为空

30080201=上传的文件找不到
30080202=集合关联的文件找不到
30080203=未找到失败的训练数据
30080204=数据集ID、集合ID和数据ID不能为空

# ==================== 账户模块消息码规则 ====================
# 30-09-0000
# [账户模块固定30-09-错误码]

# 账户相关错误 (300900XX)
30090001=用户未登录或会话已过期

# ==================== OpenApi模块消息码规则 ====================
# 30-10-0000
# [OpenApi模块固定30-10-错误码]

# OpenApi相关错误 (301000XX)
30100001=API密钥不存在
30100002=无效的API密钥格式
30100003=无效的API密钥
30100004=API密钥已过期
30100005=使用量超过限制: {0}/{1} 点
30100006=缺少必要参数: {0}
30100007=应用不存在
30100008=缺少API密钥认证头

# ==================== 外链模块消息码规则 ====================
# 30-11-0000
# [外链模块固定30-11-错误码]

# 外链相关错误 (301100XX)
30110001=应用不存在
30110002=创建免登录链接失败: {0}
30110003=查询免登录链接列表失败: {0}
30110004=免登录链接不存在
30110005=更新免登录链接失败: {0}
30110006=删除免登录链接失败: {0}
30110007=分享链接已过期
30110008=分享链接使用次数已达上限
30110009=分享链接不存在或已过期
30110010=认证接口返回数据为空,hookUrl:{0}
30110011=认证失败,错误原因: {0}
30110012=调用免登录窗口初始化验证接口失败,hookUrl:{0}
30110013=外链无效
30110014=未知的发布渠道类型: {0}
30110015=获取应用信息失败: {0}

# ==================== 团队模块消息码规则 ====================
# 30-12-0000
# [团队模块固定30-12-错误码]

# 团队相关错误 (301200XX)
30120001=该部门没有岗位，请新建一个默认岗位，用于承载成员
30120002=群组不存在
30120003=该成员不存在
30120004=该成员[{0}]不存在
30120005=群组管理员不能修改

# ==================== 文件处理模块消息码规则 ====================
# 30-13-0000
# [文件处理模块固定30-13-错误码]

# 文件处理基础错误 (301300XX)
30130101=处理文件URL时发生错误: {0}
30130102=从HTTP URL获取文件失败: {0}
30130103=获取原始文本缓冲区失败: {0}
30130104=从缓冲区读取文件内容失败
30130105=从缓冲区创建临时文件失败
30130106=添加原始文本缓冲区失败: {0}
30130107=sourceId格式错误，sourceId: {0}
30130108=文件未找到: {0}
30130109=文件名URL解码失败: {0}
30130110=从bucketName: {0}获取文件: {1}失败
30130111=从bucketName: {0}获取基础文件信息: {1}失败
30130112=上传文件: {0}到bucketName: {1}失败
30130113=从bucketName: {0}获取文件信息: {1}失败
30130114=文件令牌验证失败: {0}