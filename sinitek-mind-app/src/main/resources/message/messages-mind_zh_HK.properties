# ==================== 國際化資源文件 ====================
# 錯誤碼規則：30-XX-YYYY
# 30 - 固定前綴
# XX - 模組代碼
# YYYY - 具體錯誤碼

# ==================== 通用模組消息碼規則 ====================
# 30-00-0000
# [通用模組固定30-00-錯誤碼]

# 文件令牌相關錯誤 (300000XX)
30000001=系統未設置FILE_TOKEN_KEY
30000002=未提供文件令牌
30000003=無效的文件令牌
30000004=無效或過期的文件令牌
30000005=圖片上傳失敗
30000006=文件ID格式錯誤
30000007=文件下載失敗
30000008=文件上傳失敗
30000009=文件大小超過限制，最大允許10MB
30000010=文件大小無效
30000011=base64數據不能為空
30000012=base64轉換為文件失敗

# ==================== 系統模組消息碼規則 ====================
# 30-01-0000
# [系統模組固定30-01-錯誤碼]

# 系統模組錯誤 (300100XX)
30010001=配置文件不存在: {0}
30010002=配置文件讀取失敗: {0}
30010003=配置文件解析失敗: {0}
30010004=配置路徑未配置
30010005=系統配置不存在
30010006=系統配置類型不支持: {0}
30010007=系統配置更新失敗
30010008=系統配置格式錯誤: {0}
30010009=系統初始化數據獲取失敗
30010010=系統版本信息獲取失敗
30010011=緩衝ID無效: {0}
30010012=系統環境配置獲取失敗
30010013=默認模型配置獲取失敗
30010014=激活模型列表獲取失敗
30010015=模型配置轉換失敗: {0}
30010016=FastGPT配置獲取失敗
30010017=FastGPT配置更新失敗
30010018=FastGPT配置緩存更新失敗
30010019=許可證數據獲取失敗
30010020=系統全局變量初始化失敗
30010021=系統全局變量更新失敗
30010022=系統全局變量獲取失敗
30010023=系統安全環境配置獲取失敗
30010024=系統安全驗證失敗

# ==================== 應用模組消息碼規則 ====================
# 30-02-0000
# [應用模組固定30-02-錯誤碼]

# 基礎錯誤 (300200XX)
30020001=缺失必要參數
30020002=appId不存在
30020003=應用不存在
30020004=目標文件夾不存在
30020005=缺少參數
30020006=應用不存在
30020007=數據集不存在
30020008=目前只支持工作流的簡單創建

# 驗證錯誤 (300201XX)
30020101=應用ID不能為空
30020102=應用名稱不能為空
30020103=應用類型不能為空
30020104=應用模塊列表不能為空
30020105=應用版本不能為空
30020106=應用插件數據不能為空

# 權限錯誤 (300202XX)
30020201=無權限訪問應用
30020202=無權限刪除應用
30020203=無權限修改應用
30020204=無權限創建應用

# 操作錯誤 (300203XX)
30020301=應用創建失敗
30020302=應用更新失敗
30020303=應用刪除失敗
30020304=應用復制失敗
30020305=應用移動失敗

# 版本錯誤 (300204XX)
30020401=應用版本不存在
30020402=應用版本創建失敗
30020403=應用版本更新失敗
30020404=應用版本刪除失敗

# 插件錯誤 (300205XX)
30020501=應用插件不存在
30020502=應用插件創建失敗
30020503=應用插件更新失敗
30020504=應用插件刪除失敗

# 文件夾錯誤 (300206XX)
30020601=應用文件夾不存在
30020602=應用文件夾創建失敗
30020603=應用文件夾更新失敗
30020604=應用文件夾刪除失敗

# HTTP插件錯誤 (300207XX)
30020701=HTTP插件URL不能為空
30020702=HTTP插件方法不能為空
30020703=HTTP插件請求頭不能為空
30020704=HTTP插件請求體不能為空
30020705=HTTP插件創建失敗
30020706=HTTP插件更新失敗
30020707=HTTP插件刪除失敗

# MCP工具錯誤 (300208XX)
30020801=MCP工具名稱不能為空
30020802=MCP工具描述不能為空
30020803=MCP工具配置不能為空
30020804=MCP工具創建失敗
30020805=MCP工具更新失敗
30020806=MCP工具刪除失敗
30020807=MCP連接失敗
30020808=MCP URL格式錯誤

# 模板錯誤 (300209XX)
30020901=模板ID不能為空
30020902=模板不存在
30020903=獲取模板列表失敗
30020904=獲取模板詳情失敗

# 枚舉類錯誤 (300210XX)
30021001=未知的節點輸入類型: {0}
30021002=未知的節點輸出類型: {0}
30021003=未知的節點類型: {0}
30021004=未知的插件來源類型: {0}
30021005=未知的大模型模型類型: {0}
30021006=未知的工作流IO值類型: {0}
30021007=未知的變量輸入類型: {0}
30021008=無效的cron表達式
30021009=未知的HeaderSecretType: {0}
30021010=未知的AppTypeEnum: {0}

# 工具類錯誤 (300211XX)
30021101=組合ID不能為空
30021102=插件來源和插件ID不能為空
30021103=無效的插件來源: {0}
30021104=密鑰加密失敗
30021105=密鑰解密失敗
30021106=生成密鑰字節數組失敗
30021107=生成隨機密鑰失敗
30021108=計算哈希值失敗
30021109=生成安全隨機字符串失敗

# ==================== 聊天模組消息碼規則 ====================
# 30-03-0000
# [聊天模組固定30-03-錯誤碼]

# 參數驗證錯誤 (300300XX)
30030001=參數不能為空
30030002=應用ID不能為空
30030003=聊天ID不能為空
30030004=消息列表不能為空
30030005=API密鑰不能為空
30030006=用戶問題不能為空
30030007=節點列表不能為空
30030008=邊列表不能為空
30030009=消息格式錯誤，必須為數組

# 權限驗證錯誤 (300301XX)
30030010=無權限訪問
30030011=應用不存在或無權限訪問
30030012=無權限訪問此聊天
30030013=權限校驗失敗
30030014=應用ID為空
30030015=用戶ID不匹配

# 業務邏輯錯誤 (300302XX)
30030016=應用不存在
30030017=分享連結不存在或已過期
30030018=您的工作流版本過低，請重新發布一次
30030019=您還沒有應用
30030020=用戶問題為空

# 系統錯誤 (300303XX)
30030021=初始化失敗: {0}
30030022=工作流執行失敗: {0}
30030023=導出失敗: {0}
30030024=加載消息失敗
30030025=獲取歷史記錄失敗: {0}
30030026=無法使用teamId和teamToken，獲取聊天記錄
30030027=參數錯誤

# 工作流錯誤 (300304XX)
30030028=工作流執行失敗

# 角色錯誤 (300305XX)
30030029=角色未找到

# 聊天處理錯誤 (300306XX)
30030030=處理消息時發生錯誤
30030031=加載消息時發生錯誤
30030032=獲取圖片base64失敗: {0}
30030033=更新交互式聊天失敗
30030034=複製ChatHistoryItemResType屬性失敗
30030035=保存聊天失敗
30030036=推送聊天日誌失敗
30030037=創建聊天使用統計失敗
30030038=添加自定義反饋失敗
30030039=獲取聊天日誌失敗
30030040=處理導出記錄時出錯
30030041=導出聊天日誌失敗
30030042=格式化JSON字符串失敗
30030043=聊天測試執行異常
30030044=分頁獲取聊天記錄失敗
30030045=統計聊天輸入引導總數失敗

# ==================== 工作流模組消息碼規則 ====================
# 30-50-0000
# [工作流模組固定30-50-錯誤碼]

# LLM and Model related
30050001=請確保已正確配置大模型
30050002=請確認所選的AI模型【{0}】配置正確
30050003=AI模型返回值為空，請確認AI模型可以調用
30050004=未找到模型: {0}

# Node type related
30050005=未知的節點類型: {0}
30050006=不支持的節點類型: {0}

# Input validation
30050007=輸入不能為空
30050008=插件ID不能為空
30050009=問題不能為空
30050010=HTTP URL不能為空
30050011=LOOP_INPUT_ARRAY必須是一個List
30050012=循環輸入數組不能為空

# Configuration related
30050013=在mind屬性中未找到sandboxUrl
30050014=無效的代碼類型: {0}
30050015=無效的JSON請求體: {0}
30050016=對象不是一個Map

# Dataset related
30050017=數據集列表不能為空
30050018=過濾數據集失敗

# Plugin related
30050019=未找到工具配置數據
30050020=工具調用失敗: {0}
30050021=Stdio URL格式錯誤，應為: stdio://command arg1 arg2
30050022=不支持的協議: {0}. 支持的協議: http, https, stdio
30050023=創建MCP客戶端失敗: {0}
30050024=工具調用返回錯誤

# Code related
30050025=運行代碼失敗

# Conversion related
30050026=轉換失敗

# Dispatch failures
30050027=問題分類調度失敗: {0}
30050028=插件模塊調度失敗: {0}
30050029=查詢擴展失敗: {0}
30050030=查詢擴展AI調用失敗: {0}

# Workflow execution related
30050031=工作流執行失敗
30050032=節點執行失敗: {0}
30050033=工作流初始化失敗
30050034=設置SSE響應頭失敗
30050035=發送心跳消息失敗
30050036=流式響應處理失敗
30050037=流式響應錯誤
30050038=無效的JSON請求體
30050039=SSE消息發送失敗
30050040=響應寫入失敗
30050041=Json序列化失敗
30050042=JSON轉換失敗:{0}
30050043=提取 toolSet 配置數據時發生錯誤，節點ID: {0}
30050044=未知的CompletionFinishReason: {0}
30050045=未知的ChatMessageTypeEnum: {0}

# ==================== 權限模組消息碼規則 ====================
# 30-06-0000
# [權限模組固定30-06-錯誤碼]

# Permission related
30060001=成員沒有對應權限
30060002=使用orgId找不到對應的組織
30060003=不支持團隊資源類型使用

# ==================== 模型模組消息碼規則 ====================
# 30-07-0000
# [模型模組固定30-07-錯誤碼]

# 基礎模型錯誤
30070001=模型標識不能為空
30070002=找不到模型: {0}
30070003=系統模型不允許刪除
30070004=配置內容不能為空
30070005=配置格式解析失敗: {0}

# 模型測試錯誤
30070006=不支持的模型類型: {0}
30070007=測試LLM模型失敗: {0}
30070008=測試Embedding模型失敗: {0}
30070009=測試TTS模型失敗: {0}
30070010=測試STT模型失敗: 無法獲取測試音頻數據
30070011=測試STT模型失敗: {0}
30070012=測試Rerank模型失敗: {0}

# 模型配置錯誤
30070013=模型配置不能為空
30070014=模型提供商不能為空
30070015=不支持的模型提供商: {0}
30070016=不支持的索引模型提供商: {0}
30070017=不支持的TTS模型提供商: {0}
30070018=不支持的STT模型提供商: {0}
30070019=不支持的重排模型提供商: {0}

# 模型服務錯誤
30070020=TTS服務調用失敗: {0}
30070021=寫入TTS數據到輸出流失敗: {0}
30070022=STT服務調用失敗: {0}
30070023=從輸入流讀取音頻數據失敗: {0}
30070024=自定義模型必須提供請求地址

# 控制器錯誤
30070025=JSON格式化失敗: {0}
30070026=模型測試失敗: {0}
30070027=清理過期模型調用日誌失敗

# ==================== 數據集模組消息碼規則 ====================
# 30-08-0000
# [數據集模組固定30-08-錯誤碼]

# 通用驗證錯誤 (300800XX)
30080001=ID不能為空
30080002=名稱不能為空
30080003=數據不存在
30080004=集合不存在
30080005=知識庫不存在
30080006=缺少必要參數
30080007=數據ID不能為空
30080008=數據集ID不能為空
30080009=集合ID不能為空
30080010=訓練數據不存在
30080011=向量模型不能為空
30080012=知識庫名稱不能為空
30080013=知識庫ID不能為空
30080014=文件類型不能為空
30080015=文件源ID不能為空
30080016=搜索文本不能為空
30080017=文本內容不能為空
30080018=datasetId不能為空
30080019=collectionId不能為空
30080020=dataId不能為空
30080021=vectorModel不能為空
30080022=重疊比例不能為空
30080023=文件內容無法解析或為空
30080024=文件處理失敗
30080025=不支持的分塊預覽類型: {0}
30080026=限制數量不能為空

30080201=上傳的檔案找不到
30080202=集合關聯的檔案找不到
30080203=未找到失敗的訓練資料
30080204=資料集ID、集合ID和資料ID不能為空

# ==================== 賬戶模組消息碼規則 ====================
# 30-09-0000
# [賬戶模組固定30-09-錯誤碼]

# 賬戶相關錯誤 (300900XX)
30090001=用戶未登錄或會話已過期

# ==================== OpenApi模組消息碼規則 ====================
# 30-10-0000
# [OpenApi模組固定30-10-錯誤碼]

# OpenApi相關錯誤 (301000XX)
30100001=API密鑰不存在
30100002=無效的API密鑰格式
30100003=無效的API密鑰
30100004=API密鑰已過期
30100005=使用量超過限制: {0}/{1} 點
30100006=缺少必要參數: {0}
30100007=應用不存在
30100008=缺少API密鑰認證頭

# ==================== 外鏈模組消息碼規則 ====================
# 30-11-0000
# [外鏈模組固定30-11-錯誤碼]

# 外鏈相關錯誤 (301100XX)
30110001=應用不存在
30110002=創建免登錄鏈接失敗: {0}
30110003=查詢免登錄鏈接列表失敗: {0}
30110004=免登錄鏈接不存在
30110005=更新免登錄鏈接失敗: {0}
30110006=刪除免登錄鏈接失敗: {0}
30110007=分享鏈接已過期
30110008=分享鏈接使用次數已達上限
30110009=分享鏈接不存在或已過期
30110010=認證接口返回數據為空,hookUrl:{0}
30110011=認證失敗,錯誤原因: {0}
30110012=調用免登錄窗口初始化驗證接口失敗,hookUrl:{0}
30110013=外鏈無效
30110014=未知的發布渠道類型: {0}
30110015=獲取應用信息失敗: {0}

# ==================== 團隊模組消息碼規則 ====================
# 30-12-0000
# [團隊模組固定30-12-錯誤碼]

# 團隊相關錯誤 (301200XX)
30120001=該部門沒有崗位，請新建一個默認崗位，用於承載成員
30120002=群組不存在
30120003=該成員不存在
30120004=該成員[{0}]不存在
30120005=群組管理員不能修改

# ==================== 文件處理模組消息碼規則 ====================
# 30-13-0000
# [文件處理模組固定30-13-錯誤碼]

# 文件處理基礎錯誤 (301300XX)
30130101=處理文件URL時發生錯誤: {0}
30130102=從HTTP URL獲取文件失敗: {0}
30130103=獲取原始文本緩衝區失敗: {0}
30130104=從緩衝區讀取文件內容失敗
30130105=從緩衝區創建臨時文件失敗
30130106=添加原始文本緩衝區失敗: {0}
30130107=sourceId格式錯誤，sourceId: {0}
30130108=文件未找到: {0}
30130109=文件名URL解碼失敗: {0}
30130110=從bucketName: {0}獲取文件: {1}失敗
30130111=從bucketName: {0}獲取基礎文件信息: {1}失敗
30130112=上傳文件: {0}到bucketName: {1}失敗
30130113=從bucketName: {0}獲取文件信息: {1}失敗
30130114=文件令牌驗證失敗: {0}