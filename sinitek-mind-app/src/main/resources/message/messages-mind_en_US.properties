# ==================== Internationalization Resource File ====================
# Error code rules: 30-XX-YYYY
# 30 - Fixed prefix
# XX - Module code
# YYYY - Specific error code

# ==================== Common Module Message Code Rules ====================
# 30-00-0000
# [Common module fixed 30-00-error code]

# File token related errors (300000XX)
30000001=FILE_TOKEN_KEY is not set in the system
30000002=File token not provided
30000003=Invalid file token
30000004=Invalid or expired file token
30000005=Image upload failed
30000006=File ID format error
30000007=File download failed
30000008=File upload failed
30000009=File size exceeded, maximum allowed 10MB
30000010=File size is invalid
30000011=Base64 data cannot be empty
30000012=Base64 to file conversion failed

# ==================== System Module Message Code Rules ====================
# 30-01-0000
# [System module fixed 30-01-error code]

# System module errors (300100XX)
30010001=Configuration file does not exist: {0}
30010002=Configuration file read failed: {0}
30010003=Configuration file parsing failed: {0}
30010004=Configuration path not configured
30010005=System configuration does not exist
30010006=System configuration type not supported: {0}
30010007=System configuration update failed
30010008=System configuration format error: {0}
30010009=System initialization data acquisition failed
30010010=System version information acquisition failed
30010011=Buffer ID invalid: {0}
30010012=System environment configuration acquisition failed
30010013=Default model configuration acquisition failed
30010014=Active model list acquisition failed
30010015=Model configuration conversion failed: {0}
30010016=FastGPT configuration acquisition failed
30010017=FastGPT configuration update failed
30010018=FastGPT configuration cache update failed
30010019=License data acquisition failed
30010020=System global variables initialization failed
30010021=System global variables update failed
30010022=System global variables acquisition failed
30010023=System security environment configuration acquisition failed
30010024=System security validation failed

# ==================== App Module Message Code Rules ====================
# 30-02-0000
# [App module fixed 30-02-error code]

# Basic errors (300200XX)
30020001=Missing required parameters
30020002=appId does not exist
30020003=App does not exist
30020004=Target folder does not exist
30020005=Missing parameters
30020006=App does not exist
30020007=Dataset not found
30020008=Currently only supports simple creation of workflows

# Validation errors (300201XX)
30020101=App ID cannot be empty
30020102=App name cannot be empty
30020103=App type cannot be empty
30020104=App modules list cannot be empty
30020105=App version cannot be empty
30020106=App plugin data cannot be empty

# Permission errors (300202XX)
30020201=No permission to access app
30020202=No permission to delete app
30020203=No permission to update app
30020204=No permission to create app

# Operation errors (300203XX)
30020301=App creation failed
30020302=App update failed
30020303=App deletion failed
30020304=App copy failed
30020305=App move failed

# Version errors (300204XX)
30020401=App version does not exist
30020402=App version creation failed
30020403=App version update failed
30020404=App version deletion failed

# Plugin errors (300205XX)
30020501=App plugin does not exist
30020502=App plugin creation failed
30020503=App plugin update failed
30020504=App plugin deletion failed

# Folder errors (300206XX)
30020601=App folder does not exist
30020602=App folder creation failed
30020603=App folder update failed
30020604=App folder deletion failed

# HTTP plugin errors (300207XX)
30020701=HTTP plugin URL cannot be empty
30020702=HTTP plugin method cannot be empty
30020703=HTTP plugin headers cannot be empty
30020704=HTTP plugin body cannot be empty
30020705=HTTP plugin creation failed
30020706=HTTP plugin update failed
30020707=HTTP plugin deletion failed

# MCP tools errors (300208XX)
30020801=MCP tools name cannot be empty
30020802=MCP tools description cannot be empty
30020803=MCP tools configuration cannot be empty
30020804=MCP tools creation failed
30020805=MCP tools update failed
30020806=MCP tools deletion failed
30020807=MCP connection failed
30020808=MCP URL format error

# Template errors (300209XX)
30020901=Template ID cannot be empty
30020902=Template not found
30020903=Failed to get template list
30020904=Failed to get template details

# Enum class errors (300210XX)
30021001=Unknown flow node input type: {0}
30021002=Unknown flow node output type: {0}
30021003=Unknown flow node type: {0}
30021004=Unknown plugin source type: {0}
30021005=Unknown LLM model type: {0}
30021006=Unknown workflow IO value type: {0}
30021007=Unknown variable input type: {0}
30021008=Invalid cron expression
30021009=Unknown HeaderSecretType: {0}
30021010=Unknown AppTypeEnum: {0}

# Utility class errors (300211XX)
30021101=Combine ID cannot be empty
30021102=Plugin source and plugin ID cannot be empty
30021103=Invalid plugin source: {0}
30021104=Secret encryption failed
30021105=Secret decryption failed
30021106=Failed to generate secret key bytes
30021107=Failed to generate random secret
30021108=Failed to calculate hash
30021109=Failed to generate secure random string

# ==================== Chat Module Message Code Rules ====================
# 30-03-0000
# [Chat module fixed 30-03-error code]

# Parameter validation errors (300300XX)
30030001=Parameter cannot be empty
30030002=Application ID cannot be empty
30030003=Chat ID cannot be empty
30030004=Message list cannot be empty
30030005=API key cannot be empty
30030006=User question cannot be empty
30030007=Node list cannot be empty
30030008=Edge list cannot be empty
30030009=Message format error, must be an array

# Permission validation errors (300301XX)
30030010=No permission to access
30030011=Application does not exist or no permission to access
30030012=No permission to access this chat
30030013=Permission validation failed
30030014=Application ID is empty
30030015=User ID mismatch

# Business logic errors (300302XX)
30030016=Application does not exist
30030017=Share link does not exist or has expired
30030018=Your workflow version is too low, please republish
30030019=You don't have an app yet
30030020=User question is empty

# System errors (300303XX)
30030021=Initialization failed: {0}
30030022=Workflow execution failed: {0}
30030023=Export failed: {0}
30030024=Failed to load message
30030025=Failed to get history: {0}
30030026=Unable to get chat records using teamId and teamToken
30030027=Parameter error

# Workflow errors (300304XX)
30030028=Workflow execution failed

# Role errors (300305XX)
30030029=Role not found

# Chat processing errors (300306XX)
30030030=Error occurred while processing message
30030031=Error occurred while loading message
30030032=Failed to get image base64: {0}
30030033=Failed to update interactive chat
30030034=Failed to copy ChatHistoryItemResType property
30030035=Failed to save chat
30030036=Failed to push chat log
30030037=Failed to create chat usage statistics
30030038=Failed to add custom feedback
30030039=Failed to get chat log
30030040=Error occurred while processing export record
30030041=Failed to export chat log
30030042=Failed to format JSON string
30030043=Chat test execution exception
30030044=Failed to get chat page
30030045=Failed to count chat input guide total

# ==================== Workflow Module Message Code Rules ====================
# 30-50-0000
# [Workflow module fixed 30-50-error code]

# LLM and Model related
30050001=Please ensure the large model is properly configured
30050002=Please confirm the selected AI model [{0}] is configured correctly
30050003=AI model returned empty value, please confirm the AI model can be called
30050004=Model not found: {0}

# Node type related
30050005=Unknown node type: {0}
30050006=Unsupported node type: {0}

# Input validation
30050007=Input cannot be empty
30050008=Plugin ID cannot be empty
30050009=Question cannot be empty
30050010=HTTP URL cannot be empty
30050011=LOOP_INPUT_ARRAY must be a List
30050012=Loop input array cannot be empty

# Configuration related
30050013=sandboxUrl not found in mind properties
30050014=Invalid code type: {0}
30050015=Invalid JSON request body: {0}
30050016=Object is not a Map

# Dataset related
30050017=Dataset list cannot be empty
30050018=Dataset filtering failed

# Plugin related
30050019=Tool configuration data not found
30050020=Tool call failed: {0}
30050021=Stdio URL format error, should be: stdio://command arg1 arg2
30050022=Unsupported protocol: {0}. Supported protocols: http, https, stdio
30050023=Failed to create MCP client: {0}
30050024=Tool call returned error

# Code related
30050025=Code execution failed

# Conversion related
30050026=Conversion failed

# Dispatch failures
30050027=Question classification dispatch failed: {0}
30050028=Plugin module dispatch failed: {0}
30050029=Query extension failed: {0}
30050030=Query extension AI call failed: {0}

# Workflow execution related
30050031=Workflow execution failed
30050032=Node execution failed: {0}
30050033=Workflow initialization failed
30050034=Set SSE response header failed
30050035=Send heartbeat message failed
30050036=Stream response processing failed
30050037=Stream response error
30050038=Invalid JSON request body
30050039=SSE message send failed
30050040=Response write failed
30050041=Json serialization failed
30050042=JSON conversion failed:{0}
30050043=Failed to extract toolSet configuration data, node ID: {0}
30050044=Unknown CompletionFinishReason: {0}
30050045=Unknown ChatMessageTypeEnum: {0}

# ==================== Permission Module Message Code Rules ====================
# 30-06-0000
# [Permission module fixed 30-06-error code]

# Permission related
30060001=Member does not have corresponding permission
30060002=Organization not found using orgId
30060003=Team resource type is not supported

# ==================== Model Module Message Code Rules ====================
# 30-07-0000
# [Model module fixed 30-07-error code]

# Basic model errors
30070001=Model ID cannot be empty
30070002=Model not found: {0}
30070003=System model cannot be deleted
30070004=Configuration content cannot be empty
30070005=Configuration format parsing failed: {0}

# Model test errors
30070006=Unsupported model type: {0}
30070007=Test LLM model failed: {0}
30070008=Test Embedding model failed: {0}
30070009=Test TTS model failed: {0}
30070010=Test STT model failed: Unable to get test audio data
30070011=Test STT model failed: {0}
30070012=Test Rerank model failed: {0}

# Model configuration errors
30070013=Model configuration cannot be empty
30070014=Model provider cannot be empty
30070015=Unsupported model provider: {0}
30070016=Unsupported embedding model provider: {0}
30070017=Unsupported TTS model provider: {0}
30070018=Unsupported STT model provider: {0}
30070019=Unsupported rerank model provider: {0}

# Model service errors
30070020=TTS service call failed: {0}
30070021=Failed to write TTS data to output stream: {0}
30070022=STT service call failed: {0}
30070023=Failed to read audio data from input stream: {0}
30070024=Custom model must provide request URL

# Controller errors
30070025=JSON formatting failed: {0}
30070026=Model test failed: {0}
30070027=Failed to clean expired model call logs

# ==================== Dataset Module Message Code Rules ====================
# 30-08-0000
# [Dataset module fixed 30-08-error code]

# General validation errors (300800XX)
30080001=ID cannot be empty
30080002=Name cannot be empty
30080003=Data does not exist
30080004=Collection does not exist
30080005=Dataset does not exist
30080006=Missing required parameters
30080007=Data ID cannot be empty
30080008=Dataset ID cannot be empty
30080009=Collection ID cannot be empty
30080010=Training data does not exist
30080011=Vector model cannot be empty
30080012=Dataset name cannot be empty
30080013=Dataset ID cannot be empty
30080014=File type cannot be empty
30080015=File source ID cannot be empty
30080016=Search text cannot be empty
30080017=Text content cannot be empty
30080018=datasetId cannot be empty
30080019=collectionId cannot be empty
********=dataId cannot be empty
********=vectorModel cannot be empty
********=Overlap ratio cannot be empty
********=File content cannot be parsed or is empty
********=File processing failed
********=Unsupported chunk preview type: {0}
********=Limit cannot be empty

********=Uploaded file not found
********=File associated with the collection not found
********=Failed training data not found
********=Dataset ID, collection ID and data ID cannot be empty

# ==================== Account Module Message Code Rules ====================
# 30-09-0000
# [Account module fixed 30-09-error code]

# Account related errors (300900XX)
********=User not logged in or session expired

# ==================== OpenApi Module Message Code Rules ====================
# 30-10-0000
# [OpenApi module fixed 30-10-error code]

# OpenApi related errors (301000XX)
********=API key does not exist
********=Invalid API key format
********=Invalid API key
********=API key has expired
********=Usage limit exceeded: {0}/{1} points
********=Missing required parameter: {0}
********=Application does not exist
********=Missing API key authorization header

# ==================== OutLink Module Message Code Rules ====================
# 30-11-0000
# [OutLink module fixed 30-11-error code]

# OutLink related errors (301100XX)
********=Application does not exist
********=Failed to create login-free link: {0}
********=Failed to query login-free link list: {0}
********=Login-free link does not exist
********=Failed to update login-free link: {0}
********=Failed to delete login-free link: {0}
********=Share link has expired
30110008=Share link usage limit reached
30110009=Share link does not exist or has expired
30110010=Authentication interface returned empty data, hookUrl: {0}
30110011=Authentication failed, error reason: {0}
30110012=Failed to call login-free window initialization verification interface, hookUrl: {0}
30110013=OutLink is invalid
30110014=Unknown publish channel type: {0}
30110015=Failed to get application information: {0}

# ==================== Team Module Message Code Rules ====================
# 30-12-0000
# [Team module fixed 30-12-error code]

# Team related errors (301200XX)
30120001=The department has no positions, please create a default position to carry members
30120002=Group does not exist
30120003=Member does not exist
30120004=Member [{0}] does not exist
30120005=Group administrator cannot be modified

# ==================== File Processing Module Message Code Rules ====================
# 30-13-0000
# [File processing module fixed 30-13-error code]

# File processing basic errors (301300XX)
30130101=Error processing file URL: {0}
30130102=Error fetching file from HTTP URL: {0}
30130103=Error getting raw text buffer for sourceId: {0}
30130104=Error reading file content from buffer
30130105=Failed to create temp file from buffer
30130106=Error adding raw text buffer for sourceId: {0}
30130107=Source ID format error, sourceId: {0}
30130108=File not found: {0}
30130109=File name URL decode failed: {0}
30130110=Failed to get file: {1} from bucketName: {0}
30130111=Failed to get basic file info: {1} from bucketName: {0}
30130112=Failed to upload file: {0} to bucketName: {1}
30130113=Failed to get file info: {1} from bucketName: {0}
30130114=File token verification failed: {0}