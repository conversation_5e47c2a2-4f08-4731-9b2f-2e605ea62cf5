{"code": 200, "statusText": "", "message": "", "data": {"list": [{"id": "147cbe18-c77d-4e59-937e-9f20b10d73ac", "updateTime": "", "q": "---\nsidebarDepth: 3\n---\n\n# Spring安全漏洞\n\n## CVE-2024-38819\n\n\n### 漏洞说明\n\n#### 漏洞描述\n\n通过功能性 Web 框架 WebMvc.fn 或 WebFlux.fn 提供静态资源的应用程序容易受到路径遍历攻击。攻击者可以构建恶意 HTTP 请求，并获取文件系统上任何文件，这些文件也可以被运行 Spring 应用程序的进程访问\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.13\n\n* Spring Framework 6.0.0 - 6.0.24\n\n* Spring Framework 5.3.0 - 5.3.40\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.41\n\n#### 推荐版本\n\nSpring 升级到 5.3.41及以上\n\n### 解决方案\n\n#### 前置了解：Spring5.x停止维护说明\n\n* 简单说：就是Spring5+SpringBoot2的开源版本已经停止维护了，只有购买了Spring商业版才能升级到Spring 5.3.41，所以无法在直接进行升级\n\n* 更多信息可查看[Spring5开源版停止维护说明](/guides/upgrade/vulnerability/Spring5开源版停止维护说明.html)\n\n#### 解决方案一：针对漏洞实际情况进行分析处理\n\n* 找到Spring官方对这个漏洞的说明[https://spring.io/security/cve-2024-38819](https://spring.io/security/cve-2024-38819)\n\n* 其中有说到漏洞的触发条件，需同时满足以下两个条件：\n  * 1、应用程序通过RouterFunctions（WebMvc.fn/WebFlux.fn 的核心组件）处理静态资源\n  * 2、静态资源的位置被显式配置为FileSystemResource（即资源存储在服务器本地文件系统中，而非打包在应用 JAR 包内的类路径资源等）\n\n* 针对条件一、条件二可以项目全局搜索`RouterFunctions`，看看是否有如此处理静态资源。例如类似如下的代码\n\n```java\n@Bean\npublic RouterFunction<ServerResponse> staticResourceRouter() {\n    return RouterFunctions\n        .resources(\"/static/**\", new FileSystemResource(\"/path/to/local/static/files/\")); // 关键：使用FileSystemResource\n}\n```\n\n* `总结：` 目前是前后端分离项目，框架在后端不会放静态资源所以也就不存在使用RouterFunctions处理静态资源，同时更不可能用FileSystemResource直接使用服务器的静态资源，\n两者需要同时满足才有可能触发该漏洞。`而现在两者都不成立故此这个漏洞本质上根本不可能触发，项目上进行检查明确后可沟通讨论无需处理`\n\n#### 解决方案二：升级到Spring6\n\n* 框架[8.\n", "a": "", "chunkIndex": 0, "datasetId": "68905601e35435613e1eec43", "collectionId": "68905602e35435613e1eec46", "sourceId": "68905602e35435613e1eec44", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8485549092292786, "index": 0}, {"type": "rrf", "value": 0.00546448087431694, "index": null}], "tokens": 346}, {"id": "ac60e63f-4fdb-4442-8215-b98dc6ad3afe", "updateTime": "", "q": "---\nsidebarDepth: 3\n---\n\n# Spring安全漏洞\n\n## CVE-2024-38819\n\n\n### 漏洞说明\n\n#### 漏洞描述\n\n通过功能性 Web 框架 WebMvc.fn 或 WebFlux.fn 提供静态资源的应用程序容易受到路径遍历攻击。攻击者可以构建恶意 HTTP 请求，并获取文件系统上任何文件，这些文件也可以被运行 Spring 应用程序的进程访问\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.13\n\n* Spring Framework 6.0.0 - 6.0.24\n\n* Spring Framework 5.3.0 - 5.3.40\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.41\n\n#### 推荐版本\n\nSpring 升级到 5.3.41及以上\n\n### 解决方案\n\n#### 前置了解：Spring5.x停止维护说明\n\n* 简单说：就是Spring5+SpringBoot2的开源版本已经停止维护了，只有购买了Spring商业版才能升级到Spring 5.3.41，所以无法在直接进行升级\n\n* 更多信息可查看[Spring5开源版停止维护说明](/guides/upgrade/vulnerability/Spring5开源版停止维护说明.html)\n\n#### 解决方案一：针对漏洞实际情况进行分析处理\n\n* 找到Spring官方对这个漏洞的说明[https://spring.io/security/cve-2024-38819](https://spring.io/security/cve-2024-38819)\n\n* 其中有说到漏洞的触发条件，需同时满足以下两个条件：\n  * 1、应用程序通过RouterFunctions（WebMvc.fn/WebFlux.fn 的核心组件）处理静态资源\n  * 2、静态资源的位置被显式配置为FileSystemResource（即资源存储在服务器本地文件系统中，而非打包在应用 JAR 包内的类路径资源等）\n\n* 针对条件一、条件二可以项目全局搜索`RouterFunctions`，看看是否有如此处理静态资源。例如类似如下的代码\n\n```java\n@Bean\npublic RouterFunction<ServerResponse> staticResourceRouter() {\n    return RouterFunctions\n        .resources(\"/static/**\", new FileSystemResource(\"/path/to/local/static/files/\")); // 关键：使用FileSystemResource\n}\n```\n\n* `总结：` 目前是前后端分离项目，框架在后端不会放静态资源所以也就不存在使用RouterFunctions处理静态资源，同时更不可能用FileSystemResource直接使用服务器的静态资源，\n两者需要同时满足才有可能触发该漏洞。`而现在两者都不成立故此这个漏洞本质上根本不可能触发，项目上进行检查明确后可沟通讨论无需处理`\n\n#### 解决方案二：升级到Spring6\n\n* 框架[8.", "a": "", "chunkIndex": 1, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403b2233e4d4b422be035", "sourceId": "689403b2233e4d4b422be033", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8485549092292786, "index": 1}, {"type": "rrf", "value": 0.005376344086021505, "index": null}], "tokens": 346}, {"id": "4de72d44-140f-449c-bcd3-af66bafe1d02", "updateTime": "", "q": "---\nsidebarDepth: 3\n---\n\n# Spring安全漏洞\n\n## CVE-2024-38819\n\n\n### 漏洞说明\n\n#### 漏洞描述\n\n通过功能性 Web 框架 WebMvc.fn 或 WebFlux.fn 提供静态资源的应用程序容易受到路径遍历攻击。攻击者可以构建恶意 HTTP 请求，并获取文件系统上任何文件，这些文件也可以被运行 Spring 应用程序的进程访问\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.13\n\n* Spring Framework 6.0.0 - 6.0.24\n\n* Spring Framework 5.3.0 - 5.3.40\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.41\n\n#### 推荐版本\n\nSpring 升级到 5.3.41及以上\n\n### 解决方案\n\n#### 前置了解：Spring5.x停止维护说明\n\n* 简单说：就是Spring5+SpringBoot2的开源版本已经停止维护了，只有购买了Spring商业版才能升级到Spring 5.3.41，所以无法在直接进行升级\n\n* 更多信息可查看[Spring5开源版停止维护说明](/guides/upgrade/vulnerability/Spring5开源版停止维护说明.html)\n\n#### 解决方案一：针对漏洞实际情况进行分析处理\n\n* 找到Spring官方对这个漏洞的说明[https://spring.io/security/cve-2024-38819](https://spring.io/security/cve-2024-38819)\n\n* 其中有说到漏洞的触发条件，需同时满足以下两个条件：\n  * 1、应用程序通过RouterFunctions（WebMvc.fn/WebFlux.fn 的核心组件）处理静态资源\n  * 2、静态资源的位置被显式配置为FileSystemResource（即资源存储在服务器本地文件系统中，而非打包在应用 JAR 包内的类路径资源等）\n\n* 针对条件一、条件二可以项目全局搜索`RouterFunctions`，看看是否有如此处理静态资源。例如类似如下的代码\n\n```java\n@Bean\npublic RouterFunction<ServerResponse> staticResourceRouter() {\n    return RouterFunctions\n        .resources(\"/static/**\", new FileSystemResource(\"/path/to/local/static/files/\")); // 关键：使用FileSystemResource\n}\n```\n\n* `总结：` 目前是前后端分离项目，框架在后端不会放静态资源所以也就不存在使用RouterFunctions处理静态资源，同时更不可能用FileSystemResource直接使用服务器的静态资源，\n两者需要同时满足才有可能触发该漏洞。`而现在两者都不成立故此这个漏洞本质上根本不可能触发，项目上进行检查明确后可沟通讨论无需处理`\n\n#### 解决方案二：升级到Spring6\n\n* 框架[8.", "a": "", "chunkIndex": 2, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8485549092292786, "index": 2}, {"type": "rrf", "value": 0.005291005291005291, "index": null}], "tokens": 346}, {"id": "6eab3def-f6de-49d3-a38c-a5112ee61887", "updateTime": "", "q": "0.x](/guides/upgrade/update/8.0版本升级.html)就是Spring6的，等于需要项目上升级到框架8.0才行。**非必要，请勿升级。这属于大升级改造，应项目有规划时考虑，而不应该为了一个无法触发的漏洞升级**\n\n* 升级Spring6的代价很高，JDK需要从8最低升级到17，配套的SpringBoot、SpringCloud全部都需要升级，还有各种第三方框架Tomcat、Swagger都有较多调整\n\n## CVE-2024-22262\n\n### 漏洞说明\n\n#### 漏洞描述\n\n如果 URL 在通过验证检查后使用，则用于解析外部提供的 URL（例如通过查询参数）并在已解析 URL\n的主机上执行验证检查的应用程序可能容易受到开放重定向攻击或 SSRF 攻击。UriComponentsBuilder\n\n这与 CVE-2024-22259 和 CVE-2024-22243 相同，但输入不同\n\n官方修复说明: [https://spring.io/security/cve-2024-22262](https://spring.io/security/cve-2024-22262)\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.5\n\n* Spring Framework 6.0.0 - 6.0.18\n\n* Spring Framework 5.3.0 - 5.3.33\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.34\n\n#### 推荐版本\n\nSpring 升级到 5.3.34及以上\n\n### 解决方案\n\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.34</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.368及以上 前端: 7.4.330及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.3.441及以上 前端: 7.3.449及以上 | 请参考[7.3版本升级说明](/guides/upgrade/update/7.3版本升级.md) |", "a": "", "chunkIndex": 3, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8432164788246155, "index": 3}, {"type": "rrf", "value": 0.005208333333333333, "index": null}], "tokens": 346}, {"id": "0a4c95cc-1b30-4a82-9185-4cfe19d3497d", "updateTime": "", "q": "0.x](/guides/upgrade/update/8.0版本升级.html)就是Spring6的，等于需要项目上升级到框架8.0才行。**非必要，请勿升级。这属于大升级改造，应项目有规划时考虑，而不应该为了一个无法触发的漏洞升级**\n\n* 升级Spring6的代价很高，JDK需要从8最低升级到17，配套的SpringBoot、SpringCloud全部都需要升级，还有各种第三方框架Tomcat、Swagger都有较多调整\n\n## CVE-2024-22262\n\n### 漏洞说明\n\n#### 漏洞描述\n\n如果 URL 在通过验证检查后使用，则用于解析外部提供的 URL（例如通过查询参数）并在已解析 URL\n的主机上执行验证检查的应用程序可能容易受到开放重定向攻击或 SSRF 攻击。UriComponentsBuilder\n\n这与 CVE-2024-22259 和 CVE-2024-22243 相同，但输入不同\n\n官方修复说明: [https://spring.io/security/cve-2024-22262](https://spring.io/security/cve-2024-22262)\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.5\n\n* Spring Framework 6.0.0 - 6.0.18\n\n* Spring Framework 5.3.0 - 5.3.33\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.34\n\n#### 推荐版本\n\nSpring 升级到 5.3.34及以上\n\n### 解决方案\n\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.34</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.368及以上 前端: 7.4.330及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.3.441及以上 前端: 7.3.449及以上 | 请参考[7.3版本升级说明](/guides/upgrade/update/7.3版本升级.md) |", "a": "", "chunkIndex": 4, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403b2233e4d4b422be035", "sourceId": "689403b2233e4d4b422be033", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8432164788246155, "index": 4}, {"type": "rrf", "value": 0.005128205128205128, "index": null}], "tokens": 346}, {"id": "837ffe18-c5f2-43dd-8f82-2e10af15e681", "updateTime": "", "q": "0.x](/guides/upgrade/update/8.0版本升级.html)就是Spring6的，等于需要项目上升级到框架8.0才行。**非必要，请勿升级。这属于大升级改造，应项目有规划时考虑，而不应该为了一个无法触发的漏洞升级**\n\n* 升级Spring6的代价很高，JDK需要从8最低升级到17，配套的SpringBoot、SpringCloud全部都需要升级，还有各种第三方框架Tomcat、Swagger都有较多调整\n\n## CVE-2024-22262\n\n### 漏洞说明\n\n#### 漏洞描述\n\n如果 URL 在通过验证检查后使用，则用于解析外部提供的 URL（例如通过查询参数）并在已解析 URL\n的主机上执行验证检查的应用程序可能容易受到开放重定向攻击或 SSRF 攻击。UriComponentsBuilder\n\n这与 CVE-2024-22259 和 CVE-2024-22243 相同，但输入不同\n\n官方修复说明: [https://spring.io/security/cve-2024-22262](https://spring.io/security/cve-2024-22262)\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.5\n\n* Spring Framework 6.0.0 - 6.0.18\n\n* Spring Framework 5.3.0 - 5.3.33\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.34\n\n#### 推荐版本\n\nSpring 升级到 5.3.34及以上\n\n### 解决方案\n\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.34</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.368及以上 前端: 7.4.330及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.3.441及以上 前端: 7.3.449及以上 | 请参考[7.3版本升级说明](/guides/upgrade/update/7.3版本升级.md) |", "a": "", "chunkIndex": 5, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8432164788246155, "index": 5}, {"type": "rrf", "value": 0.00505050505050505, "index": null}], "tokens": 346}, {"id": "94507fc6-be10-45ea-b1b5-cdab7908255b", "updateTime": "", "q": "0.x](/guides/upgrade/update/8.0版本升级.html)就是Spring6的，等于需要项目上升级到框架8.0才行。**非必要，请勿升级。这属于大升级改造，应项目有规划时考虑，而不应该为了一个无法触发的漏洞升级**\n\n* 升级Spring6的代价很高，JDK需要从8最低升级到17，配套的SpringBoot、SpringCloud全部都需要升级，还有各种第三方框架Tomcat、Swagger都有较多调整\n\n## CVE-2024-22262\n\n### 漏洞说明\n\n#### 漏洞描述\n\n如果 URL 在通过验证检查后使用，则用于解析外部提供的 URL（例如通过查询参数）并在已解析 URL\n的主机上执行验证检查的应用程序可能容易受到开放重定向攻击或 SSRF 攻击。UriComponentsBuilder\n\n这与 CVE-2024-22259 和 CVE-2024-22243 相同，但输入不同\n\n官方修复说明: [https://spring.io/security/cve-2024-22262](https://spring.io/security/cve-2024-22262)\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.5\n\n* Spring Framework 6.0.0 - 6.0.18\n\n* Spring Framework 5.3.0 - 5.3.33\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.34\n\n#### 推荐版本\n\nSpring 升级到 5.3.34及以上\n\n### 解决方案\n\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.34</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.368及以上 前端: 7.4.330及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.3.441及以上 前端: 7.3.449及以上 | 请参考[7.3版本升级说明](/guides/upgrade/update/7.3版本升级.md) |\n", "a": "", "chunkIndex": 6, "datasetId": "68905601e35435613e1eec43", "collectionId": "68905602e35435613e1eec46", "sourceId": "68905602e35435613e1eec44", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8432164788246155, "index": 6}, {"type": "rrf", "value": 0.004975124378109453, "index": null}], "tokens": 346}, {"id": "cae09069-2d5a-4e95-a71c-3a52db108386", "updateTime": "", "q": "Spring Framework受影响版本中，由于UriComponentsBuilder中在处理URL 时未正确过滤用户信息中的”\\[“，可能导致威胁者构造恶意URL绕过验证。\n当应用程序使用UriComponentsBuilder来解析外部提供的URL（如通过查询参数）并对解析的URL的主机执行验证检查时可能容易受到Open重定向攻击和SSRF攻击，导致网络钓鱼和内部网络探测等。\n\n官方修复说明: [https://spring.io/security/cve-2024-22243](https://spring.io/security/cve-2024-22243)\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.3\n\n* Spring Framework 6.0.0 - 6.0.16\n\n* Spring Framework 5.3.0 - 5.3.31\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.32\n\n#### 推荐版本\n\nSpring 升级到 5.3.32及以上\n\n### 解决方案\n\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.32</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n* **注意注意注意:** 框架7.2及以上有国际化功能，而spring5.3对国际化相关功能有调整\n  * `当框架版本为框架7.2及以上时`,请复制如下代码到项目上(注意包名要和java中相同,实现覆盖SiniCube框架逻辑)。`框架7.1及以下无需该处理`\n  * [SpringMessageConfig.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/SpringMessageConfig.java)[点击下载或右键另存为]\n  * [LocaleChangeInterceptor.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/LocaleChangeInterceptor.java)[点击下载或右键另存为]\n\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.331及以上 前端: 7.4.302及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.1.281及以上 前端: 7.1.\n", "a": "", "chunkIndex": 7, "datasetId": "68905601e35435613e1eec43", "collectionId": "68905602e35435613e1eec46", "sourceId": "68905602e35435613e1eec44", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8112437725067139, "index": 7}, {"type": "rrf", "value": 0.004901960784313725, "index": null}], "tokens": 376}, {"id": "2daacee6-6cd7-4366-becc-429f8aeaa133", "updateTime": "", "q": "Spring Framework受影响版本中，由于UriComponentsBuilder中在处理URL 时未正确过滤用户信息中的”\\[“，可能导致威胁者构造恶意URL绕过验证。\n当应用程序使用UriComponentsBuilder来解析外部提供的URL（如通过查询参数）并对解析的URL的主机执行验证检查时可能容易受到Open重定向攻击和SSRF攻击，导致网络钓鱼和内部网络探测等。\n\n官方修复说明: [https://spring.io/security/cve-2024-22243](https://spring.io/security/cve-2024-22243)\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.3\n\n* Spring Framework 6.0.0 - 6.0.16\n\n* Spring Framework 5.3.0 - 5.3.31\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.32\n\n#### 推荐版本\n\nSpring 升级到 5.3.32及以上\n\n### 解决方案\n\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.32</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n* **注意注意注意:** 框架7.2及以上有国际化功能，而spring5.3对国际化相关功能有调整\n  * `当框架版本为框架7.2及以上时`,请复制如下代码到项目上(注意包名要和java中相同,实现覆盖SiniCube框架逻辑)。`框架7.1及以下无需该处理`\n  * [SpringMessageConfig.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/SpringMessageConfig.java)[点击下载或右键另存为]\n  * [LocaleChangeInterceptor.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/LocaleChangeInterceptor.java)[点击下载或右键另存为]\n\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.331及以上 前端: 7.4.302及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.1.281及以上 前端: 7.1.\n", "a": "", "chunkIndex": 8, "datasetId": "68905601e35435613e1eec43", "collectionId": "68905602e35435613e1eec46", "sourceId": "68905602e35435613e1eec44", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8112437725067139, "index": 8}, {"type": "rrf", "value": 0.004830917874396135, "index": null}], "tokens": 376}, {"id": "f7de2ff7-aef1-4a42-b9d2-9000d8c81a0a", "updateTime": "", "q": "Spring Framework受影响版本中，由于UriComponentsBuilder中在处理URL 时未正确过滤用户信息中的”\\[“，可能导致威胁者构造恶意URL绕过验证。\n当应用程序使用UriComponentsBuilder来解析外部提供的URL（如通过查询参数）并对解析的URL的主机执行验证检查时可能容易受到Open重定向攻击和SSRF攻击，导致网络钓鱼和内部网络探测等。\n\n官方修复说明: [https://spring.io/security/cve-2024-22243](https://spring.io/security/cve-2024-22243)\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.3\n\n* Spring Framework 6.0.0 - 6.0.16\n\n* Spring Framework 5.3.0 - 5.3.31\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.32\n\n#### 推荐版本\n\nSpring 升级到 5.3.32及以上\n\n### 解决方案\n\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.32</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n* **注意注意注意:** 框架7.2及以上有国际化功能，而spring5.3对国际化相关功能有调整\n  * `当框架版本为框架7.2及以上时`,请复制如下代码到项目上(注意包名要和java中相同,实现覆盖SiniCube框架逻辑)。`框架7.1及以下无需该处理`\n  * [SpringMessageConfig.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/SpringMessageConfig.java)[点击下载或右键另存为]\n  * [LocaleChangeInterceptor.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/LocaleChangeInterceptor.java)[点击下载或右键另存为]\n\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.331及以上 前端: 7.4.302及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.1.281及以上 前端: 7.1.", "a": "", "chunkIndex": 9, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403b2233e4d4b422be035", "sourceId": "689403b2233e4d4b422be033", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8112437725067139, "index": 9}, {"type": "rrf", "value": 0.0047619047619047615, "index": null}], "tokens": 375}, {"id": "7b239b45-72f1-4a2d-8eac-91b8247e576c", "updateTime": "", "q": "Spring Framework受影响版本中，由于UriComponentsBuilder中在处理URL 时未正确过滤用户信息中的”\\[“，可能导致威胁者构造恶意URL绕过验证。\n当应用程序使用UriComponentsBuilder来解析外部提供的URL（如通过查询参数）并对解析的URL的主机执行验证检查时可能容易受到Open重定向攻击和SSRF攻击，导致网络钓鱼和内部网络探测等。\n\n官方修复说明: [https://spring.io/security/cve-2024-22243](https://spring.io/security/cve-2024-22243)\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.3\n\n* Spring Framework 6.0.0 - 6.0.16\n\n* Spring Framework 5.3.0 - 5.3.31\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.32\n\n#### 推荐版本\n\nSpring 升级到 5.3.32及以上\n\n### 解决方案\n\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.32</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n* **注意注意注意:** 框架7.2及以上有国际化功能，而spring5.3对国际化相关功能有调整\n  * `当框架版本为框架7.2及以上时`,请复制如下代码到项目上(注意包名要和java中相同,实现覆盖SiniCube框架逻辑)。`框架7.1及以下无需该处理`\n  * [SpringMessageConfig.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/SpringMessageConfig.java)[点击下载或右键另存为]\n  * [LocaleChangeInterceptor.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/LocaleChangeInterceptor.java)[点击下载或右键另存为]\n\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.331及以上 前端: 7.4.302及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.1.281及以上 前端: 7.1.", "a": "", "chunkIndex": 10, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8112437725067139, "index": 10}, {"type": "rrf", "value": 0.004694835680751174, "index": null}], "tokens": 375}, {"id": "f9d74af2-f241-428f-9e60-08c38f571511", "updateTime": "", "q": "Spring Framework受影响版本中，由于UriComponentsBuilder中在处理URL 时未正确过滤用户信息中的”\\[“，可能导致威胁者构造恶意URL绕过验证。\n当应用程序使用UriComponentsBuilder来解析外部提供的URL（如通过查询参数）并对解析的URL的主机执行验证检查时可能容易受到Open重定向攻击和SSRF攻击，导致网络钓鱼和内部网络探测等。\n\n官方修复说明: [https://spring.io/security/cve-2024-22243](https://spring.io/security/cve-2024-22243)\n\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.3\n\n* Spring Framework 6.0.0 - 6.0.16\n\n* Spring Framework 5.3.0 - 5.3.31\n\n以及不受支持的旧版本。\n\n#### 安全版本\n\nSpring >= 5.3.32\n\n#### 推荐版本\n\nSpring 升级到 5.3.32及以上\n\n### 解决方案\n\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.32</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n* **注意注意注意:** 框架7.2及以上有国际化功能，而spring5.3对国际化相关功能有调整\n  * `当框架版本为框架7.2及以上时`,请复制如下代码到项目上(注意包名要和java中相同,实现覆盖SiniCube框架逻辑)。`框架7.1及以下无需该处理`\n  * [SpringMessageConfig.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/SpringMessageConfig.java)[点击下载或右键另存为]\n  * [LocaleChangeInterceptor.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/LocaleChangeInterceptor.java)[点击下载或右键另存为]\n\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.331及以上 前端: 7.4.302及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.1.281及以上 前端: 7.1.", "a": "", "chunkIndex": 11, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8112437725067139, "index": 11}, {"type": "rrf", "value": 0.004629629629629629, "index": null}], "tokens": 375}, {"id": "7913d4a4-ea6a-4752-a7d1-98f58a584521", "updateTime": "", "q": "### 微服务项目特别说明（仅微服务项目看）\n\n* 微服务项目中，**有Gateway时，如果SpringBoot的版本为2.3.4，则无法直接升级spring的版本到5.3.x**，将得到如下报错\n\n```text\n[ERROR][SIRM][51360,144,reactor-http-nio-4][2024-08-05 17:09:17,961][reactor.util.Loggers$Slf4JLogger.error:319] - [id: 0x0db35a4b, L:/127.0.0.1:9999 - R:/127.0.0.1:9146] \njava.lang.NoSuchMethodError: reactor.netty.http.server.HttpServerRequest.hostPort()I\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.resolveBaseUrl(ReactorServerHttpRequest.java:84) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.initUri(ReactorServerHttpRequest.java:79) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.<init>(ReactorServerHttpRequest.java:71) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:58) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:40) ~[spring-web-5.3.34.jar:5.3.34]\n\tat reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:64) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:537) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:278) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:475) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:191) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]", "a": "", "chunkIndex": 12, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403b2233e4d4b422be035", "sourceId": "689403b2233e4d4b422be033", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8075941801071167, "index": 12}, {"type": "rrf", "value": 0.0045662100456621, "index": null}], "tokens": 647}, {"id": "83f2b3ee-9890-46a7-a6fd-632253ed7b0d", "updateTime": "", "q": "### 微服务项目特别说明（仅微服务项目看）\n\n* 微服务项目中，**有Gateway时，如果SpringBoot的版本为2.3.4，则无法直接升级spring的版本到5.3.x**，将得到如下报错\n\n```text\n[ERROR][SIRM][51360,144,reactor-http-nio-4][2024-08-05 17:09:17,961][reactor.util.Loggers$Slf4JLogger.error:319] - [id: 0x0db35a4b, L:/127.0.0.1:9999 - R:/127.0.0.1:9146] \njava.lang.NoSuchMethodError: reactor.netty.http.server.HttpServerRequest.hostPort()I\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.resolveBaseUrl(ReactorServerHttpRequest.java:84) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.initUri(ReactorServerHttpRequest.java:79) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.<init>(ReactorServerHttpRequest.java:71) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:58) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:40) ~[spring-web-5.3.34.jar:5.3.34]\n\tat reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:64) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:537) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:278) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:475) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:191) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n", "a": "", "chunkIndex": 13, "datasetId": "68905601e35435613e1eec43", "collectionId": "68905602e35435613e1eec46", "sourceId": "68905602e35435613e1eec44", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8075941801071167, "index": 13}, {"type": "rrf", "value": 0.0045045045045045045, "index": null}], "tokens": 647}, {"id": "94fe47af-9d12-42e8-92bd-a0d69d306b5f", "updateTime": "", "q": "### 微服务项目特别说明（仅微服务项目看）\n\n* 微服务项目中，**有Gateway时，如果SpringBoot的版本为2.3.4，则无法直接升级spring的版本到5.3.x**，将得到如下报错\n\n```text\n[ERROR][SIRM][51360,144,reactor-http-nio-4][2024-08-05 17:09:17,961][reactor.util.Loggers$Slf4JLogger.error:319] - [id: 0x0db35a4b, L:/127.0.0.1:9999 - R:/127.0.0.1:9146] \njava.lang.NoSuchMethodError: reactor.netty.http.server.HttpServerRequest.hostPort()I\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.resolveBaseUrl(ReactorServerHttpRequest.java:84) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.initUri(ReactorServerHttpRequest.java:79) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.<init>(ReactorServerHttpRequest.java:71) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:58) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:40) ~[spring-web-5.3.34.jar:5.3.34]\n\tat reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:64) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:537) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:278) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:475) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:191) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]", "a": "", "chunkIndex": 14, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8075941801071167, "index": 14}, {"type": "rrf", "value": 0.0044444444444444444, "index": null}], "tokens": 647}, {"id": "5c641953-2ce2-4dae-9996-5667491b77f4", "updateTime": "", "q": "### 微服务项目特别说明（仅微服务项目看）\n\n* 微服务项目中，**有Gateway时，如果SpringBoot的版本为2.3.4，则无法直接升级spring的版本到5.3.x**，将得到如下报错\n\n```text\n[ERROR][SIRM][51360,144,reactor-http-nio-4][2024-08-05 17:09:17,961][reactor.util.Loggers$Slf4JLogger.error:319] - [id: 0x0db35a4b, L:/127.0.0.1:9999 - R:/127.0.0.1:9146] \njava.lang.NoSuchMethodError: reactor.netty.http.server.HttpServerRequest.hostPort()I\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.resolveBaseUrl(ReactorServerHttpRequest.java:84) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.initUri(ReactorServerHttpRequest.java:79) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.<init>(ReactorServerHttpRequest.java:71) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:58) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:40) ~[spring-web-5.3.34.jar:5.3.34]\n\tat reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:64) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:537) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:278) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:475) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:191) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]", "a": "", "chunkIndex": 15, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8075941801071167, "index": 15}, {"type": "rrf", "value": 0.0043859649122807015, "index": null}], "tokens": 647}, {"id": "5d872e1c-0ac4-4a2b-9726-2a15a89f19df", "updateTime": "", "q": "SelectedKeys(NioEventLoop.java:650) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_201]\n```\n\n* **原因：** Spring5.3.x删除了一些方法，导致SpringCloudGateway不兼容\n\n* **解决方案一：** 升级框架版本到[sirmapp7.4.353 -> sirmapp7.4.365(漏洞升级-重要重要重要)](/guides/upgrade/update/7.4版本升级.html#sirmapp7-4-353-sirmapp7-4-365-漏洞升级-重要重要重要)，这将提升SpringBoot、SpringCloud的版本从而兼容\n\n* **解决方案二：** Gateway项目单独不升级Spring版本，还是保持原版本\n  * 不升级Gateway项目也不受该漏洞影响，漏洞的核心是`UriComponentsBuilder`类中的方法`fromUriString()`等可能导致验证绕过，而Gateway项目不会用到此类方法\n\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.3.32.jar】和【spring-context-5.3.34.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.34**。\n\n## CVE-2024-22243\n\n### 漏洞说明\n\n#### 漏洞描述\n\nSpring Framework中修复了一个URL解析不当漏洞（CVE-2024-22243）。", "a": "", "chunkIndex": 16, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403b2233e4d4b422be035", "sourceId": "689403b2233e4d4b422be033", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7814499139785767, "index": 16}, {"type": "rrf", "value": 0.004329004329004329, "index": null}], "tokens": 368}, {"id": "b20bb3dd-010b-4ff6-83c5-f21e60f48ae4", "updateTime": "", "q": "SelectedKeys(NioEventLoop.java:650) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_201]\n```\n\n* **原因：** Spring5.3.x删除了一些方法，导致SpringCloudGateway不兼容\n\n* **解决方案一：** 升级框架版本到[sirmapp7.4.353 -> sirmapp7.4.365(漏洞升级-重要重要重要)](/guides/upgrade/update/7.4版本升级.html#sirmapp7-4-353-sirmapp7-4-365-漏洞升级-重要重要重要)，这将提升SpringBoot、SpringCloud的版本从而兼容\n\n* **解决方案二：** Gateway项目单独不升级Spring版本，还是保持原版本\n  * 不升级Gateway项目也不受该漏洞影响，漏洞的核心是`UriComponentsBuilder`类中的方法`fromUriString()`等可能导致验证绕过，而Gateway项目不会用到此类方法\n\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.3.32.jar】和【spring-context-5.3.34.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.34**。\n\n## CVE-2024-22243\n\n### 漏洞说明\n\n#### 漏洞描述\n\nSpring Framework中修复了一个URL解析不当漏洞（CVE-2024-22243）。", "a": "", "chunkIndex": 17, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7814499139785767, "index": 17}, {"type": "rrf", "value": 0.004273504273504273, "index": null}], "tokens": 368}, {"id": "191aadae-67d0-4f23-8118-fc951b7882ba", "updateTime": "", "q": "SelectedKeys(NioEventLoop.java:650) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_201]\n```\n\n* **原因：** Spring5.3.x删除了一些方法，导致SpringCloudGateway不兼容\n\n* **解决方案一：** 升级框架版本到[sirmapp7.4.353 -> sirmapp7.4.365(漏洞升级-重要重要重要)](/guides/upgrade/update/7.4版本升级.html#sirmapp7-4-353-sirmapp7-4-365-漏洞升级-重要重要重要)，这将提升SpringBoot、SpringCloud的版本从而兼容\n\n* **解决方案二：** Gateway项目单独不升级Spring版本，还是保持原版本\n  * 不升级Gateway项目也不受该漏洞影响，漏洞的核心是`UriComponentsBuilder`类中的方法`fromUriString()`等可能导致验证绕过，而Gateway项目不会用到此类方法\n\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.3.32.jar】和【spring-context-5.3.34.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.34**。\n\n## CVE-2024-22243\n\n### 漏洞说明\n\n#### 漏洞描述\n\nSpring Framework中修复了一个URL解析不当漏洞（CVE-2024-22243）。", "a": "", "chunkIndex": 18, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7814499139785767, "index": 18}, {"type": "rrf", "value": 0.004219409282700422, "index": null}], "tokens": 368}, {"id": "c65f49a9-d491-4497-8960-4f53edf9f8e8", "updateTime": "", "q": "SelectedKeys(NioEventLoop.java:650) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_201]\n```\n\n* **原因：** Spring5.3.x删除了一些方法，导致SpringCloudGateway不兼容\n\n* **解决方案一：** 升级框架版本到[sirmapp7.4.353 -> sirmapp7.4.365(漏洞升级-重要重要重要)](/guides/upgrade/update/7.4版本升级.html#sirmapp7-4-353-sirmapp7-4-365-漏洞升级-重要重要重要)，这将提升SpringBoot、SpringCloud的版本从而兼容\n\n* **解决方案二：** Gateway项目单独不升级Spring版本，还是保持原版本\n  * 不升级Gateway项目也不受该漏洞影响，漏洞的核心是`UriComponentsBuilder`类中的方法`fromUriString()`等可能导致验证绕过，而Gateway项目不会用到此类方法\n\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.3.32.jar】和【spring-context-5.3.34.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.34**。\n\n## CVE-2024-22243\n\n### 漏洞说明\n\n#### 漏洞描述\n\nSpring Framework中修复了一个URL解析不当漏洞（CVE-2024-22243）。\n", "a": "", "chunkIndex": 19, "datasetId": "68905601e35435613e1eec43", "collectionId": "68905602e35435613e1eec46", "sourceId": "68905602e35435613e1eec44", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7814499139785767, "index": 19}, {"type": "rrf", "value": 0.004166666666666667, "index": null}], "tokens": 369}, {"id": "fe5e2944-7f64-4f2d-9a3a-eef86f42e9b2", "updateTime": "", "q": "SelectedKeys(NioEventLoop.java:650) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_201]\n```\n\n* **原因：** Spring5.3.x删除了一些方法，导致SpringCloudGateway不兼容\n\n* **解决方案一：** 升级框架版本到[sirmapp7.4.353 -> sirmapp7.4.365(漏洞升级-重要重要重要)](/guides/upgrade/update/7.4版本升级.html#sirmapp7-4-353-sirmapp7-4-365-漏洞升级-重要重要重要)，这将提升SpringBoot、SpringCloud的版本从而兼容\n\n* **解决方案二：** Gateway项目单独不升级Spring版本，还是保持原版本\n  * 不升级Gateway项目也不受该漏洞影响，漏洞的核心是`UriComponentsBuilder`类中的方法`fromUriString()`等可能导致验证绕过，而Gateway项目不会用到此类方法\n\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.3.32.jar】和【spring-context-5.3.34.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.34**。\n\n## CVE-2024-22243\n\n### 漏洞说明\n\n#### 漏洞描述\n\nSpring Framework中修复了一个URL解析不当漏洞（CVE-2024-22243）。\n", "a": "", "chunkIndex": 20, "datasetId": "68905601e35435613e1eec43", "collectionId": "68905602e35435613e1eec46", "sourceId": "68905602e35435613e1eec44", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7814499139785767, "index": 20}, {"type": "rrf", "value": 0.004115226337448559, "index": null}], "tokens": 369}, {"id": "aab59815-ba28-4854-ac99-3133cdd1bd10", "updateTime": "", "q": "148及以上 | 请参考[7.1版本升级说明](/guides/upgrade/update/7.1版本升级.md) |\n\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.2.24.jar】和【spring-context-5.3.32.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.\n", "a": "", "chunkIndex": 21, "datasetId": "68905601e35435613e1eec43", "collectionId": "68905602e35435613e1eec46", "sourceId": "68905602e35435613e1eec44", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7757048606872559, "index": 21}, {"type": "rrf", "value": 0.004065040650406504, "index": null}], "tokens": 96}, {"id": "dc5af916-fdcb-476d-b426-e6509d447c19", "updateTime": "", "q": "148及以上 | 请参考[7.1版本升级说明](/guides/upgrade/update/7.1版本升级.md) |\n\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.2.24.jar】和【spring-context-5.3.32.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.\n", "a": "", "chunkIndex": 22, "datasetId": "68905601e35435613e1eec43", "collectionId": "68905602e35435613e1eec46", "sourceId": "68905602e35435613e1eec44", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7757048606872559, "index": 22}, {"type": "rrf", "value": 0.004016064257028112, "index": null}], "tokens": 96}, {"id": "ffcb7947-02b3-4ea4-b0bc-5c0ee6275e10", "updateTime": "", "q": "148及以上 | 请参考[7.1版本升级说明](/guides/upgrade/update/7.1版本升级.md) |\n\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.2.24.jar】和【spring-context-5.3.32.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.", "a": "", "chunkIndex": 23, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7757048606872559, "index": 23}, {"type": "rrf", "value": 0.003968253968253968, "index": null}], "tokens": 95}, {"id": "6d66f54b-3373-4d58-8f92-8d0080573502", "updateTime": "", "q": "148及以上 | 请参考[7.1版本升级说明](/guides/upgrade/update/7.1版本升级.md) |\n\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.2.24.jar】和【spring-context-5.3.32.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.", "a": "", "chunkIndex": 24, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7757048606872559, "index": 24}, {"type": "rrf", "value": 0.00392156862745098, "index": null}], "tokens": 95}, {"id": "0e7faa69-f43d-4169-9db5-7b05c80c37be", "updateTime": "", "q": "148及以上 | 请参考[7.1版本升级说明](/guides/upgrade/update/7.1版本升级.md) |\n\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.2.24.jar】和【spring-context-5.3.32.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.", "a": "", "chunkIndex": 25, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403b2233e4d4b422be035", "sourceId": "689403b2233e4d4b422be033", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7757048606872559, "index": 25}, {"type": "rrf", "value": 0.003875968992248062, "index": null}], "tokens": 95}, {"id": "ae5b8d09-a595-49f8-b7d9-aedaf64632e7", "updateTime": "", "q": "at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.\n", "a": "", "chunkIndex": 26, "datasetId": "68905601e35435613e1eec43", "collectionId": "68905602e35435613e1eec46", "sourceId": "68905602e35435613e1eec44", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7263838052749634, "index": 26}, {"type": "rrf", "value": 0.0038314176245210726, "index": null}], "tokens": 693}, {"id": "c89a4fa3-78a7-474a-bca0-ec50897dafa2", "updateTime": "", "q": "at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.\n", "a": "", "chunkIndex": 27, "datasetId": "68905601e35435613e1eec43", "collectionId": "68905602e35435613e1eec46", "sourceId": "68905602e35435613e1eec44", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7263838052749634, "index": 27}, {"type": "rrf", "value": 0.003787878787878788, "index": null}], "tokens": 693}, {"id": "ff8cf2fe-c22b-4b09-bcfe-eeb3f3897747", "updateTime": "", "q": "at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.", "a": "", "chunkIndex": 28, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7263838052749634, "index": 28}, {"type": "rrf", "value": 0.0037453183520599247, "index": null}], "tokens": 693}, {"id": "fdb2bb6c-208c-47da-811c-f6436f696640", "updateTime": "", "q": "at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.", "a": "", "chunkIndex": 29, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403f85ae665737e5f7e37", "sourceId": "689403f85ae665737e5f7e35", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7263838052749634, "index": 29}, {"type": "rrf", "value": 0.003703703703703704, "index": null}], "tokens": 693}, {"id": "2bdc7ade-b737-4960-8770-189cf016b22e", "updateTime": "", "q": "at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.", "a": "", "chunkIndex": 30, "datasetId": "68905601e35435613e1eec43", "collectionId": "689403b2233e4d4b422be035", "sourceId": "689403b2233e4d4b422be033", "sourceName": "单元测试之Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.7263838052749634, "index": 30}, {"type": "rrf", "value": 0.003663003663003663, "index": null}], "tokens": 693}], "duration": "0.936s", "usingReRank": false, "searchMode": "embedding", "limit": 5000, "similarity": 0.0, "usingSimilarityFilter": true, "rerankWeight": 0.5, "embeddingWeight": 0.5}, "success": true}