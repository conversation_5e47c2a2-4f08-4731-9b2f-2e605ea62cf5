{"code": 200, "statusText": "", "message": "", "data": {"list": [{"id": "689078f27e51d3dcd1aa6bbf", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:10.488Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.5\n\n* Spring Framework 6.0.0 - 6.0.18\n\n* Spring Framework 5.3.0 - 5.3.33\n\n以及不受支持的旧版本。", "a": "", "chunkIndex": 9, "indexes": [{"type": "default", "dataId": "211", "text": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.5\n\n* Spring Framework 6.0.0 - 6.0.18\n\n* Spring Framework 5.3.0 - 5.3.33\n\n以及不受支持的旧版本。", "_id": "689078f27e51d3dcd1aa6bc0"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 1.0416666666666667, "index": 0}], "tokens": 96}, {"id": "689078f27e51d3dcd1aa6bf0", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:10.646Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.3\n\n* Spring Framework 6.0.0 - 6.0.16\n\n* Spring Framework 5.3.0 - 5.3.31\n\n以及不受支持的旧版本。", "a": "", "chunkIndex": 17, "indexes": [{"type": "default", "dataId": "218", "text": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.3\n\n* Spring Framework 6.0.0 - 6.0.16\n\n* Spring Framework 5.3.0 - 5.3.31\n\n以及不受支持的旧版本。", "_id": "689078f27e51d3dcd1aa6bf1"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 1.0416666666666667, "index": 1}], "tokens": 96}, {"id": "689078f17e51d3dcd1aa6b68", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:09.066Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.13\n\n* Spring Framework 6.0.0 - 6.0.24\n\n* Spring Framework 5.3.0 - 5.3.40\n\n以及不受支持的旧版本。", "a": "", "chunkIndex": 2, "indexes": [{"type": "default", "dataId": "203", "text": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.13\n\n* Spring Framework 6.0.0 - 6.0.24\n\n* Spring Framework 5.3.0 - 5.3.40\n\n以及不受支持的旧版本。", "_id": "689078f17e51d3dcd1aa6b69"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 1.0416666666666667, "index": 2}], "tokens": 96}, {"id": "689078f37e51d3dcd1aa6c0a", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:11.381Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 解决方案\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.32</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n* **注意注意注意:** 框架7.2及以上有国际化功能，而spring5.3对国际化相关功能有调整\n  * `当框架版本为框架7.2及以上时`,请复制如下代码到项目上(注意包名要和java中相同,实现覆盖SiniCube框架逻辑)。`框架7.1及以下无需该处理`\n  * [SpringMessageConfig.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/SpringMessageConfig.java)[点击下载或右键另存为]\n  * [LocaleChangeInterceptor.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/LocaleChangeInterceptor.java)[点击下载或右键另存为]", "a": "", "chunkIndex": 20, "indexes": [{"type": "default", "dataId": "221", "text": "# Spring安全漏洞\n## CVE-2024-22243\n### 解决方案\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.32</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n* **注意注意注意:** 框架7.2及以上有国际化功能，而spring5.3对国际化相关功能有调整\n  * `当框架版本为框架7.2及以上时`,请复制如下代码到项目上(注意包名要和java中相同,实现覆盖SiniCube框架逻辑)。`框架7.1及以下无需该处理`\n  * [SpringMessageConfig.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/SpringMessageConfig.java)[点击下载或右键另存为]\n  * [LocaleChangeInterceptor.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/LocaleChangeInterceptor.java)[点击下载或右键另存为]", "_id": "689078f37e51d3dcd1aa6c0b"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 1.0348557692307692, "index": 3}], "tokens": 352}, {"id": "689078f37e51d3dcd1aa6c1e", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:11.434Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.2.24.jar】和【spring-context-5.3.32.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.32**。", "a": "", "chunkIndex": 22, "indexes": [{"type": "default", "dataId": "224", "text": "# Spring安全漏洞\n## CVE-2024-22243\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.2.24.jar】和【spring-context-5.3.32.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.32**。", "_id": "689078f37e51d3dcd1aa6c1f"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 1.0244252873563218, "index": 4}], "tokens": 258}, {"id": "689078f27e51d3dcd1aa6bec", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:10.639Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.3.32.jar】和【spring-context-5.3.34.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.34**。", "a": "", "chunkIndex": 15, "indexes": [{"type": "default", "dataId": "217", "text": "# Spring安全漏洞\n## CVE-2024-22262\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.3.32.jar】和【spring-context-5.3.34.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.34**。", "_id": "689078f27e51d3dcd1aa6bed"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 1.0244252873563218, "index": 5}], "tokens": 258}, {"id": "689078f27e51d3dcd1aa6bce", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:10.526Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 解决方案\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.34</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)", "a": "", "chunkIndex": 12, "indexes": [{"type": "default", "dataId": "214", "text": "# Spring安全漏洞\n## CVE-2024-22262\n### 解决方案\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.34</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)", "_id": "689078f27e51d3dcd1aa6bcf"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 1.0125000000000002, "index": 6}], "tokens": 155}, {"id": "689078f27e51d3dcd1aa6be4", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:10.608Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 微服务项目特别说明（仅微服务项目看）\n\n* 微服务项目中，**有Gateway时，如果SpringBoot的版本为2.3.4，则无法直接升级spring的版本到5.3.x**，将得到如下报错\n\n```text\n[ERROR][SIRM][51360,144,reactor-http-nio-4][2024-08-05 17:09:17,961][reactor.util.Loggers$Slf4JLogger.error:319] - [id: 0x0db35a4b, L:/127.0.0.1:9999 - R:/127.0.0.1:9146] \njava.lang.NoSuchMethodError: reactor.netty.http.server.HttpServerRequest.hostPort()I\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.resolveBaseUrl(ReactorServerHttpRequest.java:84) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.initUri(ReactorServerHttpRequest.java:79) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.<init>(ReactorServerHttpRequest.java:71) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:58) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:40) ~[spring-web-5.3.34.jar:5.3.34]\n\tat reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:64) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:537) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:278) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:475) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:191) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_201]\n```\n\n* **原因：** Spring5.3.x删除了一些方法，导致SpringCloudGateway不兼容\n\n* **解决方案一：** 升级框架版本到[sirmapp7.4.353 -> sirmapp7.4.365(漏洞升级-重要重要重要)](/guides/upgrade/update/7.4版本升级.html#sirmapp7-4-353-sirmapp7-4-365-漏洞升级-重要重要重要)，这将提升SpringBoot、SpringCloud的版本从而兼容\n\n* **解决方案二：** Gateway项目单独不升级Spring版本，还是保持原版本\n  * 不升级Gateway项目也不受该漏洞影响，漏洞的核心是`UriComponentsBuilder`类中的方法`fromUriString()`等可能导致验证绕过，而Gateway项目不会用到此类方法", "a": "", "chunkIndex": 14, "indexes": [{"type": "default", "dataId": "216", "text": "# Spring安全漏洞\n## CVE-2024-22262\n### 微服务项目特别说明（仅微服务项目看）\n\n* 微服务项目中，**有Gateway时，如果SpringBoot的版本为2.3.4，则无法直接升级spring的版本到5.3.x**，将得到如下报错\n\n```text\n[ERROR][SIRM][51360,144,reactor-http-nio-4][2024-08-05 17:09:17,961][reactor.util.Loggers$Slf4JLogger.error:319] - [id: 0x0db35a4b, L:/127.0.0.1:9999 - R:/127.0.0.1:9146] \njava.lang.NoSuchMethodError: reactor.netty.http.server.HttpServerRequest.hostPort()I\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.resolveBaseUrl(ReactorServerHttpRequest.java:84) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.initUri(ReactorServerHttpRequest.java:79) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.<init>(ReactorServerHttpRequest.java:71) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:58) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:40) ~[spring-web-5.3.34.jar:5.3.34]\n\tat reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:64) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:537) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:278) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:475) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:191) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_201]\n```\n\n* **原因：** Spring5.3.x删除了一些方法，导致SpringCloudGateway不兼容\n\n* **解决方案一：** 升级框架版本到[sirmapp7.4.353 -> sirmapp7.4.365(漏洞升级-重要重要重要)](/guides/upgrade/update/7.4版本升级.html#sirmapp7-4-353-sirmapp7-4-365-漏洞升级-重要重要重要)，这将提升SpringBoot、SpringCloud的版本从而兼容\n\n* **解决方案二：** Gateway项目单独不升级Spring版本，还是保持原版本\n  * 不升级Gateway项目也不受该漏洞影响，漏洞的核心是`UriComponentsBuilder`类中的方法`fromUriString()`等可能导致验证绕过，而Gateway项目不会用到此类方法", "_id": "689078f27e51d3dcd1aa6be5"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 1.006389292635659, "index": 7}], "tokens": 2080}, {"id": "689078f17e51d3dcd1aa6b98", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:09.386Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 解决方案\n#### 前置了解：Spring5.x停止维护说明\n\n* 简单说：就是Spring5+SpringBoot2的开源版本已经停止维护了，只有购买了Spring商业版才能升级到Spring 5.3.41，所以无法在直接进行升级\n\n* 更多信息可查看[Spring5开源版停止维护说明](/guides/upgrade/vulnerability/Spring5开源版停止维护说明.html)", "a": "", "chunkIndex": 5, "indexes": [{"type": "default", "dataId": "207", "text": "# Spring安全漏洞\n## CVE-2024-38819\n### 解决方案\n#### 前置了解：Spring5.x停止维护说明\n\n* 简单说：就是Spring5+SpringBoot2的开源版本已经停止维护了，只有购买了Spring商业版才能升级到Spring 5.3.41，所以无法在直接进行升级\n\n* 更多信息可查看[Spring5开源版停止维护说明](/guides/upgrade/vulnerability/Spring5开源版停止维护说明.html)", "_id": "689078f17e51d3dcd1aa6b99"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.9320652173913044, "index": 8}], "tokens": 154}, {"id": "689078f27e51d3dcd1aa6c02", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:10.679Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 漏洞描述\n\nSpring Framework中修复了一个URL解析不当漏洞（CVE-2024-22243）。\n\nSpring Framework受影响版本中，由于UriComponentsBuilder中在处理URL 时未正确过滤用户信息中的”\\[“，可能导致威胁者构造恶意URL绕过验证。\n当应用程序使用UriComponentsBuilder来解析外部提供的URL（如通过查询参数）并对解析的URL的主机执行验证检查时可能容易受到Open重定向攻击和SSRF攻击，导致网络钓鱼和内部网络探测等。\n\n官方修复说明: [https://spring.io/security/cve-2024-22243](https://spring.io/security/cve-2024-22243)", "a": "", "chunkIndex": 16, "indexes": [{"type": "default", "dataId": "220", "text": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 漏洞描述\n\nSpring Framework中修复了一个URL解析不当漏洞（CVE-2024-22243）。\n\nSpring Framework受影响版本中，由于UriComponentsBuilder中在处理URL 时未正确过滤用户信息中的”\\[“，可能导致威胁者构造恶意URL绕过验证。\n当应用程序使用UriComponentsBuilder来解析外部提供的URL（如通过查询参数）并对解析的URL的主机执行验证检查时可能容易受到Open重定向攻击和SSRF攻击，导致网络钓鱼和内部网络探测等。\n\n官方修复说明: [https://spring.io/security/cve-2024-22243](https://spring.io/security/cve-2024-22243)", "_id": "689078f27e51d3dcd1aa6c03"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.9125000000000001, "index": 9}], "tokens": 227}, {"id": "689078f27e51d3dcd1aa6bc6", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:10.500Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 安全版本\n\nSpring >= 5.3.34", "a": "", "chunkIndex": 10, "indexes": [{"type": "default", "dataId": "213", "text": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 安全版本\n\nSpring >= 5.3.34", "_id": "689078f27e51d3dcd1aa6bc7"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.8863636363636365, "index": 10}], "tokens": 40}, {"id": "689078f17e51d3dcd1aa6b75", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:09.096Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 安全版本\n\nSpring >= 5.3.41", "a": "", "chunkIndex": 3, "indexes": [{"type": "default", "dataId": "205", "text": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 安全版本\n\nSpring >= 5.3.41", "_id": "689078f17e51d3dcd1aa6b76"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.8863636363636365, "index": 11}], "tokens": 40}, {"id": "689078f27e51d3dcd1aa6bf7", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:10.658Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 安全版本\n\nSpring >= 5.3.32", "a": "", "chunkIndex": 18, "indexes": [{"type": "default", "dataId": "219", "text": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 安全版本\n\nSpring >= 5.3.32", "_id": "689078f27e51d3dcd1aa6bf8"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.8863636363636365, "index": 12}], "tokens": 40}, {"id": "689078f17e51d3dcd1aa6b8f", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:09.302Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 推荐版本\n\nSpring 升级到 5.3.41及以上", "a": "", "chunkIndex": 4, "indexes": [{"type": "default", "dataId": "206", "text": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 推荐版本\n\nSpring 升级到 5.3.41及以上", "_id": "689078f17e51d3dcd1aa6b90"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.8653846153846153, "index": 13}], "tokens": 46}, {"id": "689078f37e51d3dcd1aa6c10", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:11.390Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 推荐版本\n\nSpring 升级到 5.3.32及以上", "a": "", "chunkIndex": 19, "indexes": [{"type": "default", "dataId": "222", "text": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 推荐版本\n\nSpring 升级到 5.3.32及以上", "_id": "689078f37e51d3dcd1aa6c11"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.8653846153846153, "index": 14}], "tokens": 46}, {"id": "689078f27e51d3dcd1aa6bbc", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:10.485Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 推荐版本\n\nSpring 升级到 5.3.34及以上", "a": "", "chunkIndex": 11, "indexes": [{"type": "default", "dataId": "212", "text": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 推荐版本\n\nSpring 升级到 5.3.34及以上", "_id": "689078f27e51d3dcd1aa6bbd"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.8653846153846153, "index": 15}], "tokens": 46}, {"id": "689078f17e51d3dcd1aa6b6b", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:09.069Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 漏洞描述\n\n通过功能性 Web 框架 WebMvc.fn 或 WebFlux.fn 提供静态资源的应用程序容易受到路径遍历攻击。攻击者可以构建恶意 HTTP 请求，并获取文件系统上任何文件，这些文件也可以被运行 Spring 应用程序的进程访问", "a": "", "chunkIndex": 1, "indexes": [{"type": "default", "dataId": "204", "text": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 漏洞描述\n\n通过功能性 Web 框架 WebMvc.fn 或 WebFlux.fn 提供静态资源的应用程序容易受到路径遍历攻击。攻击者可以构建恶意 HTTP 请求，并获取文件系统上任何文件，这些文件也可以被运行 Spring 应用程序的进程访问", "_id": "689078f17e51d3dcd1aa6b6c"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.7905405405405405, "index": 16}], "tokens": 115}, {"id": "689078f17e51d3dcd1aa6ba1", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:09.504Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 漏洞描述\n\n如果 URL 在通过验证检查后使用，则用于解析外部提供的 URL（例如通过查询参数）并在已解析 URL\n的主机上执行验证检查的应用程序可能容易受到开放重定向攻击或 SSRF 攻击。UriComponentsBuilder\n\n这与 CVE-2024-22259 和 CVE-2024-22243 相同，但输入不同\n\n官方修复说明: [https://spring.io/security/cve-2024-22262](https://spring.io/security/cve-2024-22262)", "a": "", "chunkIndex": 8, "indexes": [{"type": "default", "dataId": "209", "text": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 漏洞描述\n\n如果 URL 在通过验证检查后使用，则用于解析外部提供的 URL（例如通过查询参数）并在已解析 URL\n的主机上执行验证检查的应用程序可能容易受到开放重定向攻击或 SSRF 攻击。UriComponentsBuilder\n\n这与 CVE-2024-22259 和 CVE-2024-22243 相同，但输入不同\n\n官方修复说明: [https://spring.io/security/cve-2024-22262](https://spring.io/security/cve-2024-22262)", "_id": "689078f17e51d3dcd1aa6ba2"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.5116279069767442, "index": 17}], "tokens": 166}, {"id": "689078ae7e51d3dcd1aa657f", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078ad7e51d3dcd1aa654a", "updateTime": "2025-08-04T09:09:02.600Z", "q": "\n## 项目情况\n\n* 框架版本7.4.246 \n\n## 计划\n\n### 升级框架版本 \n\n* 改动点1：从框架7.4.246升级到7.4.613，这块有功能的变动、SQL脚本配置的调整等等需要处理 \n* 改动点2:  sirmapp7.4.365有过一次漏洞升级，升级了spring、springboot、springcloud的版本，关联影响较大，也会导致改动多\n\nhttp://192.168.1.121:18056/doc/sirmapp-dev/guides/upgrade/update/7.4版本升级.html\n\n![[Pasted image 20250519175652.png]]\n\n ", "a": "", "chunkIndex": 0, "indexes": [{"type": "default", "dataId": "116", "text": "## 项目情况\n\n* 框架版本7.4.246", "_id": "689078ae7e51d3dcd1aa6580"}, {"type": "default", "dataId": "117", "text": "## 计划\n### 升级框架版本 \n\n* 改动点1：从框架7.4.246升级到7.4.613，这块有功能的变动、SQL脚本配置的调整等等需要处理 \n* 改动点2:  sirmapp7.4.365有过一次漏洞升级，升级了spring、springboot、springcloud的版本，关联影响较大，也会导致改动多\n\nhttp://192.168.1.121:18056/doc/sirmapp-dev/guides/upgrade/update/7.4版本升级.html\n\n![[Pasted image 20250519175652.png]]", "_id": "689078ae7e51d3dcd1aa6581"}], "sourceId": "689078a57e51d3dcd1aa652c", "sourceName": "项目支持切换到minio作为文件存储中心.md", "score": [{"type": "fullText", "value": 0.5094339622641509, "index": 18}], "tokens": 192}, {"id": "689078f17e51d3dcd1aa6ba4", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:09.508Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 解决方案\n#### 解决方案二：升级到Spring6\n\n* 框架[8.0.x](/guides/upgrade/update/8.0版本升级.html)就是Spring6的，等于需要项目上升级到框架8.0才行。**非必要，请勿升级。这属于大升级改造，应项目有规划时考虑，而不应该为了一个无法触发的漏洞升级**\n\n* 升级Spring6的代价很高，JDK需要从8最低升级到17，配套的SpringBoot、SpringCloud全部都需要升级，还有各种第三方框架Tomcat、Swagger都有较多调整", "a": "", "chunkIndex": 7, "indexes": [{"type": "default", "dataId": "210", "text": "# Spring安全漏洞\n## CVE-2024-38819\n### 解决方案\n#### 解决方案二：升级到Spring6\n\n* 框架[8.0.x](/guides/upgrade/update/8.0版本升级.html)就是Spring6的，等于需要项目上升级到框架8.0才行。**非必要，请勿升级。这属于大升级改造，应项目有规划时考虑，而不应该为了一个无法触发的漏洞升级**\n\n* 升级Spring6的代价很高，JDK需要从8最低升级到17，配套的SpringBoot、SpringCloud全部都需要升级，还有各种第三方框架Tomcat、Swagger都有较多调整", "_id": "689078f17e51d3dcd1aa6ba5"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.5075757575757576, "index": 19}], "tokens": 214}, {"id": "689078f37e51d3dcd1aa6c18", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:11.423Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 解决方案\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.331及以上 前端: 7.4.302及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.1.281及以上 前端: 7.1.148及以上 | 请参考[7.1版本升级说明](/guides/upgrade/update/7.1版本升级.md) |", "a": "", "chunkIndex": 21, "indexes": [{"type": "default", "dataId": "223", "text": "# Spring安全漏洞\n## CVE-2024-22243\n### 解决方案\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.331及以上 前端: 7.4.302及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.1.281及以上 前端: 7.1.148及以上 | 请参考[7.1版本升级说明](/guides/upgrade/update/7.1版本升级.md) |", "_id": "689078f37e51d3dcd1aa6c19"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.5064935064935064, "index": 20}], "tokens": 247}, {"id": "689078f27e51d3dcd1aa6bd5", "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "updateTime": "2025-08-04T09:10:10.575Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 解决方案\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.368及以上 前端: 7.4.330及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.3.441及以上 前端: 7.3.449及以上 | 请参考[7.3版本升级说明](/guides/upgrade/update/7.3版本升级.md) |", "a": "", "chunkIndex": 13, "indexes": [{"type": "default", "dataId": "215", "text": "# Spring安全漏洞\n## CVE-2024-22262\n### 解决方案\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.368及以上 前端: 7.4.330及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.3.441及以上 前端: 7.3.449及以上 | 请参考[7.3版本升级说明](/guides/upgrade/update/7.3版本升级.md) |", "_id": "689078f27e51d3dcd1aa6bd6"}], "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "fullText", "value": 0.5064935064935064, "index": 21}], "tokens": 247}], "duration": "0.165s", "usingReRank": false, "searchMode": "fullTextRecall", "limit": 5000, "similarity": 0, "usingSimilarityFilter": false}}