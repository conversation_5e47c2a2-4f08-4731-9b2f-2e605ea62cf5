{"code": 200, "statusText": "", "message": "", "data": {"list": [{"id": "689078f17e51d3dcd1aa6b6b", "updateTime": "2025-08-04T09:10:09.069Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 漏洞描述\n\n通过功能性 Web 框架 WebMvc.fn 或 WebFlux.fn 提供静态资源的应用程序容易受到路径遍历攻击。攻击者可以构建恶意 HTTP 请求，并获取文件系统上任何文件，这些文件也可以被运行 Spring 应用程序的进程访问", "a": "", "chunkIndex": 1, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8413941860198975, "index": 10}, {"type": "reRank", "value": 0.9957333207130432, "index": 0}, {"type": "rrf", "value": 0.03047794966520434, "index": 0}], "tokens": 115}, {"id": "689078f17e51d3dcd1aa6b68", "updateTime": "2025-08-04T09:10:09.066Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.13\n\n* Spring Framework 6.0.0 - 6.0.24\n\n* Spring Framework 5.3.0 - 5.3.40\n\n以及不受支持的旧版本。", "a": "", "chunkIndex": 2, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8451368808746338, "index": 7}, {"type": "reRank", "value": 0.8582666516304016, "index": 3}, {"type": "rrf", "value": 0.030330882352941176, "index": 1}], "tokens": 96}, {"id": "689078f27e51d3dcd1aa6be4", "updateTime": "2025-08-04T09:10:10.608Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 微服务项目特别说明（仅微服务项目看）\n\n* 微服务项目中，**有Gateway时，如果SpringBoot的版本为2.3.4，则无法直接升级spring的版本到5.3.x**，将得到如下报错\n\n```text\n[ERROR][SIRM][51360,144,reactor-http-nio-4][2024-08-05 17:09:17,961][reactor.util.Loggers$Slf4JLogger.error:319] - [id: 0x0db35a4b, L:/127.0.0.1:9999 - R:/127.0.0.1:9146] \njava.lang.NoSuchMethodError: reactor.netty.http.server.HttpServerRequest.hostPort()I\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.resolveBaseUrl(ReactorServerHttpRequest.java:84) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.initUri(ReactorServerHttpRequest.java:79) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorServerHttpRequest.<init>(ReactorServerHttpRequest.java:71) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:58) ~[spring-web-5.3.34.jar:5.3.34]\n\tat org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:40) ~[spring-web-5.3.34.jar:5.3.34]\n\tat reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:64) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:537) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:278) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:475) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:191) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]\n\tat java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_201]\n```\n\n* **原因：** Spring5.3.x删除了一些方法，导致SpringCloudGateway不兼容\n\n* **解决方案一：** 升级框架版本到[sirmapp7.4.353 -> sirmapp7.4.365(漏洞升级-重要重要重要)](/guides/upgrade/update/7.4版本升级.html#sirmapp7-4-353-sirmapp7-4-365-漏洞升级-重要重要重要)，这将提升SpringBoot、SpringCloud的版本从而兼容\n\n* **解决方案二：** Gateway项目单独不升级Spring版本，还是保持原版本\n  * 不升级Gateway项目也不受该漏洞影响，漏洞的核心是`UriComponentsBuilder`类中的方法`fromUriString()`等可能导致验证绕过，而Gateway项目不会用到此类方法", "a": "", "chunkIndex": 14, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8423961997032166, "index": 9}, {"type": "reRank", "value": 0.9110597372055054, "index": 2}, {"type": "rrf", "value": 0.030158730158730156, "index": 2}], "tokens": 2080}, {"id": "689078f27e51d3dcd1aa6bbc", "updateTime": "2025-08-04T09:10:10.485Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 推荐版本\n\nSpring 升级到 5.3.34及以上", "a": "", "chunkIndex": 11, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.861883282661438, "index": 3}, {"type": "reRank", "value": 0.6443778872489929, "index": 8}, {"type": "rrf", "value": 0.030117753623188408, "index": 3}], "tokens": 46}, {"id": "689078f27e51d3dcd1aa6bbf", "updateTime": "2025-08-04T09:10:10.488Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.5\n\n* Spring Framework 6.0.0 - 6.0.18\n\n* Spring Framework 5.3.0 - 5.3.33\n\n以及不受支持的旧版本。", "a": "", "chunkIndex": 9, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8452323079109192, "index": 6}, {"type": "reRank", "value": 0.8472121953964233, "index": 5}, {"type": "rrf", "value": 0.03007688828584351, "index": 4}], "tokens": 96}, {"id": "689078f27e51d3dcd1aa6bc6", "updateTime": "2025-08-04T09:10:10.500Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 安全版本\n\nSpring >= 5.3.34", "a": "", "chunkIndex": 10, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8765210509300232, "index": 0}, {"type": "reRank", "value": 0.466854989528656, "index": 14}, {"type": "rrf", "value": 0.029726775956284153, "index": 5}], "tokens": 40}, {"id": "689078f37e51d3dcd1aa6c10", "updateTime": "2025-08-04T09:10:11.390Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 推荐版本\n\nSpring 升级到 5.3.32及以上", "a": "", "chunkIndex": 19, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8592641353607178, "index": 4}, {"type": "reRank", "value": 0.5948898196220398, "index": 9}, {"type": "rrf", "value": 0.02967032967032967, "index": 6}], "tokens": 46}, {"id": "689078f17e51d3dcd1aa6b75", "updateTime": "2025-08-04T09:10:09.096Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 安全版本\n\nSpring >= 5.3.41", "a": "", "chunkIndex": 3, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.875523567199707, "index": 1}, {"type": "reRank", "value": 0.45967090129852295, "index": 15}, {"type": "rrf", "value": 0.02928692699490662, "index": 7}], "tokens": 40}, {"id": "689078f17e51d3dcd1aa6b8f", "updateTime": "2025-08-04T09:10:09.302Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 漏洞说明\n#### 推荐版本\n\nSpring 升级到 5.3.41及以上", "a": "", "chunkIndex": 4, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8582656979560852, "index": 5}, {"type": "reRank", "value": 0.5840940475463867, "index": 10}, {"type": "rrf", "value": 0.029236022193768675, "index": 8}], "tokens": 46}, {"id": "689078f27e51d3dcd1aa6bf0", "updateTime": "2025-08-04T09:10:10.646Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 影响版本\n\n* Spring Framework 6.1.0 - 6.1.3\n\n* Spring Framework 6.0.0 - 6.0.16\n\n* Spring Framework 5.3.0 - 5.3.31\n\n以及不受支持的旧版本。", "a": "", "chunkIndex": 17, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8442624807357788, "index": 8}, {"type": "reRank", "value": 0.8409078121185303, "index": 7}, {"type": "rrf", "value": 0.02919863597612958, "index": 9}], "tokens": 96}, {"id": "689078f17e51d3dcd1aa6ba1", "updateTime": "2025-08-04T09:10:09.504Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 漏洞说明\n#### 漏洞描述\n\n如果 URL 在通过验证检查后使用，则用于解析外部提供的 URL（例如通过查询参数）并在已解析 URL\n的主机上执行验证检查的应用程序可能容易受到开放重定向攻击或 SSRF 攻击。UriComponentsBuilder\n\n这与 CVE-2024-22259 和 CVE-2024-22243 相同，但输入不同\n\n官方修复说明: [https://spring.io/security/cve-2024-22262](https://spring.io/security/cve-2024-22262)", "a": "", "chunkIndex": 8, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.810733437538147, "index": 16}, {"type": "reRank", "value": 0.9893082976341248, "index": 1}, {"type": "rrf", "value": 0.029116045245077504, "index": 10}], "tokens": 166}, {"id": "689078f27e51d3dcd1aa6bf7", "updateTime": "2025-08-04T09:10:10.658Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 安全版本\n\nSpring >= 5.3.32", "a": "", "chunkIndex": 18, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.873542308807373, "index": 2}, {"type": "reRank", "value": 0.39658302068710327, "index": 16}, {"type": "rrf", "value": 0.02886002886002886, "index": 11}], "tokens": 40}, {"id": "689078f27e51d3dcd1aa6c02", "updateTime": "2025-08-04T09:10:10.679Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 漏洞说明\n#### 漏洞描述\n\nSpring Framework中修复了一个URL解析不当漏洞（CVE-2024-22243）。\n\nSpring Framework受影响版本中，由于UriComponentsBuilder中在处理URL 时未正确过滤用户信息中的”\\[“，可能导致威胁者构造恶意URL绕过验证。\n当应用程序使用UriComponentsBuilder来解析外部提供的URL（如通过查询参数）并对解析的URL的主机执行验证检查时可能容易受到Open重定向攻击和SSRF攻击，导致网络钓鱼和内部网络探测等。\n\n官方修复说明: [https://spring.io/security/cve-2024-22243](https://spring.io/security/cve-2024-22243)", "a": "", "chunkIndex": 16, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8103084564208984, "index": 17}, {"type": "reRank", "value": 0.8424513339996338, "index": 6}, {"type": "rrf", "value": 0.027745885954841176, "index": 12}], "tokens": 227}, {"id": "689078f17e51d3dcd1aa6b9e", "updateTime": "2025-08-04T09:10:09.438Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 解决方案\n#### 解决方案一：针对漏洞实际情况进行分析处理\n\n* 找到Spring官方对这个漏洞的说明[https://spring.io/security/cve-2024-38819](https://spring.io/security/cve-2024-38819)\n\n* 其中有说到漏洞的触发条件，需同时满足以下两个条件：\n  * 1、应用程序通过RouterFunctions（WebMvc.fn/WebFlux.fn 的核心组件）处理静态资源\n  * 2、静态资源的位置被显式配置为FileSystemResource（即资源存储在服务器本地文件系统中，而非打包在应用 JAR 包内的类路径资源等）\n\n* 针对条件一、条件二可以项目全局搜索`RouterFunctions`，看看是否有如此处理静态资源。例如类似如下的代码\n\n```java\n@Bean\npublic RouterFunction<ServerResponse> staticResourceRouter() {\n    return RouterFunctions\n        .resources(\"/static/**\", new FileSystemResource(\"/path/to/local/static/files/\")); // 关键：使用FileSystemResource\n}\n```\n\n* `总结：` 目前是前后端分离项目，框架在后端不会放静态资源所以也就不存在使用RouterFunctions处理静态资源，同时更不可能用FileSystemResource直接使用服务器的静态资源，\n两者需要同时满足才有可能触发该漏洞。`而现在两者都不成立故此这个漏洞本质上根本不可能触发，项目上进行检查明确后可沟通讨论无需处理`", "a": "", "chunkIndex": 6, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.79240882396698, "index": 21}, {"type": "reRank", "value": 0.8512976765632629, "index": 4}, {"type": "rrf", "value": 0.027579737335834898, "index": 13}], "tokens": 435}, {"id": "689078f27e51d3dcd1aa6bec", "updateTime": "2025-08-04T09:10:10.639Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.3.32.jar】和【spring-context-5.3.34.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.34**。", "a": "", "chunkIndex": 15, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8257259130477905, "index": 14}, {"type": "reRank", "value": 0.5812674164772034, "index": 11}, {"type": "rrf", "value": 0.027222222222222224, "index": 14}], "tokens": 258}, {"id": "689078f37e51d3dcd1aa6c1e", "updateTime": "2025-08-04T09:10:11.434Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 测试方案\n\n能做的是：\n\n* 安全扫描。\n* 冒烟测试。\n* 确认修复后部署包中Spring的版本\n  * 1、找开发人员询问对应项目的部署包,然后解压部署包\n  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。\n  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.2.24.jar】和【spring-context-5.3.32.jar】，这种情况就属于不同版本号的重复的jar\n  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.32**。", "a": "", "chunkIndex": 22, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8264645338058472, "index": 13}, {"type": "reRank", "value": 0.5783630013465881, "index": 12}, {"type": "rrf", "value": 0.027212143650499815, "index": 15}], "tokens": 258}, {"id": "689078f37e51d3dcd1aa6c0a", "updateTime": "2025-08-04T09:10:11.381Z", "q": "# Spring安全漏洞\n## CVE-2024-22243\n### 解决方案\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.32</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)\n\n* **注意注意注意:** 框架7.2及以上有国际化功能，而spring5.3对国际化相关功能有调整\n  * `当框架版本为框架7.2及以上时`,请复制如下代码到项目上(注意包名要和java中相同,实现覆盖SiniCube框架逻辑)。`框架7.1及以下无需该处理`\n  * [SpringMessageConfig.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/SpringMessageConfig.java)[点击下载或右键另存为]\n  * [LocaleChangeInterceptor.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/LocaleChangeInterceptor.java)[点击下载或右键另存为]", "a": "", "chunkIndex": 20, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8247277140617371, "index": 15}, {"type": "reRank", "value": 0.5006208419799805, "index": 13}, {"type": "rrf", "value": 0.02667140825035562, "index": 16}], "tokens": 352}, {"id": "689078f27e51d3dcd1aa6bce", "updateTime": "2025-08-04T09:10:10.526Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 解决方案\n#### 方式一: 产品/项目的Pom.xml指定spring的版本\n\n推荐场景: 较老项目、不太能升级框架版本的项目\n\n```xml\n<properties>\n    <spring.version>5.3.34</spring.version>\n</properties>\n```\n\n第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)", "a": "", "chunkIndex": 12, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8379770517349243, "index": 11}, {"type": "reRank", "value": 0.21403037011623383, "index": 19}, {"type": "rrf", "value": 0.02638888888888889, "index": 17}], "tokens": 155}, {"id": "689078f17e51d3dcd1aa6ba4", "updateTime": "2025-08-04T09:10:09.508Z", "q": "# Spring安全漏洞\n## CVE-2024-38819\n### 解决方案\n#### 解决方案二：升级到Spring6\n\n* 框架[8.0.x](/guides/upgrade/update/8.0版本升级.html)就是Spring6的，等于需要项目上升级到框架8.0才行。**非必要，请勿升级。这属于大升级改造，应项目有规划时考虑，而不应该为了一个无法触发的漏洞升级**\n\n* 升级Spring6的代价很高，JDK需要从8最低升级到17，配套的SpringBoot、SpringCloud全部都需要升级，还有各种第三方框架Tomcat、Swagger都有较多调整", "a": "", "chunkIndex": 7, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8286399841308594, "index": 12}, {"type": "reRank", "value": 0.04257583990693092, "index": 22}, {"type": "rrf", "value": 0.02574682290807064, "index": 18}], "tokens": 214}, {"id": "689078f27e51d3dcd1aa6bd5", "updateTime": "2025-08-04T09:10:10.575Z", "q": "# Spring安全漏洞\n## CVE-2024-22262\n### 解决方案\n#### 方式二: 升级框架版本\n\n推荐场景: 产品最新版本、或者能持续升级框架版本的项目\n\n以下框架版本已解决该问题，**内置了方式一的解决**\n\n| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             \n|-------------------------------|---------------------------------------------------|\n| 后端: 7.4.368及以上 前端: 7.4.330及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |\n| 后端: 7.3.441及以上 前端: 7.3.449及以上 | 请参考[7.3版本升级说明](/guides/upgrade/update/7.3版本升级.md) |", "a": "", "chunkIndex": 13, "datasetId": "688c88ce3dfaebd458ea5132", "collectionId": "689078e17e51d3dcd1aa672e", "sourceId": "689078da7e51d3dcd1aa66a6", "sourceName": "Spring安全漏洞.txt", "score": [{"type": "embedding", "value": 0.8090394735336304, "index": 18}, {"type": "reRank", "value": 0.343763142824173, "index": 17}, {"type": "rrf", "value": 0.025478740668614087, "index": 19}], "tokens": 247}], "duration": "2.428s", "usingReRank": true, "searchMode": "embedding", "limit": 5000, "similarity": 0, "usingSimilarityFilter": true}}