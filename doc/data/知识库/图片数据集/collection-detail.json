{"code": 200, "statusText": "", "message": "", "data": {"_id": "68a594c98418a5efe1b5f3b4", "parentId": null, "teamId": "685cdf81b8ad138b9d3c00df", "tmbId": "685cdf81b8ad138b9d3c00e1", "datasetId": "6890415ff78ac5722344680c", "type": "images", "name": "111", "trainingType": "imageParse", "chunkSplitMode": "paragraph", "paragraphChunkAIMode": "forbid", "paragraphChunkDeep": 5, "paragraphChunkMinSize": 100, "createTime": "2025-08-20T09:26:33.917Z", "updateTime": "2025-08-20T09:26:33.917Z", "__v": 0, "dataset": {"_id": "6890415ff78ac5722344680c", "parentId": null, "teamId": "685cdf81b8ad138b9d3c00df", "tmbId": "685cdf81b8ad138b9d3c00e1", "type": "dataset", "avatar": "core/dataset/commonDatasetColor", "name": "漏洞库", "vectorModel": "text-embedding-v3", "agentModel": "qwen-max", "vlmModel": "qwen-vl-max", "intro": "", "inheritPermission": true, "updateTime": "2025-08-04T05:13:03.880Z", "__v": 0}, "indexAmount": 8, "sourceName": "111", "permission": {"permission": 4294967295, "isOwner": true, "hasManagePer": true, "hasWritePer": true, "hasReadPer": true, "hasManageRole": true, "hasWriteRole": true, "hasReadRole": true, "role": 4294967295, "roleList": {"read": {"name": "common:permission.read", "description": "dataset:permission.des.read", "value": 4, "checkBoxType": "single"}, "write": {"name": "common:permission.write", "description": "dataset:permission.des.write", "value": 2, "checkBoxType": "single"}, "manage": {"name": "common:permission.manager", "description": "dataset:permission.des.manage", "value": 1, "checkBoxType": "single"}}, "perList": {"owner": 4294967295, "read": 4, "write": 2, "manage": 1}, "rolePerMap": {}}, "errorCount": 0}}