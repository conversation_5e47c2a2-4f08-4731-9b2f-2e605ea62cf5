<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.sinitek.sinicube</groupId>
        <artifactId>sinitek-sinicube</artifactId>
        <version>8.0.8</version>
    </parent>

    <artifactId>sini-mind-backend</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>sinitek-mind-api</module>
        <module>sinitek-mind-app</module>
        <module>sinitek-mind-plugins</module>
    </modules>

    <properties>
        <spring-ai.version>1.0.0</spring-ai.version>
        <langchain4j-ext.version>1.2.0-beta8</langchain4j-ext.version>
        <langchain4j.version>1.2.0</langchain4j.version>

        <!-- 使用环境变量 -->
<!--        <dockerRegistry>192.168.23.94:80</dockerRegistry>-->
<!--        <dockerNamespace>sirmapp</dockerNamespace>-->
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-community-xinference</artifactId>
                <version>${langchain4j-ext.version}</version>
            </dependency>

            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-document-parser-apache-pdfbox</artifactId>
                <version>${langchain4j-ext.version}</version>
            </dependency>

            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-document-parser-apache-tika</artifactId>
                <version>${langchain4j-ext.version}</version>
            </dependency>

            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.sinitek.sinicube</groupId>
                <artifactId>sinitek-mind-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinitek.sinicube</groupId>
                <artifactId>sinitek-mind-app</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinitek.sinicube</groupId>
                <artifactId>sinitek-mind-plugins-vector-store-milvus</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinitek.sinicube</groupId>
                <artifactId>sinitek-mind-vector-store-pgvector</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>data/**</exclude>
                </excludes>
            </testResource>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>data/**</include>
                </includes>
            </testResource>
            <testResource>
                <directory>src/test/java</directory>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </testResource>
        </testResources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <!-- 使打包后的jar不包含assembly目录 -->
                        <exclude>assembly/**</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- 使用 Jib 的配置方式（无需 Dockerfile） -->
<!--            <plugin>-->
<!--                <groupId>com.google.cloud.tools</groupId>-->
<!--                <artifactId>jib-maven-plugin</artifactId>-->
<!--                <version>3.3.2</version>-->
<!--                <configuration>-->
<!--                    <from>-->
<!--                        &lt;!&ndash; 使用你的自定义基础镜像 &ndash;&gt;-->
<!--                        <image>${dockerRegistry}/jdk/sinitek-jdk17:1.0</image>-->
<!--                    </from>-->
<!--                    <to>-->
<!--                        <image>${dockerRegistry}/${dockerNamespace}/${project.name}</image>-->
<!--                        <tags>-->
<!--                            <tag>${project.version}</tag>-->
<!--                        </tags>-->
<!--                        &lt;!&ndash; 认证配置 &ndash;&gt;-->
<!--                        <auth>-->
<!--                            <username>${dockerUsername}</username>-->
<!--                            <password>${dockerPassword}</password>-->
<!--                        </auth>-->
<!--                    </to>-->
<!--                    <container>-->
<!--                        &lt;!&ndash; 容器内的工作目录 &ndash;&gt;-->
<!--                        <workingDirectory>/home/<USER>/workingDirectory>-->
<!--                        &lt;!&ndash; 容器内的应用根目录 &ndash;&gt;-->
<!--                        <appRoot>/home/<USER>/appRoot>-->
<!--                        &lt;!&ndash; 暴露端口 &ndash;&gt;-->
<!--                        <ports>-->
<!--                            <port>8096</port>-->
<!--                        </ports>-->
<!--                        &lt;!&ndash; 设置应用 jar 文件名 &ndash;&gt;-->
<!--                        <mainClass>org.springframework.boot.loader.JarLauncher</mainClass>-->
<!--                        &lt;!&ndash; 设置环境变量 &ndash;&gt;-->
<!--                        <environment>-->
<!--                            <JAVA_OPTS>-Xmx512m -Xms256m</JAVA_OPTS>-->
<!--                        </environment>-->
<!--                        &lt;!&ndash; 设置创建时间 &ndash;&gt;-->
<!--                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>-->
<!--                    </container>-->
<!--                    &lt;!&ndash; 允许不安全的镜像仓库（如果是 HTTP 而非 HTTPS） &ndash;&gt;-->
<!--                    <allowInsecureRegistries>true</allowInsecureRegistries>-->
<!--                </configuration>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
        </plugins>
    </build>

    <!--MAVEN打包选择运行环境-->
    <!--
         1:dev(默认) 开发环境
         2:test 测试环境
         3:prod 生产环境
    -->
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profileActive>dev</profileActive>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profileActive>test</profileActive>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profileActive>prod</profileActive>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
    </profiles>

</project>
