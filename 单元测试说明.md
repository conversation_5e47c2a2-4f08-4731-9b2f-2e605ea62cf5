# 单元测试说明

## 一、单元测试的重要性

本项目实施单元测试主要基于以下原因：

### 1. 应对频繁架构变动
项目前期处于快速迭代阶段，会经历多次重大技术架构调整（如 MongoDB 替换为 MySQL 数据库、前端 React 框架替换为 Vue 框架等）。单元测试可以帮助我们：
- 快速验证代码在新架构下的正确性
- 精准定位架构迁移过程中出现的问题
- 确保核心业务逻辑在架构变动中不受影响

### 2. 保障项目长期稳定性
随着项目成熟度提高，单元测试将成为保障系统稳定性的重要手段：
- 防止代码重构或功能扩展时引入回归问题
- 提高代码质量，减少线上故障
- 增强团队对代码的信心，降低维护成本

## 二、当前测试环境配置

### 1. 数据库配置
- **当前策略**：单元测试直接使用 MongoDB 数据库（非内存数据库）
- **原因**：为了提高测试效率，避免内存数据库额外的配置和初始化脚本维护
- **数据影响**：测试数据会真实写入 MongoDB 数据库，不会自动回滚
- **未来规划**：项目稳定后，将切换到 H2 等内存数据库，并开启事务自动回滚

### 2. 事务配置
- `MindBaseTestApplication.java` 类中的 `@Transactional` 注解默认处于注释状态
  ```java
  //@Transactional
  ```
- **原因**：便于测试完成后查看真实数据库中的数据状态，快速定位问题
- **未来规划**：切换到内存数据库后，将启用 `@Transactional` 注解，使测试数据在每次测试后自动回滚

## 三、单元测试规范

项目单元测试需严格遵循 `.trae/rules/project_rules.md` 文件中第 11 节「单元测试规范」的要求，主要包括：
- 测试类必须继承 `MindBaseTestApplication`
- 使用父类提供的 `MockMvc` 对象和请求构建方法
- 原则上禁止使用 Mockito 模拟方法返回值
- CRUD 接口测试需按指定顺序执行并验证
- 测试数据需隔离，确保测试独立性
- 仅支持 `GET` 和 `POST` 两种 HTTP 请求方法

## 四、注意事项

1. 执行单元测试前，请确保 MongoDB 数据库连接正常
2. 测试完成后，如需清理测试数据，请手动执行删除操作
3. 编写测试用例时，应确保测试数据的唯一性，避免相互干扰
4. 项目稳定后，测试环境将切换到内存数据库并启用事务回滚，相关测试用例无需大幅修改

## 五、测试策略与实践

### 1. 测试对象选择
- **默认策略**：以接口测试为主，不需要为每个 Service 方法单独编写单元测试
- **原因**：接口测试更接近实际业务场景，能够验证完整的业务流程
- **范围**：重点测试 Controller 层暴露的 API 接口

### 2. 测试组合策略
- **组合测试**：不要求一个接口对应一个单元测试，可以根据业务逻辑组合多个接口进行测试
- **流程测试**：推荐按照业务流程组合测试（如新增 → 修改 → 查询 → 删除），验证完整业务链路
- **示例**：
  ```java
  @Test
  public void testModelCrud() {
      // 新增模型
      String modelId = saveModel();
      
      // 更新模型
      updateModel(modelId);
      
      // 查看模型详情
      getModelDetail(modelId);
      
      // 删除模型
      deleteModel(modelId);
  }
  ```

### 3. 业务场景化测试
- **场景驱动**：根据具体业务场景设计测试用例
- **前置条件处理**：对于有依赖关系的测试，需先完成前置操作
- **示例**：知识库测试流程
  1. 先创建知识库
  2. 上传文件到知识库
  3. 验证文件数据分块效果
  4. 测试向量检索功能

### 4. 参考示例
项目中已有单元测试实现可供参考：
* ModelControllerTest.java
  * 包含模型CRUD、列表查询、默认模型更新等测试
* DatasetCollectionControllerTest.java
  * 包含知识库创建、文件上传、数据分块预览等测试

### 5. 测试代码组织
- **私有方法封装**：将重复的测试逻辑封装为私有方法
- **测试数据准备**：使用 `@BeforeEach` 注解准备测试数据
- **断言验证**：每个操作后必须验证返回结果的正确性
  ```java
  // 验证HTTP状态码和业务响应码
  .andExpect(status().isOk())
  .andExpect(jsonPathCodeIs200())
  
  // 验证具体字段值
  .andExpect(jsonPath("$.data.model", is(modelId)))
  ```

## 六、使用Trae实现单元测试

Trae是本项目使用的AI辅助开发工具，可以显著提高单元测试的编写效率和质量。以下是使用Trae实现单元测试的方法：

### 1. Trae生成单元测试类
通过Trae的代码生成功能，可以快速为Controller类创建对应的单元测试类。

**提示词示例**：
```
#ModelController.java增加对应的单元测试类，初始化测试数据如下：
{
  "modelName": "测试模型",
  "modelCode": "test_model",
  "modelType": "LLM",
  "provider": "OPENAI"
}
```

**操作步骤**：
1. 打开需要生成测试的Controller类（如ModelController.java）
2. 在Trae聊天窗口中输入上述提示词
3. Trae会自动分析Controller中的接口方法，并生成对应的测试类
4. 生成的测试类会包含基础的CRUD测试方法和初始化数据
