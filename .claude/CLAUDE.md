---
alwaysApply: true
---

# Mind AI项目规范文档

## 目录
1. [项目概述](#1-项目概述)
2. [技术栈](#2-技术栈)
3. [包结构规范](#3-包结构规范)
4. [命名规范](#4-命名规范)
5. [接口规范](#5-接口规范)
6. [数据库规范](#6-数据库规范)
7. [异常处理规范](#7-异常处理规范)
8. [MyBatis Plus使用规范](#8-mybatis-plus使用规范)
9. [Swagger文档规范](#9-swagger文档规范)
10. [缓存规范](#10-缓存规范)
11. [单元测试规范](#11-单元测试规范)
12. [国际化改造](#12-国际化改造)

## 1. 项目概述
Mind AI是一个AI平台，支持连接不同大模型、私有知识库向量化训练和动态智能体配置。本文档规定了项目开发必须遵循的技术规范。

## 2. 技术栈
**核心技术栈**：Java17 + SpringBoot3 + Spring AI 1 + MongoDB

## 3. 包结构规范

### 3.1 标准包结构
```
com.sinitek.mind.[模块名]
├── dao/          # 持久化层
├── controller/   # 控制器层
├── service/      # 业务逻辑层
│   └── impl/     # 业务逻辑实现
├── mapper/       # 数据访问层
├── entity/       # 实体类
├── dto/          # 数据传输对象
├── po/           # 持久化对象
├── config/       # 配置类
├── util/         # 工具类
├── exception/    # 异常类
├── constant/     # 常量定义
├── support/      # 功能实现支持类
├── event/        # 当前模块声明的事件
├── enumation/    # 枚举类
└── listener/     # 当前模块监听的事件
```

### 3.2 包约束规则
- **【强制】** service实现类必须通过dao层进行持久化操作
- **【强制】** dao层入参/出参仅限po或entity包下的类
- **【强制】** service层入参/出参仅限dto包下的类
- **【强制】** mapper接口命名格式：`[Entity名称]Mapper`
- **【强制】** entity类必须：
  ```java
  @TableName("table_name")
  public class UserEntity extends BaseEntity {
      public static final String ENTITY_NAME = "USER";
      // 字段定义
  }
  ```

## 4. 命名规范

### 4.1 类命名约定
| 包名 | 命名规则 | 示例 |
|------|----------|------|
| controller | *Controller结尾 | UserController |
| service接口 | I*Service结尾 | IUserService |
| service.impl | *ServiceImpl结尾 | UserServiceImpl |
| entity | *Entity结尾 | UserEntity |
| dto | *DTO结尾 | UserDTO |
| po | *PO结尾 | UserPO |
| util | *Util结尾 | StringUtil |
| event | *Event结尾 | UserEvent |
| listener | *Listener结尾 | UserListener |
| enumation | *Enum结尾 | UserTypeEnum |

### 4.2 属性命名约定
- **【强制】** Boolean类型属性命名：`xxx_flag`/`xxxFlag`（禁止使用`is_xxx`/`isXxx`）
- **【强制】** 时间日期类型：必须使用`java.util.Date`

### 4.3 Lombok注解规范
- **【强制】** 继承类必须添加：`@EqualsAndHashCode(callSuper = true)`
- **【强制】** 工具类构造函数：`@AllArgsConstructor(access = AccessLevel.PRIVATE)`
- **【强制】** 数据类必须添加：
  ```java
  @Data
  @SuperBuilder
  @NoArgsConstructor
  @AllArgsConstructor
  ```

### 4.4 Swagger注解规范
- **【强制】** 数据类必须添加：`@ApiModel(description = "xxx")`
- **【强制】** 字段必须添加：`@ApiModelProperty("xxx")`

## 5. 接口规范

### 5.1 URL命名规则
基础格式：`/frontend/api/[通用分类]/[模块]/[功能]`

通用分类：
- `/frontend/api` - 应用数据接口
- `/frontend/api/remote` - 对外服务接口
- `/frontend/api/open-api` - 第三方系统接口

功能动词规范：
| 动词 | 说明 |
|------|------|
| list | 查询列表 |
| draft | 保存草稿 |
| save | 保存生效 |
| submit | 提交流程 |
| delete | 删除操作 |
| detail | 详情查询 |
| load | 加载数据 |
| match | 模糊匹配 |

### 5.2 统一响应格式
```java
public class RequestResult<T> {
    private boolean success;  // 接口是否成功
    private String message;   // 提示信息
    private String code;      // 状态码
    private T data;           // 业务数据
}
```

### 5.3 参数验证规范
**【强制】** Controller方法必须添加参数校验：
```java
@PostMapping("/save")
@ApiOperation("保存用户信息")
public RequestResult<Void> saveUser(@RequestBody @Valid UserCreateDTO dto) {
    userService.saveUser(dto);
    return RequestResult.success();
}
```

## 6. 数据库规范

### 6.1 表设计规约
- **【强制】** 表名格式：`业务名称_表作用`（例：`product_register_change`）
- **【强制】** 表名使用下划线命名，对应Java类使用大驼峰命名
- **【强制】** 禁止使用复数名词，避免以`info`结尾

### 6.2 字段设计规范
数据类型映射：
| 业务类型 | Java类型 | 数据库类型 | 备注 |
|----------|----------|------------|------|
| 固定长度字符串 | String | char(n) | 如：性别用char(1) |
| 可变长度字符串 | String | varchar(n) | 建议指定长度 |
| 布尔型 | Integer | tinyint | 1=是,0=否 |
| 整数 | Integer | int | 普通整数 |
| 长整数 | Long | bigint | ID/大数值 |
| 金额/百分比 | BigDecimal | decimal(a,b) | 金额保留4位小数 |
| 日期 | Date | date | yyyy-MM-dd |
| 日期时间 | Date | datetime | yyyy-MM-dd HH:mm:ss |
| 大文本 | String | longtext | 超过255字符的文本 |

特殊字段命名：
- 布尔字段：`xxx_flag`（例：`delete_flag`）
- 时间字段：`xxx_time`/`xxx_date`（例：`create_time`）
- 状态字段：`xxx_status`（例：`order_status`）

### 6.3 索引规范
- **【强制】** 普通索引命名：`i_表名两位序号`（例：`i_user01`）
- **【强制】** 唯一索引命名：`uk_表名两位序号`（例：`uk_user01`）

## 7. 异常处理规范

### 7.1 异常类型
- **业务异常**：用户操作错误，需返回明确提示
  ```java
  throw new BusinessException("用户名已存在");
  ```
- **系统异常**：系统内部错误，不暴露敏感信息
  ```java
  throw new SirmException("数据库连接失败");
  ```

### 7.2 异常消息规范
- **【强制】** 异常消息需定义在：`resources/message/messages-[模块]_zh_CN.properties`
- **【强制】** messageCode格式：`com.sinitek.mind.[模块].[具体错误]`

## 8. MyBatis Plus使用规范

### 8.1 实体类规范
**【强制】** 基础实体继承：
```java
@Data
@TableName("user")
@EqualsAndHashCode(callSuper = true)
public class UserEntity extends BaseEntity {
    public static final String ENTITY_NAME = "USER";
    
    @ApiModelProperty("用户名")
    private String username;
    
    @ApiModelProperty("年龄")
    private Integer age;
}
```

### 8.2 Service层实现规范
**【强制】** Service实现类模板：
```java
@Service
public class UserServiceImpl implements IUserService {
    
    @Autowired
    private UserDao userDao;
    
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveUser(UserDTO dto) {
        UserEntity entity = UserEntity.builder()
            .username(dto.getUsername())
            .age(dto.getAge())
            .build();
        userDao.save(entity);
    }
    
    @Service
    static class UserDao extends ServiceImpl<UserMapper, UserEntity> {}
}
```

## 9. Swagger文档规范
**【强制】** Controller类必须添加Swagger注解：
```java
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "用户管理")
@RequestMapping("/frontend/api/user")
@RestController
public class UserController {
    
    @Operation(summary = "获取用户列表")
    @GetMapping("/list")
    public RequestResult<List<UserDTO>> getUserList() {
        // 实现
    }
}
```

## 10. 缓存规范
**【强制】** @Cacheable注解使用规范：
```java
@Cacheable(value = "com.sinitek.mind.user.getUserById", key = "#id")
public UserDTO getUserById(Long id) {
    // 实现
}
```

## 11. 单元测试规范

### 11.1 基础要求
- **测试类继承**：所有单元测试类必须继承`MindBaseTestApplication`，通过`import com.sinitek.mind.MindBaseTestApplication;`引入
- **请求构建**：使用父类提供的`MockMvc`对象和`getMockRequestForAdmin()`、`postMockRequestForAdmin()`方法组装`MockHttpServletRequestBuilder`

### 11.2 模拟限制
- **禁止Mock**：原则上禁止使用Mockito模拟方法返回值，必须调用实际方法
- **特殊处理**：如确需模拟，必须提前申请并明确模拟范围和方式，经架构师批准后方可实施

### 11.3 CRUD接口测试规范
1. **测试组织**：将Controller的新增/修改/详情/删除接口测试逻辑分别封装为**private方法**
2. **执行流程**：在单个测试方法中按以下顺序执行：
   ```java
   @Test
   public void testUserCrud() {
       // 新增用户
       Long id = saveUser();  
       
       // 更新用户
       updateUser(id); 
       
       // 查看用户详情
       getUserDetail(id);     
       
       // 删除用户
       deleteUser(id);            
   }
   ```
3. **验证要求**：每个操作后必须验证返回值正确性
4. **修改策略**：仅修改非唯一字段，修改后通过详情查询验证更新结果
5. **ID**: 所操作的实体ID应该由创建接口返回的，而不是mock出的

### 11.4 初始化测试数据规范
- **适用范围**：所有需要前置测试数据的接口测试（如列表查询、统计分析等）
- **实现方式**：使用`@BeforeEach`注解标记初始化方法，在每个测试方法运行前执行以下操作：
    1. **数据准备**：
        - 若存在名称等唯一标识字段，先按唯一标识删除已有测试数据
        - 执行新增操作创建全新测试数据（此数据在测试结束后不删除）
    2. **数据隔离**：确保测试数据与其他测试用例使用的标识不重复

**示例代码**：
```java
/**
 * 每个单元测试执行前自动初始化测试数据
 */
@BeforeEach
public void initTestData() {
    clearInitData();
    createInitData();
}
```

> **注意**：初始化方法（如示例中的`initTestData`）应作为测试类中的第一个方法，确保初始化逻辑在所有测试方法前执行且代码结构清晰。

### 11.5 列表查询测试规范
- **前置条件**：已执行11.4 初始化测试数据规范章节
- **查询验证**：列表查询测试应验证：
    - 返回状态码正确性
    - 列表非空
    - 包含初始化的测试数据
    - 分页参数生效（如适用）
- **数据保留**：测试完成后不删除列表查询专用的测试数据，但需确保每次测试前通过唯一标识清理旧数据

### 11.6 测试独立性
- **无依赖**：不同Controller的测试方法间禁止存在依赖关系
- **执行顺序**：禁止使用`@Order`注解控制执行顺序，确保测试可在任何顺序下正确运行
- **数据隔离**：每个测试方法必须使用独立的测试数据

### 11.6 请求方法规范
- **允许方法**：仅支持`GET`和`POST`两种HTTP请求方法，禁止使用`DELETE`、`PUT`、`PATCH`等其他方法
- **前置检查**：执行单元测试前必须验证接口请求方法，发现非允许方法时需：
    1. 立即提示用户接口方法不符合规范
    2. 待用户确认后将接口统一修改为`POST`方法
    3. 更新接口文档及调用方式
    4. 重新执行单元测试
- **测试要求**：所有单元测试用例必须使用`getMockRequestForAdmin()`或`postMockRequestForAdmin()`方法构建请求，禁止使用其他HTTP方法构建函数

### 11.7 结果验证规范
- **通用验证要求**：每个接口测试必须按以下顺序进行基础验证：
  ```java
  .andExpect(status().isOk())          // 验证HTTP状态码为200
  .andExpect(jsonPathCodeIs200())     // 验证业务响应码为200
  ```

- **参数验证要求**：对关键业务参数必须进行显式验证，使用jsonPath匹配具体字段：
  ```java
  // 验证数据非空
  .andExpect(jsonPath("$.data", notNullValue()))
  
  // 验证具体字段值
  .andExpect(jsonPath("$.data.defaultModels.llm.model", is("Qwen3-32B-AWQ")))
  ```

- **验证完整性**：
    - 列表查询接口必须验证返回集合非空且包含测试数据
    - 详情查询接口必须验证至少3个关键业务字段
    - 更新操作必须验证更新字段的新值
    - 删除操作必须验证资源不可访问

## 12. 国际化改造

### 12.1 异常信息处理规范

1. **异常信息常量类创建**
   - 在输出异常信息前，需要先创建一个异常信息常量类
   - 命名规则为：XXXErrorCodeConstant，其中XXX为具体的业务场景
   - 例如：UserErrorCodeConstant、OrderErrorCodeConstant

2. **错误信息编码规则**
   - 在异常信息常量类中定义每一个错误信息
   - 编码规则为：30XXYYYY
     - XX为模块编码，在国际化资源文件中有定义，如果没有则进行新建
     - **【强制】** YYYY为具体的错误编码，不能在进行分段，需要连续递增，一个模块最高可有9999个异常信息
     - 模块为唯一值
   - 例如：30010001表示用户模块的第0001个错误
   - **【强制】** 错误码必须使用String类型定义，便于国际化框架处理

3. **异常输出统一规范**
   - **【强制】** 输出异常统一使用：`throw new BussinessException(XXXErrorCodeConstant.XXXXX)`
   - 异常导包路径，import com.sinitek.sirm.framework.exception.BussinessException;
   - 直接在 `BussinessException` 构造函数中传入消息码，底层会自动处理国际化,不需要使用I18nUtil
   - **【强制】** 如果是捕获到的异常进行转化时，需要在输出业务异常前添加`log.error`，将错误堆栈保留下来
   - 例如：
     ```java
     try {
         // 业务逻辑
     } catch (Exception e) {
         log.error("处理用户信息失败", e);
         throw new BussinessException(UserErrorCodeConstant.USER_INFO_PROCESS_ERROR);
     }
     ```

4. **异常信息参数处理**
   - 异常信息需要传入参数时，直接在BussinessException后按照顺序添加参数
   - 在国际化资源文件中添加占位符，使用{0}、{1}等格式
   - 例如：
     ```java
     throw new BussinessException(UserErrorCodeConstant.USER_NOT_FOUND, userId);
     ```
     对应的资源文件：
     ```
     user.not.found=用户{0}不存在
     ```

5. **BussinessException说明**
   - BussinessException是基于spring的国际化封装的
   - 支持自动根据当前语言环境选择对应的错误信息
   - 支持参数化错误信息
   - 直接传入错误码即可，无需再次调用I18nUtil.t获取翻译后的资源，错误码应已经定义在XXErrorCodeConstant中，直接进行引用即可

6. **国际化资源文件规范**
   - **【强制】** 每个模块的错误信息必须在三个资源文件中同时定义：
     - `messages-mind_zh_CN.properties` - 简体中文
     - `messages-mind_zh_HK.properties` - 繁体中文
     - `messages-mind_en_US.properties` - 英文
   - **【强制】** 三个资源文件中的错误码必须保持一致，确保多语言环境下的错误信息统一
   - 资源文件中的消息码与错误码常量类中的定义完全对应
   - 支持参数化消息，使用{0}、{1}等占位符

7. **权限模块国际化示例**
   - 权限模块使用30-06作为模块编码
   - 错误码常量类：`PermissionErrorCodeConstant`
   - 已实现的错误码：
     - `30060001` - 成员没有对应权限
     - `30060002` - 使用orgId找不到对应的组织
     - `30060003` - 不支持团队资源类型使用
   - 三个资源文件均已添加对应的错误信息，支持中英文和繁体中文 

## 附录：规范检查清单
1. 所有类是否遵循命名规则
2. Controller方法是否添加参数校验
3. 数据库表名是否符合命名规范
4. 异常是否正确分类处理
5. Swagger注解是否完整
6. 缓存Key是否符合命名规范

