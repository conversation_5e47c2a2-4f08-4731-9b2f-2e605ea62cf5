# 携宁智脑

## 模块结构

``` lua
sinitek-mind-backend
├── sinitek-mind-api -- 基础API模块：供其他模块可使用的api
├── sinitek-mind-app -- 应用启动模块：放核心业务逻辑
├── sinitek-mind-plugins -- 插件模块：供可插拔选择的功能
|    ├── sinitek-mind-plugins-vector-store-milvus -- 对接milvus向量数据库(默认使用)
|    ├── sinitek-mind-vector-store-pgvector -- 对接pgvector向量数据库
```

## 核心技术栈

> 后端框架

Spring Boot - 主要应用框架

Spring Cloud - 微服务架构支持

Spring AI (1.0.0) - AI集成框架

MyBatis Plus - ORM框架

> 前端框架

vue3 + Reactflow


> AI/机器学习

大模型集成 - openai、ollama、xinference

JTokkit (0.3.0) - Token计算工具


> 数据存储

Mysql - 主要数据存储（支持切换）

PGvector - 向量数据库（支持切换Milvus、Chroma、Es、Weavuate、Oracle、Redis等）

MongoDB - fastgpt数据存储 (前期使用)



> 部署与运维

Docker - 容器化部署 (基于JDK17镜像)

Jenkins - 自动化构建及部署






## 目录结构

com.sinitek.mind




## 混合部署方式

1. 通过Jenkins打包成docker镜像
2. 测试环境直接通过docker-compose运行镜像
3. nginx统一配置反向代理

## 权限说明

FastGPT中的权限部分，是由框架内置的权限进行实现的，以下为FastGPT中权限字段与框架中权限字段对应关系

| FastGPT    | 框架               | 备注                                                             | 
|------------|------------------|----------------------------------------------------------------|
| teamId     | tenantId         | teamId对应着框架中的多租户Id，目前并未使用                                      |
| tmbId      | orgId（组织表的orgId） | FastGPT中的成员、部门、群组都直接映射到框架中的组织结构表，可以直接使用orgId全部表示               | 
| groupId    | orgId（组织表的orgId） | FastGPT中的成员、部门、群组都直接映射到框架中的组织结构表，可以直接使用orgId全部表示               |
| orgId      | orgId（组织表的orgId） | FastGPT中的成员、部门、群组都直接映射到框架中的组织结构表，可以直接使用orgId全部表示               |
| permission | permission       | permission使用long类型进行表示，这样才可以表示拥有者的权限-0xFFFFFFFF（4,294,967,295） |

### 权限存储逻辑

FastGPT中的权限分为三种资源，分别为应用app、知识库dataset以及团队team。

权限类型可以为read、write、manage和owner，但是owner级别的权限并不是在权限表中进行存储的，而是通过资源的所属人进行判断的，所以实际上数据库在存储时，只会存储三种权限类型（read、write、manage,详情见AuthTypeEnum）。

其中team较为特殊，每一个授权对象（成员/群组/部门）都会对应着四种权限（可以看作授权资源），app创建权限、apikey创建权限、dataset创建权限以及团队管理权限。所以在存储上与app和dataset进行了区分，下面是具体的存储逻辑。

#### app和dataset

表示授权资源和授权对象时，可以通过sprt_rightauth表中的objectkey和authorgid两个字段进行表示，而表示该对象拥有这个资源的什么权限时，则使用accesstype来进行表示。

这样只是表示出了授权对象拥有哪些资源的权限，但是并不确定这个资源是谁。sprt_rightauth表中的rightdefinekey可以来表示，资源类型（详情见ResourceTypeEnum）。

例如：

用户1（id为999000001）拥有app（Id为12345）的管理权限，那么在数据库中就会存在这么一条数据，来进行表示。

| authorgid | objectkey | rightdefinekey | righttype |
|-----------|-----------|----------------|-----------|
| 999000001 | 12345     | APP_PERMISSION | MANAGE    |

注：
- rightdefinekey为APP_PERMISSION，表示该资源为app
- righttype为MANAGE，表示权限类型为管理权限

#### team

由于团队类型的权限，每一个授权对象都会固定拥有四种权限，这四种权限可以直接看作四个资源，资源类型都是TEAM_PERMISSION，而表示是否拥有权限时，则使用sprt_rightauth表中的rejectflag字段进行表示。

rejectflag在数据库中存储为1，表示对象被拒绝，未拥有该权限。rejectflag在数据库中存储为0，表示对象未被拒绝，拥有该权限。

例如：

用户1（id为999000001）拥有app创建权限，未拥有其他三种权限，那么在数据库中就会存在这么四条数据，来进行表示。

| authorgid | objectkey     | rightdefinekey  | righttype | rejectflag | 
|-----------|---------------|-----------------|-----------|------------|
| 999000001 | appCreate     | TEAM_PERMISSION | ACCESS    | 0          | 
| 999000001 | datasetCreate | TEAM_PERMISSION | ACCESS    | 1          | 
| 999000001 | apikeyCreate  | TEAM_PERMISSION | ACCESS    | 1          | 
| 999000001 | manager       | TEAM_PERMISSION | ACCESS    | 1          | 

注：
- 团队的objectkey一共只有四种，详见TeamResourceEnum枚举
- 团队的righttype一直为ACCESS，这是框架默认的righttype，而rejectflag则是表示是否拥有权限的字段
- 因为团队的权限一共只有四种，为了统一，在数据库中会将该对象的四种权限都进行了存储。

## 免登录窗口自定义身份验证

在使用免登录窗口时，可能需要添加自定义的身份验证，使用方来校验是否可以进行对话。

使用步骤：
1. 在创建免登录窗口时，设置身份验证为接收authToken的api地址
2. 获取到免登录窗口的链接后，使用方需要在url上在拼接一个authToken的字段用于校验
3. 免登录窗口在请求数据或对话时，后端会向第一步中配置的api地址发送POST请求，请求参数为authToken
4. 获取到返回值中的success字段进行判断，是否通过校验，success字段为true表示校验通过

注：
- 身份验证的api地址，需要接受post请求，请求参数为：{"authToken":"XXXX"},返回参数必须要包含succes字段，否则默认校验失败，例:{"success":true},当失败时，可以返回message字段或msg字段来表示失败原因
- 免登录窗口还可以手动设置uid，通过在url上添加customUid字段进行设置，例：http://localhost:5100/gpt/chat/share?shareId=9667441f47774f0eb2f96d83&authToken=sinitek&customUid=yunqilee
- customUid主要用于保存对话历史和获取对话历史
- authToken是由使用方自己拼接的，例：项目A需要将免登录窗口内嵌至项目A的页面中，当打开免登录窗口时，项目A的前端自动拼接当前用户的token，然后进行校验，校验通过则可以进行对话

例如：
1. 创建免登录窗口时，设置身份验证为http://localhost:8080/share/auth
2. 获取到免登录窗口的链接后，需要在url上在拼接一个authToken的字段用于校验，例：http://localhost:5100/gpt/chat/share?shareId=9667441f47774f0eb2f96d83&authToken=sinitek
3. 如果需要自定义customUid，则拼接一个customUid，例：http://localhost:5100/gpt/chat/share?shareId=9667441f47774f0eb2f96d83&authToken=sinitek&customUid=yunqilee
4. 使用者在访问拼接好的免登录窗口链接后，后端会先向http://localhost:8080/share/auth这个地址发送post请求，请求参数为{"authToken":"sinitek"}，如果返回参数为{"success":true}，则校验通过
5. 校验通过后，才可以获取历史对话记录（每一次获取历史对话记录和对话时，都会进行调用身份验证接口）